{"ast": null, "code": "var Browser = function () {\n  function Browser() {\n    this.firefox = false;\n    this.ie = false;\n    this.edge = false;\n    this.newEdge = false;\n    this.weChat = false;\n  }\n  return Browser;\n}();\nvar Env = function () {\n  function Env() {\n    this.browser = new Browser();\n    this.node = false;\n    this.wxa = false;\n    this.worker = false;\n    this.svgSupported = false;\n    this.touchEventsSupported = false;\n    this.pointerEventsSupported = false;\n    this.domSupported = false;\n    this.transformSupported = false;\n    this.transform3dSupported = false;\n    this.hasGlobalWindow = typeof window !== 'undefined';\n  }\n  return Env;\n}();\nvar env = new Env();\nif (typeof wx === 'object' && typeof wx.getSystemInfoSync === 'function') {\n  env.wxa = true;\n  env.touchEventsSupported = true;\n} else if (typeof document === 'undefined' && typeof self !== 'undefined') {\n  env.worker = true;\n} else if (!env.hasGlobalWindow || 'Deno' in window) {\n  env.node = true;\n  env.svgSupported = true;\n} else {\n  detect(navigator.userAgent, env);\n}\nfunction detect(ua, env) {\n  var browser = env.browser;\n  var firefox = ua.match(/Firefox\\/([\\d.]+)/);\n  var ie = ua.match(/MSIE\\s([\\d.]+)/) || ua.match(/Trident\\/.+?rv:(([\\d.]+))/);\n  var edge = ua.match(/Edge?\\/([\\d.]+)/);\n  var weChat = /micromessenger/i.test(ua);\n  if (firefox) {\n    browser.firefox = true;\n    browser.version = firefox[1];\n  }\n  if (ie) {\n    browser.ie = true;\n    browser.version = ie[1];\n  }\n  if (edge) {\n    browser.edge = true;\n    browser.version = edge[1];\n    browser.newEdge = +edge[1].split('.')[0] > 18;\n  }\n  if (weChat) {\n    browser.weChat = true;\n  }\n  env.svgSupported = typeof SVGRect !== 'undefined';\n  env.touchEventsSupported = 'ontouchstart' in window && !browser.ie && !browser.edge;\n  env.pointerEventsSupported = 'onpointerdown' in window && (browser.edge || browser.ie && +browser.version >= 11);\n  env.domSupported = typeof document !== 'undefined';\n  var style = document.documentElement.style;\n  env.transform3dSupported = (browser.ie && 'transition' in style || browser.edge || 'WebKitCSSMatrix' in window && 'm11' in new WebKitCSSMatrix() || 'MozPerspective' in style) && !('OTransition' in style);\n  env.transformSupported = env.transform3dSupported || browser.ie && +browser.version >= 9;\n}\nexport default env;", "map": {"version": 3, "names": ["Browser", "firefox", "ie", "edge", "newEdge", "weChat", "Env", "browser", "node", "wxa", "worker", "svgSupported", "touchEventsSupported", "pointerEventsSupported", "domSupported", "transformSupported", "transform3dSupported", "hasGlobalWindow", "window", "env", "wx", "getSystemInfoSync", "document", "self", "detect", "navigator", "userAgent", "ua", "match", "test", "version", "split", "SVGRect", "style", "documentElement", "WebKitCSSMatrix"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/core/env.js"], "sourcesContent": ["var Browser = (function () {\n    function Browser() {\n        this.firefox = false;\n        this.ie = false;\n        this.edge = false;\n        this.newEdge = false;\n        this.weChat = false;\n    }\n    return Browser;\n}());\nvar Env = (function () {\n    function Env() {\n        this.browser = new Browser();\n        this.node = false;\n        this.wxa = false;\n        this.worker = false;\n        this.svgSupported = false;\n        this.touchEventsSupported = false;\n        this.pointerEventsSupported = false;\n        this.domSupported = false;\n        this.transformSupported = false;\n        this.transform3dSupported = false;\n        this.hasGlobalWindow = typeof window !== 'undefined';\n    }\n    return Env;\n}());\nvar env = new Env();\nif (typeof wx === 'object' && typeof wx.getSystemInfoSync === 'function') {\n    env.wxa = true;\n    env.touchEventsSupported = true;\n}\nelse if (typeof document === 'undefined' && typeof self !== 'undefined') {\n    env.worker = true;\n}\nelse if (!env.hasGlobalWindow || 'Deno' in window) {\n    env.node = true;\n    env.svgSupported = true;\n}\nelse {\n    detect(navigator.userAgent, env);\n}\nfunction detect(ua, env) {\n    var browser = env.browser;\n    var firefox = ua.match(/Firefox\\/([\\d.]+)/);\n    var ie = ua.match(/MSIE\\s([\\d.]+)/)\n        || ua.match(/Trident\\/.+?rv:(([\\d.]+))/);\n    var edge = ua.match(/Edge?\\/([\\d.]+)/);\n    var weChat = (/micromessenger/i).test(ua);\n    if (firefox) {\n        browser.firefox = true;\n        browser.version = firefox[1];\n    }\n    if (ie) {\n        browser.ie = true;\n        browser.version = ie[1];\n    }\n    if (edge) {\n        browser.edge = true;\n        browser.version = edge[1];\n        browser.newEdge = +edge[1].split('.')[0] > 18;\n    }\n    if (weChat) {\n        browser.weChat = true;\n    }\n    env.svgSupported = typeof SVGRect !== 'undefined';\n    env.touchEventsSupported = 'ontouchstart' in window && !browser.ie && !browser.edge;\n    env.pointerEventsSupported = 'onpointerdown' in window\n        && (browser.edge || (browser.ie && +browser.version >= 11));\n    env.domSupported = typeof document !== 'undefined';\n    var style = document.documentElement.style;\n    env.transform3dSupported = ((browser.ie && 'transition' in style)\n        || browser.edge\n        || (('WebKitCSSMatrix' in window) && ('m11' in new WebKitCSSMatrix()))\n        || 'MozPerspective' in style)\n        && !('OTransition' in style);\n    env.transformSupported = env.transform3dSupported\n        || (browser.ie && +browser.version >= 9);\n}\nexport default env;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAI,YAAY;EACvB,SAASA,OAAOA,CAAA,EAAG;IACf,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,EAAE,GAAG,KAAK;IACf,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACA,OAAOL,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,IAAIM,GAAG,GAAI,YAAY;EACnB,SAASA,GAAGA,CAAA,EAAG;IACX,IAAI,CAACC,OAAO,GAAG,IAAIP,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACQ,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,eAAe,GAAG,OAAOC,MAAM,KAAK,WAAW;EACxD;EACA,OAAOZ,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,IAAIa,GAAG,GAAG,IAAIb,GAAG,CAAC,CAAC;AACnB,IAAI,OAAOc,EAAE,KAAK,QAAQ,IAAI,OAAOA,EAAE,CAACC,iBAAiB,KAAK,UAAU,EAAE;EACtEF,GAAG,CAACV,GAAG,GAAG,IAAI;EACdU,GAAG,CAACP,oBAAoB,GAAG,IAAI;AACnC,CAAC,MACI,IAAI,OAAOU,QAAQ,KAAK,WAAW,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EACrEJ,GAAG,CAACT,MAAM,GAAG,IAAI;AACrB,CAAC,MACI,IAAI,CAACS,GAAG,CAACF,eAAe,IAAI,MAAM,IAAIC,MAAM,EAAE;EAC/CC,GAAG,CAACX,IAAI,GAAG,IAAI;EACfW,GAAG,CAACR,YAAY,GAAG,IAAI;AAC3B,CAAC,MACI;EACDa,MAAM,CAACC,SAAS,CAACC,SAAS,EAAEP,GAAG,CAAC;AACpC;AACA,SAASK,MAAMA,CAACG,EAAE,EAAER,GAAG,EAAE;EACrB,IAAIZ,OAAO,GAAGY,GAAG,CAACZ,OAAO;EACzB,IAAIN,OAAO,GAAG0B,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;EAC3C,IAAI1B,EAAE,GAAGyB,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC,IAC5BD,EAAE,CAACC,KAAK,CAAC,2BAA2B,CAAC;EAC5C,IAAIzB,IAAI,GAAGwB,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtC,IAAIvB,MAAM,GAAI,iBAAiB,CAAEwB,IAAI,CAACF,EAAE,CAAC;EACzC,IAAI1B,OAAO,EAAE;IACTM,OAAO,CAACN,OAAO,GAAG,IAAI;IACtBM,OAAO,CAACuB,OAAO,GAAG7B,OAAO,CAAC,CAAC,CAAC;EAChC;EACA,IAAIC,EAAE,EAAE;IACJK,OAAO,CAACL,EAAE,GAAG,IAAI;IACjBK,OAAO,CAACuB,OAAO,GAAG5B,EAAE,CAAC,CAAC,CAAC;EAC3B;EACA,IAAIC,IAAI,EAAE;IACNI,OAAO,CAACJ,IAAI,GAAG,IAAI;IACnBI,OAAO,CAACuB,OAAO,GAAG3B,IAAI,CAAC,CAAC,CAAC;IACzBI,OAAO,CAACH,OAAO,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACjD;EACA,IAAI1B,MAAM,EAAE;IACRE,OAAO,CAACF,MAAM,GAAG,IAAI;EACzB;EACAc,GAAG,CAACR,YAAY,GAAG,OAAOqB,OAAO,KAAK,WAAW;EACjDb,GAAG,CAACP,oBAAoB,GAAG,cAAc,IAAIM,MAAM,IAAI,CAACX,OAAO,CAACL,EAAE,IAAI,CAACK,OAAO,CAACJ,IAAI;EACnFgB,GAAG,CAACN,sBAAsB,GAAG,eAAe,IAAIK,MAAM,KAC9CX,OAAO,CAACJ,IAAI,IAAKI,OAAO,CAACL,EAAE,IAAI,CAACK,OAAO,CAACuB,OAAO,IAAI,EAAG,CAAC;EAC/DX,GAAG,CAACL,YAAY,GAAG,OAAOQ,QAAQ,KAAK,WAAW;EAClD,IAAIW,KAAK,GAAGX,QAAQ,CAACY,eAAe,CAACD,KAAK;EAC1Cd,GAAG,CAACH,oBAAoB,GAAG,CAAET,OAAO,CAACL,EAAE,IAAI,YAAY,IAAI+B,KAAK,IACzD1B,OAAO,CAACJ,IAAI,IACV,iBAAiB,IAAIe,MAAM,IAAM,KAAK,IAAI,IAAIiB,eAAe,CAAC,CAAG,IACnE,gBAAgB,IAAIF,KAAK,KACzB,EAAE,aAAa,IAAIA,KAAK,CAAC;EAChCd,GAAG,CAACJ,kBAAkB,GAAGI,GAAG,CAACH,oBAAoB,IACzCT,OAAO,CAACL,EAAE,IAAI,CAACK,OAAO,CAACuB,OAAO,IAAI,CAAE;AAChD;AACA,eAAeX,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}