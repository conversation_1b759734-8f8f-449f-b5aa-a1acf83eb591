{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as interactionMutex from './interactionMutex.js';\nimport { isString, bind, defaults, clone } from 'zrender/lib/core/util.js';\n;\nvar RoamController = /** @class */function (_super) {\n  __extends(RoamController, _super);\n  function RoamController(zr) {\n    var _this = _super.call(this) || this;\n    _this._zr = zr;\n    // Avoid two roamController bind the same handler\n    var mousedownHandler = bind(_this._mousedownHandler, _this);\n    var mousemoveHandler = bind(_this._mousemoveHandler, _this);\n    var mouseupHandler = bind(_this._mouseupHandler, _this);\n    var mousewheelHandler = bind(_this._mousewheelHandler, _this);\n    var pinchHandler = bind(_this._pinchHandler, _this);\n    /**\r\n     * Notice: only enable needed types. For example, if 'zoom'\r\n     * is not needed, 'zoom' should not be enabled, otherwise\r\n     * default mousewheel behaviour (scroll page) will be disabled.\r\n     */\n    _this.enable = function (controlType, opt) {\n      // Disable previous first\n      this.disable();\n      this._opt = defaults(clone(opt) || {}, {\n        zoomOnMouseWheel: true,\n        moveOnMouseMove: true,\n        // By default, wheel do not trigger move.\n        moveOnMouseWheel: false,\n        preventDefaultMouseMove: true\n      });\n      if (controlType == null) {\n        controlType = true;\n      }\n      if (controlType === true || controlType === 'move' || controlType === 'pan') {\n        zr.on('mousedown', mousedownHandler);\n        zr.on('mousemove', mousemoveHandler);\n        zr.on('mouseup', mouseupHandler);\n      }\n      if (controlType === true || controlType === 'scale' || controlType === 'zoom') {\n        zr.on('mousewheel', mousewheelHandler);\n        zr.on('pinch', pinchHandler);\n      }\n    };\n    _this.disable = function () {\n      zr.off('mousedown', mousedownHandler);\n      zr.off('mousemove', mousemoveHandler);\n      zr.off('mouseup', mouseupHandler);\n      zr.off('mousewheel', mousewheelHandler);\n      zr.off('pinch', pinchHandler);\n    };\n    return _this;\n  }\n  RoamController.prototype.isDragging = function () {\n    return this._dragging;\n  };\n  RoamController.prototype.isPinching = function () {\n    return this._pinching;\n  };\n  RoamController.prototype.setPointerChecker = function (pointerChecker) {\n    this.pointerChecker = pointerChecker;\n  };\n  RoamController.prototype.dispose = function () {\n    this.disable();\n  };\n  RoamController.prototype._mousedownHandler = function (e) {\n    if (eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      return;\n    }\n    var el = e.target;\n    while (el) {\n      if (el.draggable) {\n        return;\n      }\n      // check if host is draggable\n      el = el.__hostTarget || el.parent;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    // Only check on mosedown, but not mousemove.\n    // Mouse can be out of target when mouse moving.\n    if (this.pointerChecker && this.pointerChecker(e, x, y)) {\n      this._x = x;\n      this._y = y;\n      this._dragging = true;\n    }\n  };\n  RoamController.prototype._mousemoveHandler = function (e) {\n    if (!this._dragging || !isAvailableBehavior('moveOnMouseMove', e, this._opt) || e.gestureEvent === 'pinch' || interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var oldX = this._x;\n    var oldY = this._y;\n    var dx = x - oldX;\n    var dy = y - oldY;\n    this._x = x;\n    this._y = y;\n    this._opt.preventDefaultMouseMove && eventTool.stop(e.event);\n    trigger(this, 'pan', 'moveOnMouseMove', e, {\n      dx: dx,\n      dy: dy,\n      oldX: oldX,\n      oldY: oldY,\n      newX: x,\n      newY: y,\n      isAvailableBehavior: null\n    });\n  };\n  RoamController.prototype._mouseupHandler = function (e) {\n    if (!eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      this._dragging = false;\n    }\n  };\n  RoamController.prototype._mousewheelHandler = function (e) {\n    var shouldZoom = isAvailableBehavior('zoomOnMouseWheel', e, this._opt);\n    var shouldMove = isAvailableBehavior('moveOnMouseWheel', e, this._opt);\n    var wheelDelta = e.wheelDelta;\n    var absWheelDeltaDelta = Math.abs(wheelDelta);\n    var originX = e.offsetX;\n    var originY = e.offsetY;\n    // wheelDelta maybe -0 in chrome mac.\n    if (wheelDelta === 0 || !shouldZoom && !shouldMove) {\n      return;\n    }\n    // If both `shouldZoom` and `shouldMove` is true, trigger\n    // their event both, and the final behavior is determined\n    // by event listener themselves.\n    if (shouldZoom) {\n      // Convenience:\n      // Mac and VM Windows on Mac: scroll up: zoom out.\n      // Windows: scroll up: zoom in.\n      // FIXME: Should do more test in different environment.\n      // wheelDelta is too complicated in difference nvironment\n      // (https://developer.mozilla.org/en-US/docs/Web/Events/mousewheel),\n      // although it has been normallized by zrender.\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var factor = absWheelDeltaDelta > 3 ? 1.4 : absWheelDeltaDelta > 1 ? 1.2 : 1.1;\n      var scale = wheelDelta > 0 ? factor : 1 / factor;\n      checkPointerAndTrigger(this, 'zoom', 'zoomOnMouseWheel', e, {\n        scale: scale,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n    if (shouldMove) {\n      // FIXME: Should do more test in different environment.\n      var absDelta = Math.abs(wheelDelta);\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var scrollDelta = (wheelDelta > 0 ? 1 : -1) * (absDelta > 3 ? 0.4 : absDelta > 1 ? 0.15 : 0.05);\n      checkPointerAndTrigger(this, 'scrollMove', 'moveOnMouseWheel', e, {\n        scrollDelta: scrollDelta,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n  };\n  RoamController.prototype._pinchHandler = function (e) {\n    if (interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var scale = e.pinchScale > 1 ? 1.1 : 1 / 1.1;\n    checkPointerAndTrigger(this, 'zoom', null, e, {\n      scale: scale,\n      originX: e.pinchX,\n      originY: e.pinchY,\n      isAvailableBehavior: null\n    });\n  };\n  return RoamController;\n}(Eventful);\nfunction checkPointerAndTrigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  if (controller.pointerChecker && controller.pointerChecker(e, contollerEvent.originX, contollerEvent.originY)) {\n    // When mouse is out of roamController rect,\n    // default befavoius should not be be disabled, otherwise\n    // page sliding is disabled, contrary to expectation.\n    eventTool.stop(e.event);\n    trigger(controller, eventName, behaviorToCheck, e, contollerEvent);\n  }\n}\nfunction trigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  // Also provide behavior checker for event listener, for some case that\n  // multiple components share one listener.\n  contollerEvent.isAvailableBehavior = bind(isAvailableBehavior, null, behaviorToCheck, e);\n  // TODO should not have type issue.\n  controller.trigger(eventName, contollerEvent);\n}\n// settings: {\n//     zoomOnMouseWheel\n//     moveOnMouseMove\n//     moveOnMouseWheel\n// }\n// The value can be: true / false / 'shift' / 'ctrl' / 'alt'.\nfunction isAvailableBehavior(behaviorToCheck, e, settings) {\n  var setting = settings[behaviorToCheck];\n  return !behaviorToCheck || setting && (!isString(setting) || e.event[setting + 'Key']);\n}\nexport default RoamController;", "map": {"version": 3, "names": ["__extends", "Eventful", "eventTool", "interactionMutex", "isString", "bind", "defaults", "clone", "RoamController", "_super", "zr", "_this", "call", "_zr", "mousedownHandler", "_mouse<PERSON><PERSON><PERSON><PERSON>", "mousemoveHandler", "_mousemoveHandler", "mouseup<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "mousewheelHandler", "_mousewheel<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON>", "enable", "controlType", "opt", "disable", "_opt", "zoomOnMouseWheel", "moveOnMouseMove", "moveOnMouseWheel", "preventDefaultMouseMove", "on", "off", "prototype", "isDragging", "_dragging", "isPinching", "_pinching", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON>", "dispose", "e", "isMiddleOrRightButtonOnMouseUpDown", "el", "target", "draggable", "__host<PERSON><PERSON>get", "parent", "x", "offsetX", "y", "offsetY", "_x", "_y", "isAvailableBehavior", "gestureEvent", "isTaken", "oldX", "oldY", "dx", "dy", "stop", "event", "trigger", "newX", "newY", "shouldZoom", "<PERSON><PERSON><PERSON>", "wheelDelta", "absWheelDeltaDelta", "Math", "abs", "originX", "originY", "factor", "scale", "checkPointerAndTrigger", "absD<PERSON><PERSON>", "scrollDelta", "pinchScale", "pinchX", "pinchY", "controller", "eventName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contollerEvent", "settings", "setting"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/helper/RoamController.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as interactionMutex from './interactionMutex.js';\nimport { isString, bind, defaults, clone } from 'zrender/lib/core/util.js';\n;\nvar RoamController = /** @class */function (_super) {\n  __extends(RoamController, _super);\n  function RoamController(zr) {\n    var _this = _super.call(this) || this;\n    _this._zr = zr;\n    // Avoid two roamController bind the same handler\n    var mousedownHandler = bind(_this._mousedownHandler, _this);\n    var mousemoveHandler = bind(_this._mousemoveHandler, _this);\n    var mouseupHandler = bind(_this._mouseupHandler, _this);\n    var mousewheelHandler = bind(_this._mousewheelHandler, _this);\n    var pinchHandler = bind(_this._pinchHandler, _this);\n    /**\r\n     * Notice: only enable needed types. For example, if 'zoom'\r\n     * is not needed, 'zoom' should not be enabled, otherwise\r\n     * default mousewheel behaviour (scroll page) will be disabled.\r\n     */\n    _this.enable = function (controlType, opt) {\n      // Disable previous first\n      this.disable();\n      this._opt = defaults(clone(opt) || {}, {\n        zoomOnMouseWheel: true,\n        moveOnMouseMove: true,\n        // By default, wheel do not trigger move.\n        moveOnMouseWheel: false,\n        preventDefaultMouseMove: true\n      });\n      if (controlType == null) {\n        controlType = true;\n      }\n      if (controlType === true || controlType === 'move' || controlType === 'pan') {\n        zr.on('mousedown', mousedownHandler);\n        zr.on('mousemove', mousemoveHandler);\n        zr.on('mouseup', mouseupHandler);\n      }\n      if (controlType === true || controlType === 'scale' || controlType === 'zoom') {\n        zr.on('mousewheel', mousewheelHandler);\n        zr.on('pinch', pinchHandler);\n      }\n    };\n    _this.disable = function () {\n      zr.off('mousedown', mousedownHandler);\n      zr.off('mousemove', mousemoveHandler);\n      zr.off('mouseup', mouseupHandler);\n      zr.off('mousewheel', mousewheelHandler);\n      zr.off('pinch', pinchHandler);\n    };\n    return _this;\n  }\n  RoamController.prototype.isDragging = function () {\n    return this._dragging;\n  };\n  RoamController.prototype.isPinching = function () {\n    return this._pinching;\n  };\n  RoamController.prototype.setPointerChecker = function (pointerChecker) {\n    this.pointerChecker = pointerChecker;\n  };\n  RoamController.prototype.dispose = function () {\n    this.disable();\n  };\n  RoamController.prototype._mousedownHandler = function (e) {\n    if (eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      return;\n    }\n    var el = e.target;\n    while (el) {\n      if (el.draggable) {\n        return;\n      }\n      // check if host is draggable\n      el = el.__hostTarget || el.parent;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    // Only check on mosedown, but not mousemove.\n    // Mouse can be out of target when mouse moving.\n    if (this.pointerChecker && this.pointerChecker(e, x, y)) {\n      this._x = x;\n      this._y = y;\n      this._dragging = true;\n    }\n  };\n  RoamController.prototype._mousemoveHandler = function (e) {\n    if (!this._dragging || !isAvailableBehavior('moveOnMouseMove', e, this._opt) || e.gestureEvent === 'pinch' || interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var oldX = this._x;\n    var oldY = this._y;\n    var dx = x - oldX;\n    var dy = y - oldY;\n    this._x = x;\n    this._y = y;\n    this._opt.preventDefaultMouseMove && eventTool.stop(e.event);\n    trigger(this, 'pan', 'moveOnMouseMove', e, {\n      dx: dx,\n      dy: dy,\n      oldX: oldX,\n      oldY: oldY,\n      newX: x,\n      newY: y,\n      isAvailableBehavior: null\n    });\n  };\n  RoamController.prototype._mouseupHandler = function (e) {\n    if (!eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      this._dragging = false;\n    }\n  };\n  RoamController.prototype._mousewheelHandler = function (e) {\n    var shouldZoom = isAvailableBehavior('zoomOnMouseWheel', e, this._opt);\n    var shouldMove = isAvailableBehavior('moveOnMouseWheel', e, this._opt);\n    var wheelDelta = e.wheelDelta;\n    var absWheelDeltaDelta = Math.abs(wheelDelta);\n    var originX = e.offsetX;\n    var originY = e.offsetY;\n    // wheelDelta maybe -0 in chrome mac.\n    if (wheelDelta === 0 || !shouldZoom && !shouldMove) {\n      return;\n    }\n    // If both `shouldZoom` and `shouldMove` is true, trigger\n    // their event both, and the final behavior is determined\n    // by event listener themselves.\n    if (shouldZoom) {\n      // Convenience:\n      // Mac and VM Windows on Mac: scroll up: zoom out.\n      // Windows: scroll up: zoom in.\n      // FIXME: Should do more test in different environment.\n      // wheelDelta is too complicated in difference nvironment\n      // (https://developer.mozilla.org/en-US/docs/Web/Events/mousewheel),\n      // although it has been normallized by zrender.\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var factor = absWheelDeltaDelta > 3 ? 1.4 : absWheelDeltaDelta > 1 ? 1.2 : 1.1;\n      var scale = wheelDelta > 0 ? factor : 1 / factor;\n      checkPointerAndTrigger(this, 'zoom', 'zoomOnMouseWheel', e, {\n        scale: scale,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n    if (shouldMove) {\n      // FIXME: Should do more test in different environment.\n      var absDelta = Math.abs(wheelDelta);\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var scrollDelta = (wheelDelta > 0 ? 1 : -1) * (absDelta > 3 ? 0.4 : absDelta > 1 ? 0.15 : 0.05);\n      checkPointerAndTrigger(this, 'scrollMove', 'moveOnMouseWheel', e, {\n        scrollDelta: scrollDelta,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n  };\n  RoamController.prototype._pinchHandler = function (e) {\n    if (interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var scale = e.pinchScale > 1 ? 1.1 : 1 / 1.1;\n    checkPointerAndTrigger(this, 'zoom', null, e, {\n      scale: scale,\n      originX: e.pinchX,\n      originY: e.pinchY,\n      isAvailableBehavior: null\n    });\n  };\n  return RoamController;\n}(Eventful);\nfunction checkPointerAndTrigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  if (controller.pointerChecker && controller.pointerChecker(e, contollerEvent.originX, contollerEvent.originY)) {\n    // When mouse is out of roamController rect,\n    // default befavoius should not be be disabled, otherwise\n    // page sliding is disabled, contrary to expectation.\n    eventTool.stop(e.event);\n    trigger(controller, eventName, behaviorToCheck, e, contollerEvent);\n  }\n}\nfunction trigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  // Also provide behavior checker for event listener, for some case that\n  // multiple components share one listener.\n  contollerEvent.isAvailableBehavior = bind(isAvailableBehavior, null, behaviorToCheck, e);\n  // TODO should not have type issue.\n  controller.trigger(eventName, contollerEvent);\n}\n// settings: {\n//     zoomOnMouseWheel\n//     moveOnMouseMove\n//     moveOnMouseWheel\n// }\n// The value can be: true / false / 'shift' / 'ctrl' / 'alt'.\nfunction isAvailableBehavior(behaviorToCheck, e, settings) {\n  var setting = settings[behaviorToCheck];\n  return !behaviorToCheck || setting && (!isString(setting) || e.event[setting + 'Key']);\n}\nexport default RoamController;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAO,KAAKC,gBAAgB,MAAM,uBAAuB;AACzD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,0BAA0B;AAC1E;AACA,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDT,SAAS,CAACQ,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,EAAE,EAAE;IAC1B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,GAAG,GAAGH,EAAE;IACd;IACA,IAAII,gBAAgB,GAAGT,IAAI,CAACM,KAAK,CAACI,iBAAiB,EAAEJ,KAAK,CAAC;IAC3D,IAAIK,gBAAgB,GAAGX,IAAI,CAACM,KAAK,CAACM,iBAAiB,EAAEN,KAAK,CAAC;IAC3D,IAAIO,cAAc,GAAGb,IAAI,CAACM,KAAK,CAACQ,eAAe,EAAER,KAAK,CAAC;IACvD,IAAIS,iBAAiB,GAAGf,IAAI,CAACM,KAAK,CAACU,kBAAkB,EAAEV,KAAK,CAAC;IAC7D,IAAIW,YAAY,GAAGjB,IAAI,CAACM,KAAK,CAACY,aAAa,EAAEZ,KAAK,CAAC;IACnD;AACJ;AACA;AACA;AACA;IACIA,KAAK,CAACa,MAAM,GAAG,UAAUC,WAAW,EAAEC,GAAG,EAAE;MACzC;MACA,IAAI,CAACC,OAAO,CAAC,CAAC;MACd,IAAI,CAACC,IAAI,GAAGtB,QAAQ,CAACC,KAAK,CAACmB,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;QACrCG,gBAAgB,EAAE,IAAI;QACtBC,eAAe,EAAE,IAAI;QACrB;QACAC,gBAAgB,EAAE,KAAK;QACvBC,uBAAuB,EAAE;MAC3B,CAAC,CAAC;MACF,IAAIP,WAAW,IAAI,IAAI,EAAE;QACvBA,WAAW,GAAG,IAAI;MACpB;MACA,IAAIA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,KAAK,EAAE;QAC3Ef,EAAE,CAACuB,EAAE,CAAC,WAAW,EAAEnB,gBAAgB,CAAC;QACpCJ,EAAE,CAACuB,EAAE,CAAC,WAAW,EAAEjB,gBAAgB,CAAC;QACpCN,EAAE,CAACuB,EAAE,CAAC,SAAS,EAAEf,cAAc,CAAC;MAClC;MACA,IAAIO,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,EAAE;QAC7Ef,EAAE,CAACuB,EAAE,CAAC,YAAY,EAAEb,iBAAiB,CAAC;QACtCV,EAAE,CAACuB,EAAE,CAAC,OAAO,EAAEX,YAAY,CAAC;MAC9B;IACF,CAAC;IACDX,KAAK,CAACgB,OAAO,GAAG,YAAY;MAC1BjB,EAAE,CAACwB,GAAG,CAAC,WAAW,EAAEpB,gBAAgB,CAAC;MACrCJ,EAAE,CAACwB,GAAG,CAAC,WAAW,EAAElB,gBAAgB,CAAC;MACrCN,EAAE,CAACwB,GAAG,CAAC,SAAS,EAAEhB,cAAc,CAAC;MACjCR,EAAE,CAACwB,GAAG,CAAC,YAAY,EAAEd,iBAAiB,CAAC;MACvCV,EAAE,CAACwB,GAAG,CAAC,OAAO,EAAEZ,YAAY,CAAC;IAC/B,CAAC;IACD,OAAOX,KAAK;EACd;EACAH,cAAc,CAAC2B,SAAS,CAACC,UAAU,GAAG,YAAY;IAChD,OAAO,IAAI,CAACC,SAAS;EACvB,CAAC;EACD7B,cAAc,CAAC2B,SAAS,CAACG,UAAU,GAAG,YAAY;IAChD,OAAO,IAAI,CAACC,SAAS;EACvB,CAAC;EACD/B,cAAc,CAAC2B,SAAS,CAACK,iBAAiB,GAAG,UAAUC,cAAc,EAAE;IACrE,IAAI,CAACA,cAAc,GAAGA,cAAc;EACtC,CAAC;EACDjC,cAAc,CAAC2B,SAAS,CAACO,OAAO,GAAG,YAAY;IAC7C,IAAI,CAACf,OAAO,CAAC,CAAC;EAChB,CAAC;EACDnB,cAAc,CAAC2B,SAAS,CAACpB,iBAAiB,GAAG,UAAU4B,CAAC,EAAE;IACxD,IAAIzC,SAAS,CAAC0C,kCAAkC,CAACD,CAAC,CAAC,EAAE;MACnD;IACF;IACA,IAAIE,EAAE,GAAGF,CAAC,CAACG,MAAM;IACjB,OAAOD,EAAE,EAAE;MACT,IAAIA,EAAE,CAACE,SAAS,EAAE;QAChB;MACF;MACA;MACAF,EAAE,GAAGA,EAAE,CAACG,YAAY,IAAIH,EAAE,CAACI,MAAM;IACnC;IACA,IAAIC,CAAC,GAAGP,CAAC,CAACQ,OAAO;IACjB,IAAIC,CAAC,GAAGT,CAAC,CAACU,OAAO;IACjB;IACA;IACA,IAAI,IAAI,CAACZ,cAAc,IAAI,IAAI,CAACA,cAAc,CAACE,CAAC,EAAEO,CAAC,EAAEE,CAAC,CAAC,EAAE;MACvD,IAAI,CAACE,EAAE,GAAGJ,CAAC;MACX,IAAI,CAACK,EAAE,GAAGH,CAAC;MACX,IAAI,CAACf,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;EACD7B,cAAc,CAAC2B,SAAS,CAAClB,iBAAiB,GAAG,UAAU0B,CAAC,EAAE;IACxD,IAAI,CAAC,IAAI,CAACN,SAAS,IAAI,CAACmB,mBAAmB,CAAC,iBAAiB,EAAEb,CAAC,EAAE,IAAI,CAACf,IAAI,CAAC,IAAIe,CAAC,CAACc,YAAY,KAAK,OAAO,IAAItD,gBAAgB,CAACuD,OAAO,CAAC,IAAI,CAAC7C,GAAG,EAAE,WAAW,CAAC,EAAE;MAC7J;IACF;IACA,IAAIqC,CAAC,GAAGP,CAAC,CAACQ,OAAO;IACjB,IAAIC,CAAC,GAAGT,CAAC,CAACU,OAAO;IACjB,IAAIM,IAAI,GAAG,IAAI,CAACL,EAAE;IAClB,IAAIM,IAAI,GAAG,IAAI,CAACL,EAAE;IAClB,IAAIM,EAAE,GAAGX,CAAC,GAAGS,IAAI;IACjB,IAAIG,EAAE,GAAGV,CAAC,GAAGQ,IAAI;IACjB,IAAI,CAACN,EAAE,GAAGJ,CAAC;IACX,IAAI,CAACK,EAAE,GAAGH,CAAC;IACX,IAAI,CAACxB,IAAI,CAACI,uBAAuB,IAAI9B,SAAS,CAAC6D,IAAI,CAACpB,CAAC,CAACqB,KAAK,CAAC;IAC5DC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAEtB,CAAC,EAAE;MACzCkB,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNH,IAAI,EAAEA,IAAI;MACVC,IAAI,EAAEA,IAAI;MACVM,IAAI,EAAEhB,CAAC;MACPiB,IAAI,EAAEf,CAAC;MACPI,mBAAmB,EAAE;IACvB,CAAC,CAAC;EACJ,CAAC;EACDhD,cAAc,CAAC2B,SAAS,CAAChB,eAAe,GAAG,UAAUwB,CAAC,EAAE;IACtD,IAAI,CAACzC,SAAS,CAAC0C,kCAAkC,CAACD,CAAC,CAAC,EAAE;MACpD,IAAI,CAACN,SAAS,GAAG,KAAK;IACxB;EACF,CAAC;EACD7B,cAAc,CAAC2B,SAAS,CAACd,kBAAkB,GAAG,UAAUsB,CAAC,EAAE;IACzD,IAAIyB,UAAU,GAAGZ,mBAAmB,CAAC,kBAAkB,EAAEb,CAAC,EAAE,IAAI,CAACf,IAAI,CAAC;IACtE,IAAIyC,UAAU,GAAGb,mBAAmB,CAAC,kBAAkB,EAAEb,CAAC,EAAE,IAAI,CAACf,IAAI,CAAC;IACtE,IAAI0C,UAAU,GAAG3B,CAAC,CAAC2B,UAAU;IAC7B,IAAIC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC;IAC7C,IAAII,OAAO,GAAG/B,CAAC,CAACQ,OAAO;IACvB,IAAIwB,OAAO,GAAGhC,CAAC,CAACU,OAAO;IACvB;IACA,IAAIiB,UAAU,KAAK,CAAC,IAAI,CAACF,UAAU,IAAI,CAACC,UAAU,EAAE;MAClD;IACF;IACA;IACA;IACA;IACA,IAAID,UAAU,EAAE;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIQ,MAAM,GAAGL,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAGA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;MAC9E,IAAIM,KAAK,GAAGP,UAAU,GAAG,CAAC,GAAGM,MAAM,GAAG,CAAC,GAAGA,MAAM;MAChDE,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,EAAEnC,CAAC,EAAE;QAC1DkC,KAAK,EAAEA,KAAK;QACZH,OAAO,EAAEA,OAAO;QAChBC,OAAO,EAAEA,OAAO;QAChBnB,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ;IACA,IAAIa,UAAU,EAAE;MACd;MACA,IAAIU,QAAQ,GAAGP,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC;MACnC;MACA,IAAIU,WAAW,GAAG,CAACV,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAKS,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;MAC/FD,sBAAsB,CAAC,IAAI,EAAE,YAAY,EAAE,kBAAkB,EAAEnC,CAAC,EAAE;QAChEqC,WAAW,EAAEA,WAAW;QACxBN,OAAO,EAAEA,OAAO;QAChBC,OAAO,EAAEA,OAAO;QAChBnB,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ;EACF,CAAC;EACDhD,cAAc,CAAC2B,SAAS,CAACZ,aAAa,GAAG,UAAUoB,CAAC,EAAE;IACpD,IAAIxC,gBAAgB,CAACuD,OAAO,CAAC,IAAI,CAAC7C,GAAG,EAAE,WAAW,CAAC,EAAE;MACnD;IACF;IACA,IAAIgE,KAAK,GAAGlC,CAAC,CAACsC,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;IAC5CH,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAEnC,CAAC,EAAE;MAC5CkC,KAAK,EAAEA,KAAK;MACZH,OAAO,EAAE/B,CAAC,CAACuC,MAAM;MACjBP,OAAO,EAAEhC,CAAC,CAACwC,MAAM;MACjB3B,mBAAmB,EAAE;IACvB,CAAC,CAAC;EACJ,CAAC;EACD,OAAOhD,cAAc;AACvB,CAAC,CAACP,QAAQ,CAAC;AACX,SAAS6E,sBAAsBA,CAACM,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAE3C,CAAC,EAAE4C,cAAc,EAAE;EACzF,IAAIH,UAAU,CAAC3C,cAAc,IAAI2C,UAAU,CAAC3C,cAAc,CAACE,CAAC,EAAE4C,cAAc,CAACb,OAAO,EAAEa,cAAc,CAACZ,OAAO,CAAC,EAAE;IAC7G;IACA;IACA;IACAzE,SAAS,CAAC6D,IAAI,CAACpB,CAAC,CAACqB,KAAK,CAAC;IACvBC,OAAO,CAACmB,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAE3C,CAAC,EAAE4C,cAAc,CAAC;EACpE;AACF;AACA,SAAStB,OAAOA,CAACmB,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAE3C,CAAC,EAAE4C,cAAc,EAAE;EAC1E;EACA;EACAA,cAAc,CAAC/B,mBAAmB,GAAGnD,IAAI,CAACmD,mBAAmB,EAAE,IAAI,EAAE8B,eAAe,EAAE3C,CAAC,CAAC;EACxF;EACAyC,UAAU,CAACnB,OAAO,CAACoB,SAAS,EAAEE,cAAc,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/B,mBAAmBA,CAAC8B,eAAe,EAAE3C,CAAC,EAAE6C,QAAQ,EAAE;EACzD,IAAIC,OAAO,GAAGD,QAAQ,CAACF,eAAe,CAAC;EACvC,OAAO,CAACA,eAAe,IAAIG,OAAO,KAAK,CAACrF,QAAQ,CAACqF,OAAO,CAAC,IAAI9C,CAAC,CAACqB,KAAK,CAACyB,OAAO,GAAG,KAAK,CAAC,CAAC;AACxF;AACA,eAAejF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}