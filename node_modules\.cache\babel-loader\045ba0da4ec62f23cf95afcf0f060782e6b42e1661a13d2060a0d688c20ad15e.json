{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { getECData } from '../../util/innerStore.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\nvar LegendView = /** @class */function (_super) {\n  __extends(LegendView, _super);\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\r\n   * @override\r\n   */\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    }\n    // selector has been normalized to an array in model\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    // Perform layout.\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition);\n    // Place mainGroup, based on the calculated `mainRect`.\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw();\n    // Render background after group is layout.\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name');\n      // Use empty string or \\n as a newline string\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group();\n        // @ts-ignore\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      }\n      // Representitive series.\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      }\n      // Legend to control series.\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\r\n         * `data.getVisual('style')` may be the color from the register\r\n         * in series. For example, for line series,\r\n         */\n        var style = data.getVisual('style');\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        if (ecModel.ssr) {\n          itemGroup.eachChild(function (child) {\n            var ecData = getECData(child);\n            ecData.seriesIndex = seriesModel.seriesIndex;\n            ecData.dataIndex = dataIndex;\n            ecData.ssrType = 'legend';\n          });\n        }\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n            if (!provider.containName(name)) {\n              return;\n            }\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill);\n            // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2;\n              // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api);\n            // FIXME: consider different series has items with the same name.\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId))\n            // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            if (ecModel.ssr) {\n              itemGroup.eachChild(function (child) {\n                var ecData = getECData(child);\n                ecData.seriesIndex = seriesModel.seriesIndex;\n                ecData.dataIndex = dataIndex;\n                ecData.ssrType = 'legend';\n              });\n            }\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect',\n            legendId: legendModel.id\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    }));\n    // Add a invisible rect to increase the area of mouse hover\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      style: {\n        // Cannot use 'invisible' because SVG SSR will miss the node\n        fill: 'transparent'\n      }\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup);\n    // @ts-ignore\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup();\n    // Place items in contentGroup.\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box(\n      // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      }\n      // Always align selector to content as 'middle'\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\r\n   * Use series style if is inherit;\r\n   * elsewise, use legend style\r\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  }\n  // itemStyle\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n  if (itemStyle.fill === 'inherit') {\n    /**\r\n     * Series with visualDrawType as 'stroke' should have\r\n     * series stroke as legend fill\r\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n  if (itemStyle.stroke === 'inherit') {\n    /**\r\n     * icon type with \"emptyXXX\" should use fill color\r\n     * in visual style\r\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n  if (itemStyle.opacity === 'inherit') {\n    /**\r\n     * Use lineStyle.opacity if drawType is stroke\r\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n  handleCommonProps(itemStyle, itemVisualStyle);\n  // lineStyle\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle);\n  // Fix auto color to real color\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\r\n     * Since stroke is set to be inactiveBorderColor, it may occur that\r\n     * there is no border in series but border in legend, so we need to\r\n     * use border only when series has border if is set to be auto\r\n     */\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n  return icon;\n}\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  });\n  // highlight after select\n  // TODO highlight immediately may cause animation loss.\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n  return emphasisState && emphasisState.hoverLayer;\n}\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nexport default LegendView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "parse", "stringify", "graphic", "enableHoverEmphasis", "setLabelStyle", "createTextStyle", "makeBackground", "layoutUtil", "ComponentView", "createSymbol", "createOrUpdatePatternFromDecal", "getECData", "curry", "each", "Group", "LegendView", "_super", "_this", "apply", "arguments", "type", "newlineDisabled", "prototype", "init", "group", "add", "_contentGroup", "_selectorGroup", "_isFirstRender", "getContentGroup", "getSelectorGroup", "render", "legend<PERSON><PERSON><PERSON>", "ecModel", "api", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetInner", "get", "itemAlign", "orient", "selector", "selectorPosition", "renderInner", "positionInfo", "getBoxLayoutParams", "viewportSize", "width", "getWidth", "height", "getHeight", "padding", "maxSize", "getLayoutRect", "mainRect", "layoutInner", "layoutRect", "defaults", "x", "y", "mark<PERSON><PERSON><PERSON>", "_backgroundEl", "removeAll", "remove", "contentGroup", "legendDrawnMap", "createHashMap", "selectMode", "excludeSeriesId", "eachRawSeries", "seriesModel", "push", "id", "getData", "legendItemModel", "dataIndex", "name", "g", "newline", "getSeriesByName", "data", "lineVisualStyle", "getVisual", "legendIcon", "style", "itemGroup", "_createItem", "on", "dispatchSelectAction", "dispatchHighlightAction", "dispatchDownplayAction", "ssr", "<PERSON><PERSON><PERSON><PERSON>", "child", "ecData", "seriesIndex", "ssrType", "set", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider", "containName", "idx", "indexOfName", "getItemVisual", "colorArr", "fill", "extend", "process", "env", "NODE_ENV", "console", "warn", "_createSelector", "selectorGroup", "createSelectorButton", "selectorItem", "labelText", "Text", "align", "verticalAlign", "onclick", "dispatchAction", "legendId", "labelModel", "getModel", "emphasisLabelModel", "normal", "emphasis", "defaultText", "title", "itemVisualStyle", "drawType", "visualDrawType", "itemWidth", "itemHeight", "isSelected", "iconRotate", "symbolKeepAspect", "legendIconType", "getLegendStyle", "textStyleModel", "isFunction", "getLegendIcon", "icon", "itemStyle", "lineStyle", "rotate", "getDefaultLegendIcon", "textX", "textAlign", "formatter", "content", "isString", "replace", "textColor", "getTextColor", "text", "inheritColor", "hitRect", "Rect", "shape", "getBoundingRect", "tooltipModel", "setTooltipConfig", "el", "componentModel", "itemName", "itemTooltipOption", "option", "silent", "__legendDataIndex", "box", "contentRect", "contentPos", "selectorRect", "selectorPos", "selectorButtonGap", "orientIdx", "getOrient", "index", "wh", "hw", "yx", "Math", "max", "min", "iconType", "handleCommonProps", "visualStyle", "lineWidth", "propVal", "propName", "itemStyleModel", "getItemStyle", "iconBrushType", "lastIndexOf", "decalStyle", "getShallow", "decal", "stroke", "opacity", "legendLineModel", "getLineStyle", "borderWidth", "visualHasBorder", "opt", "symboType", "setStyle", "rotation", "PI", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "seriesName", "dataName", "isUseHoverLayer", "list", "getZr", "storage", "getDisplayList", "emphasisState", "i", "len", "length", "states", "hoverLayer"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/legend/LegendView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parse, stringify } from 'zrender/lib/tool/color.js';\nimport * as graphic from '../../util/graphic.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, createTextStyle } from '../../label/labelStyle.js';\nimport { makeBackground } from '../helper/listComponent.js';\nimport * as layoutUtil from '../../util/layout.js';\nimport ComponentView from '../../view/Component.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { getECData } from '../../util/innerStore.js';\nvar curry = zrUtil.curry;\nvar each = zrUtil.each;\nvar Group = graphic.Group;\nvar LegendView = /** @class */function (_super) {\n  __extends(LegendView, _super);\n  function LegendView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LegendView.type;\n    _this.newlineDisabled = false;\n    return _this;\n  }\n  LegendView.prototype.init = function () {\n    this.group.add(this._contentGroup = new Group());\n    this.group.add(this._selectorGroup = new Group());\n    this._isFirstRender = true;\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.getContentGroup = function () {\n    return this._contentGroup;\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.getSelectorGroup = function () {\n    return this._selectorGroup;\n  };\n  /**\r\n   * @override\r\n   */\n  LegendView.prototype.render = function (legendModel, ecModel, api) {\n    var isFirstRender = this._isFirstRender;\n    this._isFirstRender = false;\n    this.resetInner();\n    if (!legendModel.get('show', true)) {\n      return;\n    }\n    var itemAlign = legendModel.get('align');\n    var orient = legendModel.get('orient');\n    if (!itemAlign || itemAlign === 'auto') {\n      itemAlign = legendModel.get('left') === 'right' && orient === 'vertical' ? 'right' : 'left';\n    }\n    // selector has been normalized to an array in model\n    var selector = legendModel.get('selector', true);\n    var selectorPosition = legendModel.get('selectorPosition', true);\n    if (selector && (!selectorPosition || selectorPosition === 'auto')) {\n      selectorPosition = orient === 'horizontal' ? 'end' : 'start';\n    }\n    this.renderInner(itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition);\n    // Perform layout.\n    var positionInfo = legendModel.getBoxLayoutParams();\n    var viewportSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    };\n    var padding = legendModel.get('padding');\n    var maxSize = layoutUtil.getLayoutRect(positionInfo, viewportSize, padding);\n    var mainRect = this.layoutInner(legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition);\n    // Place mainGroup, based on the calculated `mainRect`.\n    var layoutRect = layoutUtil.getLayoutRect(zrUtil.defaults({\n      width: mainRect.width,\n      height: mainRect.height\n    }, positionInfo), viewportSize, padding);\n    this.group.x = layoutRect.x - mainRect.x;\n    this.group.y = layoutRect.y - mainRect.y;\n    this.group.markRedraw();\n    // Render background after group is layout.\n    this.group.add(this._backgroundEl = makeBackground(mainRect, legendModel));\n  };\n  LegendView.prototype.resetInner = function () {\n    this.getContentGroup().removeAll();\n    this._backgroundEl && this.group.remove(this._backgroundEl);\n    this.getSelectorGroup().removeAll();\n  };\n  LegendView.prototype.renderInner = function (itemAlign, legendModel, ecModel, api, selector, orient, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var legendDrawnMap = zrUtil.createHashMap();\n    var selectMode = legendModel.get('selectedMode');\n    var excludeSeriesId = [];\n    ecModel.eachRawSeries(function (seriesModel) {\n      !seriesModel.get('legendHoverLink') && excludeSeriesId.push(seriesModel.id);\n    });\n    each(legendModel.getData(), function (legendItemModel, dataIndex) {\n      var name = legendItemModel.get('name');\n      // Use empty string or \\n as a newline string\n      if (!this.newlineDisabled && (name === '' || name === '\\n')) {\n        var g = new Group();\n        // @ts-ignore\n        g.newline = true;\n        contentGroup.add(g);\n        return;\n      }\n      // Representitive series.\n      var seriesModel = ecModel.getSeriesByName(name)[0];\n      if (legendDrawnMap.get(name)) {\n        // Have been drawn\n        return;\n      }\n      // Legend to control series.\n      if (seriesModel) {\n        var data = seriesModel.getData();\n        var lineVisualStyle = data.getVisual('legendLineStyle') || {};\n        var legendIcon = data.getVisual('legendIcon');\n        /**\r\n         * `data.getVisual('style')` may be the color from the register\r\n         * in series. For example, for line series,\r\n         */\n        var style = data.getVisual('style');\n        var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, style, legendIcon, selectMode, api);\n        itemGroup.on('click', curry(dispatchSelectAction, name, null, api, excludeSeriesId)).on('mouseover', curry(dispatchHighlightAction, seriesModel.name, null, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, seriesModel.name, null, api, excludeSeriesId));\n        if (ecModel.ssr) {\n          itemGroup.eachChild(function (child) {\n            var ecData = getECData(child);\n            ecData.seriesIndex = seriesModel.seriesIndex;\n            ecData.dataIndex = dataIndex;\n            ecData.ssrType = 'legend';\n          });\n        }\n        legendDrawnMap.set(name, true);\n      } else {\n        // Legend to control data. In pie and funnel.\n        ecModel.eachRawSeries(function (seriesModel) {\n          // In case multiple series has same data name\n          if (legendDrawnMap.get(name)) {\n            return;\n          }\n          if (seriesModel.legendVisualProvider) {\n            var provider = seriesModel.legendVisualProvider;\n            if (!provider.containName(name)) {\n              return;\n            }\n            var idx = provider.indexOfName(name);\n            var style = provider.getItemVisual(idx, 'style');\n            var legendIcon = provider.getItemVisual(idx, 'legendIcon');\n            var colorArr = parse(style.fill);\n            // Color may be set to transparent in visualMap when data is out of range.\n            // Do not show nothing.\n            if (colorArr && colorArr[3] === 0) {\n              colorArr[3] = 0.2;\n              // TODO color is set to 0, 0, 0, 0. Should show correct RGBA\n              style = zrUtil.extend(zrUtil.extend({}, style), {\n                fill: stringify(colorArr, 'rgba')\n              });\n            }\n            var itemGroup = this._createItem(seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, {}, style, legendIcon, selectMode, api);\n            // FIXME: consider different series has items with the same name.\n            itemGroup.on('click', curry(dispatchSelectAction, null, name, api, excludeSeriesId))\n            // Should not specify the series name, consider legend controls\n            // more than one pie series.\n            .on('mouseover', curry(dispatchHighlightAction, null, name, api, excludeSeriesId)).on('mouseout', curry(dispatchDownplayAction, null, name, api, excludeSeriesId));\n            if (ecModel.ssr) {\n              itemGroup.eachChild(function (child) {\n                var ecData = getECData(child);\n                ecData.seriesIndex = seriesModel.seriesIndex;\n                ecData.dataIndex = dataIndex;\n                ecData.ssrType = 'legend';\n              });\n            }\n            legendDrawnMap.set(name, true);\n          }\n        }, this);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (!legendDrawnMap.get(name)) {\n          console.warn(name + ' series not exists. Legend data should be same with series name or data name.');\n        }\n      }\n    }, this);\n    if (selector) {\n      this._createSelector(selector, legendModel, api, orient, selectorPosition);\n    }\n  };\n  LegendView.prototype._createSelector = function (selector, legendModel, api, orient, selectorPosition) {\n    var selectorGroup = this.getSelectorGroup();\n    each(selector, function createSelectorButton(selectorItem) {\n      var type = selectorItem.type;\n      var labelText = new graphic.Text({\n        style: {\n          x: 0,\n          y: 0,\n          align: 'center',\n          verticalAlign: 'middle'\n        },\n        onclick: function () {\n          api.dispatchAction({\n            type: type === 'all' ? 'legendAllSelect' : 'legendInverseSelect',\n            legendId: legendModel.id\n          });\n        }\n      });\n      selectorGroup.add(labelText);\n      var labelModel = legendModel.getModel('selectorLabel');\n      var emphasisLabelModel = legendModel.getModel(['emphasis', 'selectorLabel']);\n      setLabelStyle(labelText, {\n        normal: labelModel,\n        emphasis: emphasisLabelModel\n      }, {\n        defaultText: selectorItem.title\n      });\n      enableHoverEmphasis(labelText);\n    });\n  };\n  LegendView.prototype._createItem = function (seriesModel, name, dataIndex, legendItemModel, legendModel, itemAlign, lineVisualStyle, itemVisualStyle, legendIcon, selectMode, api) {\n    var drawType = seriesModel.visualDrawType;\n    var itemWidth = legendModel.get('itemWidth');\n    var itemHeight = legendModel.get('itemHeight');\n    var isSelected = legendModel.isSelected(name);\n    var iconRotate = legendItemModel.get('symbolRotate');\n    var symbolKeepAspect = legendItemModel.get('symbolKeepAspect');\n    var legendIconType = legendItemModel.get('icon');\n    legendIcon = legendIconType || legendIcon || 'roundRect';\n    var style = getLegendStyle(legendIcon, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api);\n    var itemGroup = new Group();\n    var textStyleModel = legendItemModel.getModel('textStyle');\n    if (zrUtil.isFunction(seriesModel.getLegendIcon) && (!legendIconType || legendIconType === 'inherit')) {\n      // Series has specific way to define legend icon\n      itemGroup.add(seriesModel.getLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: iconRotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    } else {\n      // Use default legend icon policy for most series\n      var rotate = legendIconType === 'inherit' && seriesModel.getData().getVisual('symbol') ? iconRotate === 'inherit' ? seriesModel.getData().getVisual('symbolRotate') : iconRotate : 0; // No rotation for no icon\n      itemGroup.add(getDefaultLegendIcon({\n        itemWidth: itemWidth,\n        itemHeight: itemHeight,\n        icon: legendIcon,\n        iconRotate: rotate,\n        itemStyle: style.itemStyle,\n        lineStyle: style.lineStyle,\n        symbolKeepAspect: symbolKeepAspect\n      }));\n    }\n    var textX = itemAlign === 'left' ? itemWidth + 5 : -5;\n    var textAlign = itemAlign;\n    var formatter = legendModel.get('formatter');\n    var content = name;\n    if (zrUtil.isString(formatter) && formatter) {\n      content = formatter.replace('{name}', name != null ? name : '');\n    } else if (zrUtil.isFunction(formatter)) {\n      content = formatter(name);\n    }\n    var textColor = isSelected ? textStyleModel.getTextColor() : legendItemModel.get('inactiveColor');\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: content,\n        x: textX,\n        y: itemHeight / 2,\n        fill: textColor,\n        align: textAlign,\n        verticalAlign: 'middle'\n      }, {\n        inheritColor: textColor\n      })\n    }));\n    // Add a invisible rect to increase the area of mouse hover\n    var hitRect = new graphic.Rect({\n      shape: itemGroup.getBoundingRect(),\n      style: {\n        // Cannot use 'invisible' because SVG SSR will miss the node\n        fill: 'transparent'\n      }\n    });\n    var tooltipModel = legendItemModel.getModel('tooltip');\n    if (tooltipModel.get('show')) {\n      graphic.setTooltipConfig({\n        el: hitRect,\n        componentModel: legendModel,\n        itemName: name,\n        itemTooltipOption: tooltipModel.option\n      });\n    }\n    itemGroup.add(hitRect);\n    itemGroup.eachChild(function (child) {\n      child.silent = true;\n    });\n    hitRect.silent = !selectMode;\n    this.getContentGroup().add(itemGroup);\n    enableHoverEmphasis(itemGroup);\n    // @ts-ignore\n    itemGroup.__legendDataIndex = dataIndex;\n    return itemGroup;\n  };\n  LegendView.prototype.layoutInner = function (legendModel, itemAlign, maxSize, isFirstRender, selector, selectorPosition) {\n    var contentGroup = this.getContentGroup();\n    var selectorGroup = this.getSelectorGroup();\n    // Place items in contentGroup.\n    layoutUtil.box(legendModel.get('orient'), contentGroup, legendModel.get('itemGap'), maxSize.width, maxSize.height);\n    var contentRect = contentGroup.getBoundingRect();\n    var contentPos = [-contentRect.x, -contentRect.y];\n    selectorGroup.markRedraw();\n    contentGroup.markRedraw();\n    if (selector) {\n      // Place buttons in selectorGroup\n      layoutUtil.box(\n      // Buttons in selectorGroup always layout horizontally\n      'horizontal', selectorGroup, legendModel.get('selectorItemGap', true));\n      var selectorRect = selectorGroup.getBoundingRect();\n      var selectorPos = [-selectorRect.x, -selectorRect.y];\n      var selectorButtonGap = legendModel.get('selectorButtonGap', true);\n      var orientIdx = legendModel.getOrient().index;\n      var wh = orientIdx === 0 ? 'width' : 'height';\n      var hw = orientIdx === 0 ? 'height' : 'width';\n      var yx = orientIdx === 0 ? 'y' : 'x';\n      if (selectorPosition === 'end') {\n        selectorPos[orientIdx] += contentRect[wh] + selectorButtonGap;\n      } else {\n        contentPos[orientIdx] += selectorRect[wh] + selectorButtonGap;\n      }\n      // Always align selector to content as 'middle'\n      selectorPos[1 - orientIdx] += contentRect[hw] / 2 - selectorRect[hw] / 2;\n      selectorGroup.x = selectorPos[0];\n      selectorGroup.y = selectorPos[1];\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      var mainRect = {\n        x: 0,\n        y: 0\n      };\n      mainRect[wh] = contentRect[wh] + selectorButtonGap + selectorRect[wh];\n      mainRect[hw] = Math.max(contentRect[hw], selectorRect[hw]);\n      mainRect[yx] = Math.min(0, selectorRect[yx] + selectorPos[1 - orientIdx]);\n      return mainRect;\n    } else {\n      contentGroup.x = contentPos[0];\n      contentGroup.y = contentPos[1];\n      return this.group.getBoundingRect();\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  LegendView.prototype.remove = function () {\n    this.getContentGroup().removeAll();\n    this._isFirstRender = true;\n  };\n  LegendView.type = 'legend.plain';\n  return LegendView;\n}(ComponentView);\nfunction getLegendStyle(iconType, legendItemModel, lineVisualStyle, itemVisualStyle, drawType, isSelected, api) {\n  /**\r\n   * Use series style if is inherit;\r\n   * elsewise, use legend style\r\n   */\n  function handleCommonProps(style, visualStyle) {\n    // If lineStyle.width is 'auto', it is set to be 2 if series has border\n    if (style.lineWidth === 'auto') {\n      style.lineWidth = visualStyle.lineWidth > 0 ? 2 : 0;\n    }\n    each(style, function (propVal, propName) {\n      style[propName] === 'inherit' && (style[propName] = visualStyle[propName]);\n    });\n  }\n  // itemStyle\n  var itemStyleModel = legendItemModel.getModel('itemStyle');\n  var itemStyle = itemStyleModel.getItemStyle();\n  var iconBrushType = iconType.lastIndexOf('empty', 0) === 0 ? 'fill' : 'stroke';\n  var decalStyle = itemStyleModel.getShallow('decal');\n  itemStyle.decal = !decalStyle || decalStyle === 'inherit' ? itemVisualStyle.decal : createOrUpdatePatternFromDecal(decalStyle, api);\n  if (itemStyle.fill === 'inherit') {\n    /**\r\n     * Series with visualDrawType as 'stroke' should have\r\n     * series stroke as legend fill\r\n     */\n    itemStyle.fill = itemVisualStyle[drawType];\n  }\n  if (itemStyle.stroke === 'inherit') {\n    /**\r\n     * icon type with \"emptyXXX\" should use fill color\r\n     * in visual style\r\n     */\n    itemStyle.stroke = itemVisualStyle[iconBrushType];\n  }\n  if (itemStyle.opacity === 'inherit') {\n    /**\r\n     * Use lineStyle.opacity if drawType is stroke\r\n     */\n    itemStyle.opacity = (drawType === 'fill' ? itemVisualStyle : lineVisualStyle).opacity;\n  }\n  handleCommonProps(itemStyle, itemVisualStyle);\n  // lineStyle\n  var legendLineModel = legendItemModel.getModel('lineStyle');\n  var lineStyle = legendLineModel.getLineStyle();\n  handleCommonProps(lineStyle, lineVisualStyle);\n  // Fix auto color to real color\n  itemStyle.fill === 'auto' && (itemStyle.fill = itemVisualStyle.fill);\n  itemStyle.stroke === 'auto' && (itemStyle.stroke = itemVisualStyle.fill);\n  lineStyle.stroke === 'auto' && (lineStyle.stroke = itemVisualStyle.fill);\n  if (!isSelected) {\n    var borderWidth = legendItemModel.get('inactiveBorderWidth');\n    /**\r\n     * Since stroke is set to be inactiveBorderColor, it may occur that\r\n     * there is no border in series but border in legend, so we need to\r\n     * use border only when series has border if is set to be auto\r\n     */\n    var visualHasBorder = itemStyle[iconBrushType];\n    itemStyle.lineWidth = borderWidth === 'auto' ? itemVisualStyle.lineWidth > 0 && visualHasBorder ? 2 : 0 : itemStyle.lineWidth;\n    itemStyle.fill = legendItemModel.get('inactiveColor');\n    itemStyle.stroke = legendItemModel.get('inactiveBorderColor');\n    lineStyle.stroke = legendLineModel.get('inactiveColor');\n    lineStyle.lineWidth = legendLineModel.get('inactiveWidth');\n  }\n  return {\n    itemStyle: itemStyle,\n    lineStyle: lineStyle\n  };\n}\nfunction getDefaultLegendIcon(opt) {\n  var symboType = opt.icon || 'roundRect';\n  var icon = createSymbol(symboType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill, opt.symbolKeepAspect);\n  icon.setStyle(opt.itemStyle);\n  icon.rotation = (opt.iconRotate || 0) * Math.PI / 180;\n  icon.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n  if (symboType.indexOf('empty') > -1) {\n    icon.style.stroke = icon.style.fill;\n    icon.style.fill = '#fff';\n    icon.style.lineWidth = 2;\n  }\n  return icon;\n}\nfunction dispatchSelectAction(seriesName, dataName, api, excludeSeriesId) {\n  // downplay before unselect\n  dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId);\n  api.dispatchAction({\n    type: 'legendToggleSelect',\n    name: seriesName != null ? seriesName : dataName\n  });\n  // highlight after select\n  // TODO highlight immediately may cause animation loss.\n  dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId);\n}\nfunction isUseHoverLayer(api) {\n  var list = api.getZr().storage.getDisplayList();\n  var emphasisState;\n  var i = 0;\n  var len = list.length;\n  while (i < len && !(emphasisState = list[i].states.emphasis)) {\n    i++;\n  }\n  return emphasisState && emphasisState.hoverLayer;\n}\nfunction dispatchHighlightAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'highlight',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nfunction dispatchDownplayAction(seriesName, dataName, api, excludeSeriesId) {\n  // If element hover will move to a hoverLayer.\n  if (!isUseHoverLayer(api)) {\n    api.dispatchAction({\n      type: 'downplay',\n      seriesName: seriesName,\n      name: dataName,\n      excludeSeriesId: excludeSeriesId\n    });\n  }\n}\nexport default LegendView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,KAAK,EAAEC,SAAS,QAAQ,2BAA2B;AAC5D,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,aAAa,EAAEC,eAAe,QAAQ,2BAA2B;AAC1E,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,KAAK,GAAGb,MAAM,CAACa,KAAK;AACxB,IAAIC,IAAI,GAAGd,MAAM,CAACc,IAAI;AACtB,IAAIC,KAAK,GAAGZ,OAAO,CAACY,KAAK;AACzB,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9ClB,SAAS,CAACiB,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5BH,KAAK,CAACI,eAAe,GAAG,KAAK;IAC7B,OAAOJ,KAAK;EACd;EACAF,UAAU,CAACO,SAAS,CAACC,IAAI,GAAG,YAAY;IACtC,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACC,aAAa,GAAG,IAAIZ,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACU,KAAK,CAACC,GAAG,CAAC,IAAI,CAACE,cAAc,GAAG,IAAIb,KAAK,CAAC,CAAC,CAAC;IACjD,IAAI,CAACc,cAAc,GAAG,IAAI;EAC5B,CAAC;EACD;AACF;AACA;EACEb,UAAU,CAACO,SAAS,CAACO,eAAe,GAAG,YAAY;IACjD,OAAO,IAAI,CAACH,aAAa;EAC3B,CAAC;EACD;AACF;AACA;EACEX,UAAU,CAACO,SAAS,CAACQ,gBAAgB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACH,cAAc;EAC5B,CAAC;EACD;AACF;AACA;EACEZ,UAAU,CAACO,SAAS,CAACS,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAIC,aAAa,GAAG,IAAI,CAACP,cAAc;IACvC,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACQ,UAAU,CAAC,CAAC;IACjB,IAAI,CAACJ,WAAW,CAACK,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC;IACF;IACA,IAAIC,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,OAAO,CAAC;IACxC,IAAIE,MAAM,GAAGP,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAI,CAACC,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;MACtCA,SAAS,GAAGN,WAAW,CAACK,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,IAAIE,MAAM,KAAK,UAAU,GAAG,OAAO,GAAG,MAAM;IAC7F;IACA;IACA,IAAIC,QAAQ,GAAGR,WAAW,CAACK,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IAChD,IAAII,gBAAgB,GAAGT,WAAW,CAACK,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC;IAChE,IAAIG,QAAQ,KAAK,CAACC,gBAAgB,IAAIA,gBAAgB,KAAK,MAAM,CAAC,EAAE;MAClEA,gBAAgB,GAAGF,MAAM,KAAK,YAAY,GAAG,KAAK,GAAG,OAAO;IAC9D;IACA,IAAI,CAACG,WAAW,CAACJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,CAAC;IAC1F;IACA,IAAIE,YAAY,GAAGX,WAAW,CAACY,kBAAkB,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAG;MACjBC,KAAK,EAAEZ,GAAG,CAACa,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAEd,GAAG,CAACe,SAAS,CAAC;IACxB,CAAC;IACD,IAAIC,OAAO,GAAGlB,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC;IACxC,IAAIc,OAAO,GAAG5C,UAAU,CAAC6C,aAAa,CAACT,YAAY,EAAEE,YAAY,EAAEK,OAAO,CAAC;IAC3E,IAAIG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,CAAC;IAC3G;IACA,IAAIc,UAAU,GAAGhD,UAAU,CAAC6C,aAAa,CAACrD,MAAM,CAACyD,QAAQ,CAAC;MACxDV,KAAK,EAAEO,QAAQ,CAACP,KAAK;MACrBE,MAAM,EAAEK,QAAQ,CAACL;IACnB,CAAC,EAAEL,YAAY,CAAC,EAAEE,YAAY,EAAEK,OAAO,CAAC;IACxC,IAAI,CAAC1B,KAAK,CAACiC,CAAC,GAAGF,UAAU,CAACE,CAAC,GAAGJ,QAAQ,CAACI,CAAC;IACxC,IAAI,CAACjC,KAAK,CAACkC,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAGL,QAAQ,CAACK,CAAC;IACxC,IAAI,CAAClC,KAAK,CAACmC,UAAU,CAAC,CAAC;IACvB;IACA,IAAI,CAACnC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACmC,aAAa,GAAGtD,cAAc,CAAC+C,QAAQ,EAAErB,WAAW,CAAC,CAAC;EAC5E,CAAC;EACDjB,UAAU,CAACO,SAAS,CAACc,UAAU,GAAG,YAAY;IAC5C,IAAI,CAACP,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACD,aAAa,IAAI,IAAI,CAACpC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC3D,IAAI,CAAC9B,gBAAgB,CAAC,CAAC,CAAC+B,SAAS,CAAC,CAAC;EACrC,CAAC;EACD9C,UAAU,CAACO,SAAS,CAACoB,WAAW,GAAG,UAAUJ,SAAS,EAAEN,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEM,QAAQ,EAAED,MAAM,EAAEE,gBAAgB,EAAE;IACrH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAImC,cAAc,GAAGjE,MAAM,CAACkE,aAAa,CAAC,CAAC;IAC3C,IAAIC,UAAU,GAAGlC,WAAW,CAACK,GAAG,CAAC,cAAc,CAAC;IAChD,IAAI8B,eAAe,GAAG,EAAE;IACxBlC,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;MAC3C,CAACA,WAAW,CAAChC,GAAG,CAAC,iBAAiB,CAAC,IAAI8B,eAAe,CAACG,IAAI,CAACD,WAAW,CAACE,EAAE,CAAC;IAC7E,CAAC,CAAC;IACF1D,IAAI,CAACmB,WAAW,CAACwC,OAAO,CAAC,CAAC,EAAE,UAAUC,eAAe,EAAEC,SAAS,EAAE;MAChE,IAAIC,IAAI,GAAGF,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC;MACtC;MACA,IAAI,CAAC,IAAI,CAAChB,eAAe,KAAKsD,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;QAC3D,IAAIC,CAAC,GAAG,IAAI9D,KAAK,CAAC,CAAC;QACnB;QACA8D,CAAC,CAACC,OAAO,GAAG,IAAI;QAChBd,YAAY,CAACtC,GAAG,CAACmD,CAAC,CAAC;QACnB;MACF;MACA;MACA,IAAIP,WAAW,GAAGpC,OAAO,CAAC6C,eAAe,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;MAClD,IAAIX,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;QAC5B;QACA;MACF;MACA;MACA,IAAIN,WAAW,EAAE;QACf,IAAIU,IAAI,GAAGV,WAAW,CAACG,OAAO,CAAC,CAAC;QAChC,IAAIQ,eAAe,GAAGD,IAAI,CAACE,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAIC,UAAU,GAAGH,IAAI,CAACE,SAAS,CAAC,YAAY,CAAC;QAC7C;AACR;AACA;AACA;QACQ,IAAIE,KAAK,GAAGJ,IAAI,CAACE,SAAS,CAAC,OAAO,CAAC;QACnC,IAAIG,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAEG,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC;QAC5JkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAEZ,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAEnB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAEpB,WAAW,CAACM,IAAI,EAAE,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;QAC9Q,IAAIlC,OAAO,CAACyD,GAAG,EAAE;UACfN,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;YACnC,IAAIC,MAAM,GAAGlF,SAAS,CAACiF,KAAK,CAAC;YAC7BC,MAAM,CAACC,WAAW,GAAGzB,WAAW,CAACyB,WAAW;YAC5CD,MAAM,CAACnB,SAAS,GAAGA,SAAS;YAC5BmB,MAAM,CAACE,OAAO,GAAG,QAAQ;UAC3B,CAAC,CAAC;QACJ;QACA/B,cAAc,CAACgC,GAAG,CAACrB,IAAI,EAAE,IAAI,CAAC;MAChC,CAAC,MAAM;QACL;QACA1C,OAAO,CAACmC,aAAa,CAAC,UAAUC,WAAW,EAAE;UAC3C;UACA,IAAIL,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;YAC5B;UACF;UACA,IAAIN,WAAW,CAAC4B,oBAAoB,EAAE;YACpC,IAAIC,QAAQ,GAAG7B,WAAW,CAAC4B,oBAAoB;YAC/C,IAAI,CAACC,QAAQ,CAACC,WAAW,CAACxB,IAAI,CAAC,EAAE;cAC/B;YACF;YACA,IAAIyB,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAAC1B,IAAI,CAAC;YACpC,IAAIQ,KAAK,GAAGe,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,OAAO,CAAC;YAChD,IAAIlB,UAAU,GAAGgB,QAAQ,CAACI,aAAa,CAACF,GAAG,EAAE,YAAY,CAAC;YAC1D,IAAIG,QAAQ,GAAGvG,KAAK,CAACmF,KAAK,CAACqB,IAAI,CAAC;YAChC;YACA;YACA,IAAID,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;cACjCA,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG;cACjB;cACApB,KAAK,GAAGpF,MAAM,CAAC0G,MAAM,CAAC1G,MAAM,CAAC0G,MAAM,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAAC,EAAE;gBAC9CqB,IAAI,EAAEvG,SAAS,CAACsG,QAAQ,EAAE,MAAM;cAClC,CAAC,CAAC;YACJ;YACA,IAAInB,SAAS,GAAG,IAAI,CAACC,WAAW,CAAChB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE,CAAC,CAAC,EAAE6C,KAAK,EAAED,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,CAAC;YAC/I;YACAkD,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE1E,KAAK,CAAC2E,oBAAoB,EAAE,IAAI,EAAEZ,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC;YACnF;YACA;YAAA,CACCmB,EAAE,CAAC,WAAW,EAAE1E,KAAK,CAAC4E,uBAAuB,EAAE,IAAI,EAAEb,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC,CAACmB,EAAE,CAAC,UAAU,EAAE1E,KAAK,CAAC6E,sBAAsB,EAAE,IAAI,EAAEd,IAAI,EAAEzC,GAAG,EAAEiC,eAAe,CAAC,CAAC;YAClK,IAAIlC,OAAO,CAACyD,GAAG,EAAE;cACfN,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;gBACnC,IAAIC,MAAM,GAAGlF,SAAS,CAACiF,KAAK,CAAC;gBAC7BC,MAAM,CAACC,WAAW,GAAGzB,WAAW,CAACyB,WAAW;gBAC5CD,MAAM,CAACnB,SAAS,GAAGA,SAAS;gBAC5BmB,MAAM,CAACE,OAAO,GAAG,QAAQ;cAC3B,CAAC,CAAC;YACJ;YACA/B,cAAc,CAACgC,GAAG,CAACrB,IAAI,EAAE,IAAI,CAAC;UAChC;QACF,CAAC,EAAE,IAAI,CAAC;MACV;MACA,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAAC5C,cAAc,CAAC3B,GAAG,CAACsC,IAAI,CAAC,EAAE;UAC7BkC,OAAO,CAACC,IAAI,CAACnC,IAAI,GAAG,+EAA+E,CAAC;QACtG;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAInC,QAAQ,EAAE;MACZ,IAAI,CAACuE,eAAe,CAACvE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,CAAC;IAC5E;EACF,CAAC;EACD1B,UAAU,CAACO,SAAS,CAACyF,eAAe,GAAG,UAAUvE,QAAQ,EAAER,WAAW,EAAEE,GAAG,EAAEK,MAAM,EAAEE,gBAAgB,EAAE;IACrG,IAAIuE,aAAa,GAAG,IAAI,CAAClF,gBAAgB,CAAC,CAAC;IAC3CjB,IAAI,CAAC2B,QAAQ,EAAE,SAASyE,oBAAoBA,CAACC,YAAY,EAAE;MACzD,IAAI9F,IAAI,GAAG8F,YAAY,CAAC9F,IAAI;MAC5B,IAAI+F,SAAS,GAAG,IAAIjH,OAAO,CAACkH,IAAI,CAAC;QAC/BjC,KAAK,EAAE;UACL1B,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJ2D,KAAK,EAAE,QAAQ;UACfC,aAAa,EAAE;QACjB,CAAC;QACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;UACnBrF,GAAG,CAACsF,cAAc,CAAC;YACjBpG,IAAI,EAAEA,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAAG,qBAAqB;YAChEqG,QAAQ,EAAEzF,WAAW,CAACuC;UACxB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACFyC,aAAa,CAACvF,GAAG,CAAC0F,SAAS,CAAC;MAC5B,IAAIO,UAAU,GAAG1F,WAAW,CAAC2F,QAAQ,CAAC,eAAe,CAAC;MACtD,IAAIC,kBAAkB,GAAG5F,WAAW,CAAC2F,QAAQ,CAAC,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;MAC5EvH,aAAa,CAAC+G,SAAS,EAAE;QACvBU,MAAM,EAAEH,UAAU;QAClBI,QAAQ,EAAEF;MACZ,CAAC,EAAE;QACDG,WAAW,EAAEb,YAAY,CAACc;MAC5B,CAAC,CAAC;MACF7H,mBAAmB,CAACgH,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EACDpG,UAAU,CAACO,SAAS,CAAC+D,WAAW,GAAG,UAAUhB,WAAW,EAAEM,IAAI,EAAED,SAAS,EAAED,eAAe,EAAEzC,WAAW,EAAEM,SAAS,EAAE0C,eAAe,EAAEiD,eAAe,EAAE/C,UAAU,EAAEhB,UAAU,EAAEhC,GAAG,EAAE;IACjL,IAAIgG,QAAQ,GAAG7D,WAAW,CAAC8D,cAAc;IACzC,IAAIC,SAAS,GAAGpG,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIgG,UAAU,GAAGrG,WAAW,CAACK,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIiG,UAAU,GAAGtG,WAAW,CAACsG,UAAU,CAAC3D,IAAI,CAAC;IAC7C,IAAI4D,UAAU,GAAG9D,eAAe,CAACpC,GAAG,CAAC,cAAc,CAAC;IACpD,IAAImG,gBAAgB,GAAG/D,eAAe,CAACpC,GAAG,CAAC,kBAAkB,CAAC;IAC9D,IAAIoG,cAAc,GAAGhE,eAAe,CAACpC,GAAG,CAAC,MAAM,CAAC;IAChD6C,UAAU,GAAGuD,cAAc,IAAIvD,UAAU,IAAI,WAAW;IACxD,IAAIC,KAAK,GAAGuD,cAAc,CAACxD,UAAU,EAAET,eAAe,EAAEO,eAAe,EAAEiD,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAEpG,GAAG,CAAC;IACpH,IAAIkD,SAAS,GAAG,IAAItE,KAAK,CAAC,CAAC;IAC3B,IAAI6H,cAAc,GAAGlE,eAAe,CAACkD,QAAQ,CAAC,WAAW,CAAC;IAC1D,IAAI5H,MAAM,CAAC6I,UAAU,CAACvE,WAAW,CAACwE,aAAa,CAAC,KAAK,CAACJ,cAAc,IAAIA,cAAc,KAAK,SAAS,CAAC,EAAE;MACrG;MACArD,SAAS,CAAC3D,GAAG,CAAC4C,WAAW,CAACwE,aAAa,CAAC;QACtCT,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAE5D,UAAU;QAChBqD,UAAU,EAAEA,UAAU;QACtBQ,SAAS,EAAE5D,KAAK,CAAC4D,SAAS;QAC1BC,SAAS,EAAE7D,KAAK,CAAC6D,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA,IAAIS,MAAM,GAAGR,cAAc,KAAK,SAAS,IAAIpE,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,QAAQ,CAAC,GAAGsD,UAAU,KAAK,SAAS,GAAGlE,WAAW,CAACG,OAAO,CAAC,CAAC,CAACS,SAAS,CAAC,cAAc,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC,CAAC;MACtLnD,SAAS,CAAC3D,GAAG,CAACyH,oBAAoB,CAAC;QACjCd,SAAS,EAAEA,SAAS;QACpBC,UAAU,EAAEA,UAAU;QACtBS,IAAI,EAAE5D,UAAU;QAChBqD,UAAU,EAAEU,MAAM;QAClBF,SAAS,EAAE5D,KAAK,CAAC4D,SAAS;QAC1BC,SAAS,EAAE7D,KAAK,CAAC6D,SAAS;QAC1BR,gBAAgB,EAAEA;MACpB,CAAC,CAAC,CAAC;IACL;IACA,IAAIW,KAAK,GAAG7G,SAAS,KAAK,MAAM,GAAG8F,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIgB,SAAS,GAAG9G,SAAS;IACzB,IAAI+G,SAAS,GAAGrH,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIiH,OAAO,GAAG3E,IAAI;IAClB,IAAI5E,MAAM,CAACwJ,QAAQ,CAACF,SAAS,CAAC,IAAIA,SAAS,EAAE;MAC3CC,OAAO,GAAGD,SAAS,CAACG,OAAO,CAAC,QAAQ,EAAE7E,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;IACjE,CAAC,MAAM,IAAI5E,MAAM,CAAC6I,UAAU,CAACS,SAAS,CAAC,EAAE;MACvCC,OAAO,GAAGD,SAAS,CAAC1E,IAAI,CAAC;IAC3B;IACA,IAAI8E,SAAS,GAAGnB,UAAU,GAAGK,cAAc,CAACe,YAAY,CAAC,CAAC,GAAGjF,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACjG+C,SAAS,CAAC3D,GAAG,CAAC,IAAIvB,OAAO,CAACkH,IAAI,CAAC;MAC7BjC,KAAK,EAAE9E,eAAe,CAACsI,cAAc,EAAE;QACrCgB,IAAI,EAAEL,OAAO;QACb7F,CAAC,EAAE0F,KAAK;QACRzF,CAAC,EAAE2E,UAAU,GAAG,CAAC;QACjB7B,IAAI,EAAEiD,SAAS;QACfpC,KAAK,EAAE+B,SAAS;QAChB9B,aAAa,EAAE;MACjB,CAAC,EAAE;QACDsC,YAAY,EAAEH;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;IACH;IACA,IAAII,OAAO,GAAG,IAAI3J,OAAO,CAAC4J,IAAI,CAAC;MAC7BC,KAAK,EAAE3E,SAAS,CAAC4E,eAAe,CAAC,CAAC;MAClC7E,KAAK,EAAE;QACL;QACAqB,IAAI,EAAE;MACR;IACF,CAAC,CAAC;IACF,IAAIyD,YAAY,GAAGxF,eAAe,CAACkD,QAAQ,CAAC,SAAS,CAAC;IACtD,IAAIsC,YAAY,CAAC5H,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5BnC,OAAO,CAACgK,gBAAgB,CAAC;QACvBC,EAAE,EAAEN,OAAO;QACXO,cAAc,EAAEpI,WAAW;QAC3BqI,QAAQ,EAAE1F,IAAI;QACd2F,iBAAiB,EAAEL,YAAY,CAACM;MAClC,CAAC,CAAC;IACJ;IACAnF,SAAS,CAAC3D,GAAG,CAACoI,OAAO,CAAC;IACtBzE,SAAS,CAACO,SAAS,CAAC,UAAUC,KAAK,EAAE;MACnCA,KAAK,CAAC4E,MAAM,GAAG,IAAI;IACrB,CAAC,CAAC;IACFX,OAAO,CAACW,MAAM,GAAG,CAACtG,UAAU;IAC5B,IAAI,CAACrC,eAAe,CAAC,CAAC,CAACJ,GAAG,CAAC2D,SAAS,CAAC;IACrCjF,mBAAmB,CAACiF,SAAS,CAAC;IAC9B;IACAA,SAAS,CAACqF,iBAAiB,GAAG/F,SAAS;IACvC,OAAOU,SAAS;EAClB,CAAC;EACDrE,UAAU,CAACO,SAAS,CAACgC,WAAW,GAAG,UAAUtB,WAAW,EAAEM,SAAS,EAAEa,OAAO,EAAEhB,aAAa,EAAEK,QAAQ,EAAEC,gBAAgB,EAAE;IACvH,IAAIsB,YAAY,GAAG,IAAI,CAAClC,eAAe,CAAC,CAAC;IACzC,IAAImF,aAAa,GAAG,IAAI,CAAClF,gBAAgB,CAAC,CAAC;IAC3C;IACAvB,UAAU,CAACmK,GAAG,CAAC1I,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAE0B,YAAY,EAAE/B,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC,EAAEc,OAAO,CAACL,KAAK,EAAEK,OAAO,CAACH,MAAM,CAAC;IAClH,IAAI2H,WAAW,GAAG5G,YAAY,CAACiG,eAAe,CAAC,CAAC;IAChD,IAAIY,UAAU,GAAG,CAAC,CAACD,WAAW,CAAClH,CAAC,EAAE,CAACkH,WAAW,CAACjH,CAAC,CAAC;IACjDsD,aAAa,CAACrD,UAAU,CAAC,CAAC;IAC1BI,YAAY,CAACJ,UAAU,CAAC,CAAC;IACzB,IAAInB,QAAQ,EAAE;MACZ;MACAjC,UAAU,CAACmK,GAAG;MACd;MACA,YAAY,EAAE1D,aAAa,EAAEhF,WAAW,CAACK,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;MACtE,IAAIwI,YAAY,GAAG7D,aAAa,CAACgD,eAAe,CAAC,CAAC;MAClD,IAAIc,WAAW,GAAG,CAAC,CAACD,YAAY,CAACpH,CAAC,EAAE,CAACoH,YAAY,CAACnH,CAAC,CAAC;MACpD,IAAIqH,iBAAiB,GAAG/I,WAAW,CAACK,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;MAClE,IAAI2I,SAAS,GAAGhJ,WAAW,CAACiJ,SAAS,CAAC,CAAC,CAACC,KAAK;MAC7C,IAAIC,EAAE,GAAGH,SAAS,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;MAC7C,IAAII,EAAE,GAAGJ,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,OAAO;MAC7C,IAAIK,EAAE,GAAGL,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;MACpC,IAAIvI,gBAAgB,KAAK,KAAK,EAAE;QAC9BqI,WAAW,CAACE,SAAS,CAAC,IAAIL,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D,CAAC,MAAM;QACLH,UAAU,CAACI,SAAS,CAAC,IAAIH,YAAY,CAACM,EAAE,CAAC,GAAGJ,iBAAiB;MAC/D;MACA;MACAD,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,IAAIL,WAAW,CAACS,EAAE,CAAC,GAAG,CAAC,GAAGP,YAAY,CAACO,EAAE,CAAC,GAAG,CAAC;MACxEpE,aAAa,CAACvD,CAAC,GAAGqH,WAAW,CAAC,CAAC,CAAC;MAChC9D,aAAa,CAACtD,CAAC,GAAGoH,WAAW,CAAC,CAAC,CAAC;MAChC/G,YAAY,CAACN,CAAC,GAAGmH,UAAU,CAAC,CAAC,CAAC;MAC9B7G,YAAY,CAACL,CAAC,GAAGkH,UAAU,CAAC,CAAC,CAAC;MAC9B,IAAIvH,QAAQ,GAAG;QACbI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACDL,QAAQ,CAAC8H,EAAE,CAAC,GAAGR,WAAW,CAACQ,EAAE,CAAC,GAAGJ,iBAAiB,GAAGF,YAAY,CAACM,EAAE,CAAC;MACrE9H,QAAQ,CAAC+H,EAAE,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACZ,WAAW,CAACS,EAAE,CAAC,EAAEP,YAAY,CAACO,EAAE,CAAC,CAAC;MAC1D/H,QAAQ,CAACgI,EAAE,CAAC,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEX,YAAY,CAACQ,EAAE,CAAC,GAAGP,WAAW,CAAC,CAAC,GAAGE,SAAS,CAAC,CAAC;MACzE,OAAO3H,QAAQ;IACjB,CAAC,MAAM;MACLU,YAAY,CAACN,CAAC,GAAGmH,UAAU,CAAC,CAAC,CAAC;MAC9B7G,YAAY,CAACL,CAAC,GAAGkH,UAAU,CAAC,CAAC,CAAC;MAC9B,OAAO,IAAI,CAACpJ,KAAK,CAACwI,eAAe,CAAC,CAAC;IACrC;EACF,CAAC;EACD;AACF;AACA;EACEjJ,UAAU,CAACO,SAAS,CAACwC,MAAM,GAAG,YAAY;IACxC,IAAI,CAACjC,eAAe,CAAC,CAAC,CAACgC,SAAS,CAAC,CAAC;IAClC,IAAI,CAACjC,cAAc,GAAG,IAAI;EAC5B,CAAC;EACDb,UAAU,CAACK,IAAI,GAAG,cAAc;EAChC,OAAOL,UAAU;AACnB,CAAC,CAACP,aAAa,CAAC;AAChB,SAASkI,cAAcA,CAAC+C,QAAQ,EAAEhH,eAAe,EAAEO,eAAe,EAAEiD,eAAe,EAAEC,QAAQ,EAAEI,UAAU,EAAEpG,GAAG,EAAE;EAC9G;AACF;AACA;AACA;EACE,SAASwJ,iBAAiBA,CAACvG,KAAK,EAAEwG,WAAW,EAAE;IAC7C;IACA,IAAIxG,KAAK,CAACyG,SAAS,KAAK,MAAM,EAAE;MAC9BzG,KAAK,CAACyG,SAAS,GAAGD,WAAW,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACrD;IACA/K,IAAI,CAACsE,KAAK,EAAE,UAAU0G,OAAO,EAAEC,QAAQ,EAAE;MACvC3G,KAAK,CAAC2G,QAAQ,CAAC,KAAK,SAAS,KAAK3G,KAAK,CAAC2G,QAAQ,CAAC,GAAGH,WAAW,CAACG,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;EACA;EACA,IAAIC,cAAc,GAAGtH,eAAe,CAACkD,QAAQ,CAAC,WAAW,CAAC;EAC1D,IAAIoB,SAAS,GAAGgD,cAAc,CAACC,YAAY,CAAC,CAAC;EAC7C,IAAIC,aAAa,GAAGR,QAAQ,CAACS,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC9E,IAAIC,UAAU,GAAGJ,cAAc,CAACK,UAAU,CAAC,OAAO,CAAC;EACnDrD,SAAS,CAACsD,KAAK,GAAG,CAACF,UAAU,IAAIA,UAAU,KAAK,SAAS,GAAGlE,eAAe,CAACoE,KAAK,GAAG3L,8BAA8B,CAACyL,UAAU,EAAEjK,GAAG,CAAC;EACnI,IAAI6G,SAAS,CAACvC,IAAI,KAAK,SAAS,EAAE;IAChC;AACJ;AACA;AACA;IACIuC,SAAS,CAACvC,IAAI,GAAGyB,eAAe,CAACC,QAAQ,CAAC;EAC5C;EACA,IAAIa,SAAS,CAACuD,MAAM,KAAK,SAAS,EAAE;IAClC;AACJ;AACA;AACA;IACIvD,SAAS,CAACuD,MAAM,GAAGrE,eAAe,CAACgE,aAAa,CAAC;EACnD;EACA,IAAIlD,SAAS,CAACwD,OAAO,KAAK,SAAS,EAAE;IACnC;AACJ;AACA;IACIxD,SAAS,CAACwD,OAAO,GAAG,CAACrE,QAAQ,KAAK,MAAM,GAAGD,eAAe,GAAGjD,eAAe,EAAEuH,OAAO;EACvF;EACAb,iBAAiB,CAAC3C,SAAS,EAAEd,eAAe,CAAC;EAC7C;EACA,IAAIuE,eAAe,GAAG/H,eAAe,CAACkD,QAAQ,CAAC,WAAW,CAAC;EAC3D,IAAIqB,SAAS,GAAGwD,eAAe,CAACC,YAAY,CAAC,CAAC;EAC9Cf,iBAAiB,CAAC1C,SAAS,EAAEhE,eAAe,CAAC;EAC7C;EACA+D,SAAS,CAACvC,IAAI,KAAK,MAAM,KAAKuC,SAAS,CAACvC,IAAI,GAAGyB,eAAe,CAACzB,IAAI,CAAC;EACpEuC,SAAS,CAACuD,MAAM,KAAK,MAAM,KAAKvD,SAAS,CAACuD,MAAM,GAAGrE,eAAe,CAACzB,IAAI,CAAC;EACxEwC,SAAS,CAACsD,MAAM,KAAK,MAAM,KAAKtD,SAAS,CAACsD,MAAM,GAAGrE,eAAe,CAACzB,IAAI,CAAC;EACxE,IAAI,CAAC8B,UAAU,EAAE;IACf,IAAIoE,WAAW,GAAGjI,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC5D;AACJ;AACA;AACA;AACA;IACI,IAAIsK,eAAe,GAAG5D,SAAS,CAACkD,aAAa,CAAC;IAC9ClD,SAAS,CAAC6C,SAAS,GAAGc,WAAW,KAAK,MAAM,GAAGzE,eAAe,CAAC2D,SAAS,GAAG,CAAC,IAAIe,eAAe,GAAG,CAAC,GAAG,CAAC,GAAG5D,SAAS,CAAC6C,SAAS;IAC7H7C,SAAS,CAACvC,IAAI,GAAG/B,eAAe,CAACpC,GAAG,CAAC,eAAe,CAAC;IACrD0G,SAAS,CAACuD,MAAM,GAAG7H,eAAe,CAACpC,GAAG,CAAC,qBAAqB,CAAC;IAC7D2G,SAAS,CAACsD,MAAM,GAAGE,eAAe,CAACnK,GAAG,CAAC,eAAe,CAAC;IACvD2G,SAAS,CAAC4C,SAAS,GAAGY,eAAe,CAACnK,GAAG,CAAC,eAAe,CAAC;EAC5D;EACA,OAAO;IACL0G,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA;EACb,CAAC;AACH;AACA,SAASE,oBAAoBA,CAAC0D,GAAG,EAAE;EACjC,IAAIC,SAAS,GAAGD,GAAG,CAAC9D,IAAI,IAAI,WAAW;EACvC,IAAIA,IAAI,GAAGrI,YAAY,CAACoM,SAAS,EAAE,CAAC,EAAE,CAAC,EAAED,GAAG,CAACxE,SAAS,EAAEwE,GAAG,CAACvE,UAAU,EAAEuE,GAAG,CAAC7D,SAAS,CAACvC,IAAI,EAAEoG,GAAG,CAACpE,gBAAgB,CAAC;EACjHM,IAAI,CAACgE,QAAQ,CAACF,GAAG,CAAC7D,SAAS,CAAC;EAC5BD,IAAI,CAACiE,QAAQ,GAAG,CAACH,GAAG,CAACrE,UAAU,IAAI,CAAC,IAAI+C,IAAI,CAAC0B,EAAE,GAAG,GAAG;EACrDlE,IAAI,CAACmE,SAAS,CAAC,CAACL,GAAG,CAACxE,SAAS,GAAG,CAAC,EAAEwE,GAAG,CAACvE,UAAU,GAAG,CAAC,CAAC,CAAC;EACvD,IAAIwE,SAAS,CAACK,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACnCpE,IAAI,CAAC3D,KAAK,CAACmH,MAAM,GAAGxD,IAAI,CAAC3D,KAAK,CAACqB,IAAI;IACnCsC,IAAI,CAAC3D,KAAK,CAACqB,IAAI,GAAG,MAAM;IACxBsC,IAAI,CAAC3D,KAAK,CAACyG,SAAS,GAAG,CAAC;EAC1B;EACA,OAAO9C,IAAI;AACb;AACA,SAASvD,oBAAoBA,CAAC4H,UAAU,EAAEC,QAAQ,EAAElL,GAAG,EAAEiC,eAAe,EAAE;EACxE;EACAsB,sBAAsB,CAAC0H,UAAU,EAAEC,QAAQ,EAAElL,GAAG,EAAEiC,eAAe,CAAC;EAClEjC,GAAG,CAACsF,cAAc,CAAC;IACjBpG,IAAI,EAAE,oBAAoB;IAC1BuD,IAAI,EAAEwI,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGC;EAC1C,CAAC,CAAC;EACF;EACA;EACA5H,uBAAuB,CAAC2H,UAAU,EAAEC,QAAQ,EAAElL,GAAG,EAAEiC,eAAe,CAAC;AACrE;AACA,SAASkJ,eAAeA,CAACnL,GAAG,EAAE;EAC5B,IAAIoL,IAAI,GAAGpL,GAAG,CAACqL,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,cAAc,CAAC,CAAC;EAC/C,IAAIC,aAAa;EACjB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,GAAG,GAAGN,IAAI,CAACO,MAAM;EACrB,OAAOF,CAAC,GAAGC,GAAG,IAAI,EAAEF,aAAa,GAAGJ,IAAI,CAACK,CAAC,CAAC,CAACG,MAAM,CAAChG,QAAQ,CAAC,EAAE;IAC5D6F,CAAC,EAAE;EACL;EACA,OAAOD,aAAa,IAAIA,aAAa,CAACK,UAAU;AAClD;AACA,SAASvI,uBAAuBA,CAAC2H,UAAU,EAAEC,QAAQ,EAAElL,GAAG,EAAEiC,eAAe,EAAE;EAC3E;EACA,IAAI,CAACkJ,eAAe,CAACnL,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACsF,cAAc,CAAC;MACjBpG,IAAI,EAAE,WAAW;MACjB+L,UAAU,EAAEA,UAAU;MACtBxI,IAAI,EAAEyI,QAAQ;MACdjJ,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AACA,SAASsB,sBAAsBA,CAAC0H,UAAU,EAAEC,QAAQ,EAAElL,GAAG,EAAEiC,eAAe,EAAE;EAC1E;EACA,IAAI,CAACkJ,eAAe,CAACnL,GAAG,CAAC,EAAE;IACzBA,GAAG,CAACsF,cAAc,CAAC;MACjBpG,IAAI,EAAE,UAAU;MAChB+L,UAAU,EAAEA,UAAU;MACtBxI,IAAI,EAAEyI,QAAQ;MACdjJ,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;AACF;AACA,eAAepD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}