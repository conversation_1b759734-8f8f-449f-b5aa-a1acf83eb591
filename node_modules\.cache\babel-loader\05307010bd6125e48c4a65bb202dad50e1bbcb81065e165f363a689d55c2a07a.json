{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as markerHelper from './markerHelper.js';\nimport LineDraw from '../../chart/helper/LineDraw.js';\nimport MarkerView from './MarkerView.js';\nimport { getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getECData } from '../../util/innerStore.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isArray, retrieve, retrieve2, clone, extend, logError, merge, map, curry, filter, isNumber } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nvar inner = makeInner();\nvar markLineTransform = function (seriesModel, coordSys, mlModel, item) {\n  var data = seriesModel.getData();\n  var itemArray;\n  if (!isArray(item)) {\n    // Special type markLine like 'min', 'max', 'average', 'median'\n    var mlType = item.type;\n    if (mlType === 'min' || mlType === 'max' || mlType === 'average' || mlType === 'median'\n    // In case\n    // data: [{\n    //   yAxis: 10\n    // }]\n    || item.xAxis != null || item.yAxis != null) {\n      var valueAxis = void 0;\n      var value = void 0;\n      if (item.yAxis != null || item.xAxis != null) {\n        valueAxis = coordSys.getAxis(item.yAxis != null ? 'y' : 'x');\n        value = retrieve(item.yAxis, item.xAxis);\n      } else {\n        var axisInfo = markerHelper.getAxisInfo(item, data, coordSys, seriesModel);\n        valueAxis = axisInfo.valueAxis;\n        var valueDataDim = getStackedDimension(data, axisInfo.valueDataDim);\n        value = markerHelper.numCalculate(data, valueDataDim, mlType);\n      }\n      var valueIndex = valueAxis.dim === 'x' ? 0 : 1;\n      var baseIndex = 1 - valueIndex;\n      // Normized to 2d data with start and end point\n      var mlFrom = clone(item);\n      var mlTo = {\n        coord: []\n      };\n      mlFrom.type = null;\n      mlFrom.coord = [];\n      mlFrom.coord[baseIndex] = -Infinity;\n      mlTo.coord[baseIndex] = Infinity;\n      var precision = mlModel.get('precision');\n      if (precision >= 0 && isNumber(value)) {\n        value = +value.toFixed(Math.min(precision, 20));\n      }\n      mlFrom.coord[valueIndex] = mlTo.coord[valueIndex] = value;\n      itemArray = [mlFrom, mlTo, {\n        type: mlType,\n        valueIndex: item.valueIndex,\n        // Force to use the value of calculated value.\n        value: value\n      }];\n    } else {\n      // Invalid data\n      if (process.env.NODE_ENV !== 'production') {\n        logError('Invalid markLine data.');\n      }\n      itemArray = [];\n    }\n  } else {\n    itemArray = item;\n  }\n  var normalizedItem = [markerHelper.dataTransform(seriesModel, itemArray[0]), markerHelper.dataTransform(seriesModel, itemArray[1]), extend({}, itemArray[2])];\n  // Avoid line data type is extended by from(to) data type\n  normalizedItem[2].type = normalizedItem[2].type || null;\n  // Merge from option and to option into line option\n  merge(normalizedItem[2], normalizedItem[0]);\n  merge(normalizedItem[2], normalizedItem[1]);\n  return normalizedItem;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markLine has one dim\nfunction ifMarkLineHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  var dimName = coordSys.dimensions[dimIndex];\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]) && fromCoord[dimIndex] === toCoord[dimIndex] && coordSys.getAxis(dimName).containData(fromCoord[dimIndex]);\n}\nfunction markLineFilter(coordSys, item) {\n  if (coordSys.type === 'cartesian2d') {\n    var fromCoord = item[0].coord;\n    var toCoord = item[1].coord;\n    // In case\n    // {\n    //  markLine: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkLineHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkLineHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n  }\n  return markerHelper.dataFilter(coordSys, item[0]) && markerHelper.dataFilter(coordSys, item[1]);\n}\nfunction updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(data.getValues(data.dimensions, idx));\n    } else {\n      var dims = coordSys.dimensions;\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Expand line to the edge of grid if value on one axis is Inifnity\n    // In case\n    //  markLine: {\n    //    data: [{\n    //      yAxis: 2\n    //      // or\n    //      type: 'average'\n    //    }]\n    //  }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var dims = coordSys.dimensions;\n      if (isInfinity(data.get(dims[0], idx))) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[isFrom ? 0 : 1]);\n      } else if (isInfinity(data.get(dims[1], idx))) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[isFrom ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  data.setItemLayout(idx, point);\n}\nvar MarkLineView = /** @class */function (_super) {\n  __extends(MarkLineView, _super);\n  function MarkLineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineView.type;\n    return _this;\n  }\n  MarkLineView.prototype.updateTransform = function (markLineModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mlModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markLine');\n      if (mlModel) {\n        var mlData_1 = mlModel.getData();\n        var fromData_1 = inner(mlModel).from;\n        var toData_1 = inner(mlModel).to;\n        // Update visual and layout of from symbol and to symbol\n        fromData_1.each(function (idx) {\n          updateSingleMarkerEndLayout(fromData_1, idx, true, seriesModel, api);\n          updateSingleMarkerEndLayout(toData_1, idx, false, seriesModel, api);\n        });\n        // Update layout of line\n        mlData_1.each(function (idx) {\n          mlData_1.setItemLayout(idx, [fromData_1.getItemLayout(idx), toData_1.getItemLayout(idx)]);\n        });\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkLineView.prototype.renderSeries = function (seriesModel, mlModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var lineDrawMap = this.markerGroupMap;\n    var lineDraw = lineDrawMap.get(seriesId) || lineDrawMap.set(seriesId, new LineDraw());\n    this.group.add(lineDraw.group);\n    var mlData = createList(coordSys, seriesModel, mlModel);\n    var fromData = mlData.from;\n    var toData = mlData.to;\n    var lineData = mlData.line;\n    inner(mlModel).from = fromData;\n    inner(mlModel).to = toData;\n    // Line data for tooltip and formatter\n    mlModel.setData(lineData);\n    // TODO\n    // Functionally, `symbolSize` & `symbolOffset` can also be 2D array now.\n    // But the related logic and type definition are not finished yet.\n    // Finish it if required\n    var symbolType = mlModel.get('symbol');\n    var symbolSize = mlModel.get('symbolSize');\n    var symbolRotate = mlModel.get('symbolRotate');\n    var symbolOffset = mlModel.get('symbolOffset');\n    // TODO: support callback function like markPoint\n    if (!isArray(symbolType)) {\n      symbolType = [symbolType, symbolType];\n    }\n    if (!isArray(symbolSize)) {\n      symbolSize = [symbolSize, symbolSize];\n    }\n    if (!isArray(symbolRotate)) {\n      symbolRotate = [symbolRotate, symbolRotate];\n    }\n    if (!isArray(symbolOffset)) {\n      symbolOffset = [symbolOffset, symbolOffset];\n    }\n    // Update visual and layout of from symbol and to symbol\n    mlData.from.each(function (idx) {\n      updateDataVisualAndLayout(fromData, idx, true);\n      updateDataVisualAndLayout(toData, idx, false);\n    });\n    // Update visual and layout of line\n    lineData.each(function (idx) {\n      var lineStyle = lineData.getItemModel(idx).getModel('lineStyle').getLineStyle();\n      // lineData.setItemVisual(idx, {\n      //     color: lineColor || fromData.getItemVisual(idx, 'color')\n      // });\n      lineData.setItemLayout(idx, [fromData.getItemLayout(idx), toData.getItemLayout(idx)]);\n      if (lineStyle.stroke == null) {\n        lineStyle.stroke = fromData.getItemVisual(idx, 'style').fill;\n      }\n      lineData.setItemVisual(idx, {\n        fromSymbolKeepAspect: fromData.getItemVisual(idx, 'symbolKeepAspect'),\n        fromSymbolOffset: fromData.getItemVisual(idx, 'symbolOffset'),\n        fromSymbolRotate: fromData.getItemVisual(idx, 'symbolRotate'),\n        fromSymbolSize: fromData.getItemVisual(idx, 'symbolSize'),\n        fromSymbol: fromData.getItemVisual(idx, 'symbol'),\n        toSymbolKeepAspect: toData.getItemVisual(idx, 'symbolKeepAspect'),\n        toSymbolOffset: toData.getItemVisual(idx, 'symbolOffset'),\n        toSymbolRotate: toData.getItemVisual(idx, 'symbolRotate'),\n        toSymbolSize: toData.getItemVisual(idx, 'symbolSize'),\n        toSymbol: toData.getItemVisual(idx, 'symbol'),\n        style: lineStyle\n      });\n    });\n    lineDraw.updateData(lineData);\n    // Set host model for tooltip\n    // FIXME\n    mlData.line.eachItemGraphicEl(function (el) {\n      getECData(el).dataModel = mlModel;\n      el.traverse(function (child) {\n        getECData(child).dataModel = mlModel;\n      });\n    });\n    function updateDataVisualAndLayout(data, idx, isFrom) {\n      var itemModel = data.getItemModel(idx);\n      updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (style.fill == null) {\n        style.fill = getVisualFromData(seriesData, 'color');\n      }\n      data.setItemVisual(idx, {\n        symbolKeepAspect: itemModel.get('symbolKeepAspect'),\n        // `0` should be considered as a valid value, so use `retrieve2` instead of `||`\n        symbolOffset: retrieve2(itemModel.get('symbolOffset', true), symbolOffset[isFrom ? 0 : 1]),\n        symbolRotate: retrieve2(itemModel.get('symbolRotate', true), symbolRotate[isFrom ? 0 : 1]),\n        // TODO: when 2d array is supported, it should ignore parent\n        symbolSize: retrieve2(itemModel.get('symbolSize'), symbolSize[isFrom ? 0 : 1]),\n        symbol: retrieve2(itemModel.get('symbol', true), symbolType[isFrom ? 0 : 1]),\n        style: style\n      });\n    }\n    this.markKeep(lineDraw);\n    lineDraw.group.silent = mlModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkLineView.type = 'markLine';\n  return MarkLineView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, mlModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var fromData = new SeriesData(coordDimsInfos, mlModel);\n  var toData = new SeriesData(coordDimsInfos, mlModel);\n  // No dimensions\n  var lineData = new SeriesData([], mlModel);\n  var optData = map(mlModel.get('data'), curry(markLineTransform, seriesModel, coordSys, mlModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markLineFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  fromData.initData(map(optData, function (item) {\n    return item[0];\n  }), null, dimValueGetter);\n  toData.initData(map(optData, function (item) {\n    return item[1];\n  }), null, dimValueGetter);\n  lineData.initData(map(optData, function (item) {\n    return item[2];\n  }));\n  lineData.hasItemOption = true;\n  return {\n    from: fromData,\n    to: toData,\n    line: lineData\n  };\n}\nexport default MarkLineView;", "map": {"version": 3, "names": ["__extends", "SeriesData", "numberUtil", "marker<PERSON>elper", "LineDraw", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getStackedDimension", "isCoordinateSystemType", "getECData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isArray", "retrieve", "retrieve2", "clone", "extend", "logError", "merge", "map", "curry", "filter", "isNumber", "makeInner", "getVisualFromData", "inner", "markLineTransform", "seriesModel", "coordSys", "mlModel", "item", "data", "getData", "itemArray", "mlType", "type", "xAxis", "yAxis", "valueAxis", "value", "getAxis", "axisInfo", "getAxisInfo", "valueDataDim", "numCalculate", "valueIndex", "dim", "baseIndex", "mlFrom", "mlTo", "coord", "Infinity", "precision", "get", "toFixed", "Math", "min", "process", "env", "NODE_ENV", "normalizedItem", "dataTransform", "isInfinity", "val", "isNaN", "isFinite", "ifMarkLineHasOnlyDim", "dimIndex", "fromCoord", "toCoord", "otherDimIndex", "dimName", "dimensions", "containData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataFilter", "updateSingleMarkerEndLayout", "idx", "isFrom", "api", "coordinateSystem", "itemModel", "getItemModel", "point", "xPx", "parsePercent", "getWidth", "yPx", "getHeight", "getMarkerPosition", "getV<PERSON>ues", "dims", "x", "y", "dataToPoint", "toGlobalCoord", "getExtent", "setItemLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "prototype", "updateTransform", "markLineModel", "ecModel", "eachSeries", "getMarkerModelFromSeries", "mlData_1", "fromData_1", "from", "toData_1", "to", "each", "getItemLayout", "markerGroupMap", "id", "updateLayout", "renderSeries", "seriesId", "seriesData", "lineDrawMap", "lineDraw", "set", "group", "add", "mlData", "createList", "fromData", "toData", "lineData", "line", "setData", "symbolType", "symbolSize", "symbolRotate", "symbolOffset", "updateDataVisualAndLayout", "lineStyle", "getModel", "getLineStyle", "stroke", "getItemVisual", "fill", "setItemVisual", "fromSymbolKeepAspect", "fromSymbolOffset", "fromSymbolRotate", "fromSymbolSize", "fromSymbol", "toSymbolKeepAspect", "toSymbolOffset", "toSymbolRotate", "toSymbolSize", "toSymbol", "style", "updateData", "eachItemGraphicEl", "el", "dataModel", "traverse", "child", "getItemStyle", "symbolKeepAspect", "symbol", "<PERSON><PERSON><PERSON>", "silent", "coordDimsInfos", "coordDim", "info", "getDimensionInfo", "mapDimension", "name", "ordinalMeta", "optData", "dimValueGetter", "createMarkerDimValueGetter", "initData", "hasItemOption"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/marker/MarkLineView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as markerHelper from './markerHelper.js';\nimport LineDraw from '../../chart/helper/LineDraw.js';\nimport MarkerView from './MarkerView.js';\nimport { getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getECData } from '../../util/innerStore.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isArray, retrieve, retrieve2, clone, extend, logError, merge, map, curry, filter, isNumber } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nvar inner = makeInner();\nvar markLineTransform = function (seriesModel, coordSys, mlModel, item) {\n  var data = seriesModel.getData();\n  var itemArray;\n  if (!isArray(item)) {\n    // Special type markLine like 'min', 'max', 'average', 'median'\n    var mlType = item.type;\n    if (mlType === 'min' || mlType === 'max' || mlType === 'average' || mlType === 'median'\n    // In case\n    // data: [{\n    //   yAxis: 10\n    // }]\n    || item.xAxis != null || item.yAxis != null) {\n      var valueAxis = void 0;\n      var value = void 0;\n      if (item.yAxis != null || item.xAxis != null) {\n        valueAxis = coordSys.getAxis(item.yAxis != null ? 'y' : 'x');\n        value = retrieve(item.yAxis, item.xAxis);\n      } else {\n        var axisInfo = markerHelper.getAxisInfo(item, data, coordSys, seriesModel);\n        valueAxis = axisInfo.valueAxis;\n        var valueDataDim = getStackedDimension(data, axisInfo.valueDataDim);\n        value = markerHelper.numCalculate(data, valueDataDim, mlType);\n      }\n      var valueIndex = valueAxis.dim === 'x' ? 0 : 1;\n      var baseIndex = 1 - valueIndex;\n      // Normized to 2d data with start and end point\n      var mlFrom = clone(item);\n      var mlTo = {\n        coord: []\n      };\n      mlFrom.type = null;\n      mlFrom.coord = [];\n      mlFrom.coord[baseIndex] = -Infinity;\n      mlTo.coord[baseIndex] = Infinity;\n      var precision = mlModel.get('precision');\n      if (precision >= 0 && isNumber(value)) {\n        value = +value.toFixed(Math.min(precision, 20));\n      }\n      mlFrom.coord[valueIndex] = mlTo.coord[valueIndex] = value;\n      itemArray = [mlFrom, mlTo, {\n        type: mlType,\n        valueIndex: item.valueIndex,\n        // Force to use the value of calculated value.\n        value: value\n      }];\n    } else {\n      // Invalid data\n      if (process.env.NODE_ENV !== 'production') {\n        logError('Invalid markLine data.');\n      }\n      itemArray = [];\n    }\n  } else {\n    itemArray = item;\n  }\n  var normalizedItem = [markerHelper.dataTransform(seriesModel, itemArray[0]), markerHelper.dataTransform(seriesModel, itemArray[1]), extend({}, itemArray[2])];\n  // Avoid line data type is extended by from(to) data type\n  normalizedItem[2].type = normalizedItem[2].type || null;\n  // Merge from option and to option into line option\n  merge(normalizedItem[2], normalizedItem[0]);\n  merge(normalizedItem[2], normalizedItem[1]);\n  return normalizedItem;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markLine has one dim\nfunction ifMarkLineHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  var dimName = coordSys.dimensions[dimIndex];\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]) && fromCoord[dimIndex] === toCoord[dimIndex] && coordSys.getAxis(dimName).containData(fromCoord[dimIndex]);\n}\nfunction markLineFilter(coordSys, item) {\n  if (coordSys.type === 'cartesian2d') {\n    var fromCoord = item[0].coord;\n    var toCoord = item[1].coord;\n    // In case\n    // {\n    //  markLine: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkLineHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkLineHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n  }\n  return markerHelper.dataFilter(coordSys, item[0]) && markerHelper.dataFilter(coordSys, item[1]);\n}\nfunction updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(data.getValues(data.dimensions, idx));\n    } else {\n      var dims = coordSys.dimensions;\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Expand line to the edge of grid if value on one axis is Inifnity\n    // In case\n    //  markLine: {\n    //    data: [{\n    //      yAxis: 2\n    //      // or\n    //      type: 'average'\n    //    }]\n    //  }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var dims = coordSys.dimensions;\n      if (isInfinity(data.get(dims[0], idx))) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[isFrom ? 0 : 1]);\n      } else if (isInfinity(data.get(dims[1], idx))) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[isFrom ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  data.setItemLayout(idx, point);\n}\nvar MarkLineView = /** @class */function (_super) {\n  __extends(MarkLineView, _super);\n  function MarkLineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineView.type;\n    return _this;\n  }\n  MarkLineView.prototype.updateTransform = function (markLineModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mlModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markLine');\n      if (mlModel) {\n        var mlData_1 = mlModel.getData();\n        var fromData_1 = inner(mlModel).from;\n        var toData_1 = inner(mlModel).to;\n        // Update visual and layout of from symbol and to symbol\n        fromData_1.each(function (idx) {\n          updateSingleMarkerEndLayout(fromData_1, idx, true, seriesModel, api);\n          updateSingleMarkerEndLayout(toData_1, idx, false, seriesModel, api);\n        });\n        // Update layout of line\n        mlData_1.each(function (idx) {\n          mlData_1.setItemLayout(idx, [fromData_1.getItemLayout(idx), toData_1.getItemLayout(idx)]);\n        });\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkLineView.prototype.renderSeries = function (seriesModel, mlModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var lineDrawMap = this.markerGroupMap;\n    var lineDraw = lineDrawMap.get(seriesId) || lineDrawMap.set(seriesId, new LineDraw());\n    this.group.add(lineDraw.group);\n    var mlData = createList(coordSys, seriesModel, mlModel);\n    var fromData = mlData.from;\n    var toData = mlData.to;\n    var lineData = mlData.line;\n    inner(mlModel).from = fromData;\n    inner(mlModel).to = toData;\n    // Line data for tooltip and formatter\n    mlModel.setData(lineData);\n    // TODO\n    // Functionally, `symbolSize` & `symbolOffset` can also be 2D array now.\n    // But the related logic and type definition are not finished yet.\n    // Finish it if required\n    var symbolType = mlModel.get('symbol');\n    var symbolSize = mlModel.get('symbolSize');\n    var symbolRotate = mlModel.get('symbolRotate');\n    var symbolOffset = mlModel.get('symbolOffset');\n    // TODO: support callback function like markPoint\n    if (!isArray(symbolType)) {\n      symbolType = [symbolType, symbolType];\n    }\n    if (!isArray(symbolSize)) {\n      symbolSize = [symbolSize, symbolSize];\n    }\n    if (!isArray(symbolRotate)) {\n      symbolRotate = [symbolRotate, symbolRotate];\n    }\n    if (!isArray(symbolOffset)) {\n      symbolOffset = [symbolOffset, symbolOffset];\n    }\n    // Update visual and layout of from symbol and to symbol\n    mlData.from.each(function (idx) {\n      updateDataVisualAndLayout(fromData, idx, true);\n      updateDataVisualAndLayout(toData, idx, false);\n    });\n    // Update visual and layout of line\n    lineData.each(function (idx) {\n      var lineStyle = lineData.getItemModel(idx).getModel('lineStyle').getLineStyle();\n      // lineData.setItemVisual(idx, {\n      //     color: lineColor || fromData.getItemVisual(idx, 'color')\n      // });\n      lineData.setItemLayout(idx, [fromData.getItemLayout(idx), toData.getItemLayout(idx)]);\n      if (lineStyle.stroke == null) {\n        lineStyle.stroke = fromData.getItemVisual(idx, 'style').fill;\n      }\n      lineData.setItemVisual(idx, {\n        fromSymbolKeepAspect: fromData.getItemVisual(idx, 'symbolKeepAspect'),\n        fromSymbolOffset: fromData.getItemVisual(idx, 'symbolOffset'),\n        fromSymbolRotate: fromData.getItemVisual(idx, 'symbolRotate'),\n        fromSymbolSize: fromData.getItemVisual(idx, 'symbolSize'),\n        fromSymbol: fromData.getItemVisual(idx, 'symbol'),\n        toSymbolKeepAspect: toData.getItemVisual(idx, 'symbolKeepAspect'),\n        toSymbolOffset: toData.getItemVisual(idx, 'symbolOffset'),\n        toSymbolRotate: toData.getItemVisual(idx, 'symbolRotate'),\n        toSymbolSize: toData.getItemVisual(idx, 'symbolSize'),\n        toSymbol: toData.getItemVisual(idx, 'symbol'),\n        style: lineStyle\n      });\n    });\n    lineDraw.updateData(lineData);\n    // Set host model for tooltip\n    // FIXME\n    mlData.line.eachItemGraphicEl(function (el) {\n      getECData(el).dataModel = mlModel;\n      el.traverse(function (child) {\n        getECData(child).dataModel = mlModel;\n      });\n    });\n    function updateDataVisualAndLayout(data, idx, isFrom) {\n      var itemModel = data.getItemModel(idx);\n      updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (style.fill == null) {\n        style.fill = getVisualFromData(seriesData, 'color');\n      }\n      data.setItemVisual(idx, {\n        symbolKeepAspect: itemModel.get('symbolKeepAspect'),\n        // `0` should be considered as a valid value, so use `retrieve2` instead of `||`\n        symbolOffset: retrieve2(itemModel.get('symbolOffset', true), symbolOffset[isFrom ? 0 : 1]),\n        symbolRotate: retrieve2(itemModel.get('symbolRotate', true), symbolRotate[isFrom ? 0 : 1]),\n        // TODO: when 2d array is supported, it should ignore parent\n        symbolSize: retrieve2(itemModel.get('symbolSize'), symbolSize[isFrom ? 0 : 1]),\n        symbol: retrieve2(itemModel.get('symbol', true), symbolType[isFrom ? 0 : 1]),\n        style: style\n      });\n    }\n    this.markKeep(lineDraw);\n    lineDraw.group.silent = mlModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkLineView.type = 'markLine';\n  return MarkLineView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, mlModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var fromData = new SeriesData(coordDimsInfos, mlModel);\n  var toData = new SeriesData(coordDimsInfos, mlModel);\n  // No dimensions\n  var lineData = new SeriesData([], mlModel);\n  var optData = map(mlModel.get('data'), curry(markLineTransform, seriesModel, coordSys, mlModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markLineFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  fromData.initData(map(optData, function (item) {\n    return item[0];\n  }), null, dimValueGetter);\n  toData.initData(map(optData, function (item) {\n    return item[1];\n  }), null, dimValueGetter);\n  lineData.initData(map(optData, function (item) {\n    return item[2];\n  }));\n  lineData.hasItemOption = true;\n  return {\n    from: fromData,\n    to: toData,\n    line: lineData\n  };\n}\nexport default MarkLineView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AACrI,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC;AACvB,IAAIG,iBAAiB,GAAG,SAAAA,CAAUC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACtE,IAAIC,IAAI,GAAGJ,WAAW,CAACK,OAAO,CAAC,CAAC;EAChC,IAAIC,SAAS;EACb,IAAI,CAACrB,OAAO,CAACkB,IAAI,CAAC,EAAE;IAClB;IACA,IAAII,MAAM,GAAGJ,IAAI,CAACK,IAAI;IACtB,IAAID,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK;IAC/E;IACA;IACA;IACA;IAAA,GACGJ,IAAI,CAACM,KAAK,IAAI,IAAI,IAAIN,IAAI,CAACO,KAAK,IAAI,IAAI,EAAE;MAC3C,IAAIC,SAAS,GAAG,KAAK,CAAC;MACtB,IAAIC,KAAK,GAAG,KAAK,CAAC;MAClB,IAAIT,IAAI,CAACO,KAAK,IAAI,IAAI,IAAIP,IAAI,CAACM,KAAK,IAAI,IAAI,EAAE;QAC5CE,SAAS,GAAGV,QAAQ,CAACY,OAAO,CAACV,IAAI,CAACO,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;QAC5DE,KAAK,GAAG1B,QAAQ,CAACiB,IAAI,CAACO,KAAK,EAAEP,IAAI,CAACM,KAAK,CAAC;MAC1C,CAAC,MAAM;QACL,IAAIK,QAAQ,GAAGpC,YAAY,CAACqC,WAAW,CAACZ,IAAI,EAAEC,IAAI,EAAEH,QAAQ,EAAED,WAAW,CAAC;QAC1EW,SAAS,GAAGG,QAAQ,CAACH,SAAS;QAC9B,IAAIK,YAAY,GAAGnC,mBAAmB,CAACuB,IAAI,EAAEU,QAAQ,CAACE,YAAY,CAAC;QACnEJ,KAAK,GAAGlC,YAAY,CAACuC,YAAY,CAACb,IAAI,EAAEY,YAAY,EAAET,MAAM,CAAC;MAC/D;MACA,IAAIW,UAAU,GAAGP,SAAS,CAACQ,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;MAC9C,IAAIC,SAAS,GAAG,CAAC,GAAGF,UAAU;MAC9B;MACA,IAAIG,MAAM,GAAGjC,KAAK,CAACe,IAAI,CAAC;MACxB,IAAImB,IAAI,GAAG;QACTC,KAAK,EAAE;MACT,CAAC;MACDF,MAAM,CAACb,IAAI,GAAG,IAAI;MAClBa,MAAM,CAACE,KAAK,GAAG,EAAE;MACjBF,MAAM,CAACE,KAAK,CAACH,SAAS,CAAC,GAAG,CAACI,QAAQ;MACnCF,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC,GAAGI,QAAQ;MAChC,IAAIC,SAAS,GAAGvB,OAAO,CAACwB,GAAG,CAAC,WAAW,CAAC;MACxC,IAAID,SAAS,IAAI,CAAC,IAAI9B,QAAQ,CAACiB,KAAK,CAAC,EAAE;QACrCA,KAAK,GAAG,CAACA,KAAK,CAACe,OAAO,CAACC,IAAI,CAACC,GAAG,CAACJ,SAAS,EAAE,EAAE,CAAC,CAAC;MACjD;MACAJ,MAAM,CAACE,KAAK,CAACL,UAAU,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAGN,KAAK;MACzDN,SAAS,GAAG,CAACe,MAAM,EAAEC,IAAI,EAAE;QACzBd,IAAI,EAAED,MAAM;QACZW,UAAU,EAAEf,IAAI,CAACe,UAAU;QAC3B;QACAN,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC1C,QAAQ,CAAC,wBAAwB,CAAC;MACpC;MACAgB,SAAS,GAAG,EAAE;IAChB;EACF,CAAC,MAAM;IACLA,SAAS,GAAGH,IAAI;EAClB;EACA,IAAI8B,cAAc,GAAG,CAACvD,YAAY,CAACwD,aAAa,CAAClC,WAAW,EAAEM,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE5B,YAAY,CAACwD,aAAa,CAAClC,WAAW,EAAEM,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEjB,MAAM,CAAC,CAAC,CAAC,EAAEiB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7J;EACA2B,cAAc,CAAC,CAAC,CAAC,CAACzB,IAAI,GAAGyB,cAAc,CAAC,CAAC,CAAC,CAACzB,IAAI,IAAI,IAAI;EACvD;EACAjB,KAAK,CAAC0C,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC;EAC3C1C,KAAK,CAAC0C,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC;EAC3C,OAAOA,cAAc;AACvB,CAAC;AACD,SAASE,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,CAACC,KAAK,CAACD,GAAG,CAAC,IAAI,CAACE,QAAQ,CAACF,GAAG,CAAC;AACtC;AACA;AACA,SAASG,oBAAoBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEzC,QAAQ,EAAE;EACpE,IAAI0C,aAAa,GAAG,CAAC,GAAGH,QAAQ;EAChC,IAAII,OAAO,GAAG3C,QAAQ,CAAC4C,UAAU,CAACL,QAAQ,CAAC;EAC3C,OAAOL,UAAU,CAACM,SAAS,CAACE,aAAa,CAAC,CAAC,IAAIR,UAAU,CAACO,OAAO,CAACC,aAAa,CAAC,CAAC,IAAIF,SAAS,CAACD,QAAQ,CAAC,KAAKE,OAAO,CAACF,QAAQ,CAAC,IAAIvC,QAAQ,CAACY,OAAO,CAAC+B,OAAO,CAAC,CAACE,WAAW,CAACL,SAAS,CAACD,QAAQ,CAAC,CAAC;AAC9L;AACA,SAASO,cAAcA,CAAC9C,QAAQ,EAAEE,IAAI,EAAE;EACtC,IAAIF,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;IACnC,IAAIiC,SAAS,GAAGtC,IAAI,CAAC,CAAC,CAAC,CAACoB,KAAK;IAC7B,IAAImB,OAAO,GAAGvC,IAAI,CAAC,CAAC,CAAC,CAACoB,KAAK;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA,IAAIkB,SAAS,IAAIC,OAAO,KAAKH,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAEzC,QAAQ,CAAC,IAAIsC,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAEzC,QAAQ,CAAC,CAAC,EAAE;MAC5I,OAAO,IAAI;IACb;EACF;EACA,OAAOvB,YAAY,CAACsE,UAAU,CAAC/C,QAAQ,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIzB,YAAY,CAACsE,UAAU,CAAC/C,QAAQ,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC;AACjG;AACA,SAAS8C,2BAA2BA,CAAC7C,IAAI,EAAE8C,GAAG,EAAEC,MAAM,EAAEnD,WAAW,EAAEoD,GAAG,EAAE;EACxE,IAAInD,QAAQ,GAAGD,WAAW,CAACqD,gBAAgB;EAC3C,IAAIC,SAAS,GAAGlD,IAAI,CAACmD,YAAY,CAACL,GAAG,CAAC;EACtC,IAAIM,KAAK;EACT,IAAIC,GAAG,GAAGhF,UAAU,CAACiF,YAAY,CAACJ,SAAS,CAAC5B,GAAG,CAAC,GAAG,CAAC,EAAE0B,GAAG,CAACO,QAAQ,CAAC,CAAC,CAAC;EACrE,IAAIC,GAAG,GAAGnF,UAAU,CAACiF,YAAY,CAACJ,SAAS,CAAC5B,GAAG,CAAC,GAAG,CAAC,EAAE0B,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC;EACtE,IAAI,CAACxB,KAAK,CAACoB,GAAG,CAAC,IAAI,CAACpB,KAAK,CAACuB,GAAG,CAAC,EAAE;IAC9BJ,KAAK,GAAG,CAACC,GAAG,EAAEG,GAAG,CAAC;EACpB,CAAC,MAAM;IACL;IACA,IAAI5D,WAAW,CAAC8D,iBAAiB,EAAE;MACjC;MACAN,KAAK,GAAGxD,WAAW,CAAC8D,iBAAiB,CAAC1D,IAAI,CAAC2D,SAAS,CAAC3D,IAAI,CAACyC,UAAU,EAAEK,GAAG,CAAC,CAAC;IAC7E,CAAC,MAAM;MACL,IAAIc,IAAI,GAAG/D,QAAQ,CAAC4C,UAAU;MAC9B,IAAIoB,CAAC,GAAG7D,IAAI,CAACsB,GAAG,CAACsC,IAAI,CAAC,CAAC,CAAC,EAAEd,GAAG,CAAC;MAC9B,IAAIgB,CAAC,GAAG9D,IAAI,CAACsB,GAAG,CAACsC,IAAI,CAAC,CAAC,CAAC,EAAEd,GAAG,CAAC;MAC9BM,KAAK,GAAGvD,QAAQ,CAACkE,WAAW,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIpF,sBAAsB,CAACmB,QAAQ,EAAE,aAAa,CAAC,EAAE;MACnD;MACA,IAAIQ,KAAK,GAAGR,QAAQ,CAACY,OAAO,CAAC,GAAG,CAAC;MACjC,IAAIH,KAAK,GAAGT,QAAQ,CAACY,OAAO,CAAC,GAAG,CAAC;MACjC,IAAImD,IAAI,GAAG/D,QAAQ,CAAC4C,UAAU;MAC9B,IAAIV,UAAU,CAAC/B,IAAI,CAACsB,GAAG,CAACsC,IAAI,CAAC,CAAC,CAAC,EAAEd,GAAG,CAAC,CAAC,EAAE;QACtCM,KAAK,CAAC,CAAC,CAAC,GAAG/C,KAAK,CAAC2D,aAAa,CAAC3D,KAAK,CAAC4D,SAAS,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACnE,CAAC,MAAM,IAAIhB,UAAU,CAAC/B,IAAI,CAACsB,GAAG,CAACsC,IAAI,CAAC,CAAC,CAAC,EAAEd,GAAG,CAAC,CAAC,EAAE;QAC7CM,KAAK,CAAC,CAAC,CAAC,GAAG9C,KAAK,CAAC0D,aAAa,CAAC1D,KAAK,CAAC2D,SAAS,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACnE;IACF;IACA;IACA,IAAI,CAACd,KAAK,CAACoB,GAAG,CAAC,EAAE;MACfD,KAAK,CAAC,CAAC,CAAC,GAAGC,GAAG;IAChB;IACA,IAAI,CAACpB,KAAK,CAACuB,GAAG,CAAC,EAAE;MACfJ,KAAK,CAAC,CAAC,CAAC,GAAGI,GAAG;IAChB;EACF;EACAxD,IAAI,CAACkE,aAAa,CAACpB,GAAG,EAAEM,KAAK,CAAC;AAChC;AACA,IAAIe,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDjG,SAAS,CAACgG,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACjE,IAAI,GAAG+D,YAAY,CAAC/D,IAAI;IAC9B,OAAOiE,KAAK;EACd;EACAF,YAAY,CAACK,SAAS,CAACC,eAAe,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAE3B,GAAG,EAAE;IAC9E2B,OAAO,CAACC,UAAU,CAAC,UAAUhF,WAAW,EAAE;MACxC,IAAIE,OAAO,GAAGlB,WAAW,CAACiG,wBAAwB,CAACjF,WAAW,EAAE,UAAU,CAAC;MAC3E,IAAIE,OAAO,EAAE;QACX,IAAIgF,QAAQ,GAAGhF,OAAO,CAACG,OAAO,CAAC,CAAC;QAChC,IAAI8E,UAAU,GAAGrF,KAAK,CAACI,OAAO,CAAC,CAACkF,IAAI;QACpC,IAAIC,QAAQ,GAAGvF,KAAK,CAACI,OAAO,CAAC,CAACoF,EAAE;QAChC;QACAH,UAAU,CAACI,IAAI,CAAC,UAAUrC,GAAG,EAAE;UAC7BD,2BAA2B,CAACkC,UAAU,EAAEjC,GAAG,EAAE,IAAI,EAAElD,WAAW,EAAEoD,GAAG,CAAC;UACpEH,2BAA2B,CAACoC,QAAQ,EAAEnC,GAAG,EAAE,KAAK,EAAElD,WAAW,EAAEoD,GAAG,CAAC;QACrE,CAAC,CAAC;QACF;QACA8B,QAAQ,CAACK,IAAI,CAAC,UAAUrC,GAAG,EAAE;UAC3BgC,QAAQ,CAACZ,aAAa,CAACpB,GAAG,EAAE,CAACiC,UAAU,CAACK,aAAa,CAACtC,GAAG,CAAC,EAAEmC,QAAQ,CAACG,aAAa,CAACtC,GAAG,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC;QACF,IAAI,CAACuC,cAAc,CAAC/D,GAAG,CAAC1B,WAAW,CAAC0F,EAAE,CAAC,CAACC,YAAY,CAAC,CAAC;MACxD;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDpB,YAAY,CAACK,SAAS,CAACgB,YAAY,GAAG,UAAU5F,WAAW,EAAEE,OAAO,EAAE6E,OAAO,EAAE3B,GAAG,EAAE;IAClF,IAAInD,QAAQ,GAAGD,WAAW,CAACqD,gBAAgB;IAC3C,IAAIwC,QAAQ,GAAG7F,WAAW,CAAC0F,EAAE;IAC7B,IAAII,UAAU,GAAG9F,WAAW,CAACK,OAAO,CAAC,CAAC;IACtC,IAAI0F,WAAW,GAAG,IAAI,CAACN,cAAc;IACrC,IAAIO,QAAQ,GAAGD,WAAW,CAACrE,GAAG,CAACmE,QAAQ,CAAC,IAAIE,WAAW,CAACE,GAAG,CAACJ,QAAQ,EAAE,IAAIlH,QAAQ,CAAC,CAAC,CAAC;IACrF,IAAI,CAACuH,KAAK,CAACC,GAAG,CAACH,QAAQ,CAACE,KAAK,CAAC;IAC9B,IAAIE,MAAM,GAAGC,UAAU,CAACpG,QAAQ,EAAED,WAAW,EAAEE,OAAO,CAAC;IACvD,IAAIoG,QAAQ,GAAGF,MAAM,CAAChB,IAAI;IAC1B,IAAImB,MAAM,GAAGH,MAAM,CAACd,EAAE;IACtB,IAAIkB,QAAQ,GAAGJ,MAAM,CAACK,IAAI;IAC1B3G,KAAK,CAACI,OAAO,CAAC,CAACkF,IAAI,GAAGkB,QAAQ;IAC9BxG,KAAK,CAACI,OAAO,CAAC,CAACoF,EAAE,GAAGiB,MAAM;IAC1B;IACArG,OAAO,CAACwG,OAAO,CAACF,QAAQ,CAAC;IACzB;IACA;IACA;IACA;IACA,IAAIG,UAAU,GAAGzG,OAAO,CAACwB,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIkF,UAAU,GAAG1G,OAAO,CAACwB,GAAG,CAAC,YAAY,CAAC;IAC1C,IAAImF,YAAY,GAAG3G,OAAO,CAACwB,GAAG,CAAC,cAAc,CAAC;IAC9C,IAAIoF,YAAY,GAAG5G,OAAO,CAACwB,GAAG,CAAC,cAAc,CAAC;IAC9C;IACA,IAAI,CAACzC,OAAO,CAAC0H,UAAU,CAAC,EAAE;MACxBA,UAAU,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;IACvC;IACA,IAAI,CAAC1H,OAAO,CAAC2H,UAAU,CAAC,EAAE;MACxBA,UAAU,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;IACvC;IACA,IAAI,CAAC3H,OAAO,CAAC4H,YAAY,CAAC,EAAE;MAC1BA,YAAY,GAAG,CAACA,YAAY,EAAEA,YAAY,CAAC;IAC7C;IACA,IAAI,CAAC5H,OAAO,CAAC6H,YAAY,CAAC,EAAE;MAC1BA,YAAY,GAAG,CAACA,YAAY,EAAEA,YAAY,CAAC;IAC7C;IACA;IACAV,MAAM,CAAChB,IAAI,CAACG,IAAI,CAAC,UAAUrC,GAAG,EAAE;MAC9B6D,yBAAyB,CAACT,QAAQ,EAAEpD,GAAG,EAAE,IAAI,CAAC;MAC9C6D,yBAAyB,CAACR,MAAM,EAAErD,GAAG,EAAE,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF;IACAsD,QAAQ,CAACjB,IAAI,CAAC,UAAUrC,GAAG,EAAE;MAC3B,IAAI8D,SAAS,GAAGR,QAAQ,CAACjD,YAAY,CAACL,GAAG,CAAC,CAAC+D,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC/E;MACA;MACA;MACAV,QAAQ,CAAClC,aAAa,CAACpB,GAAG,EAAE,CAACoD,QAAQ,CAACd,aAAa,CAACtC,GAAG,CAAC,EAAEqD,MAAM,CAACf,aAAa,CAACtC,GAAG,CAAC,CAAC,CAAC;MACrF,IAAI8D,SAAS,CAACG,MAAM,IAAI,IAAI,EAAE;QAC5BH,SAAS,CAACG,MAAM,GAAGb,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,OAAO,CAAC,CAACmE,IAAI;MAC9D;MACAb,QAAQ,CAACc,aAAa,CAACpE,GAAG,EAAE;QAC1BqE,oBAAoB,EAAEjB,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,kBAAkB,CAAC;QACrEsE,gBAAgB,EAAElB,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,cAAc,CAAC;QAC7DuE,gBAAgB,EAAEnB,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,cAAc,CAAC;QAC7DwE,cAAc,EAAEpB,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,YAAY,CAAC;QACzDyE,UAAU,EAAErB,QAAQ,CAACc,aAAa,CAAClE,GAAG,EAAE,QAAQ,CAAC;QACjD0E,kBAAkB,EAAErB,MAAM,CAACa,aAAa,CAAClE,GAAG,EAAE,kBAAkB,CAAC;QACjE2E,cAAc,EAAEtB,MAAM,CAACa,aAAa,CAAClE,GAAG,EAAE,cAAc,CAAC;QACzD4E,cAAc,EAAEvB,MAAM,CAACa,aAAa,CAAClE,GAAG,EAAE,cAAc,CAAC;QACzD6E,YAAY,EAAExB,MAAM,CAACa,aAAa,CAAClE,GAAG,EAAE,YAAY,CAAC;QACrD8E,QAAQ,EAAEzB,MAAM,CAACa,aAAa,CAAClE,GAAG,EAAE,QAAQ,CAAC;QAC7C+E,KAAK,EAAEjB;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IACFhB,QAAQ,CAACkC,UAAU,CAAC1B,QAAQ,CAAC;IAC7B;IACA;IACAJ,MAAM,CAACK,IAAI,CAAC0B,iBAAiB,CAAC,UAAUC,EAAE,EAAE;MAC1CrJ,SAAS,CAACqJ,EAAE,CAAC,CAACC,SAAS,GAAGnI,OAAO;MACjCkI,EAAE,CAACE,QAAQ,CAAC,UAAUC,KAAK,EAAE;QAC3BxJ,SAAS,CAACwJ,KAAK,CAAC,CAACF,SAAS,GAAGnI,OAAO;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,SAAS6G,yBAAyBA,CAAC3G,IAAI,EAAE8C,GAAG,EAAEC,MAAM,EAAE;MACpD,IAAIG,SAAS,GAAGlD,IAAI,CAACmD,YAAY,CAACL,GAAG,CAAC;MACtCD,2BAA2B,CAAC7C,IAAI,EAAE8C,GAAG,EAAEC,MAAM,EAAEnD,WAAW,EAAEoD,GAAG,CAAC;MAChE,IAAI6E,KAAK,GAAG3E,SAAS,CAAC2D,QAAQ,CAAC,WAAW,CAAC,CAACuB,YAAY,CAAC,CAAC;MAC1D,IAAIP,KAAK,CAACZ,IAAI,IAAI,IAAI,EAAE;QACtBY,KAAK,CAACZ,IAAI,GAAGxH,iBAAiB,CAACiG,UAAU,EAAE,OAAO,CAAC;MACrD;MACA1F,IAAI,CAACkH,aAAa,CAACpE,GAAG,EAAE;QACtBuF,gBAAgB,EAAEnF,SAAS,CAAC5B,GAAG,CAAC,kBAAkB,CAAC;QACnD;QACAoF,YAAY,EAAE3H,SAAS,CAACmE,SAAS,CAAC5B,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,EAAEoF,YAAY,CAAC3D,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F0D,YAAY,EAAE1H,SAAS,CAACmE,SAAS,CAAC5B,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,EAAEmF,YAAY,CAAC1D,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F;QACAyD,UAAU,EAAEzH,SAAS,CAACmE,SAAS,CAAC5B,GAAG,CAAC,YAAY,CAAC,EAAEkF,UAAU,CAACzD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9EuF,MAAM,EAAEvJ,SAAS,CAACmE,SAAS,CAAC5B,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAEiF,UAAU,CAACxD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5E8E,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;IACA,IAAI,CAACU,QAAQ,CAAC3C,QAAQ,CAAC;IACvBA,QAAQ,CAACE,KAAK,CAAC0C,MAAM,GAAG1I,OAAO,CAACwB,GAAG,CAAC,QAAQ,CAAC,IAAI1B,WAAW,CAAC0B,GAAG,CAAC,QAAQ,CAAC;EAC5E,CAAC;EACD6C,YAAY,CAAC/D,IAAI,GAAG,UAAU;EAC9B,OAAO+D,YAAY;AACrB,CAAC,CAAC3F,UAAU,CAAC;AACb,SAASyH,UAAUA,CAACpG,QAAQ,EAAED,WAAW,EAAEE,OAAO,EAAE;EAClD,IAAI2I,cAAc;EAClB,IAAI5I,QAAQ,EAAE;IACZ4I,cAAc,GAAGrJ,GAAG,CAACS,QAAQ,IAAIA,QAAQ,CAAC4C,UAAU,EAAE,UAAUiG,QAAQ,EAAE;MACxE,IAAIC,IAAI,GAAG/I,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC2I,gBAAgB,CAAChJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC4I,YAAY,CAACH,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;MACrG;MACA,OAAOzJ,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE0J,IAAI,CAAC,EAAE;QAC9BG,IAAI,EAAEJ,QAAQ;QACd;QACAK,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,cAAc,GAAG,CAAC;MAChBK,IAAI,EAAE,OAAO;MACb1I,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EACA,IAAI8F,QAAQ,GAAG,IAAI9H,UAAU,CAACqK,cAAc,EAAE3I,OAAO,CAAC;EACtD,IAAIqG,MAAM,GAAG,IAAI/H,UAAU,CAACqK,cAAc,EAAE3I,OAAO,CAAC;EACpD;EACA,IAAIsG,QAAQ,GAAG,IAAIhI,UAAU,CAAC,EAAE,EAAE0B,OAAO,CAAC;EAC1C,IAAIkJ,OAAO,GAAG5J,GAAG,CAACU,OAAO,CAACwB,GAAG,CAAC,MAAM,CAAC,EAAEjC,KAAK,CAACM,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAChG,IAAID,QAAQ,EAAE;IACZmJ,OAAO,GAAG1J,MAAM,CAAC0J,OAAO,EAAE3J,KAAK,CAACsD,cAAc,EAAE9C,QAAQ,CAAC,CAAC;EAC5D;EACA,IAAIoJ,cAAc,GAAG3K,YAAY,CAAC4K,0BAA0B,CAAC,CAAC,CAACrJ,QAAQ,EAAE4I,cAAc,CAAC;EACxFvC,QAAQ,CAACiD,QAAQ,CAAC/J,GAAG,CAAC4J,OAAO,EAAE,UAAUjJ,IAAI,EAAE;IAC7C,OAAOA,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,EAAE,IAAI,EAAEkJ,cAAc,CAAC;EACzB9C,MAAM,CAACgD,QAAQ,CAAC/J,GAAG,CAAC4J,OAAO,EAAE,UAAUjJ,IAAI,EAAE;IAC3C,OAAOA,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,EAAE,IAAI,EAAEkJ,cAAc,CAAC;EACzB7C,QAAQ,CAAC+C,QAAQ,CAAC/J,GAAG,CAAC4J,OAAO,EAAE,UAAUjJ,IAAI,EAAE;IAC7C,OAAOA,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC;EACHqG,QAAQ,CAACgD,aAAa,GAAG,IAAI;EAC7B,OAAO;IACLpE,IAAI,EAAEkB,QAAQ;IACdhB,EAAE,EAAEiB,MAAM;IACVE,IAAI,EAAED;EACR,CAAC;AACH;AACA,eAAejC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}