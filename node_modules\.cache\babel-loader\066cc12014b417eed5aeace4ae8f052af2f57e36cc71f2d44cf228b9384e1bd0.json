{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { isFunction, retrieve2, extend, keys, trim } from 'zrender/lib/core/util.js';\nimport { SPECIAL_STATES, DISPLAY_STATES } from '../util/states.js';\nimport { deprecateReplaceLog } from '../util/log.js';\nimport { makeInner, interpolateRawValues } from '../util/model.js';\nimport { initProps, updateProps } from '../util/graphic.js';\nvar EMPTY_OBJ = {};\nexport function setLabelText(label, labelTexts) {\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var text = labelTexts[stateName];\n    var state = label.ensureState(stateName);\n    state.style = state.style || {};\n    state.style.text = text;\n  }\n  var oldStates = label.currentStates.slice();\n  label.clearStates(true);\n  label.setStyle({\n    text: labelTexts.normal\n  });\n  label.useStates(oldStates, true);\n}\nfunction getLabelText(opt, stateModels, interpolatedValue) {\n  var labelFetcher = opt.labelFetcher;\n  var labelDataIndex = opt.labelDataIndex;\n  var labelDimIndex = opt.labelDimIndex;\n  var normalModel = stateModels.normal;\n  var baseText;\n  if (labelFetcher) {\n    baseText = labelFetcher.getFormattedLabel(labelDataIndex, 'normal', null, labelDimIndex, normalModel && normalModel.get('formatter'), interpolatedValue != null ? {\n      interpolatedValue: interpolatedValue\n    } : null);\n  }\n  if (baseText == null) {\n    baseText = isFunction(opt.defaultText) ? opt.defaultText(labelDataIndex, opt, interpolatedValue) : opt.defaultText;\n  }\n  var statesText = {\n    normal: baseText\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var stateModel = stateModels[stateName];\n    statesText[stateName] = retrieve2(labelFetcher ? labelFetcher.getFormattedLabel(labelDataIndex, stateName, null, labelDimIndex, stateModel && stateModel.get('formatter')) : null, baseText);\n  }\n  return statesText;\n}\nfunction setLabelStyle(targetEl, labelStatesModels, opt, stateSpecified\n// TODO specified position?\n) {\n  opt = opt || EMPTY_OBJ;\n  var isSetOnText = targetEl instanceof ZRText;\n  var needsCreateText = false;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateModel = labelStatesModels[DISPLAY_STATES[i]];\n    if (stateModel && stateModel.getShallow('show')) {\n      needsCreateText = true;\n      break;\n    }\n  }\n  var textContent = isSetOnText ? targetEl : targetEl.getTextContent();\n  if (needsCreateText) {\n    if (!isSetOnText) {\n      // Reuse the previous\n      if (!textContent) {\n        textContent = new ZRText();\n        targetEl.setTextContent(textContent);\n      }\n      // Use same state proxy\n      if (targetEl.stateProxy) {\n        textContent.stateProxy = targetEl.stateProxy;\n      }\n    }\n    var labelStatesTexts = getLabelText(opt, labelStatesModels);\n    var normalModel = labelStatesModels.normal;\n    var showNormal = !!normalModel.getShallow('show');\n    var normalStyle = createTextStyle(normalModel, stateSpecified && stateSpecified.normal, opt, false, !isSetOnText);\n    normalStyle.text = labelStatesTexts.normal;\n    if (!isSetOnText) {\n      // Always create new\n      targetEl.setTextConfig(createTextConfig(normalModel, opt, false));\n    }\n    for (var i = 0; i < SPECIAL_STATES.length; i++) {\n      var stateName = SPECIAL_STATES[i];\n      var stateModel = labelStatesModels[stateName];\n      if (stateModel) {\n        var stateObj = textContent.ensureState(stateName);\n        var stateShow = !!retrieve2(stateModel.getShallow('show'), showNormal);\n        if (stateShow !== showNormal) {\n          stateObj.ignore = !stateShow;\n        }\n        stateObj.style = createTextStyle(stateModel, stateSpecified && stateSpecified[stateName], opt, true, !isSetOnText);\n        stateObj.style.text = labelStatesTexts[stateName];\n        if (!isSetOnText) {\n          var targetElEmphasisState = targetEl.ensureState(stateName);\n          targetElEmphasisState.textConfig = createTextConfig(stateModel, opt, true);\n        }\n      }\n    }\n    // PENDING: if there is many requirements that emphasis position\n    // need to be different from normal position, we might consider\n    // auto silent is those cases.\n    textContent.silent = !!normalModel.getShallow('silent');\n    // Keep x and y\n    if (textContent.style.x != null) {\n      normalStyle.x = textContent.style.x;\n    }\n    if (textContent.style.y != null) {\n      normalStyle.y = textContent.style.y;\n    }\n    textContent.ignore = !showNormal;\n    // Always create new style.\n    textContent.useStyle(normalStyle);\n    textContent.dirty();\n    if (opt.enableTextSetter) {\n      labelInner(textContent).setLabelText = function (interpolatedValue) {\n        var labelStatesTexts = getLabelText(opt, labelStatesModels, interpolatedValue);\n        setLabelText(textContent, labelStatesTexts);\n      };\n    }\n  } else if (textContent) {\n    // Not display rich text.\n    textContent.ignore = true;\n  }\n  targetEl.dirty();\n}\nexport { setLabelStyle };\nexport function getLabelStatesModels(itemModel, labelName) {\n  labelName = labelName || 'label';\n  var statesModels = {\n    normal: itemModel.getModel(labelName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelName]);\n  }\n  return statesModels;\n}\n/**\r\n * Set basic textStyle properties.\r\n */\nexport function createTextStyle(textStyleModel, specifiedTextStyle,\n// Fixed style in the code. Can't be set by model.\nopt, isNotNormal, isAttached // If text is attached on an element. If so, auto color will handling in zrender.\n) {\n  var textStyle = {};\n  setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached);\n  specifiedTextStyle && extend(textStyle, specifiedTextStyle);\n  // textStyle.host && textStyle.host.dirty && textStyle.host.dirty(false);\n  return textStyle;\n}\nexport function createTextConfig(textStyleModel, opt, isNotNormal) {\n  opt = opt || {};\n  var textConfig = {};\n  var labelPosition;\n  var labelRotate = textStyleModel.getShallow('rotate');\n  var labelDistance = retrieve2(textStyleModel.getShallow('distance'), isNotNormal ? null : 5);\n  var labelOffset = textStyleModel.getShallow('offset');\n  labelPosition = textStyleModel.getShallow('position') || (isNotNormal ? null : 'inside');\n  // 'outside' is not a valid zr textPostion value, but used\n  // in bar series, and magric type should be considered.\n  labelPosition === 'outside' && (labelPosition = opt.defaultOutsidePosition || 'top');\n  if (labelPosition != null) {\n    textConfig.position = labelPosition;\n  }\n  if (labelOffset != null) {\n    textConfig.offset = labelOffset;\n  }\n  if (labelRotate != null) {\n    labelRotate *= Math.PI / 180;\n    textConfig.rotation = labelRotate;\n  }\n  if (labelDistance != null) {\n    textConfig.distance = labelDistance;\n  }\n  // fill and auto is determined by the color of path fill if it's not specified by developers.\n  textConfig.outsideFill = textStyleModel.get('color') === 'inherit' ? opt.inheritColor || null : 'auto';\n  return textConfig;\n}\n/**\r\n * The uniform entry of set text style, that is, retrieve style definitions\r\n * from `model` and set to `textStyle` object.\r\n *\r\n * Never in merge mode, but in overwrite mode, that is, all of the text style\r\n * properties will be set. (Consider the states of normal and emphasis and\r\n * default value can be adopted, merge would make the logic too complicated\r\n * to manage.)\r\n */\nfunction setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached) {\n  // Consider there will be abnormal when merge hover style to normal style if given default value.\n  opt = opt || EMPTY_OBJ;\n  var ecModel = textStyleModel.ecModel;\n  var globalTextStyle = ecModel && ecModel.option.textStyle;\n  // Consider case:\n  // {\n  //     data: [{\n  //         value: 12,\n  //         label: {\n  //             rich: {\n  //                 // no 'a' here but using parent 'a'.\n  //             }\n  //         }\n  //     }],\n  //     rich: {\n  //         a: { ... }\n  //     }\n  // }\n  var richItemNames = getRichItemNames(textStyleModel);\n  var richResult;\n  if (richItemNames) {\n    richResult = {};\n    for (var name_1 in richItemNames) {\n      if (richItemNames.hasOwnProperty(name_1)) {\n        // Cascade is supported in rich.\n        var richTextStyle = textStyleModel.getModel(['rich', name_1]);\n        // In rich, never `disableBox`.\n        // FIXME: consider `label: {formatter: '{a|xx}', color: 'blue', rich: {a: {}}}`,\n        // the default color `'blue'` will not be adopted if no color declared in `rich`.\n        // That might confuses users. So probably we should put `textStyleModel` as the\n        // root ancestor of the `richTextStyle`. But that would be a break change.\n        setTokenTextStyle(richResult[name_1] = {}, richTextStyle, globalTextStyle, opt, isNotNormal, isAttached, false, true);\n      }\n    }\n  }\n  if (richResult) {\n    textStyle.rich = richResult;\n  }\n  var overflow = textStyleModel.get('overflow');\n  if (overflow) {\n    textStyle.overflow = overflow;\n  }\n  var margin = textStyleModel.get('minMargin');\n  if (margin != null) {\n    textStyle.margin = margin;\n  }\n  setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, true, false);\n}\n// Consider case:\n// {\n//     data: [{\n//         value: 12,\n//         label: {\n//             rich: {\n//                 // no 'a' here but using parent 'a'.\n//             }\n//         }\n//     }],\n//     rich: {\n//         a: { ... }\n//     }\n// }\n// TODO TextStyleModel\nfunction getRichItemNames(textStyleModel) {\n  // Use object to remove duplicated names.\n  var richItemNameMap;\n  while (textStyleModel && textStyleModel !== textStyleModel.ecModel) {\n    var rich = (textStyleModel.option || EMPTY_OBJ).rich;\n    if (rich) {\n      richItemNameMap = richItemNameMap || {};\n      var richKeys = keys(rich);\n      for (var i = 0; i < richKeys.length; i++) {\n        var richKey = richKeys[i];\n        richItemNameMap[richKey] = 1;\n      }\n    }\n    textStyleModel = textStyleModel.parentModel;\n  }\n  return richItemNameMap;\n}\nvar TEXT_PROPS_WITH_GLOBAL = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'textShadowColor', 'textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'];\nvar TEXT_PROPS_SELF = ['align', 'lineHeight', 'width', 'height', 'tag', 'verticalAlign', 'ellipsis'];\nvar TEXT_PROPS_BOX = ['padding', 'borderWidth', 'borderRadius', 'borderDashOffset', 'backgroundColor', 'borderColor', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nfunction setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, isBlock, inRich) {\n  // In merge mode, default value should not be given.\n  globalTextStyle = !isNotNormal && globalTextStyle || EMPTY_OBJ;\n  var inheritColor = opt && opt.inheritColor;\n  var fillColor = textStyleModel.getShallow('color');\n  var strokeColor = textStyleModel.getShallow('textBorderColor');\n  var opacity = retrieve2(textStyleModel.getShallow('opacity'), globalTextStyle.opacity);\n  if (fillColor === 'inherit' || fillColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (fillColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      fillColor = inheritColor;\n    } else {\n      fillColor = null;\n    }\n  }\n  if (strokeColor === 'inherit' || strokeColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (strokeColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      strokeColor = inheritColor;\n    } else {\n      strokeColor = null;\n    }\n  }\n  if (!isAttached) {\n    // Only use default global textStyle.color if text is individual.\n    // Otherwise it will use the strategy of attached text color because text may be on a path.\n    fillColor = fillColor || globalTextStyle.color;\n    strokeColor = strokeColor || globalTextStyle.textBorderColor;\n  }\n  if (fillColor != null) {\n    textStyle.fill = fillColor;\n  }\n  if (strokeColor != null) {\n    textStyle.stroke = strokeColor;\n  }\n  var textBorderWidth = retrieve2(textStyleModel.getShallow('textBorderWidth'), globalTextStyle.textBorderWidth);\n  if (textBorderWidth != null) {\n    textStyle.lineWidth = textBorderWidth;\n  }\n  var textBorderType = retrieve2(textStyleModel.getShallow('textBorderType'), globalTextStyle.textBorderType);\n  if (textBorderType != null) {\n    textStyle.lineDash = textBorderType;\n  }\n  var textBorderDashOffset = retrieve2(textStyleModel.getShallow('textBorderDashOffset'), globalTextStyle.textBorderDashOffset);\n  if (textBorderDashOffset != null) {\n    textStyle.lineDashOffset = textBorderDashOffset;\n  }\n  if (!isNotNormal && opacity == null && !inRich) {\n    opacity = opt && opt.defaultOpacity;\n  }\n  if (opacity != null) {\n    textStyle.opacity = opacity;\n  }\n  // TODO\n  if (!isNotNormal && !isAttached) {\n    // Set default finally.\n    if (textStyle.fill == null && opt.inheritColor) {\n      textStyle.fill = opt.inheritColor;\n    }\n  }\n  // Do not use `getFont` here, because merge should be supported, where\n  // part of these properties may be changed in emphasis style, and the\n  // others should remain their original value got from normal style.\n  for (var i = 0; i < TEXT_PROPS_WITH_GLOBAL.length; i++) {\n    var key = TEXT_PROPS_WITH_GLOBAL[i];\n    var val = retrieve2(textStyleModel.getShallow(key), globalTextStyle[key]);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  for (var i = 0; i < TEXT_PROPS_SELF.length; i++) {\n    var key = TEXT_PROPS_SELF[i];\n    var val = textStyleModel.getShallow(key);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  if (textStyle.verticalAlign == null) {\n    var baseline = textStyleModel.getShallow('baseline');\n    if (baseline != null) {\n      textStyle.verticalAlign = baseline;\n    }\n  }\n  if (!isBlock || !opt.disableBox) {\n    for (var i = 0; i < TEXT_PROPS_BOX.length; i++) {\n      var key = TEXT_PROPS_BOX[i];\n      var val = textStyleModel.getShallow(key);\n      if (val != null) {\n        textStyle[key] = val;\n      }\n    }\n    var borderType = textStyleModel.getShallow('borderType');\n    if (borderType != null) {\n      textStyle.borderDash = borderType;\n    }\n    if ((textStyle.backgroundColor === 'auto' || textStyle.backgroundColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.backgroundColor === 'auto') {\n          deprecateReplaceLog('backgroundColor: \\'auto\\'', 'backgroundColor: \\'inherit\\'');\n        }\n      }\n      textStyle.backgroundColor = inheritColor;\n    }\n    if ((textStyle.borderColor === 'auto' || textStyle.borderColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.borderColor === 'auto') {\n          deprecateReplaceLog('borderColor: \\'auto\\'', 'borderColor: \\'inherit\\'');\n        }\n      }\n      textStyle.borderColor = inheritColor;\n    }\n  }\n}\nexport function getFont(opt, ecModel) {\n  var gTextStyleModel = ecModel && ecModel.getModel('textStyle');\n  return trim([\n  // FIXME in node-canvas fontWeight is before fontStyle\n  opt.fontStyle || gTextStyleModel && gTextStyleModel.getShallow('fontStyle') || '', opt.fontWeight || gTextStyleModel && gTextStyleModel.getShallow('fontWeight') || '', (opt.fontSize || gTextStyleModel && gTextStyleModel.getShallow('fontSize') || 12) + 'px', opt.fontFamily || gTextStyleModel && gTextStyleModel.getShallow('fontFamily') || 'sans-serif'].join(' '));\n}\nexport var labelInner = makeInner();\nexport function setLabelValueAnimation(label, labelStatesModels, value, getDefaultText) {\n  if (!label) {\n    return;\n  }\n  var obj = labelInner(label);\n  obj.prevValue = obj.value;\n  obj.value = value;\n  var normalLabelModel = labelStatesModels.normal;\n  obj.valueAnimation = normalLabelModel.get('valueAnimation');\n  if (obj.valueAnimation) {\n    obj.precision = normalLabelModel.get('precision');\n    obj.defaultInterpolatedText = getDefaultText;\n    obj.statesModels = labelStatesModels;\n  }\n}\nexport function animateLabelValue(textEl, dataIndex, data, animatableModel, labelFetcher) {\n  var labelInnerStore = labelInner(textEl);\n  if (!labelInnerStore.valueAnimation || labelInnerStore.prevValue === labelInnerStore.value) {\n    // Value not changed, no new label animation\n    return;\n  }\n  var defaultInterpolatedText = labelInnerStore.defaultInterpolatedText;\n  // Consider the case that being animating, do not use the `obj.value`,\n  // Otherwise it will jump to the `obj.value` when this new animation started.\n  var currValue = retrieve2(labelInnerStore.interpolatedValue, labelInnerStore.prevValue);\n  var targetValue = labelInnerStore.value;\n  function during(percent) {\n    var interpolated = interpolateRawValues(data, labelInnerStore.precision, currValue, targetValue, percent);\n    labelInnerStore.interpolatedValue = percent === 1 ? null : interpolated;\n    var labelText = getLabelText({\n      labelDataIndex: dataIndex,\n      labelFetcher: labelFetcher,\n      defaultText: defaultInterpolatedText ? defaultInterpolatedText(interpolated) : interpolated + ''\n    }, labelInnerStore.statesModels, interpolated);\n    setLabelText(textEl, labelText);\n  }\n  textEl.percent = 0;\n  (labelInnerStore.prevValue == null ? initProps : updateProps)(textEl, {\n    // percent is used to prevent animation from being aborted #15916\n    percent: 1\n  }, animatableModel, dataIndex, null, during);\n}", "map": {"version": 3, "names": ["ZRText", "isFunction", "retrieve2", "extend", "keys", "trim", "SPECIAL_STATES", "DISPLAY_STATES", "deprecateReplaceLog", "makeInner", "interpolateRawValues", "initProps", "updateProps", "EMPTY_OBJ", "setLabelText", "label", "labelTexts", "i", "length", "stateName", "text", "state", "ensureState", "style", "oldStates", "currentStates", "slice", "clearStates", "setStyle", "normal", "useStates", "getLabelText", "opt", "stateModels", "interpolatedV<PERSON>ue", "labelFetcher", "labelDataIndex", "labelDimIndex", "normalModel", "baseText", "getFormattedLabel", "get", "defaultText", "statesText", "stateModel", "setLabelStyle", "targetEl", "labelStatesModels", "stateSpecified", "isSetOnText", "needsCreateText", "getShallow", "textContent", "getTextContent", "setTextContent", "stateProxy", "labelStatesTexts", "showNormal", "normalStyle", "createTextStyle", "setTextConfig", "createTextConfig", "stateObj", "stateShow", "ignore", "targetElEmphasisState", "textConfig", "silent", "x", "y", "useStyle", "dirty", "enableTextSetter", "labelInner", "getLabelStatesModels", "itemModel", "labelName", "statesModels", "getModel", "textStyleModel", "specifiedTextStyle", "isNotNormal", "isAttached", "textStyle", "setTextStyleCommon", "labelPosition", "labelRotate", "labelDistance", "labelOffset", "defaultOutsidePosition", "position", "offset", "Math", "PI", "rotation", "distance", "outsideFill", "inheritColor", "ecModel", "globalTextStyle", "option", "richItemNames", "getRichItemNames", "rich<PERSON><PERSON>ult", "name_1", "hasOwnProperty", "richTextStyle", "setTokenTextStyle", "rich", "overflow", "margin", "richItemNameMap", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "parentModel", "TEXT_PROPS_WITH_GLOBAL", "TEXT_PROPS_SELF", "TEXT_PROPS_BOX", "isBlock", "inRich", "fillColor", "strokeColor", "opacity", "process", "env", "NODE_ENV", "color", "textBorderColor", "fill", "stroke", "textBorder<PERSON>idth", "lineWidth", "textBorderType", "lineDash", "textBorderDashOffset", "lineDashOffset", "defaultOpacity", "key", "val", "verticalAlign", "baseline", "disableBox", "borderType", "borderDash", "backgroundColor", "borderColor", "getFont", "gTextStyleModel", "fontStyle", "fontWeight", "fontSize", "fontFamily", "join", "setLabelValueAnimation", "value", "getDefaultText", "obj", "prevValue", "normalLabelModel", "valueAnimation", "precision", "defaultInterpolatedText", "animateLabelValue", "textEl", "dataIndex", "data", "animatableModel", "labelInnerStore", "currValue", "targetValue", "during", "percent", "interpolated", "labelText"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/label/labelStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { isFunction, retrieve2, extend, keys, trim } from 'zrender/lib/core/util.js';\nimport { SPECIAL_STATES, DISPLAY_STATES } from '../util/states.js';\nimport { deprecateReplaceLog } from '../util/log.js';\nimport { makeInner, interpolateRawValues } from '../util/model.js';\nimport { initProps, updateProps } from '../util/graphic.js';\nvar EMPTY_OBJ = {};\nexport function setLabelText(label, labelTexts) {\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var text = labelTexts[stateName];\n    var state = label.ensureState(stateName);\n    state.style = state.style || {};\n    state.style.text = text;\n  }\n  var oldStates = label.currentStates.slice();\n  label.clearStates(true);\n  label.setStyle({\n    text: labelTexts.normal\n  });\n  label.useStates(oldStates, true);\n}\nfunction getLabelText(opt, stateModels, interpolatedValue) {\n  var labelFetcher = opt.labelFetcher;\n  var labelDataIndex = opt.labelDataIndex;\n  var labelDimIndex = opt.labelDimIndex;\n  var normalModel = stateModels.normal;\n  var baseText;\n  if (labelFetcher) {\n    baseText = labelFetcher.getFormattedLabel(labelDataIndex, 'normal', null, labelDimIndex, normalModel && normalModel.get('formatter'), interpolatedValue != null ? {\n      interpolatedValue: interpolatedValue\n    } : null);\n  }\n  if (baseText == null) {\n    baseText = isFunction(opt.defaultText) ? opt.defaultText(labelDataIndex, opt, interpolatedValue) : opt.defaultText;\n  }\n  var statesText = {\n    normal: baseText\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var stateModel = stateModels[stateName];\n    statesText[stateName] = retrieve2(labelFetcher ? labelFetcher.getFormattedLabel(labelDataIndex, stateName, null, labelDimIndex, stateModel && stateModel.get('formatter')) : null, baseText);\n  }\n  return statesText;\n}\nfunction setLabelStyle(targetEl, labelStatesModels, opt, stateSpecified\n// TODO specified position?\n) {\n  opt = opt || EMPTY_OBJ;\n  var isSetOnText = targetEl instanceof ZRText;\n  var needsCreateText = false;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateModel = labelStatesModels[DISPLAY_STATES[i]];\n    if (stateModel && stateModel.getShallow('show')) {\n      needsCreateText = true;\n      break;\n    }\n  }\n  var textContent = isSetOnText ? targetEl : targetEl.getTextContent();\n  if (needsCreateText) {\n    if (!isSetOnText) {\n      // Reuse the previous\n      if (!textContent) {\n        textContent = new ZRText();\n        targetEl.setTextContent(textContent);\n      }\n      // Use same state proxy\n      if (targetEl.stateProxy) {\n        textContent.stateProxy = targetEl.stateProxy;\n      }\n    }\n    var labelStatesTexts = getLabelText(opt, labelStatesModels);\n    var normalModel = labelStatesModels.normal;\n    var showNormal = !!normalModel.getShallow('show');\n    var normalStyle = createTextStyle(normalModel, stateSpecified && stateSpecified.normal, opt, false, !isSetOnText);\n    normalStyle.text = labelStatesTexts.normal;\n    if (!isSetOnText) {\n      // Always create new\n      targetEl.setTextConfig(createTextConfig(normalModel, opt, false));\n    }\n    for (var i = 0; i < SPECIAL_STATES.length; i++) {\n      var stateName = SPECIAL_STATES[i];\n      var stateModel = labelStatesModels[stateName];\n      if (stateModel) {\n        var stateObj = textContent.ensureState(stateName);\n        var stateShow = !!retrieve2(stateModel.getShallow('show'), showNormal);\n        if (stateShow !== showNormal) {\n          stateObj.ignore = !stateShow;\n        }\n        stateObj.style = createTextStyle(stateModel, stateSpecified && stateSpecified[stateName], opt, true, !isSetOnText);\n        stateObj.style.text = labelStatesTexts[stateName];\n        if (!isSetOnText) {\n          var targetElEmphasisState = targetEl.ensureState(stateName);\n          targetElEmphasisState.textConfig = createTextConfig(stateModel, opt, true);\n        }\n      }\n    }\n    // PENDING: if there is many requirements that emphasis position\n    // need to be different from normal position, we might consider\n    // auto silent is those cases.\n    textContent.silent = !!normalModel.getShallow('silent');\n    // Keep x and y\n    if (textContent.style.x != null) {\n      normalStyle.x = textContent.style.x;\n    }\n    if (textContent.style.y != null) {\n      normalStyle.y = textContent.style.y;\n    }\n    textContent.ignore = !showNormal;\n    // Always create new style.\n    textContent.useStyle(normalStyle);\n    textContent.dirty();\n    if (opt.enableTextSetter) {\n      labelInner(textContent).setLabelText = function (interpolatedValue) {\n        var labelStatesTexts = getLabelText(opt, labelStatesModels, interpolatedValue);\n        setLabelText(textContent, labelStatesTexts);\n      };\n    }\n  } else if (textContent) {\n    // Not display rich text.\n    textContent.ignore = true;\n  }\n  targetEl.dirty();\n}\nexport { setLabelStyle };\nexport function getLabelStatesModels(itemModel, labelName) {\n  labelName = labelName || 'label';\n  var statesModels = {\n    normal: itemModel.getModel(labelName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelName]);\n  }\n  return statesModels;\n}\n/**\r\n * Set basic textStyle properties.\r\n */\nexport function createTextStyle(textStyleModel, specifiedTextStyle,\n// Fixed style in the code. Can't be set by model.\nopt, isNotNormal, isAttached // If text is attached on an element. If so, auto color will handling in zrender.\n) {\n  var textStyle = {};\n  setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached);\n  specifiedTextStyle && extend(textStyle, specifiedTextStyle);\n  // textStyle.host && textStyle.host.dirty && textStyle.host.dirty(false);\n  return textStyle;\n}\nexport function createTextConfig(textStyleModel, opt, isNotNormal) {\n  opt = opt || {};\n  var textConfig = {};\n  var labelPosition;\n  var labelRotate = textStyleModel.getShallow('rotate');\n  var labelDistance = retrieve2(textStyleModel.getShallow('distance'), isNotNormal ? null : 5);\n  var labelOffset = textStyleModel.getShallow('offset');\n  labelPosition = textStyleModel.getShallow('position') || (isNotNormal ? null : 'inside');\n  // 'outside' is not a valid zr textPostion value, but used\n  // in bar series, and magric type should be considered.\n  labelPosition === 'outside' && (labelPosition = opt.defaultOutsidePosition || 'top');\n  if (labelPosition != null) {\n    textConfig.position = labelPosition;\n  }\n  if (labelOffset != null) {\n    textConfig.offset = labelOffset;\n  }\n  if (labelRotate != null) {\n    labelRotate *= Math.PI / 180;\n    textConfig.rotation = labelRotate;\n  }\n  if (labelDistance != null) {\n    textConfig.distance = labelDistance;\n  }\n  // fill and auto is determined by the color of path fill if it's not specified by developers.\n  textConfig.outsideFill = textStyleModel.get('color') === 'inherit' ? opt.inheritColor || null : 'auto';\n  return textConfig;\n}\n/**\r\n * The uniform entry of set text style, that is, retrieve style definitions\r\n * from `model` and set to `textStyle` object.\r\n *\r\n * Never in merge mode, but in overwrite mode, that is, all of the text style\r\n * properties will be set. (Consider the states of normal and emphasis and\r\n * default value can be adopted, merge would make the logic too complicated\r\n * to manage.)\r\n */\nfunction setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached) {\n  // Consider there will be abnormal when merge hover style to normal style if given default value.\n  opt = opt || EMPTY_OBJ;\n  var ecModel = textStyleModel.ecModel;\n  var globalTextStyle = ecModel && ecModel.option.textStyle;\n  // Consider case:\n  // {\n  //     data: [{\n  //         value: 12,\n  //         label: {\n  //             rich: {\n  //                 // no 'a' here but using parent 'a'.\n  //             }\n  //         }\n  //     }],\n  //     rich: {\n  //         a: { ... }\n  //     }\n  // }\n  var richItemNames = getRichItemNames(textStyleModel);\n  var richResult;\n  if (richItemNames) {\n    richResult = {};\n    for (var name_1 in richItemNames) {\n      if (richItemNames.hasOwnProperty(name_1)) {\n        // Cascade is supported in rich.\n        var richTextStyle = textStyleModel.getModel(['rich', name_1]);\n        // In rich, never `disableBox`.\n        // FIXME: consider `label: {formatter: '{a|xx}', color: 'blue', rich: {a: {}}}`,\n        // the default color `'blue'` will not be adopted if no color declared in `rich`.\n        // That might confuses users. So probably we should put `textStyleModel` as the\n        // root ancestor of the `richTextStyle`. But that would be a break change.\n        setTokenTextStyle(richResult[name_1] = {}, richTextStyle, globalTextStyle, opt, isNotNormal, isAttached, false, true);\n      }\n    }\n  }\n  if (richResult) {\n    textStyle.rich = richResult;\n  }\n  var overflow = textStyleModel.get('overflow');\n  if (overflow) {\n    textStyle.overflow = overflow;\n  }\n  var margin = textStyleModel.get('minMargin');\n  if (margin != null) {\n    textStyle.margin = margin;\n  }\n  setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, true, false);\n}\n// Consider case:\n// {\n//     data: [{\n//         value: 12,\n//         label: {\n//             rich: {\n//                 // no 'a' here but using parent 'a'.\n//             }\n//         }\n//     }],\n//     rich: {\n//         a: { ... }\n//     }\n// }\n// TODO TextStyleModel\nfunction getRichItemNames(textStyleModel) {\n  // Use object to remove duplicated names.\n  var richItemNameMap;\n  while (textStyleModel && textStyleModel !== textStyleModel.ecModel) {\n    var rich = (textStyleModel.option || EMPTY_OBJ).rich;\n    if (rich) {\n      richItemNameMap = richItemNameMap || {};\n      var richKeys = keys(rich);\n      for (var i = 0; i < richKeys.length; i++) {\n        var richKey = richKeys[i];\n        richItemNameMap[richKey] = 1;\n      }\n    }\n    textStyleModel = textStyleModel.parentModel;\n  }\n  return richItemNameMap;\n}\nvar TEXT_PROPS_WITH_GLOBAL = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'textShadowColor', 'textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'];\nvar TEXT_PROPS_SELF = ['align', 'lineHeight', 'width', 'height', 'tag', 'verticalAlign', 'ellipsis'];\nvar TEXT_PROPS_BOX = ['padding', 'borderWidth', 'borderRadius', 'borderDashOffset', 'backgroundColor', 'borderColor', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nfunction setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, isBlock, inRich) {\n  // In merge mode, default value should not be given.\n  globalTextStyle = !isNotNormal && globalTextStyle || EMPTY_OBJ;\n  var inheritColor = opt && opt.inheritColor;\n  var fillColor = textStyleModel.getShallow('color');\n  var strokeColor = textStyleModel.getShallow('textBorderColor');\n  var opacity = retrieve2(textStyleModel.getShallow('opacity'), globalTextStyle.opacity);\n  if (fillColor === 'inherit' || fillColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (fillColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      fillColor = inheritColor;\n    } else {\n      fillColor = null;\n    }\n  }\n  if (strokeColor === 'inherit' || strokeColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (strokeColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      strokeColor = inheritColor;\n    } else {\n      strokeColor = null;\n    }\n  }\n  if (!isAttached) {\n    // Only use default global textStyle.color if text is individual.\n    // Otherwise it will use the strategy of attached text color because text may be on a path.\n    fillColor = fillColor || globalTextStyle.color;\n    strokeColor = strokeColor || globalTextStyle.textBorderColor;\n  }\n  if (fillColor != null) {\n    textStyle.fill = fillColor;\n  }\n  if (strokeColor != null) {\n    textStyle.stroke = strokeColor;\n  }\n  var textBorderWidth = retrieve2(textStyleModel.getShallow('textBorderWidth'), globalTextStyle.textBorderWidth);\n  if (textBorderWidth != null) {\n    textStyle.lineWidth = textBorderWidth;\n  }\n  var textBorderType = retrieve2(textStyleModel.getShallow('textBorderType'), globalTextStyle.textBorderType);\n  if (textBorderType != null) {\n    textStyle.lineDash = textBorderType;\n  }\n  var textBorderDashOffset = retrieve2(textStyleModel.getShallow('textBorderDashOffset'), globalTextStyle.textBorderDashOffset);\n  if (textBorderDashOffset != null) {\n    textStyle.lineDashOffset = textBorderDashOffset;\n  }\n  if (!isNotNormal && opacity == null && !inRich) {\n    opacity = opt && opt.defaultOpacity;\n  }\n  if (opacity != null) {\n    textStyle.opacity = opacity;\n  }\n  // TODO\n  if (!isNotNormal && !isAttached) {\n    // Set default finally.\n    if (textStyle.fill == null && opt.inheritColor) {\n      textStyle.fill = opt.inheritColor;\n    }\n  }\n  // Do not use `getFont` here, because merge should be supported, where\n  // part of these properties may be changed in emphasis style, and the\n  // others should remain their original value got from normal style.\n  for (var i = 0; i < TEXT_PROPS_WITH_GLOBAL.length; i++) {\n    var key = TEXT_PROPS_WITH_GLOBAL[i];\n    var val = retrieve2(textStyleModel.getShallow(key), globalTextStyle[key]);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  for (var i = 0; i < TEXT_PROPS_SELF.length; i++) {\n    var key = TEXT_PROPS_SELF[i];\n    var val = textStyleModel.getShallow(key);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  if (textStyle.verticalAlign == null) {\n    var baseline = textStyleModel.getShallow('baseline');\n    if (baseline != null) {\n      textStyle.verticalAlign = baseline;\n    }\n  }\n  if (!isBlock || !opt.disableBox) {\n    for (var i = 0; i < TEXT_PROPS_BOX.length; i++) {\n      var key = TEXT_PROPS_BOX[i];\n      var val = textStyleModel.getShallow(key);\n      if (val != null) {\n        textStyle[key] = val;\n      }\n    }\n    var borderType = textStyleModel.getShallow('borderType');\n    if (borderType != null) {\n      textStyle.borderDash = borderType;\n    }\n    if ((textStyle.backgroundColor === 'auto' || textStyle.backgroundColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.backgroundColor === 'auto') {\n          deprecateReplaceLog('backgroundColor: \\'auto\\'', 'backgroundColor: \\'inherit\\'');\n        }\n      }\n      textStyle.backgroundColor = inheritColor;\n    }\n    if ((textStyle.borderColor === 'auto' || textStyle.borderColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.borderColor === 'auto') {\n          deprecateReplaceLog('borderColor: \\'auto\\'', 'borderColor: \\'inherit\\'');\n        }\n      }\n      textStyle.borderColor = inheritColor;\n    }\n  }\n}\nexport function getFont(opt, ecModel) {\n  var gTextStyleModel = ecModel && ecModel.getModel('textStyle');\n  return trim([\n  // FIXME in node-canvas fontWeight is before fontStyle\n  opt.fontStyle || gTextStyleModel && gTextStyleModel.getShallow('fontStyle') || '', opt.fontWeight || gTextStyleModel && gTextStyleModel.getShallow('fontWeight') || '', (opt.fontSize || gTextStyleModel && gTextStyleModel.getShallow('fontSize') || 12) + 'px', opt.fontFamily || gTextStyleModel && gTextStyleModel.getShallow('fontFamily') || 'sans-serif'].join(' '));\n}\nexport var labelInner = makeInner();\nexport function setLabelValueAnimation(label, labelStatesModels, value, getDefaultText) {\n  if (!label) {\n    return;\n  }\n  var obj = labelInner(label);\n  obj.prevValue = obj.value;\n  obj.value = value;\n  var normalLabelModel = labelStatesModels.normal;\n  obj.valueAnimation = normalLabelModel.get('valueAnimation');\n  if (obj.valueAnimation) {\n    obj.precision = normalLabelModel.get('precision');\n    obj.defaultInterpolatedText = getDefaultText;\n    obj.statesModels = labelStatesModels;\n  }\n}\nexport function animateLabelValue(textEl, dataIndex, data, animatableModel, labelFetcher) {\n  var labelInnerStore = labelInner(textEl);\n  if (!labelInnerStore.valueAnimation || labelInnerStore.prevValue === labelInnerStore.value) {\n    // Value not changed, no new label animation\n    return;\n  }\n  var defaultInterpolatedText = labelInnerStore.defaultInterpolatedText;\n  // Consider the case that being animating, do not use the `obj.value`,\n  // Otherwise it will jump to the `obj.value` when this new animation started.\n  var currValue = retrieve2(labelInnerStore.interpolatedValue, labelInnerStore.prevValue);\n  var targetValue = labelInnerStore.value;\n  function during(percent) {\n    var interpolated = interpolateRawValues(data, labelInnerStore.precision, currValue, targetValue, percent);\n    labelInnerStore.interpolatedValue = percent === 1 ? null : interpolated;\n    var labelText = getLabelText({\n      labelDataIndex: dataIndex,\n      labelFetcher: labelFetcher,\n      defaultText: defaultInterpolatedText ? defaultInterpolatedText(interpolated) : interpolated + ''\n    }, labelInnerStore.statesModels, interpolated);\n    setLabelText(textEl, labelText);\n  }\n  textEl.percent = 0;\n  (labelInnerStore.prevValue == null ? initProps : updateProps)(textEl, {\n    // percent is used to prevent animation from being aborted #15916\n    percent: 1\n  }, animatableModel, dataIndex, null, during);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,6BAA6B;AAChD,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,0BAA0B;AACpF,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,SAAS,EAAEC,oBAAoB,QAAQ,kBAAkB;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,oBAAoB;AAC3D,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIE,SAAS,GAAGb,cAAc,CAACW,CAAC,CAAC;IACjC,IAAIG,IAAI,GAAGJ,UAAU,CAACG,SAAS,CAAC;IAChC,IAAIE,KAAK,GAAGN,KAAK,CAACO,WAAW,CAACH,SAAS,CAAC;IACxCE,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,CAAC,CAAC;IAC/BF,KAAK,CAACE,KAAK,CAACH,IAAI,GAAGA,IAAI;EACzB;EACA,IAAII,SAAS,GAAGT,KAAK,CAACU,aAAa,CAACC,KAAK,CAAC,CAAC;EAC3CX,KAAK,CAACY,WAAW,CAAC,IAAI,CAAC;EACvBZ,KAAK,CAACa,QAAQ,CAAC;IACbR,IAAI,EAAEJ,UAAU,CAACa;EACnB,CAAC,CAAC;EACFd,KAAK,CAACe,SAAS,CAACN,SAAS,EAAE,IAAI,CAAC;AAClC;AACA,SAASO,YAAYA,CAACC,GAAG,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;EACzD,IAAIC,YAAY,GAAGH,GAAG,CAACG,YAAY;EACnC,IAAIC,cAAc,GAAGJ,GAAG,CAACI,cAAc;EACvC,IAAIC,aAAa,GAAGL,GAAG,CAACK,aAAa;EACrC,IAAIC,WAAW,GAAGL,WAAW,CAACJ,MAAM;EACpC,IAAIU,QAAQ;EACZ,IAAIJ,YAAY,EAAE;IAChBI,QAAQ,GAAGJ,YAAY,CAACK,iBAAiB,CAACJ,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAEC,aAAa,EAAEC,WAAW,IAAIA,WAAW,CAACG,GAAG,CAAC,WAAW,CAAC,EAAEP,iBAAiB,IAAI,IAAI,GAAG;MAChKA,iBAAiB,EAAEA;IACrB,CAAC,GAAG,IAAI,CAAC;EACX;EACA,IAAIK,QAAQ,IAAI,IAAI,EAAE;IACpBA,QAAQ,GAAGtC,UAAU,CAAC+B,GAAG,CAACU,WAAW,CAAC,GAAGV,GAAG,CAACU,WAAW,CAACN,cAAc,EAAEJ,GAAG,EAAEE,iBAAiB,CAAC,GAAGF,GAAG,CAACU,WAAW;EACpH;EACA,IAAIC,UAAU,GAAG;IACfd,MAAM,EAAEU;EACV,CAAC;EACD,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIE,SAAS,GAAGb,cAAc,CAACW,CAAC,CAAC;IACjC,IAAI2B,UAAU,GAAGX,WAAW,CAACd,SAAS,CAAC;IACvCwB,UAAU,CAACxB,SAAS,CAAC,GAAGjB,SAAS,CAACiC,YAAY,GAAGA,YAAY,CAACK,iBAAiB,CAACJ,cAAc,EAAEjB,SAAS,EAAE,IAAI,EAAEkB,aAAa,EAAEO,UAAU,IAAIA,UAAU,CAACH,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,EAAEF,QAAQ,CAAC;EAC9L;EACA,OAAOI,UAAU;AACnB;AACA,SAASE,aAAaA,CAACC,QAAQ,EAAEC,iBAAiB,EAAEf,GAAG,EAAEgB;AACzD;AAAA,EACE;EACAhB,GAAG,GAAGA,GAAG,IAAInB,SAAS;EACtB,IAAIoC,WAAW,GAAGH,QAAQ,YAAY9C,MAAM;EAC5C,IAAIkD,eAAe,GAAG,KAAK;EAC3B,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,cAAc,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAI2B,UAAU,GAAGG,iBAAiB,CAACxC,cAAc,CAACU,CAAC,CAAC,CAAC;IACrD,IAAI2B,UAAU,IAAIA,UAAU,CAACO,UAAU,CAAC,MAAM,CAAC,EAAE;MAC/CD,eAAe,GAAG,IAAI;MACtB;IACF;EACF;EACA,IAAIE,WAAW,GAAGH,WAAW,GAAGH,QAAQ,GAAGA,QAAQ,CAACO,cAAc,CAAC,CAAC;EACpE,IAAIH,eAAe,EAAE;IACnB,IAAI,CAACD,WAAW,EAAE;MAChB;MACA,IAAI,CAACG,WAAW,EAAE;QAChBA,WAAW,GAAG,IAAIpD,MAAM,CAAC,CAAC;QAC1B8C,QAAQ,CAACQ,cAAc,CAACF,WAAW,CAAC;MACtC;MACA;MACA,IAAIN,QAAQ,CAACS,UAAU,EAAE;QACvBH,WAAW,CAACG,UAAU,GAAGT,QAAQ,CAACS,UAAU;MAC9C;IACF;IACA,IAAIC,gBAAgB,GAAGzB,YAAY,CAACC,GAAG,EAAEe,iBAAiB,CAAC;IAC3D,IAAIT,WAAW,GAAGS,iBAAiB,CAAClB,MAAM;IAC1C,IAAI4B,UAAU,GAAG,CAAC,CAACnB,WAAW,CAACa,UAAU,CAAC,MAAM,CAAC;IACjD,IAAIO,WAAW,GAAGC,eAAe,CAACrB,WAAW,EAAEU,cAAc,IAAIA,cAAc,CAACnB,MAAM,EAAEG,GAAG,EAAE,KAAK,EAAE,CAACiB,WAAW,CAAC;IACjHS,WAAW,CAACtC,IAAI,GAAGoC,gBAAgB,CAAC3B,MAAM;IAC1C,IAAI,CAACoB,WAAW,EAAE;MAChB;MACAH,QAAQ,CAACc,aAAa,CAACC,gBAAgB,CAACvB,WAAW,EAAEN,GAAG,EAAE,KAAK,CAAC,CAAC;IACnE;IACA,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAIE,SAAS,GAAGb,cAAc,CAACW,CAAC,CAAC;MACjC,IAAI2B,UAAU,GAAGG,iBAAiB,CAAC5B,SAAS,CAAC;MAC7C,IAAIyB,UAAU,EAAE;QACd,IAAIkB,QAAQ,GAAGV,WAAW,CAAC9B,WAAW,CAACH,SAAS,CAAC;QACjD,IAAI4C,SAAS,GAAG,CAAC,CAAC7D,SAAS,CAAC0C,UAAU,CAACO,UAAU,CAAC,MAAM,CAAC,EAAEM,UAAU,CAAC;QACtE,IAAIM,SAAS,KAAKN,UAAU,EAAE;UAC5BK,QAAQ,CAACE,MAAM,GAAG,CAACD,SAAS;QAC9B;QACAD,QAAQ,CAACvC,KAAK,GAAGoC,eAAe,CAACf,UAAU,EAAEI,cAAc,IAAIA,cAAc,CAAC7B,SAAS,CAAC,EAAEa,GAAG,EAAE,IAAI,EAAE,CAACiB,WAAW,CAAC;QAClHa,QAAQ,CAACvC,KAAK,CAACH,IAAI,GAAGoC,gBAAgB,CAACrC,SAAS,CAAC;QACjD,IAAI,CAAC8B,WAAW,EAAE;UAChB,IAAIgB,qBAAqB,GAAGnB,QAAQ,CAACxB,WAAW,CAACH,SAAS,CAAC;UAC3D8C,qBAAqB,CAACC,UAAU,GAAGL,gBAAgB,CAACjB,UAAU,EAAEZ,GAAG,EAAE,IAAI,CAAC;QAC5E;MACF;IACF;IACA;IACA;IACA;IACAoB,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC7B,WAAW,CAACa,UAAU,CAAC,QAAQ,CAAC;IACvD;IACA,IAAIC,WAAW,CAAC7B,KAAK,CAAC6C,CAAC,IAAI,IAAI,EAAE;MAC/BV,WAAW,CAACU,CAAC,GAAGhB,WAAW,CAAC7B,KAAK,CAAC6C,CAAC;IACrC;IACA,IAAIhB,WAAW,CAAC7B,KAAK,CAAC8C,CAAC,IAAI,IAAI,EAAE;MAC/BX,WAAW,CAACW,CAAC,GAAGjB,WAAW,CAAC7B,KAAK,CAAC8C,CAAC;IACrC;IACAjB,WAAW,CAACY,MAAM,GAAG,CAACP,UAAU;IAChC;IACAL,WAAW,CAACkB,QAAQ,CAACZ,WAAW,CAAC;IACjCN,WAAW,CAACmB,KAAK,CAAC,CAAC;IACnB,IAAIvC,GAAG,CAACwC,gBAAgB,EAAE;MACxBC,UAAU,CAACrB,WAAW,CAAC,CAACtC,YAAY,GAAG,UAAUoB,iBAAiB,EAAE;QAClE,IAAIsB,gBAAgB,GAAGzB,YAAY,CAACC,GAAG,EAAEe,iBAAiB,EAAEb,iBAAiB,CAAC;QAC9EpB,YAAY,CAACsC,WAAW,EAAEI,gBAAgB,CAAC;MAC7C,CAAC;IACH;EACF,CAAC,MAAM,IAAIJ,WAAW,EAAE;IACtB;IACAA,WAAW,CAACY,MAAM,GAAG,IAAI;EAC3B;EACAlB,QAAQ,CAACyB,KAAK,CAAC,CAAC;AAClB;AACA,SAAS1B,aAAa;AACtB,OAAO,SAAS6B,oBAAoBA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACzDA,SAAS,GAAGA,SAAS,IAAI,OAAO;EAChC,IAAIC,YAAY,GAAG;IACjBhD,MAAM,EAAE8C,SAAS,CAACG,QAAQ,CAACF,SAAS;EACtC,CAAC;EACD,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIE,SAAS,GAAGb,cAAc,CAACW,CAAC,CAAC;IACjC4D,YAAY,CAAC1D,SAAS,CAAC,GAAGwD,SAAS,CAACG,QAAQ,CAAC,CAAC3D,SAAS,EAAEyD,SAAS,CAAC,CAAC;EACtE;EACA,OAAOC,YAAY;AACrB;AACA;AACA;AACA;AACA,OAAO,SAASlB,eAAeA,CAACoB,cAAc,EAAEC,kBAAkB;AAClE;AACAhD,GAAG,EAAEiD,WAAW,EAAEC,UAAU,CAAC;AAAA,EAC3B;EACA,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClBC,kBAAkB,CAACD,SAAS,EAAEJ,cAAc,EAAE/C,GAAG,EAAEiD,WAAW,EAAEC,UAAU,CAAC;EAC3EF,kBAAkB,IAAI7E,MAAM,CAACgF,SAAS,EAAEH,kBAAkB,CAAC;EAC3D;EACA,OAAOG,SAAS;AAClB;AACA,OAAO,SAAStB,gBAAgBA,CAACkB,cAAc,EAAE/C,GAAG,EAAEiD,WAAW,EAAE;EACjEjD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIkC,UAAU,GAAG,CAAC,CAAC;EACnB,IAAImB,aAAa;EACjB,IAAIC,WAAW,GAAGP,cAAc,CAAC5B,UAAU,CAAC,QAAQ,CAAC;EACrD,IAAIoC,aAAa,GAAGrF,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAAC,UAAU,CAAC,EAAE8B,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC;EAC5F,IAAIO,WAAW,GAAGT,cAAc,CAAC5B,UAAU,CAAC,QAAQ,CAAC;EACrDkC,aAAa,GAAGN,cAAc,CAAC5B,UAAU,CAAC,UAAU,CAAC,KAAK8B,WAAW,GAAG,IAAI,GAAG,QAAQ,CAAC;EACxF;EACA;EACAI,aAAa,KAAK,SAAS,KAAKA,aAAa,GAAGrD,GAAG,CAACyD,sBAAsB,IAAI,KAAK,CAAC;EACpF,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzBnB,UAAU,CAACwB,QAAQ,GAAGL,aAAa;EACrC;EACA,IAAIG,WAAW,IAAI,IAAI,EAAE;IACvBtB,UAAU,CAACyB,MAAM,GAAGH,WAAW;EACjC;EACA,IAAIF,WAAW,IAAI,IAAI,EAAE;IACvBA,WAAW,IAAIM,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B3B,UAAU,CAAC4B,QAAQ,GAAGR,WAAW;EACnC;EACA,IAAIC,aAAa,IAAI,IAAI,EAAE;IACzBrB,UAAU,CAAC6B,QAAQ,GAAGR,aAAa;EACrC;EACA;EACArB,UAAU,CAAC8B,WAAW,GAAGjB,cAAc,CAACtC,GAAG,CAAC,OAAO,CAAC,KAAK,SAAS,GAAGT,GAAG,CAACiE,YAAY,IAAI,IAAI,GAAG,MAAM;EACtG,OAAO/B,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,kBAAkBA,CAACD,SAAS,EAAEJ,cAAc,EAAE/C,GAAG,EAAEiD,WAAW,EAAEC,UAAU,EAAE;EACnF;EACAlD,GAAG,GAAGA,GAAG,IAAInB,SAAS;EACtB,IAAIqF,OAAO,GAAGnB,cAAc,CAACmB,OAAO;EACpC,IAAIC,eAAe,GAAGD,OAAO,IAAIA,OAAO,CAACE,MAAM,CAACjB,SAAS;EACzD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIkB,aAAa,GAAGC,gBAAgB,CAACvB,cAAc,CAAC;EACpD,IAAIwB,UAAU;EACd,IAAIF,aAAa,EAAE;IACjBE,UAAU,GAAG,CAAC,CAAC;IACf,KAAK,IAAIC,MAAM,IAAIH,aAAa,EAAE;MAChC,IAAIA,aAAa,CAACI,cAAc,CAACD,MAAM,CAAC,EAAE;QACxC;QACA,IAAIE,aAAa,GAAG3B,cAAc,CAACD,QAAQ,CAAC,CAAC,MAAM,EAAE0B,MAAM,CAAC,CAAC;QAC7D;QACA;QACA;QACA;QACA;QACAG,iBAAiB,CAACJ,UAAU,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAEE,aAAa,EAAEP,eAAe,EAAEnE,GAAG,EAAEiD,WAAW,EAAEC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;MACvH;IACF;EACF;EACA,IAAIqB,UAAU,EAAE;IACdpB,SAAS,CAACyB,IAAI,GAAGL,UAAU;EAC7B;EACA,IAAIM,QAAQ,GAAG9B,cAAc,CAACtC,GAAG,CAAC,UAAU,CAAC;EAC7C,IAAIoE,QAAQ,EAAE;IACZ1B,SAAS,CAAC0B,QAAQ,GAAGA,QAAQ;EAC/B;EACA,IAAIC,MAAM,GAAG/B,cAAc,CAACtC,GAAG,CAAC,WAAW,CAAC;EAC5C,IAAIqE,MAAM,IAAI,IAAI,EAAE;IAClB3B,SAAS,CAAC2B,MAAM,GAAGA,MAAM;EAC3B;EACAH,iBAAiB,CAACxB,SAAS,EAAEJ,cAAc,EAAEoB,eAAe,EAAEnE,GAAG,EAAEiD,WAAW,EAAEC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,gBAAgBA,CAACvB,cAAc,EAAE;EACxC;EACA,IAAIgC,eAAe;EACnB,OAAOhC,cAAc,IAAIA,cAAc,KAAKA,cAAc,CAACmB,OAAO,EAAE;IAClE,IAAIU,IAAI,GAAG,CAAC7B,cAAc,CAACqB,MAAM,IAAIvF,SAAS,EAAE+F,IAAI;IACpD,IAAIA,IAAI,EAAE;MACRG,eAAe,GAAGA,eAAe,IAAI,CAAC,CAAC;MACvC,IAAIC,QAAQ,GAAG5G,IAAI,CAACwG,IAAI,CAAC;MACzB,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+F,QAAQ,CAAC9F,MAAM,EAAED,CAAC,EAAE,EAAE;QACxC,IAAIgG,OAAO,GAAGD,QAAQ,CAAC/F,CAAC,CAAC;QACzB8F,eAAe,CAACE,OAAO,CAAC,GAAG,CAAC;MAC9B;IACF;IACAlC,cAAc,GAAGA,cAAc,CAACmC,WAAW;EAC7C;EACA,OAAOH,eAAe;AACxB;AACA,IAAII,sBAAsB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;AACjK,IAAIC,eAAe,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,CAAC;AACpG,IAAIC,cAAc,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC;AACpL,SAASV,iBAAiBA,CAACxB,SAAS,EAAEJ,cAAc,EAAEoB,eAAe,EAAEnE,GAAG,EAAEiD,WAAW,EAAEC,UAAU,EAAEoC,OAAO,EAAEC,MAAM,EAAE;EACpH;EACApB,eAAe,GAAG,CAAClB,WAAW,IAAIkB,eAAe,IAAItF,SAAS;EAC9D,IAAIoF,YAAY,GAAGjE,GAAG,IAAIA,GAAG,CAACiE,YAAY;EAC1C,IAAIuB,SAAS,GAAGzC,cAAc,CAAC5B,UAAU,CAAC,OAAO,CAAC;EAClD,IAAIsE,WAAW,GAAG1C,cAAc,CAAC5B,UAAU,CAAC,iBAAiB,CAAC;EAC9D,IAAIuE,OAAO,GAAGxH,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAAC,SAAS,CAAC,EAAEgD,eAAe,CAACuB,OAAO,CAAC;EACtF,IAAIF,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACnD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIL,SAAS,KAAK,MAAM,EAAE;QACxBhH,mBAAmB,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;MAC9D;IACF;IACA,IAAIyF,YAAY,EAAE;MAChBuB,SAAS,GAAGvB,YAAY;IAC1B,CAAC,MAAM;MACLuB,SAAS,GAAG,IAAI;IAClB;EACF;EACA,IAAIC,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,MAAM,EAAE;IACvD,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIJ,WAAW,KAAK,MAAM,EAAE;QAC1BjH,mBAAmB,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;MAC9D;IACF;IACA,IAAIyF,YAAY,EAAE;MAChBwB,WAAW,GAAGxB,YAAY;IAC5B,CAAC,MAAM;MACLwB,WAAW,GAAG,IAAI;IACpB;EACF;EACA,IAAI,CAACvC,UAAU,EAAE;IACf;IACA;IACAsC,SAAS,GAAGA,SAAS,IAAIrB,eAAe,CAAC2B,KAAK;IAC9CL,WAAW,GAAGA,WAAW,IAAItB,eAAe,CAAC4B,eAAe;EAC9D;EACA,IAAIP,SAAS,IAAI,IAAI,EAAE;IACrBrC,SAAS,CAAC6C,IAAI,GAAGR,SAAS;EAC5B;EACA,IAAIC,WAAW,IAAI,IAAI,EAAE;IACvBtC,SAAS,CAAC8C,MAAM,GAAGR,WAAW;EAChC;EACA,IAAIS,eAAe,GAAGhI,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAAC,iBAAiB,CAAC,EAAEgD,eAAe,CAAC+B,eAAe,CAAC;EAC9G,IAAIA,eAAe,IAAI,IAAI,EAAE;IAC3B/C,SAAS,CAACgD,SAAS,GAAGD,eAAe;EACvC;EACA,IAAIE,cAAc,GAAGlI,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAAC,gBAAgB,CAAC,EAAEgD,eAAe,CAACiC,cAAc,CAAC;EAC3G,IAAIA,cAAc,IAAI,IAAI,EAAE;IAC1BjD,SAAS,CAACkD,QAAQ,GAAGD,cAAc;EACrC;EACA,IAAIE,oBAAoB,GAAGpI,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAAC,sBAAsB,CAAC,EAAEgD,eAAe,CAACmC,oBAAoB,CAAC;EAC7H,IAAIA,oBAAoB,IAAI,IAAI,EAAE;IAChCnD,SAAS,CAACoD,cAAc,GAAGD,oBAAoB;EACjD;EACA,IAAI,CAACrD,WAAW,IAAIyC,OAAO,IAAI,IAAI,IAAI,CAACH,MAAM,EAAE;IAC9CG,OAAO,GAAG1F,GAAG,IAAIA,GAAG,CAACwG,cAAc;EACrC;EACA,IAAId,OAAO,IAAI,IAAI,EAAE;IACnBvC,SAAS,CAACuC,OAAO,GAAGA,OAAO;EAC7B;EACA;EACA,IAAI,CAACzC,WAAW,IAAI,CAACC,UAAU,EAAE;IAC/B;IACA,IAAIC,SAAS,CAAC6C,IAAI,IAAI,IAAI,IAAIhG,GAAG,CAACiE,YAAY,EAAE;MAC9Cd,SAAS,CAAC6C,IAAI,GAAGhG,GAAG,CAACiE,YAAY;IACnC;EACF;EACA;EACA;EACA;EACA,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkG,sBAAsB,CAACjG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtD,IAAIwH,GAAG,GAAGtB,sBAAsB,CAAClG,CAAC,CAAC;IACnC,IAAIyH,GAAG,GAAGxI,SAAS,CAAC6E,cAAc,CAAC5B,UAAU,CAACsF,GAAG,CAAC,EAAEtC,eAAe,CAACsC,GAAG,CAAC,CAAC;IACzE,IAAIC,GAAG,IAAI,IAAI,EAAE;MACfvD,SAAS,CAACsD,GAAG,CAAC,GAAGC,GAAG;IACtB;EACF;EACA,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmG,eAAe,CAAClG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/C,IAAIwH,GAAG,GAAGrB,eAAe,CAACnG,CAAC,CAAC;IAC5B,IAAIyH,GAAG,GAAG3D,cAAc,CAAC5B,UAAU,CAACsF,GAAG,CAAC;IACxC,IAAIC,GAAG,IAAI,IAAI,EAAE;MACfvD,SAAS,CAACsD,GAAG,CAAC,GAAGC,GAAG;IACtB;EACF;EACA,IAAIvD,SAAS,CAACwD,aAAa,IAAI,IAAI,EAAE;IACnC,IAAIC,QAAQ,GAAG7D,cAAc,CAAC5B,UAAU,CAAC,UAAU,CAAC;IACpD,IAAIyF,QAAQ,IAAI,IAAI,EAAE;MACpBzD,SAAS,CAACwD,aAAa,GAAGC,QAAQ;IACpC;EACF;EACA,IAAI,CAACtB,OAAO,IAAI,CAACtF,GAAG,CAAC6G,UAAU,EAAE;IAC/B,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoG,cAAc,CAACnG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAIwH,GAAG,GAAGpB,cAAc,CAACpG,CAAC,CAAC;MAC3B,IAAIyH,GAAG,GAAG3D,cAAc,CAAC5B,UAAU,CAACsF,GAAG,CAAC;MACxC,IAAIC,GAAG,IAAI,IAAI,EAAE;QACfvD,SAAS,CAACsD,GAAG,CAAC,GAAGC,GAAG;MACtB;IACF;IACA,IAAII,UAAU,GAAG/D,cAAc,CAAC5B,UAAU,CAAC,YAAY,CAAC;IACxD,IAAI2F,UAAU,IAAI,IAAI,EAAE;MACtB3D,SAAS,CAAC4D,UAAU,GAAGD,UAAU;IACnC;IACA,IAAI,CAAC3D,SAAS,CAAC6D,eAAe,KAAK,MAAM,IAAI7D,SAAS,CAAC6D,eAAe,KAAK,SAAS,KAAK/C,YAAY,EAAE;MACrG,IAAI0B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI1C,SAAS,CAAC6D,eAAe,KAAK,MAAM,EAAE;UACxCxI,mBAAmB,CAAC,2BAA2B,EAAE,8BAA8B,CAAC;QAClF;MACF;MACA2E,SAAS,CAAC6D,eAAe,GAAG/C,YAAY;IAC1C;IACA,IAAI,CAACd,SAAS,CAAC8D,WAAW,KAAK,MAAM,IAAI9D,SAAS,CAAC8D,WAAW,KAAK,SAAS,KAAKhD,YAAY,EAAE;MAC7F,IAAI0B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI1C,SAAS,CAAC8D,WAAW,KAAK,MAAM,EAAE;UACpCzI,mBAAmB,CAAC,uBAAuB,EAAE,0BAA0B,CAAC;QAC1E;MACF;MACA2E,SAAS,CAAC8D,WAAW,GAAGhD,YAAY;IACtC;EACF;AACF;AACA,OAAO,SAASiD,OAAOA,CAAClH,GAAG,EAAEkE,OAAO,EAAE;EACpC,IAAIiD,eAAe,GAAGjD,OAAO,IAAIA,OAAO,CAACpB,QAAQ,CAAC,WAAW,CAAC;EAC9D,OAAOzE,IAAI,CAAC;EACZ;EACA2B,GAAG,CAACoH,SAAS,IAAID,eAAe,IAAIA,eAAe,CAAChG,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,EAAEnB,GAAG,CAACqH,UAAU,IAAIF,eAAe,IAAIA,eAAe,CAAChG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAACnB,GAAG,CAACsH,QAAQ,IAAIH,eAAe,IAAIA,eAAe,CAAChG,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,IAAI,EAAEnB,GAAG,CAACuH,UAAU,IAAIJ,eAAe,IAAIA,eAAe,CAAChG,UAAU,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAACqG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7W;AACA,OAAO,IAAI/E,UAAU,GAAGhE,SAAS,CAAC,CAAC;AACnC,OAAO,SAASgJ,sBAAsBA,CAAC1I,KAAK,EAAEgC,iBAAiB,EAAE2G,KAAK,EAAEC,cAAc,EAAE;EACtF,IAAI,CAAC5I,KAAK,EAAE;IACV;EACF;EACA,IAAI6I,GAAG,GAAGnF,UAAU,CAAC1D,KAAK,CAAC;EAC3B6I,GAAG,CAACC,SAAS,GAAGD,GAAG,CAACF,KAAK;EACzBE,GAAG,CAACF,KAAK,GAAGA,KAAK;EACjB,IAAII,gBAAgB,GAAG/G,iBAAiB,CAAClB,MAAM;EAC/C+H,GAAG,CAACG,cAAc,GAAGD,gBAAgB,CAACrH,GAAG,CAAC,gBAAgB,CAAC;EAC3D,IAAImH,GAAG,CAACG,cAAc,EAAE;IACtBH,GAAG,CAACI,SAAS,GAAGF,gBAAgB,CAACrH,GAAG,CAAC,WAAW,CAAC;IACjDmH,GAAG,CAACK,uBAAuB,GAAGN,cAAc;IAC5CC,GAAG,CAAC/E,YAAY,GAAG9B,iBAAiB;EACtC;AACF;AACA,OAAO,SAASmH,iBAAiBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,eAAe,EAAEnI,YAAY,EAAE;EACxF,IAAIoI,eAAe,GAAG9F,UAAU,CAAC0F,MAAM,CAAC;EACxC,IAAI,CAACI,eAAe,CAACR,cAAc,IAAIQ,eAAe,CAACV,SAAS,KAAKU,eAAe,CAACb,KAAK,EAAE;IAC1F;IACA;EACF;EACA,IAAIO,uBAAuB,GAAGM,eAAe,CAACN,uBAAuB;EACrE;EACA;EACA,IAAIO,SAAS,GAAGtK,SAAS,CAACqK,eAAe,CAACrI,iBAAiB,EAAEqI,eAAe,CAACV,SAAS,CAAC;EACvF,IAAIY,WAAW,GAAGF,eAAe,CAACb,KAAK;EACvC,SAASgB,MAAMA,CAACC,OAAO,EAAE;IACvB,IAAIC,YAAY,GAAGlK,oBAAoB,CAAC2J,IAAI,EAAEE,eAAe,CAACP,SAAS,EAAEQ,SAAS,EAAEC,WAAW,EAAEE,OAAO,CAAC;IACzGJ,eAAe,CAACrI,iBAAiB,GAAGyI,OAAO,KAAK,CAAC,GAAG,IAAI,GAAGC,YAAY;IACvE,IAAIC,SAAS,GAAG9I,YAAY,CAAC;MAC3BK,cAAc,EAAEgI,SAAS;MACzBjI,YAAY,EAAEA,YAAY;MAC1BO,WAAW,EAAEuH,uBAAuB,GAAGA,uBAAuB,CAACW,YAAY,CAAC,GAAGA,YAAY,GAAG;IAChG,CAAC,EAAEL,eAAe,CAAC1F,YAAY,EAAE+F,YAAY,CAAC;IAC9C9J,YAAY,CAACqJ,MAAM,EAAEU,SAAS,CAAC;EACjC;EACAV,MAAM,CAACQ,OAAO,GAAG,CAAC;EAClB,CAACJ,eAAe,CAACV,SAAS,IAAI,IAAI,GAAGlJ,SAAS,GAAGC,WAAW,EAAEuJ,MAAM,EAAE;IACpE;IACAQ,OAAO,EAAE;EACX,CAAC,EAAEL,eAAe,EAAEF,SAAS,EAAE,IAAI,EAAEM,MAAM,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}