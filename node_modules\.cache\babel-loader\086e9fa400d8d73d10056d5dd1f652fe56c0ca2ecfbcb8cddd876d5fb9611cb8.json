{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as util from './core/util.js';\nimport timsort from './core/timsort.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nvar invalidZErrorLogged = false;\nfunction logInvalidZError() {\n  if (invalidZ<PERSON>rrorLogged) {\n    return;\n  }\n  invalidZErrorLogged = true;\n  console.warn('z / z2 / zlevel of displayable is invalid, which may cause unexpected errors');\n}\nfunction shapeCompareFunc(a, b) {\n  if (a.zlevel === b.zlevel) {\n    if (a.z === b.z) {\n      return a.z2 - b.z2;\n    }\n    return a.z - b.z;\n  }\n  return a.zlevel - b.zlevel;\n}\nvar Storage = function () {\n  function Storage() {\n    this._roots = [];\n    this._displayList = [];\n    this._displayListLen = 0;\n    this.displayableSortFunc = shapeCompareFunc;\n  }\n  Storage.prototype.traverse = function (cb, context) {\n    for (var i = 0; i < this._roots.length; i++) {\n      this._roots[i].traverse(cb, context);\n    }\n  };\n  Storage.prototype.getDisplayList = function (update, includeIgnore) {\n    includeIgnore = includeIgnore || false;\n    var displayList = this._displayList;\n    if (update || !displayList.length) {\n      this.updateDisplayList(includeIgnore);\n    }\n    return displayList;\n  };\n  Storage.prototype.updateDisplayList = function (includeIgnore) {\n    this._displayListLen = 0;\n    var roots = this._roots;\n    var displayList = this._displayList;\n    for (var i = 0, len = roots.length; i < len; i++) {\n      this._updateAndAddDisplayable(roots[i], null, includeIgnore);\n    }\n    displayList.length = this._displayListLen;\n    timsort(displayList, shapeCompareFunc);\n  };\n  Storage.prototype._updateAndAddDisplayable = function (el, clipPaths, includeIgnore) {\n    if (el.ignore && !includeIgnore) {\n      return;\n    }\n    el.beforeUpdate();\n    el.update();\n    el.afterUpdate();\n    var userSetClipPath = el.getClipPath();\n    if (el.ignoreClip) {\n      clipPaths = null;\n    } else if (userSetClipPath) {\n      if (clipPaths) {\n        clipPaths = clipPaths.slice();\n      } else {\n        clipPaths = [];\n      }\n      var currentClipPath = userSetClipPath;\n      var parentClipPath = el;\n      while (currentClipPath) {\n        currentClipPath.parent = parentClipPath;\n        currentClipPath.updateTransform();\n        clipPaths.push(currentClipPath);\n        parentClipPath = currentClipPath;\n        currentClipPath = currentClipPath.getClipPath();\n      }\n    }\n    if (el.childrenRef) {\n      var children = el.childrenRef();\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        if (el.__dirty) {\n          child.__dirty |= REDRAW_BIT;\n        }\n        this._updateAndAddDisplayable(child, clipPaths, includeIgnore);\n      }\n      el.__dirty = 0;\n    } else {\n      var disp = el;\n      if (clipPaths && clipPaths.length) {\n        disp.__clipPaths = clipPaths;\n      } else if (disp.__clipPaths && disp.__clipPaths.length > 0) {\n        disp.__clipPaths = [];\n      }\n      if (isNaN(disp.z)) {\n        logInvalidZError();\n        disp.z = 0;\n      }\n      if (isNaN(disp.z2)) {\n        logInvalidZError();\n        disp.z2 = 0;\n      }\n      if (isNaN(disp.zlevel)) {\n        logInvalidZError();\n        disp.zlevel = 0;\n      }\n      this._displayList[this._displayListLen++] = disp;\n    }\n    var decalEl = el.getDecalElement && el.getDecalElement();\n    if (decalEl) {\n      this._updateAndAddDisplayable(decalEl, clipPaths, includeIgnore);\n    }\n    var textGuide = el.getTextGuideLine();\n    if (textGuide) {\n      this._updateAndAddDisplayable(textGuide, clipPaths, includeIgnore);\n    }\n    var textEl = el.getTextContent();\n    if (textEl) {\n      this._updateAndAddDisplayable(textEl, clipPaths, includeIgnore);\n    }\n  };\n  Storage.prototype.addRoot = function (el) {\n    if (el.__zr && el.__zr.storage === this) {\n      return;\n    }\n    this._roots.push(el);\n  };\n  Storage.prototype.delRoot = function (el) {\n    if (el instanceof Array) {\n      for (var i = 0, l = el.length; i < l; i++) {\n        this.delRoot(el[i]);\n      }\n      return;\n    }\n    var idx = util.indexOf(this._roots, el);\n    if (idx >= 0) {\n      this._roots.splice(idx, 1);\n    }\n  };\n  Storage.prototype.delAllRoots = function () {\n    this._roots = [];\n    this._displayList = [];\n    this._displayListLen = 0;\n    return;\n  };\n  Storage.prototype.getRoots = function () {\n    return this._roots;\n  };\n  Storage.prototype.dispose = function () {\n    this._displayList = null;\n    this._roots = null;\n  };\n  return Storage;\n}();\nexport default Storage;", "map": {"version": 3, "names": ["util", "timsort", "REDRAW_BIT", "invalid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logInvalidZError", "console", "warn", "shapeCompareFunc", "a", "b", "zlevel", "z", "z2", "Storage", "_roots", "_displayList", "_displayListLen", "displayableSortFunc", "prototype", "traverse", "cb", "context", "i", "length", "getDisplayList", "update", "includeIgnore", "displayList", "updateDisplayList", "roots", "len", "_updateAndAddDisplayable", "el", "clipPaths", "ignore", "beforeUpdate", "afterUpdate", "userSetClipPath", "getClipPath", "ignoreClip", "slice", "current<PERSON><PERSON><PERSON><PERSON>", "parentClipPath", "parent", "updateTransform", "push", "childrenRef", "children", "child", "__dirty", "disp", "__clipPaths", "isNaN", "decalEl", "getDecalElement", "textGuide", "getTextGuideLine", "textEl", "getTextContent", "addRoot", "__zr", "storage", "delRoot", "Array", "l", "idx", "indexOf", "splice", "delAllRoots", "getRoots", "dispose"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/Storage.js"], "sourcesContent": ["import * as util from './core/util.js';\nimport timsort from './core/timsort.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nvar invalidZErrorLogged = false;\nfunction logInvalidZError() {\n    if (invalidZ<PERSON>rrorLogged) {\n        return;\n    }\n    invalidZErrorLogged = true;\n    console.warn('z / z2 / zlevel of displayable is invalid, which may cause unexpected errors');\n}\nfunction shapeCompareFunc(a, b) {\n    if (a.zlevel === b.zlevel) {\n        if (a.z === b.z) {\n            return a.z2 - b.z2;\n        }\n        return a.z - b.z;\n    }\n    return a.zlevel - b.zlevel;\n}\nvar Storage = (function () {\n    function Storage() {\n        this._roots = [];\n        this._displayList = [];\n        this._displayListLen = 0;\n        this.displayableSortFunc = shapeCompareFunc;\n    }\n    Storage.prototype.traverse = function (cb, context) {\n        for (var i = 0; i < this._roots.length; i++) {\n            this._roots[i].traverse(cb, context);\n        }\n    };\n    Storage.prototype.getDisplayList = function (update, includeIgnore) {\n        includeIgnore = includeIgnore || false;\n        var displayList = this._displayList;\n        if (update || !displayList.length) {\n            this.updateDisplayList(includeIgnore);\n        }\n        return displayList;\n    };\n    Storage.prototype.updateDisplayList = function (includeIgnore) {\n        this._displayListLen = 0;\n        var roots = this._roots;\n        var displayList = this._displayList;\n        for (var i = 0, len = roots.length; i < len; i++) {\n            this._updateAndAddDisplayable(roots[i], null, includeIgnore);\n        }\n        displayList.length = this._displayListLen;\n        timsort(displayList, shapeCompareFunc);\n    };\n    Storage.prototype._updateAndAddDisplayable = function (el, clipPaths, includeIgnore) {\n        if (el.ignore && !includeIgnore) {\n            return;\n        }\n        el.beforeUpdate();\n        el.update();\n        el.afterUpdate();\n        var userSetClipPath = el.getClipPath();\n        if (el.ignoreClip) {\n            clipPaths = null;\n        }\n        else if (userSetClipPath) {\n            if (clipPaths) {\n                clipPaths = clipPaths.slice();\n            }\n            else {\n                clipPaths = [];\n            }\n            var currentClipPath = userSetClipPath;\n            var parentClipPath = el;\n            while (currentClipPath) {\n                currentClipPath.parent = parentClipPath;\n                currentClipPath.updateTransform();\n                clipPaths.push(currentClipPath);\n                parentClipPath = currentClipPath;\n                currentClipPath = currentClipPath.getClipPath();\n            }\n        }\n        if (el.childrenRef) {\n            var children = el.childrenRef();\n            for (var i = 0; i < children.length; i++) {\n                var child = children[i];\n                if (el.__dirty) {\n                    child.__dirty |= REDRAW_BIT;\n                }\n                this._updateAndAddDisplayable(child, clipPaths, includeIgnore);\n            }\n            el.__dirty = 0;\n        }\n        else {\n            var disp = el;\n            if (clipPaths && clipPaths.length) {\n                disp.__clipPaths = clipPaths;\n            }\n            else if (disp.__clipPaths && disp.__clipPaths.length > 0) {\n                disp.__clipPaths = [];\n            }\n            if (isNaN(disp.z)) {\n                logInvalidZError();\n                disp.z = 0;\n            }\n            if (isNaN(disp.z2)) {\n                logInvalidZError();\n                disp.z2 = 0;\n            }\n            if (isNaN(disp.zlevel)) {\n                logInvalidZError();\n                disp.zlevel = 0;\n            }\n            this._displayList[this._displayListLen++] = disp;\n        }\n        var decalEl = el.getDecalElement && el.getDecalElement();\n        if (decalEl) {\n            this._updateAndAddDisplayable(decalEl, clipPaths, includeIgnore);\n        }\n        var textGuide = el.getTextGuideLine();\n        if (textGuide) {\n            this._updateAndAddDisplayable(textGuide, clipPaths, includeIgnore);\n        }\n        var textEl = el.getTextContent();\n        if (textEl) {\n            this._updateAndAddDisplayable(textEl, clipPaths, includeIgnore);\n        }\n    };\n    Storage.prototype.addRoot = function (el) {\n        if (el.__zr && el.__zr.storage === this) {\n            return;\n        }\n        this._roots.push(el);\n    };\n    Storage.prototype.delRoot = function (el) {\n        if (el instanceof Array) {\n            for (var i = 0, l = el.length; i < l; i++) {\n                this.delRoot(el[i]);\n            }\n            return;\n        }\n        var idx = util.indexOf(this._roots, el);\n        if (idx >= 0) {\n            this._roots.splice(idx, 1);\n        }\n    };\n    Storage.prototype.delAllRoots = function () {\n        this._roots = [];\n        this._displayList = [];\n        this._displayListLen = 0;\n        return;\n    };\n    Storage.prototype.getRoots = function () {\n        return this._roots;\n    };\n    Storage.prototype.dispose = function () {\n        this._displayList = null;\n        this._roots = null;\n    };\n    return Storage;\n}());\nexport default Storage;\n"], "mappings": ";AAAA,OAAO,KAAKA,IAAI,MAAM,gBAAgB;AACtC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,wBAAwB;AACnD,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,SAASC,gBAAgBA,CAAA,EAAG;EACxB,IAAID,mBAAmB,EAAE;IACrB;EACJ;EACAA,mBAAmB,GAAG,IAAI;EAC1BE,OAAO,CAACC,IAAI,CAAC,8EAA8E,CAAC;AAChG;AACA,SAASC,gBAAgBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAID,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,EAAE;IACvB,IAAIF,CAAC,CAACG,CAAC,KAAKF,CAAC,CAACE,CAAC,EAAE;MACb,OAAOH,CAAC,CAACI,EAAE,GAAGH,CAAC,CAACG,EAAE;IACtB;IACA,OAAOJ,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC;EACpB;EACA,OAAOH,CAAC,CAACE,MAAM,GAAGD,CAAC,CAACC,MAAM;AAC9B;AACA,IAAIG,OAAO,GAAI,YAAY;EACvB,SAASA,OAAOA,CAAA,EAAG;IACf,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,mBAAmB,GAAGV,gBAAgB;EAC/C;EACAM,OAAO,CAACK,SAAS,CAACC,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,MAAM,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAI,CAACR,MAAM,CAACQ,CAAC,CAAC,CAACH,QAAQ,CAACC,EAAE,EAAEC,OAAO,CAAC;IACxC;EACJ,CAAC;EACDR,OAAO,CAACK,SAAS,CAACM,cAAc,GAAG,UAAUC,MAAM,EAAEC,aAAa,EAAE;IAChEA,aAAa,GAAGA,aAAa,IAAI,KAAK;IACtC,IAAIC,WAAW,GAAG,IAAI,CAACZ,YAAY;IACnC,IAAIU,MAAM,IAAI,CAACE,WAAW,CAACJ,MAAM,EAAE;MAC/B,IAAI,CAACK,iBAAiB,CAACF,aAAa,CAAC;IACzC;IACA,OAAOC,WAAW;EACtB,CAAC;EACDd,OAAO,CAACK,SAAS,CAACU,iBAAiB,GAAG,UAAUF,aAAa,EAAE;IAC3D,IAAI,CAACV,eAAe,GAAG,CAAC;IACxB,IAAIa,KAAK,GAAG,IAAI,CAACf,MAAM;IACvB,IAAIa,WAAW,GAAG,IAAI,CAACZ,YAAY;IACnC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEQ,GAAG,GAAGD,KAAK,CAACN,MAAM,EAAED,CAAC,GAAGQ,GAAG,EAAER,CAAC,EAAE,EAAE;MAC9C,IAAI,CAACS,wBAAwB,CAACF,KAAK,CAACP,CAAC,CAAC,EAAE,IAAI,EAAEI,aAAa,CAAC;IAChE;IACAC,WAAW,CAACJ,MAAM,GAAG,IAAI,CAACP,eAAe;IACzCf,OAAO,CAAC0B,WAAW,EAAEpB,gBAAgB,CAAC;EAC1C,CAAC;EACDM,OAAO,CAACK,SAAS,CAACa,wBAAwB,GAAG,UAAUC,EAAE,EAAEC,SAAS,EAAEP,aAAa,EAAE;IACjF,IAAIM,EAAE,CAACE,MAAM,IAAI,CAACR,aAAa,EAAE;MAC7B;IACJ;IACAM,EAAE,CAACG,YAAY,CAAC,CAAC;IACjBH,EAAE,CAACP,MAAM,CAAC,CAAC;IACXO,EAAE,CAACI,WAAW,CAAC,CAAC;IAChB,IAAIC,eAAe,GAAGL,EAAE,CAACM,WAAW,CAAC,CAAC;IACtC,IAAIN,EAAE,CAACO,UAAU,EAAE;MACfN,SAAS,GAAG,IAAI;IACpB,CAAC,MACI,IAAII,eAAe,EAAE;MACtB,IAAIJ,SAAS,EAAE;QACXA,SAAS,GAAGA,SAAS,CAACO,KAAK,CAAC,CAAC;MACjC,CAAC,MACI;QACDP,SAAS,GAAG,EAAE;MAClB;MACA,IAAIQ,eAAe,GAAGJ,eAAe;MACrC,IAAIK,cAAc,GAAGV,EAAE;MACvB,OAAOS,eAAe,EAAE;QACpBA,eAAe,CAACE,MAAM,GAAGD,cAAc;QACvCD,eAAe,CAACG,eAAe,CAAC,CAAC;QACjCX,SAAS,CAACY,IAAI,CAACJ,eAAe,CAAC;QAC/BC,cAAc,GAAGD,eAAe;QAChCA,eAAe,GAAGA,eAAe,CAACH,WAAW,CAAC,CAAC;MACnD;IACJ;IACA,IAAIN,EAAE,CAACc,WAAW,EAAE;MAChB,IAAIC,QAAQ,GAAGf,EAAE,CAACc,WAAW,CAAC,CAAC;MAC/B,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,QAAQ,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAI0B,KAAK,GAAGD,QAAQ,CAACzB,CAAC,CAAC;QACvB,IAAIU,EAAE,CAACiB,OAAO,EAAE;UACZD,KAAK,CAACC,OAAO,IAAI/C,UAAU;QAC/B;QACA,IAAI,CAAC6B,wBAAwB,CAACiB,KAAK,EAAEf,SAAS,EAAEP,aAAa,CAAC;MAClE;MACAM,EAAE,CAACiB,OAAO,GAAG,CAAC;IAClB,CAAC,MACI;MACD,IAAIC,IAAI,GAAGlB,EAAE;MACb,IAAIC,SAAS,IAAIA,SAAS,CAACV,MAAM,EAAE;QAC/B2B,IAAI,CAACC,WAAW,GAAGlB,SAAS;MAChC,CAAC,MACI,IAAIiB,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAAC5B,MAAM,GAAG,CAAC,EAAE;QACtD2B,IAAI,CAACC,WAAW,GAAG,EAAE;MACzB;MACA,IAAIC,KAAK,CAACF,IAAI,CAACvC,CAAC,CAAC,EAAE;QACfP,gBAAgB,CAAC,CAAC;QAClB8C,IAAI,CAACvC,CAAC,GAAG,CAAC;MACd;MACA,IAAIyC,KAAK,CAACF,IAAI,CAACtC,EAAE,CAAC,EAAE;QAChBR,gBAAgB,CAAC,CAAC;QAClB8C,IAAI,CAACtC,EAAE,GAAG,CAAC;MACf;MACA,IAAIwC,KAAK,CAACF,IAAI,CAACxC,MAAM,CAAC,EAAE;QACpBN,gBAAgB,CAAC,CAAC;QAClB8C,IAAI,CAACxC,MAAM,GAAG,CAAC;MACnB;MACA,IAAI,CAACK,YAAY,CAAC,IAAI,CAACC,eAAe,EAAE,CAAC,GAAGkC,IAAI;IACpD;IACA,IAAIG,OAAO,GAAGrB,EAAE,CAACsB,eAAe,IAAItB,EAAE,CAACsB,eAAe,CAAC,CAAC;IACxD,IAAID,OAAO,EAAE;MACT,IAAI,CAACtB,wBAAwB,CAACsB,OAAO,EAAEpB,SAAS,EAAEP,aAAa,CAAC;IACpE;IACA,IAAI6B,SAAS,GAAGvB,EAAE,CAACwB,gBAAgB,CAAC,CAAC;IACrC,IAAID,SAAS,EAAE;MACX,IAAI,CAACxB,wBAAwB,CAACwB,SAAS,EAAEtB,SAAS,EAAEP,aAAa,CAAC;IACtE;IACA,IAAI+B,MAAM,GAAGzB,EAAE,CAAC0B,cAAc,CAAC,CAAC;IAChC,IAAID,MAAM,EAAE;MACR,IAAI,CAAC1B,wBAAwB,CAAC0B,MAAM,EAAExB,SAAS,EAAEP,aAAa,CAAC;IACnE;EACJ,CAAC;EACDb,OAAO,CAACK,SAAS,CAACyC,OAAO,GAAG,UAAU3B,EAAE,EAAE;IACtC,IAAIA,EAAE,CAAC4B,IAAI,IAAI5B,EAAE,CAAC4B,IAAI,CAACC,OAAO,KAAK,IAAI,EAAE;MACrC;IACJ;IACA,IAAI,CAAC/C,MAAM,CAAC+B,IAAI,CAACb,EAAE,CAAC;EACxB,CAAC;EACDnB,OAAO,CAACK,SAAS,CAAC4C,OAAO,GAAG,UAAU9B,EAAE,EAAE;IACtC,IAAIA,EAAE,YAAY+B,KAAK,EAAE;MACrB,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAE0C,CAAC,GAAGhC,EAAE,CAACT,MAAM,EAAED,CAAC,GAAG0C,CAAC,EAAE1C,CAAC,EAAE,EAAE;QACvC,IAAI,CAACwC,OAAO,CAAC9B,EAAE,CAACV,CAAC,CAAC,CAAC;MACvB;MACA;IACJ;IACA,IAAI2C,GAAG,GAAGjE,IAAI,CAACkE,OAAO,CAAC,IAAI,CAACpD,MAAM,EAAEkB,EAAE,CAAC;IACvC,IAAIiC,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAACnD,MAAM,CAACqD,MAAM,CAACF,GAAG,EAAE,CAAC,CAAC;IAC9B;EACJ,CAAC;EACDpD,OAAO,CAACK,SAAS,CAACkD,WAAW,GAAG,YAAY;IACxC,IAAI,CAACtD,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;EACJ,CAAC;EACDH,OAAO,CAACK,SAAS,CAACmD,QAAQ,GAAG,YAAY;IACrC,OAAO,IAAI,CAACvD,MAAM;EACtB,CAAC;EACDD,OAAO,CAACK,SAAS,CAACoD,OAAO,GAAG,YAAY;IACpC,IAAI,CAACvD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,MAAM,GAAG,IAAI;EACtB,CAAC;EACD,OAAOD,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}