{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nvar ScatterSeriesModel = /** @class */function (_super) {\n  __extends(ScatterSeriesModel, _super);\n  function ScatterSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScatterSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  ScatterSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  ScatterSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n    if (progressive == null) {\n      // PENDING\n      return this.option.large ? 5e3 : this.get('progressive');\n    }\n    return progressive;\n  };\n  ScatterSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n    if (progressiveThreshold == null) {\n      // PENDING\n      return this.option.large ? 1e4 : this.get('progressiveThreshold');\n    }\n    return progressiveThreshold;\n  };\n  ScatterSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.point(data.getItemLayout(dataIndex));\n  };\n  ScatterSeriesModel.prototype.getZLevelKey = function () {\n    // Each progressive series has individual key.\n    return this.getData().count() > this.getProgressiveThreshold() ? this.id : '';\n  };\n  ScatterSeriesModel.type = 'series.scatter';\n  ScatterSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  ScatterSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    symbolSize: 10,\n    // symbolRotate: null,  // 图形旋转控制\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    // cursor: null,\n    itemStyle: {\n      opacity: 0.8\n      // color: 各异\n    },\n    emphasis: {\n      scale: true\n    },\n    // If clip the overflow graphics\n    // Works on cartesian / polar series\n    clip: true,\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    universalTransition: {\n      divideShape: 'clone'\n    }\n    // progressive: null\n  };\n  return ScatterSeriesModel;\n}(SeriesModel);\nexport default ScatterSeriesModel;", "map": {"version": 3, "names": ["__extends", "createSeriesData", "SeriesModel", "ScatterSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "prototype", "getInitialData", "option", "ecModel", "useEncodeDefaulter", "getProgressive", "progressive", "large", "get", "getProgressiveThreshold", "progressiveThreshold", "brushSelector", "dataIndex", "data", "selectors", "point", "getItemLayout", "getZLevelKey", "getData", "count", "id", "dependencies", "defaultOption", "coordinateSystem", "z", "legendHoverLink", "symbolSize", "largeThreshold", "itemStyle", "opacity", "emphasis", "scale", "clip", "select", "borderColor", "universalTransition", "divideShape"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/scatter/ScatterSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nvar ScatterSeriesModel = /** @class */function (_super) {\n  __extends(ScatterSeriesModel, _super);\n  function ScatterSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScatterSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  ScatterSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  ScatterSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n    if (progressive == null) {\n      // PENDING\n      return this.option.large ? 5e3 : this.get('progressive');\n    }\n    return progressive;\n  };\n  ScatterSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n    if (progressiveThreshold == null) {\n      // PENDING\n      return this.option.large ? 1e4 : this.get('progressiveThreshold');\n    }\n    return progressiveThreshold;\n  };\n  ScatterSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.point(data.getItemLayout(dataIndex));\n  };\n  ScatterSeriesModel.prototype.getZLevelKey = function () {\n    // Each progressive series has individual key.\n    return this.getData().count() > this.getProgressiveThreshold() ? this.id : '';\n  };\n  ScatterSeriesModel.type = 'series.scatter';\n  ScatterSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  ScatterSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    symbolSize: 10,\n    // symbolRotate: null,  // 图形旋转控制\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    // cursor: null,\n    itemStyle: {\n      opacity: 0.8\n      // color: 各异\n    },\n    emphasis: {\n      scale: true\n    },\n    // If clip the overflow graphics\n    // Works on cartesian / polar series\n    clip: true,\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    universalTransition: {\n      divideShape: 'clone'\n    }\n    // progressive: null\n  };\n  return ScatterSeriesModel;\n}(SeriesModel);\nexport default ScatterSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDJ,SAAS,CAACG,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5B,OAAOJ,KAAK;EACd;EACAF,kBAAkB,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE,OAAOZ,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCa,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EACDX,kBAAkB,CAACO,SAAS,CAACK,cAAc,GAAG,YAAY;IACxD,IAAIC,WAAW,GAAG,IAAI,CAACJ,MAAM,CAACI,WAAW;IACzC,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB;MACA,OAAO,IAAI,CAACJ,MAAM,CAACK,KAAK,GAAG,GAAG,GAAG,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1D;IACA,OAAOF,WAAW;EACpB,CAAC;EACDb,kBAAkB,CAACO,SAAS,CAACS,uBAAuB,GAAG,YAAY;IACjE,IAAIC,oBAAoB,GAAG,IAAI,CAACR,MAAM,CAACQ,oBAAoB;IAC3D,IAAIA,oBAAoB,IAAI,IAAI,EAAE;MAChC;MACA,OAAO,IAAI,CAACR,MAAM,CAACK,KAAK,GAAG,GAAG,GAAG,IAAI,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnE;IACA,OAAOE,oBAAoB;EAC7B,CAAC;EACDjB,kBAAkB,CAACO,SAAS,CAACW,aAAa,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;IACjF,OAAOA,SAAS,CAACC,KAAK,CAACF,IAAI,CAACG,aAAa,CAACJ,SAAS,CAAC,CAAC;EACvD,CAAC;EACDnB,kBAAkB,CAACO,SAAS,CAACiB,YAAY,GAAG,YAAY;IACtD;IACA,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACV,uBAAuB,CAAC,CAAC,GAAG,IAAI,CAACW,EAAE,GAAG,EAAE;EAC/E,CAAC;EACD3B,kBAAkB,CAACK,IAAI,GAAG,gBAAgB;EAC1CL,kBAAkB,CAAC4B,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC;EACpF5B,kBAAkB,CAAC6B,aAAa,GAAG;IACjCC,gBAAgB,EAAE,aAAa;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,EAAE;IACd;IACAnB,KAAK,EAAE,KAAK;IACZ;IACAoB,cAAc,EAAE,IAAI;IACpB;IACAC,SAAS,EAAE;MACTC,OAAO,EAAE;MACT;IACF,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE;IACT,CAAC;IACD;IACA;IACAC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE;MACNL,SAAS,EAAE;QACTM,WAAW,EAAE;MACf;IACF,CAAC;IACDC,mBAAmB,EAAE;MACnBC,WAAW,EAAE;IACf;IACA;EACF,CAAC;EACD,OAAO3C,kBAAkB;AAC3B,CAAC,CAACD,WAAW,CAAC;AACd,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}