{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { extend, isArray } from 'zrender/lib/core/util.js';\n/**\r\n * [Usage]:\r\n * (1)\r\n * createListSimply(seriesModel, ['value']);\r\n * (2)\r\n * createListSimply(seriesModel, {\r\n *     coordDimensions: ['value'],\r\n *     dimensionsCount: 5\r\n * });\r\n */\nexport default function createSeriesDataSimply(seriesModel, opt, nameList) {\n  opt = isArray(opt) && {\n    coordDimensions: opt\n  } || extend({\n    encodeDefine: seriesModel.getEncode()\n  }, opt);\n  var source = seriesModel.getSource();\n  var dimensions = prepareSeriesDataSchema(source, opt).dimensions;\n  var list = new SeriesData(dimensions, seriesModel);\n  list.initData(source, nameList);\n  return list;\n}", "map": {"version": 3, "names": ["prepareSeriesDataSchema", "SeriesData", "extend", "isArray", "createSeriesDataSimply", "seriesModel", "opt", "nameList", "coordDimensions", "encodeDefine", "getEncode", "source", "getSource", "dimensions", "list", "initData"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/helper/createSeriesDataSimply.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { extend, isArray } from 'zrender/lib/core/util.js';\n/**\r\n * [Usage]:\r\n * (1)\r\n * createListSimply(seriesModel, ['value']);\r\n * (2)\r\n * createListSimply(seriesModel, {\r\n *     coordDimensions: ['value'],\r\n *     dimensionsCount: 5\r\n * });\r\n */\nexport default function createSeriesDataSimply(seriesModel, opt, nameList) {\n  opt = isArray(opt) && {\n    coordDimensions: opt\n  } || extend({\n    encodeDefine: seriesModel.getEncode()\n  }, opt);\n  var source = seriesModel.getSource();\n  var dimensions = prepareSeriesDataSchema(source, opt).dimensions;\n  var list = new SeriesData(dimensions, seriesModel);\n  list.initData(source, nameList);\n  return list;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,MAAM,EAAEC,OAAO,QAAQ,0BAA0B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,sBAAsBA,CAACC,WAAW,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EACzED,GAAG,GAAGH,OAAO,CAACG,GAAG,CAAC,IAAI;IACpBE,eAAe,EAAEF;EACnB,CAAC,IAAIJ,MAAM,CAAC;IACVO,YAAY,EAAEJ,WAAW,CAACK,SAAS,CAAC;EACtC,CAAC,EAAEJ,GAAG,CAAC;EACP,IAAIK,MAAM,GAAGN,WAAW,CAACO,SAAS,CAAC,CAAC;EACpC,IAAIC,UAAU,GAAGb,uBAAuB,CAACW,MAAM,EAAEL,GAAG,CAAC,CAACO,UAAU;EAChE,IAAIC,IAAI,GAAG,IAAIb,UAAU,CAACY,UAAU,EAAER,WAAW,CAAC;EAClDS,IAAI,CAACC,QAAQ,CAACJ,MAAM,EAAEJ,QAAQ,CAAC;EAC/B,OAAOO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}