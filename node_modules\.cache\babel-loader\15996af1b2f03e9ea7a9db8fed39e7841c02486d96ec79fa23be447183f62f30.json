{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bar-card\"\n};\nconst _hoisted_2 = {\n  ref: \"volumn\",\n  class: \"volume\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, null, 512 /* NEED_PATCH */)]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [15, 8, 12, 4, 8, 10, 9];\n      var xData = [\n        \"神经\",\n        \"智力\"\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          y2: 0,\n          x2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#4379d0\",\n            },\n            {\n              offset: 0.5,\n              color: \"#7a8ddd\",\n            },\n            {\n              color: \"#a1c4ff\",\n              offset: 0.5,\n            },\n            {\n              offset: 1,\n              color: \"#bfd1ff\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#bb5763\",\n            },\n            {\n              offset: 0.5,\n              color: \"#d0938c\",\n            },\n            {\n              offset: 0.5,\n              color: \"#ffa1ac\",\n            },\n            {\n              offset: 1,\n              color: \"#ffbabe\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#4ea95d\",\n            },\n            {\n              offset: 0.5,\n              color: \"#80bc96\",\n            },\n            {\n              offset: 0.5,\n              color: \"#90e6a0\",\n            },\n            {\n              offset: 1,\n              color: \"#a8e5b7\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#ab8b52\",\n            },\n            {\n              offset: 0.5,\n              color: \"#bdb280\",\n            },\n            {\n              offset: 0.5,\n              color: \"#e6c690\",\n            },\n            {\n              offset: 1,\n              color: \"#e5d6aa\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#4395b3\",\n            },\n            {\n              offset: 0.5,\n              color: \"#769fc2\",\n            },\n            {\n              offset: 0.5,\n              color: \"#86d4f0\",\n            },\n            {\n              offset: 1,\n              color: \"#a4d1ec\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#b0ad52\",\n            },\n            {\n              offset: 0.5,\n              color: \"#b7c284\",\n            },\n            {\n              offset: 0.5,\n              color: \"#f0ed98\",\n            },\n            {\n              offset: 1,\n              color: \"#ebefae\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#3c46bc\",\n            },\n            {\n              offset: 0.5,\n              color: \"#7b6fc8\",\n            },\n            {\n              offset: 0.5,\n              color: \"#7e88f8\",\n            },\n            {\n              offset: 1,\n              color: \"#a09ef1\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var yValue = api.value(1);\n        var start = api.coord([api.value(0), yValue]);\n        var size = api.size([api.value(1) - api.value(0), yValue]);\n        // var style = api.style();\n\n        // 最右边的点坐标\n        var width = size[0] * 0.6;\n        var x = start[0] - width / 2;\n        var y = start[1];\n\n        var bottomHeight = 48;\n\n        var points = [[x + width / 2, y]];\n\n        // 左边的坐标点\n        points.push([x, size[1] + y]);\n\n        points.push([x + width / 2, size[1] + y + bottomHeight]);\n\n        // 右边的坐标点\n        points.push([x + width, size[1] + y]);\n\n        var group = {\n  type: \"group\",\n  children: [\n    {\n      // 左半部分\n      type: \"polygon\",\n      shape: {\n        points: [\n          [x + width / 2, y],          // 顶点\n          [x, size[1] + y],            // 左底角\n          [x + width / 2, size[1] + y + bottomHeight]  // 底中点\n        ]\n      },\n      style: {\n        fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n          { offset: 0, color: \"#4379d0\" },\n          { offset: 1, color: \"#bfd1ff\" }\n        ])\n      }\n    },\n    {\n      // 右半部分\n      type: \"polygon\",\n      shape: {\n        points: [\n          [x + width / 2, y],          // 顶点\n          [x + width, size[1] + y],    // 右底角\n          [x + width / 2, size[1] + y + bottomHeight]  // 底中点\n        ]\n      },\n      style: {\n        fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n          { offset: 0, color: \"#7a8ddd\" },\n          { offset: 1, color: \"#a1c4ff\" }\n        ])\n      }\n    }\n  ]\n}\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        tooltip: {\n          backgroundColor: \" rgba(9,35,75,0.7)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n            align: \"left\",\n          },\n        },\n        grid: {\n          top: \"20%\",\n          left: \"5%\",\n          bottom: \"12%\",\n          right: \"5%\",\n          containLabel: true,\n        },\n        xAxis: {\n          data: xData,\n          offset: 6,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: \"rgba(127, 208, 255, 0.2)\",\n              width: 1,\n            },\n          },\n          splitLine: {\n            show: false,\n            lineStyle: {\n              color: \"rgba(127, 208, 255, 0.2)\",\n            },\n          },\n          axisLabel: {\n            show: true,\n            color: \"#fff\",\n            fontSize: this.fontSize(0.35),\n            interval: 0,\n          },\n          axisTick: {\n            show: false,\n          },\n        },\n        yAxis: [\n          {\n            inverse: false,\n            name: \"单位：人\",\n            nameTextStyle: {\n              color: \"#fff\",\n              fontSize: this.fontSize(0.35),\n              nameLocation: \"start\",\n            },\n            type: \"value\",\n            position: \"left\",\n            axisLabel: {\n              show: true,\n              fontSize: this.fontSize(0.35),\n              color: \"#fff\",\n            },\n            axisLine: {\n              show: true,\n              lineStyle: {\n                color: \"rgba(127, 208, 255, 0.2)\",\n                width: 1,\n              },\n            },\n            splitLine: {\n              show: true,\n              lineStyle: {\n                color: \"rgba(127, 208, 255, 0.2)\",\n              },\n            },\n            axisTick: {\n              show: false,\n            },\n          },\n        ],\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n\n            label: {\n              show: true,\n              position: \"top\",\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.35),\n              formatter: function(params) {\n                return \"{a\" + params.dataIndex + \"|\" + params.data + \"人}\";\n              },\n              rich: {\n                a0: {\n                  color: \"#77A9FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a1: {\n                  color: \"#FEBBBD\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a2: {\n                  color: \"#A1EEB3\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a3: {\n                  color: \"#F3BD5B\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a4: {\n                  color: \"#57D1FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a5: {\n                  color: \"#F0ED97\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a6: {\n                  color: \"#9EA0FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n              },\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EACdC,GAAG,EAAC,QAAQ;EAACD,KAAK,EAAC;;;uBAD1BE,mBAAA,CAEM,OAFNC,UAEM,GADJC,mBAAA,CAAmC,OAAnCC,UAAmC,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}