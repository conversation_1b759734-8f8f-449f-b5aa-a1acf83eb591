{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport Axis from '../Axis.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar AngleAxis = /** @class */function (_super) {\n  __extends(AngleAxis, _super);\n  function AngleAxis(scale, angleExtent) {\n    return _super.call(this, 'angle', scale, angleExtent || [0, 360]) || this;\n  }\n  AngleAxis.prototype.pointToData = function (point, clamp) {\n    return this.polar.pointToData(point, clamp)[this.dim === 'radius' ? 0 : 1];\n  };\n  /**\r\n   * Only be called in category axis.\r\n   * Angle axis uses text height to decide interval\r\n   *\r\n   * @override\r\n   * @return {number} Auto interval for cateogry axis tick and label\r\n   */\n  AngleAxis.prototype.calculateCategoryInterval = function () {\n    var axis = this;\n    var labelModel = axis.getLabelModel();\n    var ordinalScale = axis.scale;\n    var ordinalExtent = ordinalScale.getExtent();\n    // Providing this method is for optimization:\n    // avoid generating a long array by `getTicks`\n    // in large category data case.\n    var tickCount = ordinalScale.count();\n    if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n      return 0;\n    }\n    var tickValue = ordinalExtent[0];\n    var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n    var unitH = Math.abs(unitSpan);\n    // Not precise, just use height as text width\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(tickValue == null ? '' : tickValue + '', labelModel.getFont(), 'center', 'top');\n    var maxH = Math.max(rect.height, 7);\n    var dh = maxH / unitH;\n    // 0/0 is NaN, 1/0 is Infinity.\n    isNaN(dh) && (dh = Infinity);\n    var interval = Math.max(0, Math.floor(dh));\n    var cache = inner(axis.model);\n    var lastAutoInterval = cache.lastAutoInterval;\n    var lastTickCount = cache.lastTickCount;\n    // Use cache to keep interval stable while moving zoom window,\n    // otherwise the calculated interval might jitter when the zoom\n    // window size is close to the interval-changing size.\n    if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n    // Always choose the bigger one, otherwise the critical\n    // point is not the same when zooming in or zooming out.\n    && lastAutoInterval > interval) {\n      interval = lastAutoInterval;\n    }\n    // Only update cache if cache not used, otherwise the\n    // changing of interval is too insensitive.\n    else {\n      cache.lastTickCount = tickCount;\n      cache.lastAutoInterval = interval;\n    }\n    return interval;\n  };\n  return AngleAxis;\n}(Axis);\nAngleAxis.prototype.dataToAngle = Axis.prototype.dataToCoord;\nAngleAxis.prototype.angleToData = Axis.prototype.coordToData;\nexport default AngleAxis;", "map": {"version": 3, "names": ["__extends", "textContain", "Axis", "makeInner", "inner", "AngleAxis", "_super", "scale", "angleExtent", "call", "prototype", "pointToData", "point", "clamp", "polar", "dim", "calculateCategoryInterval", "axis", "labelModel", "getLabelModel", "ordinalScale", "ordinalExtent", "getExtent", "tickCount", "count", "tickValue", "unitSpan", "dataToCoord", "unitH", "Math", "abs", "rect", "getBoundingRect", "getFont", "maxH", "max", "height", "dh", "isNaN", "Infinity", "interval", "floor", "cache", "model", "lastAutoInterval", "lastTickCount", "dataToAngle", "angleToData", "coordToData"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/polar/AngleAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport Axis from '../Axis.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar AngleAxis = /** @class */function (_super) {\n  __extends(AngleAxis, _super);\n  function AngleAxis(scale, angleExtent) {\n    return _super.call(this, 'angle', scale, angleExtent || [0, 360]) || this;\n  }\n  AngleAxis.prototype.pointToData = function (point, clamp) {\n    return this.polar.pointToData(point, clamp)[this.dim === 'radius' ? 0 : 1];\n  };\n  /**\r\n   * Only be called in category axis.\r\n   * Angle axis uses text height to decide interval\r\n   *\r\n   * @override\r\n   * @return {number} Auto interval for cateogry axis tick and label\r\n   */\n  AngleAxis.prototype.calculateCategoryInterval = function () {\n    var axis = this;\n    var labelModel = axis.getLabelModel();\n    var ordinalScale = axis.scale;\n    var ordinalExtent = ordinalScale.getExtent();\n    // Providing this method is for optimization:\n    // avoid generating a long array by `getTicks`\n    // in large category data case.\n    var tickCount = ordinalScale.count();\n    if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n      return 0;\n    }\n    var tickValue = ordinalExtent[0];\n    var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n    var unitH = Math.abs(unitSpan);\n    // Not precise, just use height as text width\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(tickValue == null ? '' : tickValue + '', labelModel.getFont(), 'center', 'top');\n    var maxH = Math.max(rect.height, 7);\n    var dh = maxH / unitH;\n    // 0/0 is NaN, 1/0 is Infinity.\n    isNaN(dh) && (dh = Infinity);\n    var interval = Math.max(0, Math.floor(dh));\n    var cache = inner(axis.model);\n    var lastAutoInterval = cache.lastAutoInterval;\n    var lastTickCount = cache.lastTickCount;\n    // Use cache to keep interval stable while moving zoom window,\n    // otherwise the calculated interval might jitter when the zoom\n    // window size is close to the interval-changing size.\n    if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n    // Always choose the bigger one, otherwise the critical\n    // point is not the same when zooming in or zooming out.\n    && lastAutoInterval > interval) {\n      interval = lastAutoInterval;\n    }\n    // Only update cache if cache not used, otherwise the\n    // changing of interval is too insensitive.\n    else {\n      cache.lastTickCount = tickCount;\n      cache.lastAutoInterval = interval;\n    }\n    return interval;\n  };\n  return AngleAxis;\n}(Axis);\nAngleAxis.prototype.dataToAngle = Axis.prototype.dataToCoord;\nAngleAxis.prototype.angleToData = Axis.prototype.coordToData;\nexport default AngleAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,IAAIE,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7CN,SAAS,CAACK,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,KAAK,EAAEC,WAAW,EAAE;IACrC,OAAOF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAE,OAAO,EAAEF,KAAK,EAAEC,WAAW,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI;EAC3E;EACAH,SAAS,CAACK,SAAS,CAACC,WAAW,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACxD,OAAO,IAAI,CAACC,KAAK,CAACH,WAAW,CAACC,KAAK,EAAEC,KAAK,CAAC,CAAC,IAAI,CAACE,GAAG,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEV,SAAS,CAACK,SAAS,CAACM,yBAAyB,GAAG,YAAY;IAC1D,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,UAAU,GAAGD,IAAI,CAACE,aAAa,CAAC,CAAC;IACrC,IAAIC,YAAY,GAAGH,IAAI,CAACV,KAAK;IAC7B,IAAIc,aAAa,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC;IAC5C;IACA;IACA;IACA,IAAIC,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC;IACpC,IAAIH,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC3C,OAAO,CAAC;IACV;IACA,IAAII,SAAS,GAAGJ,aAAa,CAAC,CAAC,CAAC;IAChC,IAAIK,QAAQ,GAAGT,IAAI,CAACU,WAAW,CAACF,SAAS,GAAG,CAAC,CAAC,GAAGR,IAAI,CAACU,WAAW,CAACF,SAAS,CAAC;IAC5E,IAAIG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC;IAC9B;IACA;IACA,IAAIK,IAAI,GAAG9B,WAAW,CAAC+B,eAAe,CAACP,SAAS,IAAI,IAAI,GAAG,EAAE,GAAGA,SAAS,GAAG,EAAE,EAAEP,UAAU,CAACe,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC;IACtH,IAAIC,IAAI,GAAGL,IAAI,CAACM,GAAG,CAACJ,IAAI,CAACK,MAAM,EAAE,CAAC,CAAC;IACnC,IAAIC,EAAE,GAAGH,IAAI,GAAGN,KAAK;IACrB;IACAU,KAAK,CAACD,EAAE,CAAC,KAAKA,EAAE,GAAGE,QAAQ,CAAC;IAC5B,IAAIC,QAAQ,GAAGX,IAAI,CAACM,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACY,KAAK,CAACJ,EAAE,CAAC,CAAC;IAC1C,IAAIK,KAAK,GAAGtC,KAAK,CAACa,IAAI,CAAC0B,KAAK,CAAC;IAC7B,IAAIC,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB;IAC7C,IAAIC,aAAa,GAAGH,KAAK,CAACG,aAAa;IACvC;IACA;IACA;IACA,IAAID,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,IAAIhB,IAAI,CAACC,GAAG,CAACc,gBAAgB,GAAGJ,QAAQ,CAAC,IAAI,CAAC,IAAIX,IAAI,CAACC,GAAG,CAACe,aAAa,GAAGtB,SAAS,CAAC,IAAI;IAC9I;IACA;IAAA,GACGqB,gBAAgB,GAAGJ,QAAQ,EAAE;MAC9BA,QAAQ,GAAGI,gBAAgB;IAC7B;IACA;IACA;IAAA,KACK;MACHF,KAAK,CAACG,aAAa,GAAGtB,SAAS;MAC/BmB,KAAK,CAACE,gBAAgB,GAAGJ,QAAQ;IACnC;IACA,OAAOA,QAAQ;EACjB,CAAC;EACD,OAAOnC,SAAS;AAClB,CAAC,CAACH,IAAI,CAAC;AACPG,SAAS,CAACK,SAAS,CAACoC,WAAW,GAAG5C,IAAI,CAACQ,SAAS,CAACiB,WAAW;AAC5DtB,SAAS,CAACK,SAAS,CAACqC,WAAW,GAAG7C,IAAI,CAACQ,SAAS,CAACsC,WAAW;AAC5D,eAAe3C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}