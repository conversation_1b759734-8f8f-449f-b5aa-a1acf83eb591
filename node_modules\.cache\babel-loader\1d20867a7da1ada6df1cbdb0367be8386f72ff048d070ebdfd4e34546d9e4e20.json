{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as featureManager from './featureManager.js';\nimport ComponentModel from '../../model/Component.js';\nvar ToolboxModel = /** @class */function (_super) {\n  __extends(ToolboxModel, _super);\n  function ToolboxModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ToolboxModel.type;\n    return _this;\n  }\n  ToolboxModel.prototype.optionUpdated = function () {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    var ecModel = this.ecModel;\n    zrUtil.each(this.option.feature, function (featureOpt, featureName) {\n      var Feature = featureManager.getFeature(featureName);\n      if (Feature) {\n        if (Feature.getDefaultOption) {\n          Feature.defaultOption = Feature.getDefaultOption(ecModel);\n        }\n        zrUtil.merge(featureOpt, Feature.defaultOption);\n      }\n    });\n  };\n  ToolboxModel.type = 'toolbox';\n  ToolboxModel.layoutMode = {\n    type: 'box',\n    ignoreSize: true\n  };\n  ToolboxModel.defaultOption = {\n    show: true,\n    z: 6,\n    // zlevel: 0,\n    orient: 'horizontal',\n    left: 'right',\n    top: 'top',\n    // right\n    // bottom\n    backgroundColor: 'transparent',\n    borderColor: '#ccc',\n    borderRadius: 0,\n    borderWidth: 0,\n    padding: 5,\n    itemSize: 15,\n    itemGap: 8,\n    showTitle: true,\n    iconStyle: {\n      borderColor: '#666',\n      color: 'none'\n    },\n    emphasis: {\n      iconStyle: {\n        borderColor: '#3E98C5'\n      }\n    },\n    // textStyle: {},\n    // feature\n    tooltip: {\n      show: false,\n      position: 'bottom'\n    }\n  };\n  return ToolboxModel;\n}(ComponentModel);\nexport default ToolboxModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "featureManager", "ComponentModel", "ToolboxModel", "_super", "_this", "apply", "arguments", "type", "prototype", "optionUpdated", "ecModel", "each", "option", "feature", "featureOpt", "featureName", "Feature", "getFeature", "getDefaultOption", "defaultOption", "merge", "layoutMode", "ignoreSize", "show", "z", "orient", "left", "top", "backgroundColor", "borderColor", "borderRadius", "borderWidth", "padding", "itemSize", "itemGap", "showTitle", "iconStyle", "color", "emphasis", "tooltip", "position"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/toolbox/ToolboxModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as featureManager from './featureManager.js';\nimport ComponentModel from '../../model/Component.js';\nvar ToolboxModel = /** @class */function (_super) {\n  __extends(ToolboxModel, _super);\n  function ToolboxModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ToolboxModel.type;\n    return _this;\n  }\n  ToolboxModel.prototype.optionUpdated = function () {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    var ecModel = this.ecModel;\n    zrUtil.each(this.option.feature, function (featureOpt, featureName) {\n      var Feature = featureManager.getFeature(featureName);\n      if (Feature) {\n        if (Feature.getDefaultOption) {\n          Feature.defaultOption = Feature.getDefaultOption(ecModel);\n        }\n        zrUtil.merge(featureOpt, Feature.defaultOption);\n      }\n    });\n  };\n  ToolboxModel.type = 'toolbox';\n  ToolboxModel.layoutMode = {\n    type: 'box',\n    ignoreSize: true\n  };\n  ToolboxModel.defaultOption = {\n    show: true,\n    z: 6,\n    // zlevel: 0,\n    orient: 'horizontal',\n    left: 'right',\n    top: 'top',\n    // right\n    // bottom\n    backgroundColor: 'transparent',\n    borderColor: '#ccc',\n    borderRadius: 0,\n    borderWidth: 0,\n    padding: 5,\n    itemSize: 15,\n    itemGap: 8,\n    showTitle: true,\n    iconStyle: {\n      borderColor: '#666',\n      color: 'none'\n    },\n    emphasis: {\n      iconStyle: {\n        borderColor: '#3E98C5'\n      }\n    },\n    // textStyle: {},\n    // feature\n    tooltip: {\n      show: false,\n      position: 'bottom'\n    }\n  };\n  return ToolboxModel;\n}(ComponentModel);\nexport default ToolboxModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,cAAc,MAAM,qBAAqB;AACrD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDL,SAAS,CAACI,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,aAAa,GAAG,YAAY;IACjDN,MAAM,CAACK,SAAS,CAACC,aAAa,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrD,IAAII,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1BX,MAAM,CAACY,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,OAAO,EAAE,UAAUC,UAAU,EAAEC,WAAW,EAAE;MAClE,IAAIC,OAAO,GAAGhB,cAAc,CAACiB,UAAU,CAACF,WAAW,CAAC;MACpD,IAAIC,OAAO,EAAE;QACX,IAAIA,OAAO,CAACE,gBAAgB,EAAE;UAC5BF,OAAO,CAACG,aAAa,GAAGH,OAAO,CAACE,gBAAgB,CAACR,OAAO,CAAC;QAC3D;QACAX,MAAM,CAACqB,KAAK,CAACN,UAAU,EAAEE,OAAO,CAACG,aAAa,CAAC;MACjD;IACF,CAAC,CAAC;EACJ,CAAC;EACDjB,YAAY,CAACK,IAAI,GAAG,SAAS;EAC7BL,YAAY,CAACmB,UAAU,GAAG;IACxBd,IAAI,EAAE,KAAK;IACXe,UAAU,EAAE;EACd,CAAC;EACDpB,YAAY,CAACiB,aAAa,GAAG;IAC3BI,IAAI,EAAE,IAAI;IACVC,CAAC,EAAE,CAAC;IACJ;IACAC,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,KAAK;IACV;IACA;IACAC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE;MACTP,WAAW,EAAE,MAAM;MACnBQ,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRF,SAAS,EAAE;QACTP,WAAW,EAAE;MACf;IACF,CAAC;IACD;IACA;IACAU,OAAO,EAAE;MACPhB,IAAI,EAAE,KAAK;MACXiB,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,OAAOtC,YAAY;AACrB,CAAC,CAACD,cAAc,CAAC;AACjB,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}