{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as axisPointerModelHelper from './modelHelper.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar clone = zrUtil.clone;\nvar bind = zrUtil.bind;\n/**\r\n * Base axis pointer class in 2D.\r\n */\nvar BaseAxisPointer = /** @class */function () {\n  function BaseAxisPointer() {\n    this._dragging = false;\n    /**\r\n     * In px, arbitrary value. Do not set too small,\r\n     * no animation is ok for most cases.\r\n     */\n    this.animationThreshold = 15;\n  }\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.render = function (axisModel, axisPointerModel, api, forceRender) {\n    var value = axisPointerModel.get('value');\n    var status = axisPointerModel.get('status');\n    // Bind them to `this`, not in closure, otherwise they will not\n    // be replaced when user calling setOption in not merge mode.\n    this._axisModel = axisModel;\n    this._axisPointerModel = axisPointerModel;\n    this._api = api;\n    // Optimize: `render` will be called repeatedly during mouse move.\n    // So it is power consuming if performing `render` each time,\n    // especially on mobile device.\n    if (!forceRender && this._lastValue === value && this._lastStatus === status) {\n      return;\n    }\n    this._lastValue = value;\n    this._lastStatus = status;\n    var group = this._group;\n    var handle = this._handle;\n    if (!status || status === 'hide') {\n      // Do not clear here, for animation better.\n      group && group.hide();\n      handle && handle.hide();\n      return;\n    }\n    group && group.show();\n    handle && handle.show();\n    // Otherwise status is 'show'\n    var elOption = {};\n    this.makeElOption(elOption, value, axisModel, axisPointerModel, api);\n    // Enable change axis pointer type.\n    var graphicKey = elOption.graphicKey;\n    if (graphicKey !== this._lastGraphicKey) {\n      this.clear(api);\n    }\n    this._lastGraphicKey = graphicKey;\n    var moveAnimation = this._moveAnimation = this.determineAnimation(axisModel, axisPointerModel);\n    if (!group) {\n      group = this._group = new graphic.Group();\n      this.createPointerEl(group, elOption, axisModel, axisPointerModel);\n      this.createLabelEl(group, elOption, axisModel, axisPointerModel);\n      api.getZr().add(group);\n    } else {\n      var doUpdateProps = zrUtil.curry(updateProps, axisPointerModel, moveAnimation);\n      this.updatePointerEl(group, elOption, doUpdateProps);\n      this.updateLabelEl(group, elOption, doUpdateProps, axisPointerModel);\n    }\n    updateMandatoryProps(group, axisPointerModel, true);\n    this._renderHandle(value);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.remove = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.dispose = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.determineAnimation = function (axisModel, axisPointerModel) {\n    var animation = axisPointerModel.get('animation');\n    var axis = axisModel.axis;\n    var isCategoryAxis = axis.type === 'category';\n    var useSnap = axisPointerModel.get('snap');\n    // Value axis without snap always do not snap.\n    if (!useSnap && !isCategoryAxis) {\n      return false;\n    }\n    if (animation === 'auto' || animation == null) {\n      var animationThreshold = this.animationThreshold;\n      if (isCategoryAxis && axis.getBandWidth() > animationThreshold) {\n        return true;\n      }\n      // It is important to auto animation when snap used. Consider if there is\n      // a dataZoom, animation will be disabled when too many points exist, while\n      // it will be enabled for better visual effect when little points exist.\n      if (useSnap) {\n        var seriesDataCount = axisPointerModelHelper.getAxisInfo(axisModel).seriesDataCount;\n        var axisExtent = axis.getExtent();\n        // Approximate band width\n        return Math.abs(axisExtent[0] - axisExtent[1]) / seriesDataCount > animationThreshold;\n      }\n      return false;\n    }\n    return animation === true;\n  };\n  /**\r\n   * add {pointer, label, graphicKey} to elOption\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    // Should be implemenented by sub-class.\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createPointerEl = function (group, elOption, axisModel, axisPointerModel) {\n    var pointerOption = elOption.pointer;\n    if (pointerOption) {\n      var pointerEl = inner(group).pointerEl = new graphic[pointerOption.type](clone(elOption.pointer));\n      group.add(pointerEl);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createLabelEl = function (group, elOption, axisModel, axisPointerModel) {\n    if (elOption.label) {\n      var labelEl = inner(group).labelEl = new graphic.Text(clone(elOption.label));\n      group.add(labelEl);\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updatePointerEl = function (group, elOption, updateProps) {\n    var pointerEl = inner(group).pointerEl;\n    if (pointerEl && elOption.pointer) {\n      pointerEl.setStyle(elOption.pointer.style);\n      updateProps(pointerEl, {\n        shape: elOption.pointer.shape\n      });\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updateLabelEl = function (group, elOption, updateProps, axisPointerModel) {\n    var labelEl = inner(group).labelEl;\n    if (labelEl) {\n      labelEl.setStyle(elOption.label.style);\n      updateProps(labelEl, {\n        // Consider text length change in vertical axis, animation should\n        // be used on shape, otherwise the effect will be weird.\n        // TODOTODO\n        // shape: elOption.label.shape,\n        x: elOption.label.x,\n        y: elOption.label.y\n      });\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype._renderHandle = function (value) {\n    if (this._dragging || !this.updateHandleTransform) {\n      return;\n    }\n    var axisPointerModel = this._axisPointerModel;\n    var zr = this._api.getZr();\n    var handle = this._handle;\n    var handleModel = axisPointerModel.getModel('handle');\n    var status = axisPointerModel.get('status');\n    if (!handleModel.get('show') || !status || status === 'hide') {\n      handle && zr.remove(handle);\n      this._handle = null;\n      return;\n    }\n    var isInit;\n    if (!this._handle) {\n      isInit = true;\n      handle = this._handle = graphic.createIcon(handleModel.get('icon'), {\n        cursor: 'move',\n        draggable: true,\n        onmousemove: function (e) {\n          // For mobile device, prevent screen slider on the button.\n          eventTool.stop(e.event);\n        },\n        onmousedown: bind(this._onHandleDragMove, this, 0, 0),\n        drift: bind(this._onHandleDragMove, this),\n        ondragend: bind(this._onHandleDragEnd, this)\n      });\n      zr.add(handle);\n    }\n    updateMandatoryProps(handle, axisPointerModel, false);\n    // update style\n    handle.setStyle(handleModel.getItemStyle(null, ['color', 'borderColor', 'borderWidth', 'opacity', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY']));\n    // update position\n    var handleSize = handleModel.get('size');\n    if (!zrUtil.isArray(handleSize)) {\n      handleSize = [handleSize, handleSize];\n    }\n    handle.scaleX = handleSize[0] / 2;\n    handle.scaleY = handleSize[1] / 2;\n    throttleUtil.createOrUpdate(this, '_doDispatchAxisPointer', handleModel.get('throttle') || 0, 'fixRate');\n    this._moveHandleToValue(value, isInit);\n  };\n  BaseAxisPointer.prototype._moveHandleToValue = function (value, isInit) {\n    updateProps(this._axisPointerModel, !isInit && this._moveAnimation, this._handle, getHandleTransProps(this.getHandleTransform(value, this._axisModel, this._axisPointerModel)));\n  };\n  BaseAxisPointer.prototype._onHandleDragMove = function (dx, dy) {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    this._dragging = true;\n    // Persistent for throttle.\n    var trans = this.updateHandleTransform(getHandleTransProps(handle), [dx, dy], this._axisModel, this._axisPointerModel);\n    this._payloadInfo = trans;\n    handle.stopAnimation();\n    handle.attr(getHandleTransProps(trans));\n    inner(handle).lastProp = null;\n    this._doDispatchAxisPointer();\n  };\n  /**\r\n   * Throttled method.\r\n   */\n  BaseAxisPointer.prototype._doDispatchAxisPointer = function () {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var payloadInfo = this._payloadInfo;\n    var axisModel = this._axisModel;\n    this._api.dispatchAction({\n      type: 'updateAxisPointer',\n      x: payloadInfo.cursorPoint[0],\n      y: payloadInfo.cursorPoint[1],\n      tooltipOption: payloadInfo.tooltipOption,\n      axesInfo: [{\n        axisDim: axisModel.axis.dim,\n        axisIndex: axisModel.componentIndex\n      }]\n    });\n  };\n  BaseAxisPointer.prototype._onHandleDragEnd = function () {\n    this._dragging = false;\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var value = this._axisPointerModel.get('value');\n    // Consider snap or categroy axis, handle may be not consistent with\n    // axisPointer. So move handle to align the exact value position when\n    // drag ended.\n    this._moveHandleToValue(value);\n    // For the effect: tooltip will be shown when finger holding on handle\n    // button, and will be hidden after finger left handle button.\n    this._api.dispatchAction({\n      type: 'hideTip'\n    });\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype.clear = function (api) {\n    this._lastValue = null;\n    this._lastStatus = null;\n    var zr = api.getZr();\n    var group = this._group;\n    var handle = this._handle;\n    if (zr && group) {\n      this._lastGraphicKey = null;\n      group && zr.remove(group);\n      handle && zr.remove(handle);\n      this._group = null;\n      this._handle = null;\n      this._payloadInfo = null;\n    }\n    throttleUtil.clear(this, '_doDispatchAxisPointer');\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.doClear = function () {\n    // Implemented by sub-class if necessary.\n  };\n  BaseAxisPointer.prototype.buildLabel = function (xy, wh, xDimIndex) {\n    xDimIndex = xDimIndex || 0;\n    return {\n      x: xy[xDimIndex],\n      y: xy[1 - xDimIndex],\n      width: wh[xDimIndex],\n      height: wh[1 - xDimIndex]\n    };\n  };\n  return BaseAxisPointer;\n}();\nfunction updateProps(animationModel, moveAnimation, el, props) {\n  // Animation optimize.\n  if (!propsEqual(inner(el).lastProp, props)) {\n    inner(el).lastProp = props;\n    moveAnimation ? graphic.updateProps(el, props, animationModel) : (el.stopAnimation(), el.attr(props));\n  }\n}\nfunction propsEqual(lastProps, newProps) {\n  if (zrUtil.isObject(lastProps) && zrUtil.isObject(newProps)) {\n    var equals_1 = true;\n    zrUtil.each(newProps, function (item, key) {\n      equals_1 = equals_1 && propsEqual(lastProps[key], item);\n    });\n    return !!equals_1;\n  } else {\n    return lastProps === newProps;\n  }\n}\nfunction updateLabelShowHide(labelEl, axisPointerModel) {\n  labelEl[axisPointerModel.get(['label', 'show']) ? 'show' : 'hide']();\n}\nfunction getHandleTransProps(trans) {\n  return {\n    x: trans.x || 0,\n    y: trans.y || 0,\n    rotation: trans.rotation || 0\n  };\n}\nfunction updateMandatoryProps(group, axisPointerModel, silent) {\n  var z = axisPointerModel.get('z');\n  var zlevel = axisPointerModel.get('zlevel');\n  group && group.traverse(function (el) {\n    if (el.type !== 'group') {\n      z != null && (el.z = z);\n      zlevel != null && (el.zlevel = zlevel);\n      el.silent = silent;\n    }\n  });\n}\nexport default BaseAxisPointer;", "map": {"version": 3, "names": ["zrUtil", "graphic", "axisPointerModelHelper", "eventTool", "throttleUtil", "makeInner", "inner", "clone", "bind", "BaseAxisPointer", "_dragging", "animationThreshold", "prototype", "render", "axisModel", "axisPointerModel", "api", "forceRender", "value", "get", "status", "_axisModel", "_axisPointerModel", "_api", "_lastValue", "_lastStatus", "group", "_group", "handle", "_handle", "hide", "show", "elOption", "makeElOption", "<PERSON><PERSON><PERSON>", "_last<PERSON><PERSON><PERSON><PERSON>", "clear", "moveAnimation", "_moveAnimation", "determineAnimation", "Group", "createPointerEl", "createLabelEl", "getZr", "add", "doUpdateProps", "curry", "updateProps", "updatePointerEl", "updateLabelEl", "updateMandatoryProps", "_renderHandle", "remove", "dispose", "animation", "axis", "isCategoryAxis", "type", "useSnap", "getBandWidth", "seriesDataCount", "getAxisInfo", "axisExtent", "getExtent", "Math", "abs", "pointerOption", "pointer", "pointerEl", "label", "labelEl", "Text", "updateLabelShowHide", "setStyle", "style", "shape", "x", "y", "updateHandleTransform", "zr", "handleModel", "getModel", "isInit", "createIcon", "cursor", "draggable", "<PERSON><PERSON><PERSON><PERSON>", "e", "stop", "event", "onmousedown", "_onHandleDragMove", "drift", "ondragend", "_onHandleDragEnd", "getItemStyle", "handleSize", "isArray", "scaleX", "scaleY", "createOrUpdate", "_moveHandleToValue", "getHandleTransProps", "getHandleTransform", "dx", "dy", "trans", "_payloadInfo", "stopAnimation", "attr", "lastProp", "_doDispatchAxisPointer", "payloadInfo", "dispatchAction", "cursorPoint", "tooltipOption", "axesInfo", "axisDim", "dim", "axisIndex", "componentIndex", "doClear", "buildLabel", "xy", "wh", "xDimIndex", "width", "height", "animationModel", "el", "props", "propsEqual", "lastProps", "newProps", "isObject", "equals_1", "each", "item", "key", "rotation", "silent", "z", "zlevel", "traverse"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/axisPointer/BaseAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as axisPointerModelHelper from './modelHelper.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar clone = zrUtil.clone;\nvar bind = zrUtil.bind;\n/**\r\n * Base axis pointer class in 2D.\r\n */\nvar BaseAxisPointer = /** @class */function () {\n  function BaseAxisPointer() {\n    this._dragging = false;\n    /**\r\n     * In px, arbitrary value. Do not set too small,\r\n     * no animation is ok for most cases.\r\n     */\n    this.animationThreshold = 15;\n  }\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.render = function (axisModel, axisPointerModel, api, forceRender) {\n    var value = axisPointerModel.get('value');\n    var status = axisPointerModel.get('status');\n    // Bind them to `this`, not in closure, otherwise they will not\n    // be replaced when user calling setOption in not merge mode.\n    this._axisModel = axisModel;\n    this._axisPointerModel = axisPointerModel;\n    this._api = api;\n    // Optimize: `render` will be called repeatedly during mouse move.\n    // So it is power consuming if performing `render` each time,\n    // especially on mobile device.\n    if (!forceRender && this._lastValue === value && this._lastStatus === status) {\n      return;\n    }\n    this._lastValue = value;\n    this._lastStatus = status;\n    var group = this._group;\n    var handle = this._handle;\n    if (!status || status === 'hide') {\n      // Do not clear here, for animation better.\n      group && group.hide();\n      handle && handle.hide();\n      return;\n    }\n    group && group.show();\n    handle && handle.show();\n    // Otherwise status is 'show'\n    var elOption = {};\n    this.makeElOption(elOption, value, axisModel, axisPointerModel, api);\n    // Enable change axis pointer type.\n    var graphicKey = elOption.graphicKey;\n    if (graphicKey !== this._lastGraphicKey) {\n      this.clear(api);\n    }\n    this._lastGraphicKey = graphicKey;\n    var moveAnimation = this._moveAnimation = this.determineAnimation(axisModel, axisPointerModel);\n    if (!group) {\n      group = this._group = new graphic.Group();\n      this.createPointerEl(group, elOption, axisModel, axisPointerModel);\n      this.createLabelEl(group, elOption, axisModel, axisPointerModel);\n      api.getZr().add(group);\n    } else {\n      var doUpdateProps = zrUtil.curry(updateProps, axisPointerModel, moveAnimation);\n      this.updatePointerEl(group, elOption, doUpdateProps);\n      this.updateLabelEl(group, elOption, doUpdateProps, axisPointerModel);\n    }\n    updateMandatoryProps(group, axisPointerModel, true);\n    this._renderHandle(value);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.remove = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.dispose = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.determineAnimation = function (axisModel, axisPointerModel) {\n    var animation = axisPointerModel.get('animation');\n    var axis = axisModel.axis;\n    var isCategoryAxis = axis.type === 'category';\n    var useSnap = axisPointerModel.get('snap');\n    // Value axis without snap always do not snap.\n    if (!useSnap && !isCategoryAxis) {\n      return false;\n    }\n    if (animation === 'auto' || animation == null) {\n      var animationThreshold = this.animationThreshold;\n      if (isCategoryAxis && axis.getBandWidth() > animationThreshold) {\n        return true;\n      }\n      // It is important to auto animation when snap used. Consider if there is\n      // a dataZoom, animation will be disabled when too many points exist, while\n      // it will be enabled for better visual effect when little points exist.\n      if (useSnap) {\n        var seriesDataCount = axisPointerModelHelper.getAxisInfo(axisModel).seriesDataCount;\n        var axisExtent = axis.getExtent();\n        // Approximate band width\n        return Math.abs(axisExtent[0] - axisExtent[1]) / seriesDataCount > animationThreshold;\n      }\n      return false;\n    }\n    return animation === true;\n  };\n  /**\r\n   * add {pointer, label, graphicKey} to elOption\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    // Should be implemenented by sub-class.\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createPointerEl = function (group, elOption, axisModel, axisPointerModel) {\n    var pointerOption = elOption.pointer;\n    if (pointerOption) {\n      var pointerEl = inner(group).pointerEl = new graphic[pointerOption.type](clone(elOption.pointer));\n      group.add(pointerEl);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createLabelEl = function (group, elOption, axisModel, axisPointerModel) {\n    if (elOption.label) {\n      var labelEl = inner(group).labelEl = new graphic.Text(clone(elOption.label));\n      group.add(labelEl);\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updatePointerEl = function (group, elOption, updateProps) {\n    var pointerEl = inner(group).pointerEl;\n    if (pointerEl && elOption.pointer) {\n      pointerEl.setStyle(elOption.pointer.style);\n      updateProps(pointerEl, {\n        shape: elOption.pointer.shape\n      });\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updateLabelEl = function (group, elOption, updateProps, axisPointerModel) {\n    var labelEl = inner(group).labelEl;\n    if (labelEl) {\n      labelEl.setStyle(elOption.label.style);\n      updateProps(labelEl, {\n        // Consider text length change in vertical axis, animation should\n        // be used on shape, otherwise the effect will be weird.\n        // TODOTODO\n        // shape: elOption.label.shape,\n        x: elOption.label.x,\n        y: elOption.label.y\n      });\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype._renderHandle = function (value) {\n    if (this._dragging || !this.updateHandleTransform) {\n      return;\n    }\n    var axisPointerModel = this._axisPointerModel;\n    var zr = this._api.getZr();\n    var handle = this._handle;\n    var handleModel = axisPointerModel.getModel('handle');\n    var status = axisPointerModel.get('status');\n    if (!handleModel.get('show') || !status || status === 'hide') {\n      handle && zr.remove(handle);\n      this._handle = null;\n      return;\n    }\n    var isInit;\n    if (!this._handle) {\n      isInit = true;\n      handle = this._handle = graphic.createIcon(handleModel.get('icon'), {\n        cursor: 'move',\n        draggable: true,\n        onmousemove: function (e) {\n          // For mobile device, prevent screen slider on the button.\n          eventTool.stop(e.event);\n        },\n        onmousedown: bind(this._onHandleDragMove, this, 0, 0),\n        drift: bind(this._onHandleDragMove, this),\n        ondragend: bind(this._onHandleDragEnd, this)\n      });\n      zr.add(handle);\n    }\n    updateMandatoryProps(handle, axisPointerModel, false);\n    // update style\n    handle.setStyle(handleModel.getItemStyle(null, ['color', 'borderColor', 'borderWidth', 'opacity', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY']));\n    // update position\n    var handleSize = handleModel.get('size');\n    if (!zrUtil.isArray(handleSize)) {\n      handleSize = [handleSize, handleSize];\n    }\n    handle.scaleX = handleSize[0] / 2;\n    handle.scaleY = handleSize[1] / 2;\n    throttleUtil.createOrUpdate(this, '_doDispatchAxisPointer', handleModel.get('throttle') || 0, 'fixRate');\n    this._moveHandleToValue(value, isInit);\n  };\n  BaseAxisPointer.prototype._moveHandleToValue = function (value, isInit) {\n    updateProps(this._axisPointerModel, !isInit && this._moveAnimation, this._handle, getHandleTransProps(this.getHandleTransform(value, this._axisModel, this._axisPointerModel)));\n  };\n  BaseAxisPointer.prototype._onHandleDragMove = function (dx, dy) {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    this._dragging = true;\n    // Persistent for throttle.\n    var trans = this.updateHandleTransform(getHandleTransProps(handle), [dx, dy], this._axisModel, this._axisPointerModel);\n    this._payloadInfo = trans;\n    handle.stopAnimation();\n    handle.attr(getHandleTransProps(trans));\n    inner(handle).lastProp = null;\n    this._doDispatchAxisPointer();\n  };\n  /**\r\n   * Throttled method.\r\n   */\n  BaseAxisPointer.prototype._doDispatchAxisPointer = function () {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var payloadInfo = this._payloadInfo;\n    var axisModel = this._axisModel;\n    this._api.dispatchAction({\n      type: 'updateAxisPointer',\n      x: payloadInfo.cursorPoint[0],\n      y: payloadInfo.cursorPoint[1],\n      tooltipOption: payloadInfo.tooltipOption,\n      axesInfo: [{\n        axisDim: axisModel.axis.dim,\n        axisIndex: axisModel.componentIndex\n      }]\n    });\n  };\n  BaseAxisPointer.prototype._onHandleDragEnd = function () {\n    this._dragging = false;\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var value = this._axisPointerModel.get('value');\n    // Consider snap or categroy axis, handle may be not consistent with\n    // axisPointer. So move handle to align the exact value position when\n    // drag ended.\n    this._moveHandleToValue(value);\n    // For the effect: tooltip will be shown when finger holding on handle\n    // button, and will be hidden after finger left handle button.\n    this._api.dispatchAction({\n      type: 'hideTip'\n    });\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype.clear = function (api) {\n    this._lastValue = null;\n    this._lastStatus = null;\n    var zr = api.getZr();\n    var group = this._group;\n    var handle = this._handle;\n    if (zr && group) {\n      this._lastGraphicKey = null;\n      group && zr.remove(group);\n      handle && zr.remove(handle);\n      this._group = null;\n      this._handle = null;\n      this._payloadInfo = null;\n    }\n    throttleUtil.clear(this, '_doDispatchAxisPointer');\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.doClear = function () {\n    // Implemented by sub-class if necessary.\n  };\n  BaseAxisPointer.prototype.buildLabel = function (xy, wh, xDimIndex) {\n    xDimIndex = xDimIndex || 0;\n    return {\n      x: xy[xDimIndex],\n      y: xy[1 - xDimIndex],\n      width: wh[xDimIndex],\n      height: wh[1 - xDimIndex]\n    };\n  };\n  return BaseAxisPointer;\n}();\nfunction updateProps(animationModel, moveAnimation, el, props) {\n  // Animation optimize.\n  if (!propsEqual(inner(el).lastProp, props)) {\n    inner(el).lastProp = props;\n    moveAnimation ? graphic.updateProps(el, props, animationModel) : (el.stopAnimation(), el.attr(props));\n  }\n}\nfunction propsEqual(lastProps, newProps) {\n  if (zrUtil.isObject(lastProps) && zrUtil.isObject(newProps)) {\n    var equals_1 = true;\n    zrUtil.each(newProps, function (item, key) {\n      equals_1 = equals_1 && propsEqual(lastProps[key], item);\n    });\n    return !!equals_1;\n  } else {\n    return lastProps === newProps;\n  }\n}\nfunction updateLabelShowHide(labelEl, axisPointerModel) {\n  labelEl[axisPointerModel.get(['label', 'show']) ? 'show' : 'hide']();\n}\nfunction getHandleTransProps(trans) {\n  return {\n    x: trans.x || 0,\n    y: trans.y || 0,\n    rotation: trans.rotation || 0\n  };\n}\nfunction updateMandatoryProps(group, axisPointerModel, silent) {\n  var z = axisPointerModel.get('z');\n  var zlevel = axisPointerModel.get('zlevel');\n  group && group.traverse(function (el) {\n    if (el.type !== 'group') {\n      z != null && (el.z = z);\n      zlevel != null && (el.zlevel = zlevel);\n      el.silent = silent;\n    }\n  });\n}\nexport default BaseAxisPointer;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,sBAAsB,MAAM,kBAAkB;AAC1D,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAO,KAAKC,YAAY,MAAM,wBAAwB;AACtD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,IAAIE,KAAK,GAAGP,MAAM,CAACO,KAAK;AACxB,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI;AACtB;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACzB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACJ;AACA;AACA;IACI,IAAI,CAACC,kBAAkB,GAAG,EAAE;EAC9B;EACA;AACF;AACA;EACEF,eAAe,CAACG,SAAS,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,WAAW,EAAE;IAC1F,IAAIC,KAAK,GAAGH,gBAAgB,CAACI,GAAG,CAAC,OAAO,CAAC;IACzC,IAAIC,MAAM,GAAGL,gBAAgB,CAACI,GAAG,CAAC,QAAQ,CAAC;IAC3C;IACA;IACA,IAAI,CAACE,UAAU,GAAGP,SAAS;IAC3B,IAAI,CAACQ,iBAAiB,GAAGP,gBAAgB;IACzC,IAAI,CAACQ,IAAI,GAAGP,GAAG;IACf;IACA;IACA;IACA,IAAI,CAACC,WAAW,IAAI,IAAI,CAACO,UAAU,KAAKN,KAAK,IAAI,IAAI,CAACO,WAAW,KAAKL,MAAM,EAAE;MAC5E;IACF;IACA,IAAI,CAACI,UAAU,GAAGN,KAAK;IACvB,IAAI,CAACO,WAAW,GAAGL,MAAM;IACzB,IAAIM,KAAK,GAAG,IAAI,CAACC,MAAM;IACvB,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAI,CAACT,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE;MAChC;MACAM,KAAK,IAAIA,KAAK,CAACI,IAAI,CAAC,CAAC;MACrBF,MAAM,IAAIA,MAAM,CAACE,IAAI,CAAC,CAAC;MACvB;IACF;IACAJ,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,CAAC;IACrBH,MAAM,IAAIA,MAAM,CAACG,IAAI,CAAC,CAAC;IACvB;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,YAAY,CAACD,QAAQ,EAAEd,KAAK,EAAEJ,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,CAAC;IACpE;IACA,IAAIkB,UAAU,GAAGF,QAAQ,CAACE,UAAU;IACpC,IAAIA,UAAU,KAAK,IAAI,CAACC,eAAe,EAAE;MACvC,IAAI,CAACC,KAAK,CAACpB,GAAG,CAAC;IACjB;IACA,IAAI,CAACmB,eAAe,GAAGD,UAAU;IACjC,IAAIG,aAAa,GAAG,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACzB,SAAS,EAAEC,gBAAgB,CAAC;IAC9F,IAAI,CAACW,KAAK,EAAE;MACVA,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI1B,OAAO,CAACuC,KAAK,CAAC,CAAC;MACzC,IAAI,CAACC,eAAe,CAACf,KAAK,EAAEM,QAAQ,EAAElB,SAAS,EAAEC,gBAAgB,CAAC;MAClE,IAAI,CAAC2B,aAAa,CAAChB,KAAK,EAAEM,QAAQ,EAAElB,SAAS,EAAEC,gBAAgB,CAAC;MAChEC,GAAG,CAAC2B,KAAK,CAAC,CAAC,CAACC,GAAG,CAAClB,KAAK,CAAC;IACxB,CAAC,MAAM;MACL,IAAImB,aAAa,GAAG7C,MAAM,CAAC8C,KAAK,CAACC,WAAW,EAAEhC,gBAAgB,EAAEsB,aAAa,CAAC;MAC9E,IAAI,CAACW,eAAe,CAACtB,KAAK,EAAEM,QAAQ,EAAEa,aAAa,CAAC;MACpD,IAAI,CAACI,aAAa,CAACvB,KAAK,EAAEM,QAAQ,EAAEa,aAAa,EAAE9B,gBAAgB,CAAC;IACtE;IACAmC,oBAAoB,CAACxB,KAAK,EAAEX,gBAAgB,EAAE,IAAI,CAAC;IACnD,IAAI,CAACoC,aAAa,CAACjC,KAAK,CAAC;EAC3B,CAAC;EACD;AACF;AACA;EACET,eAAe,CAACG,SAAS,CAACwC,MAAM,GAAG,UAAUpC,GAAG,EAAE;IAChD,IAAI,CAACoB,KAAK,CAACpB,GAAG,CAAC;EACjB,CAAC;EACD;AACF;AACA;EACEP,eAAe,CAACG,SAAS,CAACyC,OAAO,GAAG,UAAUrC,GAAG,EAAE;IACjD,IAAI,CAACoB,KAAK,CAACpB,GAAG,CAAC;EACjB,CAAC;EACD;AACF;AACA;EACEP,eAAe,CAACG,SAAS,CAAC2B,kBAAkB,GAAG,UAAUzB,SAAS,EAAEC,gBAAgB,EAAE;IACpF,IAAIuC,SAAS,GAAGvC,gBAAgB,CAACI,GAAG,CAAC,WAAW,CAAC;IACjD,IAAIoC,IAAI,GAAGzC,SAAS,CAACyC,IAAI;IACzB,IAAIC,cAAc,GAAGD,IAAI,CAACE,IAAI,KAAK,UAAU;IAC7C,IAAIC,OAAO,GAAG3C,gBAAgB,CAACI,GAAG,CAAC,MAAM,CAAC;IAC1C;IACA,IAAI,CAACuC,OAAO,IAAI,CAACF,cAAc,EAAE;MAC/B,OAAO,KAAK;IACd;IACA,IAAIF,SAAS,KAAK,MAAM,IAAIA,SAAS,IAAI,IAAI,EAAE;MAC7C,IAAI3C,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;MAChD,IAAI6C,cAAc,IAAID,IAAI,CAACI,YAAY,CAAC,CAAC,GAAGhD,kBAAkB,EAAE;QAC9D,OAAO,IAAI;MACb;MACA;MACA;MACA;MACA,IAAI+C,OAAO,EAAE;QACX,IAAIE,eAAe,GAAG1D,sBAAsB,CAAC2D,WAAW,CAAC/C,SAAS,CAAC,CAAC8C,eAAe;QACnF,IAAIE,UAAU,GAAGP,IAAI,CAACQ,SAAS,CAAC,CAAC;QACjC;QACA,OAAOC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGF,eAAe,GAAGjD,kBAAkB;MACvF;MACA,OAAO,KAAK;IACd;IACA,OAAO2C,SAAS,KAAK,IAAI;EAC3B,CAAC;EACD;AACF;AACA;AACA;EACE7C,eAAe,CAACG,SAAS,CAACqB,YAAY,GAAG,UAAUD,QAAQ,EAAEd,KAAK,EAAEJ,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAE;IACpG;EAAA,CACD;EACD;AACF;AACA;EACEP,eAAe,CAACG,SAAS,CAAC6B,eAAe,GAAG,UAAUf,KAAK,EAAEM,QAAQ,EAAElB,SAAS,EAAEC,gBAAgB,EAAE;IAClG,IAAImD,aAAa,GAAGlC,QAAQ,CAACmC,OAAO;IACpC,IAAID,aAAa,EAAE;MACjB,IAAIE,SAAS,GAAG9D,KAAK,CAACoB,KAAK,CAAC,CAAC0C,SAAS,GAAG,IAAInE,OAAO,CAACiE,aAAa,CAACT,IAAI,CAAC,CAAClD,KAAK,CAACyB,QAAQ,CAACmC,OAAO,CAAC,CAAC;MACjGzC,KAAK,CAACkB,GAAG,CAACwB,SAAS,CAAC;IACtB;EACF,CAAC;EACD;AACF;AACA;EACE3D,eAAe,CAACG,SAAS,CAAC8B,aAAa,GAAG,UAAUhB,KAAK,EAAEM,QAAQ,EAAElB,SAAS,EAAEC,gBAAgB,EAAE;IAChG,IAAIiB,QAAQ,CAACqC,KAAK,EAAE;MAClB,IAAIC,OAAO,GAAGhE,KAAK,CAACoB,KAAK,CAAC,CAAC4C,OAAO,GAAG,IAAIrE,OAAO,CAACsE,IAAI,CAAChE,KAAK,CAACyB,QAAQ,CAACqC,KAAK,CAAC,CAAC;MAC5E3C,KAAK,CAACkB,GAAG,CAAC0B,OAAO,CAAC;MAClBE,mBAAmB,CAACF,OAAO,EAAEvD,gBAAgB,CAAC;IAChD;EACF,CAAC;EACD;AACF;AACA;EACEN,eAAe,CAACG,SAAS,CAACoC,eAAe,GAAG,UAAUtB,KAAK,EAAEM,QAAQ,EAAEe,WAAW,EAAE;IAClF,IAAIqB,SAAS,GAAG9D,KAAK,CAACoB,KAAK,CAAC,CAAC0C,SAAS;IACtC,IAAIA,SAAS,IAAIpC,QAAQ,CAACmC,OAAO,EAAE;MACjCC,SAAS,CAACK,QAAQ,CAACzC,QAAQ,CAACmC,OAAO,CAACO,KAAK,CAAC;MAC1C3B,WAAW,CAACqB,SAAS,EAAE;QACrBO,KAAK,EAAE3C,QAAQ,CAACmC,OAAO,CAACQ;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;EACElE,eAAe,CAACG,SAAS,CAACqC,aAAa,GAAG,UAAUvB,KAAK,EAAEM,QAAQ,EAAEe,WAAW,EAAEhC,gBAAgB,EAAE;IAClG,IAAIuD,OAAO,GAAGhE,KAAK,CAACoB,KAAK,CAAC,CAAC4C,OAAO;IAClC,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACG,QAAQ,CAACzC,QAAQ,CAACqC,KAAK,CAACK,KAAK,CAAC;MACtC3B,WAAW,CAACuB,OAAO,EAAE;QACnB;QACA;QACA;QACA;QACAM,CAAC,EAAE5C,QAAQ,CAACqC,KAAK,CAACO,CAAC;QACnBC,CAAC,EAAE7C,QAAQ,CAACqC,KAAK,CAACQ;MACpB,CAAC,CAAC;MACFL,mBAAmB,CAACF,OAAO,EAAEvD,gBAAgB,CAAC;IAChD;EACF,CAAC;EACD;AACF;AACA;EACEN,eAAe,CAACG,SAAS,CAACuC,aAAa,GAAG,UAAUjC,KAAK,EAAE;IACzD,IAAI,IAAI,CAACR,SAAS,IAAI,CAAC,IAAI,CAACoE,qBAAqB,EAAE;MACjD;IACF;IACA,IAAI/D,gBAAgB,GAAG,IAAI,CAACO,iBAAiB;IAC7C,IAAIyD,EAAE,GAAG,IAAI,CAACxD,IAAI,CAACoB,KAAK,CAAC,CAAC;IAC1B,IAAIf,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAImD,WAAW,GAAGjE,gBAAgB,CAACkE,QAAQ,CAAC,QAAQ,CAAC;IACrD,IAAI7D,MAAM,GAAGL,gBAAgB,CAACI,GAAG,CAAC,QAAQ,CAAC;IAC3C,IAAI,CAAC6D,WAAW,CAAC7D,GAAG,CAAC,MAAM,CAAC,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE;MAC5DQ,MAAM,IAAImD,EAAE,CAAC3B,MAAM,CAACxB,MAAM,CAAC;MAC3B,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB;IACF;IACA,IAAIqD,MAAM;IACV,IAAI,CAAC,IAAI,CAACrD,OAAO,EAAE;MACjBqD,MAAM,GAAG,IAAI;MACbtD,MAAM,GAAG,IAAI,CAACC,OAAO,GAAG5B,OAAO,CAACkF,UAAU,CAACH,WAAW,CAAC7D,GAAG,CAAC,MAAM,CAAC,EAAE;QAClEiE,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;UACxB;UACApF,SAAS,CAACqF,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;QACzB,CAAC;QACDC,WAAW,EAAElF,IAAI,CAAC,IAAI,CAACmF,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QACrDC,KAAK,EAAEpF,IAAI,CAAC,IAAI,CAACmF,iBAAiB,EAAE,IAAI,CAAC;QACzCE,SAAS,EAAErF,IAAI,CAAC,IAAI,CAACsF,gBAAgB,EAAE,IAAI;MAC7C,CAAC,CAAC;MACFf,EAAE,CAACnC,GAAG,CAAChB,MAAM,CAAC;IAChB;IACAsB,oBAAoB,CAACtB,MAAM,EAAEb,gBAAgB,EAAE,KAAK,CAAC;IACrD;IACAa,MAAM,CAAC6C,QAAQ,CAACO,WAAW,CAACe,YAAY,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC;IAClK;IACA,IAAIC,UAAU,GAAGhB,WAAW,CAAC7D,GAAG,CAAC,MAAM,CAAC;IACxC,IAAI,CAACnB,MAAM,CAACiG,OAAO,CAACD,UAAU,CAAC,EAAE;MAC/BA,UAAU,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;IACvC;IACApE,MAAM,CAACsE,MAAM,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IACjCpE,MAAM,CAACuE,MAAM,GAAGH,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IACjC5F,YAAY,CAACgG,cAAc,CAAC,IAAI,EAAE,wBAAwB,EAAEpB,WAAW,CAAC7D,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC;IACxG,IAAI,CAACkF,kBAAkB,CAACnF,KAAK,EAAEgE,MAAM,CAAC;EACxC,CAAC;EACDzE,eAAe,CAACG,SAAS,CAACyF,kBAAkB,GAAG,UAAUnF,KAAK,EAAEgE,MAAM,EAAE;IACtEnC,WAAW,CAAC,IAAI,CAACzB,iBAAiB,EAAE,CAAC4D,MAAM,IAAI,IAAI,CAAC5C,cAAc,EAAE,IAAI,CAACT,OAAO,EAAEyE,mBAAmB,CAAC,IAAI,CAACC,kBAAkB,CAACrF,KAAK,EAAE,IAAI,CAACG,UAAU,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;EACjL,CAAC;EACDb,eAAe,CAACG,SAAS,CAAC+E,iBAAiB,GAAG,UAAUa,EAAE,EAAEC,EAAE,EAAE;IAC9D,IAAI7E,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IACA,IAAI,CAAClB,SAAS,GAAG,IAAI;IACrB;IACA,IAAIgG,KAAK,GAAG,IAAI,CAAC5B,qBAAqB,CAACwB,mBAAmB,CAAC1E,MAAM,CAAC,EAAE,CAAC4E,EAAE,EAAEC,EAAE,CAAC,EAAE,IAAI,CAACpF,UAAU,EAAE,IAAI,CAACC,iBAAiB,CAAC;IACtH,IAAI,CAACqF,YAAY,GAAGD,KAAK;IACzB9E,MAAM,CAACgF,aAAa,CAAC,CAAC;IACtBhF,MAAM,CAACiF,IAAI,CAACP,mBAAmB,CAACI,KAAK,CAAC,CAAC;IACvCpG,KAAK,CAACsB,MAAM,CAAC,CAACkF,QAAQ,GAAG,IAAI;IAC7B,IAAI,CAACC,sBAAsB,CAAC,CAAC;EAC/B,CAAC;EACD;AACF;AACA;EACEtG,eAAe,CAACG,SAAS,CAACmG,sBAAsB,GAAG,YAAY;IAC7D,IAAInF,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IACA,IAAIoF,WAAW,GAAG,IAAI,CAACL,YAAY;IACnC,IAAI7F,SAAS,GAAG,IAAI,CAACO,UAAU;IAC/B,IAAI,CAACE,IAAI,CAAC0F,cAAc,CAAC;MACvBxD,IAAI,EAAE,mBAAmB;MACzBmB,CAAC,EAAEoC,WAAW,CAACE,WAAW,CAAC,CAAC,CAAC;MAC7BrC,CAAC,EAAEmC,WAAW,CAACE,WAAW,CAAC,CAAC,CAAC;MAC7BC,aAAa,EAAEH,WAAW,CAACG,aAAa;MACxCC,QAAQ,EAAE,CAAC;QACTC,OAAO,EAAEvG,SAAS,CAACyC,IAAI,CAAC+D,GAAG;QAC3BC,SAAS,EAAEzG,SAAS,CAAC0G;MACvB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD/G,eAAe,CAACG,SAAS,CAACkF,gBAAgB,GAAG,YAAY;IACvD,IAAI,CAACpF,SAAS,GAAG,KAAK;IACtB,IAAIkB,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IACA,IAAIV,KAAK,GAAG,IAAI,CAACI,iBAAiB,CAACH,GAAG,CAAC,OAAO,CAAC;IAC/C;IACA;IACA;IACA,IAAI,CAACkF,kBAAkB,CAACnF,KAAK,CAAC;IAC9B;IACA;IACA,IAAI,CAACK,IAAI,CAAC0F,cAAc,CAAC;MACvBxD,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEhD,eAAe,CAACG,SAAS,CAACwB,KAAK,GAAG,UAAUpB,GAAG,EAAE;IAC/C,IAAI,CAACQ,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAIsD,EAAE,GAAG/D,GAAG,CAAC2B,KAAK,CAAC,CAAC;IACpB,IAAIjB,KAAK,GAAG,IAAI,CAACC,MAAM;IACvB,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIkD,EAAE,IAAIrD,KAAK,EAAE;MACf,IAAI,CAACS,eAAe,GAAG,IAAI;MAC3BT,KAAK,IAAIqD,EAAE,CAAC3B,MAAM,CAAC1B,KAAK,CAAC;MACzBE,MAAM,IAAImD,EAAE,CAAC3B,MAAM,CAACxB,MAAM,CAAC;MAC3B,IAAI,CAACD,MAAM,GAAG,IAAI;MAClB,IAAI,CAACE,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC8E,YAAY,GAAG,IAAI;IAC1B;IACAvG,YAAY,CAACgC,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpD,CAAC;EACD;AACF;AACA;EACE3B,eAAe,CAACG,SAAS,CAAC6G,OAAO,GAAG,YAAY;IAC9C;EAAA,CACD;EACDhH,eAAe,CAACG,SAAS,CAAC8G,UAAU,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;IAClEA,SAAS,GAAGA,SAAS,IAAI,CAAC;IAC1B,OAAO;MACLjD,CAAC,EAAE+C,EAAE,CAACE,SAAS,CAAC;MAChBhD,CAAC,EAAE8C,EAAE,CAAC,CAAC,GAAGE,SAAS,CAAC;MACpBC,KAAK,EAAEF,EAAE,CAACC,SAAS,CAAC;MACpBE,MAAM,EAAEH,EAAE,CAAC,CAAC,GAAGC,SAAS;IAC1B,CAAC;EACH,CAAC;EACD,OAAOpH,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,SAASsC,WAAWA,CAACiF,cAAc,EAAE3F,aAAa,EAAE4F,EAAE,EAAEC,KAAK,EAAE;EAC7D;EACA,IAAI,CAACC,UAAU,CAAC7H,KAAK,CAAC2H,EAAE,CAAC,CAACnB,QAAQ,EAAEoB,KAAK,CAAC,EAAE;IAC1C5H,KAAK,CAAC2H,EAAE,CAAC,CAACnB,QAAQ,GAAGoB,KAAK;IAC1B7F,aAAa,GAAGpC,OAAO,CAAC8C,WAAW,CAACkF,EAAE,EAAEC,KAAK,EAAEF,cAAc,CAAC,IAAIC,EAAE,CAACrB,aAAa,CAAC,CAAC,EAAEqB,EAAE,CAACpB,IAAI,CAACqB,KAAK,CAAC,CAAC;EACvG;AACF;AACA,SAASC,UAAUA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACvC,IAAIrI,MAAM,CAACsI,QAAQ,CAACF,SAAS,CAAC,IAAIpI,MAAM,CAACsI,QAAQ,CAACD,QAAQ,CAAC,EAAE;IAC3D,IAAIE,QAAQ,GAAG,IAAI;IACnBvI,MAAM,CAACwI,IAAI,CAACH,QAAQ,EAAE,UAAUI,IAAI,EAAEC,GAAG,EAAE;MACzCH,QAAQ,GAAGA,QAAQ,IAAIJ,UAAU,CAACC,SAAS,CAACM,GAAG,CAAC,EAAED,IAAI,CAAC;IACzD,CAAC,CAAC;IACF,OAAO,CAAC,CAACF,QAAQ;EACnB,CAAC,MAAM;IACL,OAAOH,SAAS,KAAKC,QAAQ;EAC/B;AACF;AACA,SAAS7D,mBAAmBA,CAACF,OAAO,EAAEvD,gBAAgB,EAAE;EACtDuD,OAAO,CAACvD,gBAAgB,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;AACtE;AACA,SAASmF,mBAAmBA,CAACI,KAAK,EAAE;EAClC,OAAO;IACL9B,CAAC,EAAE8B,KAAK,CAAC9B,CAAC,IAAI,CAAC;IACfC,CAAC,EAAE6B,KAAK,CAAC7B,CAAC,IAAI,CAAC;IACf8D,QAAQ,EAAEjC,KAAK,CAACiC,QAAQ,IAAI;EAC9B,CAAC;AACH;AACA,SAASzF,oBAAoBA,CAACxB,KAAK,EAAEX,gBAAgB,EAAE6H,MAAM,EAAE;EAC7D,IAAIC,CAAC,GAAG9H,gBAAgB,CAACI,GAAG,CAAC,GAAG,CAAC;EACjC,IAAI2H,MAAM,GAAG/H,gBAAgB,CAACI,GAAG,CAAC,QAAQ,CAAC;EAC3CO,KAAK,IAAIA,KAAK,CAACqH,QAAQ,CAAC,UAAUd,EAAE,EAAE;IACpC,IAAIA,EAAE,CAACxE,IAAI,KAAK,OAAO,EAAE;MACvBoF,CAAC,IAAI,IAAI,KAAKZ,EAAE,CAACY,CAAC,GAAGA,CAAC,CAAC;MACvBC,MAAM,IAAI,IAAI,KAAKb,EAAE,CAACa,MAAM,GAAGA,MAAM,CAAC;MACtCb,EAAE,CAACW,MAAM,GAAGA,MAAM;IACpB;EACF,CAAC,CAAC;AACJ;AACA,eAAenI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}