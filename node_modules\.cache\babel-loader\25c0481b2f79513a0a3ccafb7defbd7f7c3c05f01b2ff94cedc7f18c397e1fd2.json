{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// FIXME Where to create the simple view coordinate system\nimport View from '../../coord/View.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport * as bbox from 'zrender/lib/core/bbox.js';\nimport { extend } from 'zrender/lib/core/util.js';\nfunction getViewRect(seriesModel, api, aspect) {\n  var option = extend(seriesModel.getBoxLayoutParams(), {\n    aspect: aspect\n  });\n  return getLayoutRect(option, {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport default function createViewCoordSys(ecModel, api) {\n  var viewList = [];\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var coordSysType = seriesModel.get('coordinateSystem');\n    if (!coordSysType || coordSysType === 'view') {\n      var data_1 = seriesModel.getData();\n      var positions = data_1.mapArray(function (idx) {\n        var itemModel = data_1.getItemModel(idx);\n        return [+itemModel.get('x'), +itemModel.get('y')];\n      });\n      var min = [];\n      var max = [];\n      bbox.fromPoints(positions, min, max);\n      // If width or height is 0\n      if (max[0] - min[0] === 0) {\n        max[0] += 1;\n        min[0] -= 1;\n      }\n      if (max[1] - min[1] === 0) {\n        max[1] += 1;\n        min[1] -= 1;\n      }\n      var aspect = (max[0] - min[0]) / (max[1] - min[1]);\n      // FIXME If get view rect after data processed?\n      var viewRect = getViewRect(seriesModel, api, aspect);\n      // Position may be NaN, use view rect instead\n      if (isNaN(aspect)) {\n        min = [viewRect.x, viewRect.y];\n        max = [viewRect.x + viewRect.width, viewRect.y + viewRect.height];\n      }\n      var bbWidth = max[0] - min[0];\n      var bbHeight = max[1] - min[1];\n      var viewWidth = viewRect.width;\n      var viewHeight = viewRect.height;\n      var viewCoordSys = seriesModel.coordinateSystem = new View();\n      viewCoordSys.zoomLimit = seriesModel.get('scaleLimit');\n      viewCoordSys.setBoundingRect(min[0], min[1], bbWidth, bbHeight);\n      viewCoordSys.setViewRect(viewRect.x, viewRect.y, viewWidth, viewHeight);\n      // Update roam info\n      viewCoordSys.setCenter(seriesModel.get('center'), api);\n      viewCoordSys.setZoom(seriesModel.get('zoom'));\n      viewList.push(viewCoordSys);\n    }\n  });\n  return viewList;\n}", "map": {"version": 3, "names": ["View", "getLayoutRect", "bbox", "extend", "getViewRect", "seriesModel", "api", "aspect", "option", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "createViewCoordSys", "ecModel", "viewList", "eachSeriesByType", "coordSysType", "get", "data_1", "getData", "positions", "mapArray", "idx", "itemModel", "getItemModel", "min", "max", "fromPoints", "viewRect", "isNaN", "x", "y", "bb<PERSON><PERSON><PERSON>", "bbHeight", "viewWidth", "viewHeight", "viewCoordSys", "coordinateSystem", "zoomLimit", "setBoundingRect", "setViewRect", "setCenter", "setZoom", "push"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/graph/createView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// FIXME Where to create the simple view coordinate system\nimport View from '../../coord/View.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport * as bbox from 'zrender/lib/core/bbox.js';\nimport { extend } from 'zrender/lib/core/util.js';\nfunction getViewRect(seriesModel, api, aspect) {\n  var option = extend(seriesModel.getBoxLayoutParams(), {\n    aspect: aspect\n  });\n  return getLayoutRect(option, {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport default function createViewCoordSys(ecModel, api) {\n  var viewList = [];\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var coordSysType = seriesModel.get('coordinateSystem');\n    if (!coordSysType || coordSysType === 'view') {\n      var data_1 = seriesModel.getData();\n      var positions = data_1.mapArray(function (idx) {\n        var itemModel = data_1.getItemModel(idx);\n        return [+itemModel.get('x'), +itemModel.get('y')];\n      });\n      var min = [];\n      var max = [];\n      bbox.fromPoints(positions, min, max);\n      // If width or height is 0\n      if (max[0] - min[0] === 0) {\n        max[0] += 1;\n        min[0] -= 1;\n      }\n      if (max[1] - min[1] === 0) {\n        max[1] += 1;\n        min[1] -= 1;\n      }\n      var aspect = (max[0] - min[0]) / (max[1] - min[1]);\n      // FIXME If get view rect after data processed?\n      var viewRect = getViewRect(seriesModel, api, aspect);\n      // Position may be NaN, use view rect instead\n      if (isNaN(aspect)) {\n        min = [viewRect.x, viewRect.y];\n        max = [viewRect.x + viewRect.width, viewRect.y + viewRect.height];\n      }\n      var bbWidth = max[0] - min[0];\n      var bbHeight = max[1] - min[1];\n      var viewWidth = viewRect.width;\n      var viewHeight = viewRect.height;\n      var viewCoordSys = seriesModel.coordinateSystem = new View();\n      viewCoordSys.zoomLimit = seriesModel.get('scaleLimit');\n      viewCoordSys.setBoundingRect(min[0], min[1], bbWidth, bbHeight);\n      viewCoordSys.setViewRect(viewRect.x, viewRect.y, viewWidth, viewHeight);\n      // Update roam info\n      viewCoordSys.setCenter(seriesModel.get('center'), api);\n      viewCoordSys.setZoom(seriesModel.get('zoom'));\n      viewList.push(viewCoordSys);\n    }\n  });\n  return viewList;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,IAAI,MAAM,qBAAqB;AACtC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,WAAWA,CAACC,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC7C,IAAIC,MAAM,GAAGL,MAAM,CAACE,WAAW,CAACI,kBAAkB,CAAC,CAAC,EAAE;IACpDF,MAAM,EAAEA;EACV,CAAC,CAAC;EACF,OAAON,aAAa,CAACO,MAAM,EAAE;IAC3BE,KAAK,EAAEJ,GAAG,CAACK,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAEN,GAAG,CAACO,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,eAAe,SAASC,kBAAkBA,CAACC,OAAO,EAAET,GAAG,EAAE;EACvD,IAAIU,QAAQ,GAAG,EAAE;EACjBD,OAAO,CAACE,gBAAgB,CAAC,OAAO,EAAE,UAAUZ,WAAW,EAAE;IACvD,IAAIa,YAAY,GAAGb,WAAW,CAACc,GAAG,CAAC,kBAAkB,CAAC;IACtD,IAAI,CAACD,YAAY,IAAIA,YAAY,KAAK,MAAM,EAAE;MAC5C,IAAIE,MAAM,GAAGf,WAAW,CAACgB,OAAO,CAAC,CAAC;MAClC,IAAIC,SAAS,GAAGF,MAAM,CAACG,QAAQ,CAAC,UAAUC,GAAG,EAAE;QAC7C,IAAIC,SAAS,GAAGL,MAAM,CAACM,YAAY,CAACF,GAAG,CAAC;QACxC,OAAO,CAAC,CAACC,SAAS,CAACN,GAAG,CAAC,GAAG,CAAC,EAAE,CAACM,SAAS,CAACN,GAAG,CAAC,GAAG,CAAC,CAAC;MACnD,CAAC,CAAC;MACF,IAAIQ,GAAG,GAAG,EAAE;MACZ,IAAIC,GAAG,GAAG,EAAE;MACZ1B,IAAI,CAAC2B,UAAU,CAACP,SAAS,EAAEK,GAAG,EAAEC,GAAG,CAAC;MACpC;MACA,IAAIA,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACzBC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QACXD,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;MACb;MACA,IAAIC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACzBC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QACXD,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;MACb;MACA,IAAIpB,MAAM,GAAG,CAACqB,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD;MACA,IAAIG,QAAQ,GAAG1B,WAAW,CAACC,WAAW,EAAEC,GAAG,EAAEC,MAAM,CAAC;MACpD;MACA,IAAIwB,KAAK,CAACxB,MAAM,CAAC,EAAE;QACjBoB,GAAG,GAAG,CAACG,QAAQ,CAACE,CAAC,EAAEF,QAAQ,CAACG,CAAC,CAAC;QAC9BL,GAAG,GAAG,CAACE,QAAQ,CAACE,CAAC,GAAGF,QAAQ,CAACpB,KAAK,EAAEoB,QAAQ,CAACG,CAAC,GAAGH,QAAQ,CAAClB,MAAM,CAAC;MACnE;MACA,IAAIsB,OAAO,GAAGN,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC;MAC7B,IAAIQ,QAAQ,GAAGP,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC;MAC9B,IAAIS,SAAS,GAAGN,QAAQ,CAACpB,KAAK;MAC9B,IAAI2B,UAAU,GAAGP,QAAQ,CAAClB,MAAM;MAChC,IAAI0B,YAAY,GAAGjC,WAAW,CAACkC,gBAAgB,GAAG,IAAIvC,IAAI,CAAC,CAAC;MAC5DsC,YAAY,CAACE,SAAS,GAAGnC,WAAW,CAACc,GAAG,CAAC,YAAY,CAAC;MACtDmB,YAAY,CAACG,eAAe,CAACd,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEO,OAAO,EAAEC,QAAQ,CAAC;MAC/DG,YAAY,CAACI,WAAW,CAACZ,QAAQ,CAACE,CAAC,EAAEF,QAAQ,CAACG,CAAC,EAAEG,SAAS,EAAEC,UAAU,CAAC;MACvE;MACAC,YAAY,CAACK,SAAS,CAACtC,WAAW,CAACc,GAAG,CAAC,QAAQ,CAAC,EAAEb,GAAG,CAAC;MACtDgC,YAAY,CAACM,OAAO,CAACvC,WAAW,CAACc,GAAG,CAAC,MAAM,CAAC,CAAC;MAC7CH,QAAQ,CAAC6B,IAAI,CAACP,YAAY,CAAC;IAC7B;EACF,CAAC,CAAC;EACF,OAAOtB,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}