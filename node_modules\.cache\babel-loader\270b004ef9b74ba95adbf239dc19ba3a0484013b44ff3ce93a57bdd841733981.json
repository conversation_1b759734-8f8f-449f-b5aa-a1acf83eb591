{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>',\n  data() {\n    return {\n      myChart: null\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\"个人\", \"单位\"];\n      var colors = [{\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#1e4a72\"\n        }, {\n          offset: 0.5,\n          color: \"#2d6ba3\"\n        }, {\n          offset: 1,\n          color: \"#5fb3f0\"\n        }]\n      }, {\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#0f3a5f\"\n        }, {\n          offset: 0.5,\n          color: \"#1e5a8a\"\n        }, {\n          offset: 1,\n          color: \"#4da6d9\"\n        }]\n      }];\n      var renderItem = function (params, api) {\n        var dataIndex = params.dataIndex;\n        var value = api.value(1);\n\n        // 调整高度比例，避免差距过大\n        var normalizedHeight;\n        if (value >= 50) {\n          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例\n        } else {\n          normalizedHeight = 30 + value * 0.6; // 小值适当放大\n        }\n        var start = api.coord([dataIndex, 0]);\n        var end = api.coord([dataIndex, normalizedHeight]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseWidth = height * 0.6;\n        var x = start[0];\n        var y = end[1];\n\n        // 创建左右两个独立的三角形\n        var topPoint = [x, y]; // 顶点\n        var bottomCenter = [x, start[1]]; // 底面中心点\n        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点\n        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点\n\n        var group = {\n          type: \"group\",\n          children: [\n          // 左三角形\n          {\n            type: \"polygon\",\n            z2: 3,\n            shape: {\n              points: [topPoint, bottomLeft, bottomCenter]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: style.fill.colorStops\n              },\n              stroke: 'rgba(255,255,255,0.2)',\n              lineWidth: 1\n            }\n          },\n          // 右三角形\n          {\n            type: \"polygon\",\n            z2: 3,\n            shape: {\n              points: [topPoint, bottomCenter, bottomRight]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: style.fill.colorStops[0].color\n                }, {\n                  offset: 0.5,\n                  color: style.fill.colorStops[1].color\n                }, {\n                  offset: 1,\n                  color: style.fill.colorStops[2].color\n                }]\n              },\n              stroke: 'rgba(255,255,255,0.2)',\n              lineWidth: 1,\n              opacity: 0.8\n            }\n          },\n          // 中间分割线\n          {\n            type: \"line\",\n            z2: 4,\n            shape: {\n              x1: topPoint[0],\n              y1: topPoint[1],\n              x2: bottomCenter[0],\n              y2: bottomCenter[1]\n            },\n            style: {\n              stroke: 'rgba(255,255,255,0.3)',\n              lineWidth: 1.5\n            }\n          }]\n        };\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4)\n          },\n          formatter: function (params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false\n        },\n        xAxis: {\n          data: xData,\n          show: false // 隐藏X轴\n        },\n        yAxis: {\n          show: false,\n          // 隐藏Y轴\n          max: 100\n        },\n        series: [{\n          type: \"custom\",\n          itemStyle: {\n            color: function (params) {\n              return colors[params.dataIndex];\n            }\n          },\n          label: {\n            show: true,\n            position: [0, -30],\n            color: \"#ffffff\",\n            fontSize: this.fontSize(0.8),\n            fontWeight: 'bold',\n            formatter: function (params) {\n              return params.data + '%';\n            },\n            align: 'center'\n          },\n          data: data,\n          renderItem: renderItem\n        },\n        // 添加底部标签\n        {\n          type: \"custom\",\n          renderItem: function (params, api) {\n            var x = api.coord([params.dataIndex, 0])[0];\n            var y = api.coord([params.dataIndex, 0])[1] + 40;\n            return {\n              type: 'group',\n              children: [\n              // 白色虚线\n              {\n                type: 'line',\n                shape: {\n                  x1: x,\n                  y1: y - 30,\n                  x2: x,\n                  y2: y - 10\n                },\n                style: {\n                  stroke: '#ffffff',\n                  lineWidth: 2,\n                  lineDash: [5, 5],\n                  opacity: 0.8\n                }\n              },\n              // 标签文字\n              {\n                type: 'text',\n                style: {\n                  text: xData[params.dataIndex],\n                  x: x,\n                  y: y,\n                  textAlign: 'center',\n                  textVerticalAlign: 'top',\n                  fill: '#ffffff',\n                  fontSize: 16,\n                  fontWeight: 'normal'\n                }\n              }]\n            };\n          },\n          data: data\n        }]\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "myChart", "mounted", "$refs", "volumn", "reload<PERSON><PERSON>", "window", "addEventListener", "beforeUnmount", "dispose<PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON>", "$echarts", "init", "xData", "colors", "type", "x", "y", "x2", "y2", "global", "colorStops", "offset", "color", "renderItem", "params", "api", "dataIndex", "value", "normalizedHeight", "start", "coord", "end", "style", "height", "baseWidth", "topPoint", "bottomCenter", "bottomLeft", "bottomRight", "group", "children", "z2", "shape", "points", "fill", "stroke", "lineWidth", "opacity", "x1", "y1", "option", "title", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "formatter", "grid", "bottom", "right", "containLabel", "xAxis", "show", "yAxis", "max", "series", "itemStyle", "label", "position", "align", "lineDash", "textAlign", "textVerticalAlign", "setOption", "res", "clientWidth", "innerWidth", "document", "documentElement", "body", "dispose"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\n        \"个人\",\n        \"单位\",\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#1e4a72\",\n            },\n            {\n              offset: 0.5,\n              color: \"#2d6ba3\",\n            },\n            {\n              offset: 1,\n              color: \"#5fb3f0\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#0f3a5f\",\n            },\n            {\n              offset: 0.5,\n              color: \"#1e5a8a\",\n            },\n            {\n              offset: 1,\n              color: \"#4da6d9\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var dataIndex = params.dataIndex;\n        var value = api.value(1);\n\n        // 调整高度比例，避免差距过大\n        var normalizedHeight;\n        if (value >= 50) {\n          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例\n        } else {\n          normalizedHeight = 30 + value * 0.6; // 小值适当放大\n        }\n\n        var start = api.coord([dataIndex, 0]);\n        var end = api.coord([dataIndex, normalizedHeight]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseWidth = height * 0.6;\n        var x = start[0];\n        var y = end[1];\n\n        // 创建左右两个独立的三角形\n        var topPoint = [x, y]; // 顶点\n        var bottomCenter = [x, start[1]]; // 底面中心点\n        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点\n        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点\n\n        var group = {\n          type: \"group\",\n          children: [\n            // 左三角形\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomLeft, bottomCenter],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: style.fill.colorStops\n                },\n                stroke: 'rgba(255,255,255,0.2)',\n                lineWidth: 1,\n              },\n            },\n            // 右三角形\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomCenter, bottomRight],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 0.5, color: style.fill.colorStops[1].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                stroke: 'rgba(255,255,255,0.2)',\n                lineWidth: 1,\n                opacity: 0.8\n              },\n            },\n            // 中间分割线\n            {\n              type: \"line\",\n              z2: 4,\n              shape: {\n                x1: topPoint[0],\n                y1: topPoint[1],\n                x2: bottomCenter[0],\n                y2: bottomCenter[1]\n              },\n              style: {\n                stroke: 'rgba(255,255,255,0.3)',\n                lineWidth: 1.5,\n              },\n            },\n          ],\n        };\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n          },\n          formatter: function(params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false,\n        },\n        xAxis: {\n          data: xData,\n          show: false, // 隐藏X轴\n        },\n        yAxis: {\n          show: false, // 隐藏Y轴\n          max: 100,\n        },\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n            label: {\n              show: true,\n              position: [0, -30],\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.8),\n              fontWeight: 'bold',\n              formatter: function(params) {\n                return params.data + '%';\n              },\n              align: 'center'\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n          // 添加底部标签\n          {\n            type: \"custom\",\n            renderItem: function(params, api) {\n              var x = api.coord([params.dataIndex, 0])[0];\n              var y = api.coord([params.dataIndex, 0])[1] + 40;\n\n              return {\n                type: 'group',\n                children: [\n                  // 白色虚线\n                  {\n                    type: 'line',\n                    shape: {\n                      x1: x,\n                      y1: y - 30,\n                      x2: x,\n                      y2: y - 10\n                    },\n                    style: {\n                      stroke: '#ffffff',\n                      lineWidth: 2,\n                      lineDash: [5, 5],\n                      opacity: 0.8\n                    }\n                  },\n                  // 标签文字\n                  {\n                    type: 'text',\n                    style: {\n                      text: xData[params.dataIndex],\n                      x: x,\n                      y: y,\n                      textAlign: 'center',\n                      textVerticalAlign: 'top',\n                      fill: '#ffffff',\n                      fontSize: 16,\n                      fontWeight: 'normal'\n                    }\n                  }\n                ]\n              };\n            },\n            data: data,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,EAAE;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;MAClB;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC,IAAI,CAACF,WAAW,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACAG,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,OAAO,EAAE;IACPC,SAASA,CAAA,EAAG;MACV,IAAI,CAACV,OAAM,GAAI,IAAI,CAACW,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACV,KAAK,CAACC,MAAM,CAAC;MACpD,IAAIJ,IAAG,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC;MACnB,IAAIc,KAAI,GAAI,CACV,IAAI,EACJ,IAAI,CACL;MACD,IAAIC,MAAK,GAAI,CACX;QACEC,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,EACD;QACER,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,CACF;MAED,IAAIC,UAAS,GAAI,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;QACrC,IAAIC,SAAQ,GAAIF,MAAM,CAACE,SAAS;QAChC,IAAIC,KAAI,GAAIF,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;;QAExB;QACA,IAAIC,gBAAgB;QACpB,IAAID,KAAI,IAAK,EAAE,EAAE;UACfC,gBAAe,GAAI,EAAC,GAAI,CAACD,KAAI,GAAI,EAAE,IAAI,GAAG,EAAE;QAC9C,OAAO;UACLC,gBAAe,GAAI,EAAC,GAAID,KAAI,GAAI,GAAG,EAAE;QACvC;QAEA,IAAIE,KAAI,GAAIJ,GAAG,CAACK,KAAK,CAAC,CAACJ,SAAS,EAAE,CAAC,CAAC,CAAC;QACrC,IAAIK,GAAE,GAAIN,GAAG,CAACK,KAAK,CAAC,CAACJ,SAAS,EAAEE,gBAAgB,CAAC,CAAC;QAClD,IAAII,KAAI,GAAIP,GAAG,CAACO,KAAK,CAAC,CAAC;;QAEvB;QACA,IAAIC,MAAK,GAAIJ,KAAK,CAAC,CAAC,IAAIE,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAIG,SAAQ,GAAID,MAAK,GAAI,GAAG;QAC5B,IAAIlB,CAAA,GAAIc,KAAK,CAAC,CAAC,CAAC;QAChB,IAAIb,CAAA,GAAIe,GAAG,CAAC,CAAC,CAAC;;QAEd;QACA,IAAII,QAAO,GAAI,CAACpB,CAAC,EAAEC,CAAC,CAAC,EAAE;QACvB,IAAIoB,YAAW,GAAI,CAACrB,CAAC,EAAEc,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,IAAIQ,UAAS,GAAI,CAACtB,CAAA,GAAImB,SAAQ,GAAI,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAChD,IAAIS,WAAU,GAAI,CAACvB,CAAA,GAAImB,SAAQ,GAAI,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;;QAEjD,IAAIU,KAAI,GAAI;UACVzB,IAAI,EAAE,OAAO;UACb0B,QAAQ,EAAE;UACR;UACA;YACE1B,IAAI,EAAE,SAAS;YACf2B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACR,QAAQ,EAAEE,UAAU,EAAED,YAAY;YAC7C,CAAC;YACDJ,KAAK,EAAE;cACLY,IAAI,EAAE;gBACJ9B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAEY,KAAK,CAACY,IAAI,CAACxB;cACzB,CAAC;cACDyB,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACEhC,IAAI,EAAE,SAAS;YACf2B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACR,QAAQ,EAAEC,YAAY,EAAEE,WAAW;YAC9C,CAAC;YACDN,KAAK,EAAE;cACLY,IAAI,EAAE;gBACJ9B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE,CACV;kBAAEC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACY,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACpD;kBAAED,MAAM,EAAE,GAAG;kBAAEC,KAAK,EAAEU,KAAK,CAACY,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACtD;kBAAED,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACY,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM;cAEvD,CAAC;cACDuB,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE,CAAC;cACZC,OAAO,EAAE;YACX;UACF,CAAC;UACD;UACA;YACEjC,IAAI,EAAE,MAAM;YACZ2B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLM,EAAE,EAAEb,QAAQ,CAAC,CAAC,CAAC;cACfc,EAAE,EAAEd,QAAQ,CAAC,CAAC,CAAC;cACflB,EAAE,EAAEmB,YAAY,CAAC,CAAC,CAAC;cACnBlB,EAAE,EAAEkB,YAAY,CAAC,CAAC;YACpB,CAAC;YACDJ,KAAK,EAAE;cACLa,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE;YACb;UACF,CAAC;QAEL,CAAC;QAED,OAAOP,KAAK;MACd,CAAC;;MAED;MACA,IAAIW,MAAK,GAAI;QACXC,KAAK,EAAE;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,IAAI;UACTC,SAAS,EAAE;YACTjC,KAAK,EAAE,SAAS;YAChBkC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,mBAAmB;UACpCC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfR,SAAS,EAAE;YACTjC,KAAK,EAAE,SAAS;YAChBkC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG;UAC7B,CAAC;UACDQ,SAAS,EAAE,SAAAA,CAASxC,MAAM,EAAE;YAC1B,OAAOA,MAAM,CAAC3B,IAAG,GAAI,IAAG,GAAI2B,MAAM,CAACG,KAAI,GAAI,GAAG;UAChD;QACF,CAAC;QACDsC,IAAI,EAAE;UACJX,GAAG,EAAE,KAAK;UACVD,IAAI,EAAE,KAAK;UACXa,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLvE,IAAI,EAAEc,KAAK;UACX0D,IAAI,EAAE,KAAK,CAAE;QACf,CAAC;QACDC,KAAK,EAAE;UACLD,IAAI,EAAE,KAAK;UAAE;UACbE,GAAG,EAAE;QACP,CAAC;QAEDC,MAAM,EAAE,CACN;UACE3D,IAAI,EAAE,QAAQ;UACd4D,SAAS,EAAE;YACTpD,KAAK,EAAE,SAAAA,CAASE,MAAM,EAAE;cACtB,OAAOX,MAAM,CAACW,MAAM,CAACE,SAAS,CAAC;YACjC;UACF,CAAC;UACDiD,KAAK,EAAE;YACLL,IAAI,EAAE,IAAI;YACVM,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAClBtD,KAAK,EAAE,SAAS;YAChBkC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE,MAAM;YAClBO,SAAS,EAAE,SAAAA,CAASxC,MAAM,EAAE;cAC1B,OAAOA,MAAM,CAAC1B,IAAG,GAAI,GAAG;YAC1B,CAAC;YACD+E,KAAK,EAAE;UACT,CAAC;UACD/E,IAAI,EAAEA,IAAI;UACVyB,UAAU,EAAEA;QACd,CAAC;QACD;QACA;UACET,IAAI,EAAE,QAAQ;UACdS,UAAU,EAAE,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;YAChC,IAAIV,CAAA,GAAIU,GAAG,CAACK,KAAK,CAAC,CAACN,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAIV,CAAA,GAAIS,GAAG,CAACK,KAAK,CAAC,CAACN,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YAEhD,OAAO;cACLZ,IAAI,EAAE,OAAO;cACb0B,QAAQ,EAAE;cACR;cACA;gBACE1B,IAAI,EAAE,MAAM;gBACZ4B,KAAK,EAAE;kBACLM,EAAE,EAAEjC,CAAC;kBACLkC,EAAE,EAAEjC,CAAA,GAAI,EAAE;kBACVC,EAAE,EAAEF,CAAC;kBACLG,EAAE,EAAEF,CAAA,GAAI;gBACV,CAAC;gBACDgB,KAAK,EAAE;kBACLa,MAAM,EAAE,SAAS;kBACjBC,SAAS,EAAE,CAAC;kBACZgC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBAChB/B,OAAO,EAAE;gBACX;cACF,CAAC;cACD;cACA;gBACEjC,IAAI,EAAE,MAAM;gBACZkB,KAAK,EAAE;kBACLoB,IAAI,EAAExC,KAAK,CAACY,MAAM,CAACE,SAAS,CAAC;kBAC7BX,CAAC,EAAEA,CAAC;kBACJC,CAAC,EAAEA,CAAC;kBACJ+D,SAAS,EAAE,QAAQ;kBACnBC,iBAAiB,EAAE,KAAK;kBACxBpC,IAAI,EAAE,SAAS;kBACfY,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd;cACF;YAEJ,CAAC;UACH,CAAC;UACD3D,IAAI,EAAEA;QACR,CAAC;MAEL,CAAC;MACD,IAAI,CAACC,OAAO,CAACkF,SAAS,CAAC/B,MAAM,CAAC;IAChC,CAAC;IACD;IACAM,QAAQA,CAAC0B,GAAG,EAAE;MACZ,MAAMC,WAAU,GACd/E,MAAM,CAACgF,UAAS,IAChBC,QAAQ,CAACC,eAAe,CAACH,WAAU,IACnCE,QAAQ,CAACE,IAAI,CAACJ,WAAW;MAC3B,IAAI,CAACA,WAAW,EAAE;MAClB,MAAM3B,QAAO,GAAI,EAAC,IAAK2B,WAAU,GAAI,IAAI,CAAC;MAC1C,OAAOD,GAAE,GAAI1B,QAAQ;IACvB,CAAC;IACD;IACAjD,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACR,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACyF,OAAO,CAAC,CAAC;MACxB;IACF,CAAC;IACD;IACArF,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACI,YAAY,CAAC,CAAC;MACnB,IAAI,CAACE,SAAS,CAAC,CAAC;IAClB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}