{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The tree layoutHelper implementation was originally copied from\r\n* \"d3.js\"(https://github.com/d3/d3-hierarchy) with\r\n* some modifications made for this project.\r\n* (see more details in the comment of the specific method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the licence of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n/**\r\n * @file The layout algorithm of node-link tree diagrams. Here we using Reingold-Tilford algorithm to drawing\r\n *       the tree.\r\n */\nimport * as layout from '../../util/layout.js';\n/**\r\n * Initialize all computational message for following algorithm.\r\n */\nexport function init(inRoot) {\n  var root = inRoot;\n  root.hierNode = {\n    defaultAncestor: null,\n    ancestor: root,\n    prelim: 0,\n    modifier: 0,\n    change: 0,\n    shift: 0,\n    i: 0,\n    thread: null\n  };\n  var nodes = [root];\n  var node;\n  var children;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    children = node.children;\n    if (node.isExpand && children.length) {\n      var n = children.length;\n      for (var i = n - 1; i >= 0; i--) {\n        var child = children[i];\n        child.hierNode = {\n          defaultAncestor: null,\n          ancestor: child,\n          prelim: 0,\n          modifier: 0,\n          change: 0,\n          shift: 0,\n          i: i,\n          thread: null\n        };\n        nodes.push(child);\n      }\n    }\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes a preliminary x coordinate for node. Before that, this function is\r\n * applied recursively to the children of node, as well as the function\r\n * apportion(). After spacing out the children by calling executeShifts(), the\r\n * node is placed to the midpoint of its outermost children.\r\n */\nexport function firstWalk(node, separation) {\n  var children = node.isExpand ? node.children : [];\n  var siblings = node.parentNode.children;\n  var subtreeW = node.hierNode.i ? siblings[node.hierNode.i - 1] : null;\n  if (children.length) {\n    executeShifts(node);\n    var midPoint = (children[0].hierNode.prelim + children[children.length - 1].hierNode.prelim) / 2;\n    if (subtreeW) {\n      node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n      node.hierNode.modifier = node.hierNode.prelim - midPoint;\n    } else {\n      node.hierNode.prelim = midPoint;\n    }\n  } else if (subtreeW) {\n    node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n  }\n  node.parentNode.hierNode.defaultAncestor = apportion(node, subtreeW, node.parentNode.hierNode.defaultAncestor || siblings[0], separation);\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes all real x-coordinates by summing up the modifiers recursively.\r\n */\nexport function secondWalk(node) {\n  var nodeX = node.hierNode.prelim + node.parentNode.hierNode.modifier;\n  node.setLayout({\n    x: nodeX\n  }, true);\n  node.hierNode.modifier += node.parentNode.hierNode.modifier;\n}\nexport function separation(cb) {\n  return arguments.length ? cb : defaultSeparation;\n}\n/**\r\n * Transform the common coordinate to radial coordinate.\r\n */\nexport function radialCoordinate(rad, r) {\n  rad -= Math.PI / 2;\n  return {\n    x: r * Math.cos(rad),\n    y: r * Math.sin(rad)\n  };\n}\n/**\r\n * Get the layout position of the whole view.\r\n */\nexport function getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\n/**\r\n * All other shifts, applied to the smaller subtrees between w- and w+, are\r\n * performed by this function.\r\n *\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction executeShifts(node) {\n  var children = node.children;\n  var n = children.length;\n  var shift = 0;\n  var change = 0;\n  while (--n >= 0) {\n    var child = children[n];\n    child.hierNode.prelim += shift;\n    child.hierNode.modifier += shift;\n    change += child.hierNode.change;\n    shift += child.hierNode.shift + change;\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * The core of the algorithm. Here, a new subtree is combined with the\r\n * previous subtrees. Threads are used to traverse the inside and outside\r\n * contours of the left and right subtree up to the highest common level.\r\n * Whenever two nodes of the inside contours conflict, we compute the left\r\n * one of the greatest uncommon ancestors using the function nextAncestor()\r\n * and call moveSubtree() to shift the subtree and prepare the shifts of\r\n * smaller subtrees. Finally, we add a new thread (if necessary).\r\n */\nfunction apportion(subtreeV, subtreeW, ancestor, separation) {\n  if (subtreeW) {\n    var nodeOutRight = subtreeV;\n    var nodeInRight = subtreeV;\n    var nodeOutLeft = nodeInRight.parentNode.children[0];\n    var nodeInLeft = subtreeW;\n    var sumOutRight = nodeOutRight.hierNode.modifier;\n    var sumInRight = nodeInRight.hierNode.modifier;\n    var sumOutLeft = nodeOutLeft.hierNode.modifier;\n    var sumInLeft = nodeInLeft.hierNode.modifier;\n    while (nodeInLeft = nextRight(nodeInLeft), nodeInRight = nextLeft(nodeInRight), nodeInLeft && nodeInRight) {\n      nodeOutRight = nextRight(nodeOutRight);\n      nodeOutLeft = nextLeft(nodeOutLeft);\n      nodeOutRight.hierNode.ancestor = subtreeV;\n      var shift = nodeInLeft.hierNode.prelim + sumInLeft - nodeInRight.hierNode.prelim - sumInRight + separation(nodeInLeft, nodeInRight);\n      if (shift > 0) {\n        moveSubtree(nextAncestor(nodeInLeft, subtreeV, ancestor), subtreeV, shift);\n        sumInRight += shift;\n        sumOutRight += shift;\n      }\n      sumInLeft += nodeInLeft.hierNode.modifier;\n      sumInRight += nodeInRight.hierNode.modifier;\n      sumOutRight += nodeOutRight.hierNode.modifier;\n      sumOutLeft += nodeOutLeft.hierNode.modifier;\n    }\n    if (nodeInLeft && !nextRight(nodeOutRight)) {\n      nodeOutRight.hierNode.thread = nodeInLeft;\n      nodeOutRight.hierNode.modifier += sumInLeft - sumOutRight;\n    }\n    if (nodeInRight && !nextLeft(nodeOutLeft)) {\n      nodeOutLeft.hierNode.thread = nodeInRight;\n      nodeOutLeft.hierNode.modifier += sumInRight - sumOutLeft;\n      ancestor = subtreeV;\n    }\n  }\n  return ancestor;\n}\n/**\r\n * This function is used to traverse the right contour of a subtree.\r\n * It returns the rightmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextRight(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[children.length - 1] : node.hierNode.thread;\n}\n/**\r\n * This function is used to traverse the left contour of a subtree (or a subforest).\r\n * It returns the leftmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextLeft(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[0] : node.hierNode.thread;\n}\n/**\r\n * If nodeInLeft’s ancestor is a sibling of node, returns nodeInLeft’s ancestor.\r\n * Otherwise, returns the specified ancestor.\r\n */\nfunction nextAncestor(nodeInLeft, node, ancestor) {\n  return nodeInLeft.hierNode.ancestor.parentNode === node.parentNode ? nodeInLeft.hierNode.ancestor : ancestor;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Shifts the current subtree rooted at wr.\r\n * This is done by increasing prelim(w+) and modifier(w+) by shift.\r\n */\nfunction moveSubtree(wl, wr, shift) {\n  var change = shift / (wr.hierNode.i - wl.hierNode.i);\n  wr.hierNode.change -= change;\n  wr.hierNode.shift += shift;\n  wr.hierNode.modifier += shift;\n  wr.hierNode.prelim += shift;\n  wl.hierNode.change += change;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction defaultSeparation(node1, node2) {\n  return node1.parentNode === node2.parentNode ? 1 : 2;\n}", "map": {"version": 3, "names": ["layout", "init", "inRoot", "root", "hier<PERSON><PERSON>", "defaultAncestor", "ancestor", "prelim", "modifier", "change", "shift", "i", "thread", "nodes", "node", "children", "pop", "isExpand", "length", "n", "child", "push", "firstWalk", "separation", "siblings", "parentNode", "subtreeW", "executeShifts", "midPoint", "apportion", "secondWalk", "nodeX", "setLayout", "x", "cb", "arguments", "defaultSeparation", "radialCoordinate", "rad", "r", "Math", "PI", "cos", "y", "sin", "getViewRect", "seriesModel", "api", "getLayoutRect", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "subtreeV", "nodeOutRight", "nodeInRight", "nodeOutLeft", "nodeInLeft", "sumOutRight", "sumInRight", "sumOutLeft", "sumInLeft", "nextRight", "nextLeft", "moveSubtree", "nextAncestor", "wl", "wr", "node1", "node2"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/tree/layoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The tree layoutHelper implementation was originally copied from\r\n* \"d3.js\"(https://github.com/d3/d3-hierarchy) with\r\n* some modifications made for this project.\r\n* (see more details in the comment of the specific method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the licence of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n/**\r\n * @file The layout algorithm of node-link tree diagrams. Here we using Reingold-Tilford algorithm to drawing\r\n *       the tree.\r\n */\nimport * as layout from '../../util/layout.js';\n/**\r\n * Initialize all computational message for following algorithm.\r\n */\nexport function init(inRoot) {\n  var root = inRoot;\n  root.hierNode = {\n    defaultAncestor: null,\n    ancestor: root,\n    prelim: 0,\n    modifier: 0,\n    change: 0,\n    shift: 0,\n    i: 0,\n    thread: null\n  };\n  var nodes = [root];\n  var node;\n  var children;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    children = node.children;\n    if (node.isExpand && children.length) {\n      var n = children.length;\n      for (var i = n - 1; i >= 0; i--) {\n        var child = children[i];\n        child.hierNode = {\n          defaultAncestor: null,\n          ancestor: child,\n          prelim: 0,\n          modifier: 0,\n          change: 0,\n          shift: 0,\n          i: i,\n          thread: null\n        };\n        nodes.push(child);\n      }\n    }\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes a preliminary x coordinate for node. Before that, this function is\r\n * applied recursively to the children of node, as well as the function\r\n * apportion(). After spacing out the children by calling executeShifts(), the\r\n * node is placed to the midpoint of its outermost children.\r\n */\nexport function firstWalk(node, separation) {\n  var children = node.isExpand ? node.children : [];\n  var siblings = node.parentNode.children;\n  var subtreeW = node.hierNode.i ? siblings[node.hierNode.i - 1] : null;\n  if (children.length) {\n    executeShifts(node);\n    var midPoint = (children[0].hierNode.prelim + children[children.length - 1].hierNode.prelim) / 2;\n    if (subtreeW) {\n      node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n      node.hierNode.modifier = node.hierNode.prelim - midPoint;\n    } else {\n      node.hierNode.prelim = midPoint;\n    }\n  } else if (subtreeW) {\n    node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n  }\n  node.parentNode.hierNode.defaultAncestor = apportion(node, subtreeW, node.parentNode.hierNode.defaultAncestor || siblings[0], separation);\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes all real x-coordinates by summing up the modifiers recursively.\r\n */\nexport function secondWalk(node) {\n  var nodeX = node.hierNode.prelim + node.parentNode.hierNode.modifier;\n  node.setLayout({\n    x: nodeX\n  }, true);\n  node.hierNode.modifier += node.parentNode.hierNode.modifier;\n}\nexport function separation(cb) {\n  return arguments.length ? cb : defaultSeparation;\n}\n/**\r\n * Transform the common coordinate to radial coordinate.\r\n */\nexport function radialCoordinate(rad, r) {\n  rad -= Math.PI / 2;\n  return {\n    x: r * Math.cos(rad),\n    y: r * Math.sin(rad)\n  };\n}\n/**\r\n * Get the layout position of the whole view.\r\n */\nexport function getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\n/**\r\n * All other shifts, applied to the smaller subtrees between w- and w+, are\r\n * performed by this function.\r\n *\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction executeShifts(node) {\n  var children = node.children;\n  var n = children.length;\n  var shift = 0;\n  var change = 0;\n  while (--n >= 0) {\n    var child = children[n];\n    child.hierNode.prelim += shift;\n    child.hierNode.modifier += shift;\n    change += child.hierNode.change;\n    shift += child.hierNode.shift + change;\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * The core of the algorithm. Here, a new subtree is combined with the\r\n * previous subtrees. Threads are used to traverse the inside and outside\r\n * contours of the left and right subtree up to the highest common level.\r\n * Whenever two nodes of the inside contours conflict, we compute the left\r\n * one of the greatest uncommon ancestors using the function nextAncestor()\r\n * and call moveSubtree() to shift the subtree and prepare the shifts of\r\n * smaller subtrees. Finally, we add a new thread (if necessary).\r\n */\nfunction apportion(subtreeV, subtreeW, ancestor, separation) {\n  if (subtreeW) {\n    var nodeOutRight = subtreeV;\n    var nodeInRight = subtreeV;\n    var nodeOutLeft = nodeInRight.parentNode.children[0];\n    var nodeInLeft = subtreeW;\n    var sumOutRight = nodeOutRight.hierNode.modifier;\n    var sumInRight = nodeInRight.hierNode.modifier;\n    var sumOutLeft = nodeOutLeft.hierNode.modifier;\n    var sumInLeft = nodeInLeft.hierNode.modifier;\n    while (nodeInLeft = nextRight(nodeInLeft), nodeInRight = nextLeft(nodeInRight), nodeInLeft && nodeInRight) {\n      nodeOutRight = nextRight(nodeOutRight);\n      nodeOutLeft = nextLeft(nodeOutLeft);\n      nodeOutRight.hierNode.ancestor = subtreeV;\n      var shift = nodeInLeft.hierNode.prelim + sumInLeft - nodeInRight.hierNode.prelim - sumInRight + separation(nodeInLeft, nodeInRight);\n      if (shift > 0) {\n        moveSubtree(nextAncestor(nodeInLeft, subtreeV, ancestor), subtreeV, shift);\n        sumInRight += shift;\n        sumOutRight += shift;\n      }\n      sumInLeft += nodeInLeft.hierNode.modifier;\n      sumInRight += nodeInRight.hierNode.modifier;\n      sumOutRight += nodeOutRight.hierNode.modifier;\n      sumOutLeft += nodeOutLeft.hierNode.modifier;\n    }\n    if (nodeInLeft && !nextRight(nodeOutRight)) {\n      nodeOutRight.hierNode.thread = nodeInLeft;\n      nodeOutRight.hierNode.modifier += sumInLeft - sumOutRight;\n    }\n    if (nodeInRight && !nextLeft(nodeOutLeft)) {\n      nodeOutLeft.hierNode.thread = nodeInRight;\n      nodeOutLeft.hierNode.modifier += sumInRight - sumOutLeft;\n      ancestor = subtreeV;\n    }\n  }\n  return ancestor;\n}\n/**\r\n * This function is used to traverse the right contour of a subtree.\r\n * It returns the rightmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextRight(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[children.length - 1] : node.hierNode.thread;\n}\n/**\r\n * This function is used to traverse the left contour of a subtree (or a subforest).\r\n * It returns the leftmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextLeft(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[0] : node.hierNode.thread;\n}\n/**\r\n * If nodeInLeft’s ancestor is a sibling of node, returns nodeInLeft’s ancestor.\r\n * Otherwise, returns the specified ancestor.\r\n */\nfunction nextAncestor(nodeInLeft, node, ancestor) {\n  return nodeInLeft.hierNode.ancestor.parentNode === node.parentNode ? nodeInLeft.hierNode.ancestor : ancestor;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Shifts the current subtree rooted at wr.\r\n * This is done by increasing prelim(w+) and modifier(w+) by shift.\r\n */\nfunction moveSubtree(wl, wr, shift) {\n  var change = shift / (wr.hierNode.i - wl.hierNode.i);\n  wr.hierNode.change -= change;\n  wr.hierNode.shift += shift;\n  wr.hierNode.modifier += shift;\n  wr.hierNode.prelim += shift;\n  wl.hierNode.change += change;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction defaultSeparation(node1, node2) {\n  return node1.parentNode === node2.parentNode ? 1 : 2;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,sBAAsB;AAC9C;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,MAAM,EAAE;EAC3B,IAAIC,IAAI,GAAGD,MAAM;EACjBC,IAAI,CAACC,QAAQ,GAAG;IACdC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAEH,IAAI;IACdI,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,CAAC,EAAE,CAAC;IACJC,MAAM,EAAE;EACV,CAAC;EACD,IAAIC,KAAK,GAAG,CAACV,IAAI,CAAC;EAClB,IAAIW,IAAI;EACR,IAAIC,QAAQ;EACZ,OAAOD,IAAI,GAAGD,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACzB;IACAD,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxB,IAAID,IAAI,CAACG,QAAQ,IAAIF,QAAQ,CAACG,MAAM,EAAE;MACpC,IAAIC,CAAC,GAAGJ,QAAQ,CAACG,MAAM;MACvB,KAAK,IAAIP,CAAC,GAAGQ,CAAC,GAAG,CAAC,EAAER,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/B,IAAIS,KAAK,GAAGL,QAAQ,CAACJ,CAAC,CAAC;QACvBS,KAAK,CAAChB,QAAQ,GAAG;UACfC,eAAe,EAAE,IAAI;UACrBC,QAAQ,EAAEc,KAAK;UACfb,MAAM,EAAE,CAAC;UACTC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;UACRC,CAAC,EAAEA,CAAC;UACJC,MAAM,EAAE;QACV,CAAC;QACDC,KAAK,CAACQ,IAAI,CAACD,KAAK,CAAC;MACnB;IACF;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAACR,IAAI,EAAES,UAAU,EAAE;EAC1C,IAAIR,QAAQ,GAAGD,IAAI,CAACG,QAAQ,GAAGH,IAAI,CAACC,QAAQ,GAAG,EAAE;EACjD,IAAIS,QAAQ,GAAGV,IAAI,CAACW,UAAU,CAACV,QAAQ;EACvC,IAAIW,QAAQ,GAAGZ,IAAI,CAACV,QAAQ,CAACO,CAAC,GAAGa,QAAQ,CAACV,IAAI,CAACV,QAAQ,CAACO,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACrE,IAAII,QAAQ,CAACG,MAAM,EAAE;IACnBS,aAAa,CAACb,IAAI,CAAC;IACnB,IAAIc,QAAQ,GAAG,CAACb,QAAQ,CAAC,CAAC,CAAC,CAACX,QAAQ,CAACG,MAAM,GAAGQ,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CAACd,QAAQ,CAACG,MAAM,IAAI,CAAC;IAChG,IAAImB,QAAQ,EAAE;MACZZ,IAAI,CAACV,QAAQ,CAACG,MAAM,GAAGmB,QAAQ,CAACtB,QAAQ,CAACG,MAAM,GAAGgB,UAAU,CAACT,IAAI,EAAEY,QAAQ,CAAC;MAC5EZ,IAAI,CAACV,QAAQ,CAACI,QAAQ,GAAGM,IAAI,CAACV,QAAQ,CAACG,MAAM,GAAGqB,QAAQ;IAC1D,CAAC,MAAM;MACLd,IAAI,CAACV,QAAQ,CAACG,MAAM,GAAGqB,QAAQ;IACjC;EACF,CAAC,MAAM,IAAIF,QAAQ,EAAE;IACnBZ,IAAI,CAACV,QAAQ,CAACG,MAAM,GAAGmB,QAAQ,CAACtB,QAAQ,CAACG,MAAM,GAAGgB,UAAU,CAACT,IAAI,EAAEY,QAAQ,CAAC;EAC9E;EACAZ,IAAI,CAACW,UAAU,CAACrB,QAAQ,CAACC,eAAe,GAAGwB,SAAS,CAACf,IAAI,EAAEY,QAAQ,EAAEZ,IAAI,CAACW,UAAU,CAACrB,QAAQ,CAACC,eAAe,IAAImB,QAAQ,CAAC,CAAC,CAAC,EAAED,UAAU,CAAC;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,UAAUA,CAAChB,IAAI,EAAE;EAC/B,IAAIiB,KAAK,GAAGjB,IAAI,CAACV,QAAQ,CAACG,MAAM,GAAGO,IAAI,CAACW,UAAU,CAACrB,QAAQ,CAACI,QAAQ;EACpEM,IAAI,CAACkB,SAAS,CAAC;IACbC,CAAC,EAAEF;EACL,CAAC,EAAE,IAAI,CAAC;EACRjB,IAAI,CAACV,QAAQ,CAACI,QAAQ,IAAIM,IAAI,CAACW,UAAU,CAACrB,QAAQ,CAACI,QAAQ;AAC7D;AACA,OAAO,SAASe,UAAUA,CAACW,EAAE,EAAE;EAC7B,OAAOC,SAAS,CAACjB,MAAM,GAAGgB,EAAE,GAAGE,iBAAiB;AAClD;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,GAAG,EAAEC,CAAC,EAAE;EACvCD,GAAG,IAAIE,IAAI,CAACC,EAAE,GAAG,CAAC;EAClB,OAAO;IACLR,CAAC,EAAEM,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC;IACpBK,CAAC,EAAEJ,CAAC,GAAGC,IAAI,CAACI,GAAG,CAACN,GAAG;EACrB,CAAC;AACH;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,WAAW,EAAEC,GAAG,EAAE;EAC5C,OAAO/C,MAAM,CAACgD,aAAa,CAACF,WAAW,CAACG,kBAAkB,CAAC,CAAC,EAAE;IAC5DC,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1B,aAAaA,CAACb,IAAI,EAAE;EAC3B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAII,CAAC,GAAGJ,QAAQ,CAACG,MAAM;EACvB,IAAIR,KAAK,GAAG,CAAC;EACb,IAAID,MAAM,GAAG,CAAC;EACd,OAAO,EAAEU,CAAC,IAAI,CAAC,EAAE;IACf,IAAIC,KAAK,GAAGL,QAAQ,CAACI,CAAC,CAAC;IACvBC,KAAK,CAAChB,QAAQ,CAACG,MAAM,IAAIG,KAAK;IAC9BU,KAAK,CAAChB,QAAQ,CAACI,QAAQ,IAAIE,KAAK;IAChCD,MAAM,IAAIW,KAAK,CAAChB,QAAQ,CAACK,MAAM;IAC/BC,KAAK,IAAIU,KAAK,CAAChB,QAAQ,CAACM,KAAK,GAAGD,MAAM;EACxC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,SAASA,CAACyB,QAAQ,EAAE5B,QAAQ,EAAEpB,QAAQ,EAAEiB,UAAU,EAAE;EAC3D,IAAIG,QAAQ,EAAE;IACZ,IAAI6B,YAAY,GAAGD,QAAQ;IAC3B,IAAIE,WAAW,GAAGF,QAAQ;IAC1B,IAAIG,WAAW,GAAGD,WAAW,CAAC/B,UAAU,CAACV,QAAQ,CAAC,CAAC,CAAC;IACpD,IAAI2C,UAAU,GAAGhC,QAAQ;IACzB,IAAIiC,WAAW,GAAGJ,YAAY,CAACnD,QAAQ,CAACI,QAAQ;IAChD,IAAIoD,UAAU,GAAGJ,WAAW,CAACpD,QAAQ,CAACI,QAAQ;IAC9C,IAAIqD,UAAU,GAAGJ,WAAW,CAACrD,QAAQ,CAACI,QAAQ;IAC9C,IAAIsD,SAAS,GAAGJ,UAAU,CAACtD,QAAQ,CAACI,QAAQ;IAC5C,OAAOkD,UAAU,GAAGK,SAAS,CAACL,UAAU,CAAC,EAAEF,WAAW,GAAGQ,QAAQ,CAACR,WAAW,CAAC,EAAEE,UAAU,IAAIF,WAAW,EAAE;MACzGD,YAAY,GAAGQ,SAAS,CAACR,YAAY,CAAC;MACtCE,WAAW,GAAGO,QAAQ,CAACP,WAAW,CAAC;MACnCF,YAAY,CAACnD,QAAQ,CAACE,QAAQ,GAAGgD,QAAQ;MACzC,IAAI5C,KAAK,GAAGgD,UAAU,CAACtD,QAAQ,CAACG,MAAM,GAAGuD,SAAS,GAAGN,WAAW,CAACpD,QAAQ,CAACG,MAAM,GAAGqD,UAAU,GAAGrC,UAAU,CAACmC,UAAU,EAAEF,WAAW,CAAC;MACnI,IAAI9C,KAAK,GAAG,CAAC,EAAE;QACbuD,WAAW,CAACC,YAAY,CAACR,UAAU,EAAEJ,QAAQ,EAAEhD,QAAQ,CAAC,EAAEgD,QAAQ,EAAE5C,KAAK,CAAC;QAC1EkD,UAAU,IAAIlD,KAAK;QACnBiD,WAAW,IAAIjD,KAAK;MACtB;MACAoD,SAAS,IAAIJ,UAAU,CAACtD,QAAQ,CAACI,QAAQ;MACzCoD,UAAU,IAAIJ,WAAW,CAACpD,QAAQ,CAACI,QAAQ;MAC3CmD,WAAW,IAAIJ,YAAY,CAACnD,QAAQ,CAACI,QAAQ;MAC7CqD,UAAU,IAAIJ,WAAW,CAACrD,QAAQ,CAACI,QAAQ;IAC7C;IACA,IAAIkD,UAAU,IAAI,CAACK,SAAS,CAACR,YAAY,CAAC,EAAE;MAC1CA,YAAY,CAACnD,QAAQ,CAACQ,MAAM,GAAG8C,UAAU;MACzCH,YAAY,CAACnD,QAAQ,CAACI,QAAQ,IAAIsD,SAAS,GAAGH,WAAW;IAC3D;IACA,IAAIH,WAAW,IAAI,CAACQ,QAAQ,CAACP,WAAW,CAAC,EAAE;MACzCA,WAAW,CAACrD,QAAQ,CAACQ,MAAM,GAAG4C,WAAW;MACzCC,WAAW,CAACrD,QAAQ,CAACI,QAAQ,IAAIoD,UAAU,GAAGC,UAAU;MACxDvD,QAAQ,GAAGgD,QAAQ;IACrB;EACF;EACA,OAAOhD,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAASyD,SAASA,CAACjD,IAAI,EAAE;EACvB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ,CAACG,MAAM,IAAIJ,IAAI,CAACG,QAAQ,GAAGF,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGJ,IAAI,CAACV,QAAQ,CAACQ,MAAM;AAChG;AACA;AACA;AACA;AACA;AACA;AACA,SAASoD,QAAQA,CAAClD,IAAI,EAAE;EACtB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ,CAACG,MAAM,IAAIJ,IAAI,CAACG,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACV,QAAQ,CAACQ,MAAM;AAC9E;AACA;AACA;AACA;AACA;AACA,SAASsD,YAAYA,CAACR,UAAU,EAAE5C,IAAI,EAAER,QAAQ,EAAE;EAChD,OAAOoD,UAAU,CAACtD,QAAQ,CAACE,QAAQ,CAACmB,UAAU,KAAKX,IAAI,CAACW,UAAU,GAAGiC,UAAU,CAACtD,QAAQ,CAACE,QAAQ,GAAGA,QAAQ;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2D,WAAWA,CAACE,EAAE,EAAEC,EAAE,EAAE1D,KAAK,EAAE;EAClC,IAAID,MAAM,GAAGC,KAAK,IAAI0D,EAAE,CAAChE,QAAQ,CAACO,CAAC,GAAGwD,EAAE,CAAC/D,QAAQ,CAACO,CAAC,CAAC;EACpDyD,EAAE,CAAChE,QAAQ,CAACK,MAAM,IAAIA,MAAM;EAC5B2D,EAAE,CAAChE,QAAQ,CAACM,KAAK,IAAIA,KAAK;EAC1B0D,EAAE,CAAChE,QAAQ,CAACI,QAAQ,IAAIE,KAAK;EAC7B0D,EAAE,CAAChE,QAAQ,CAACG,MAAM,IAAIG,KAAK;EAC3ByD,EAAE,CAAC/D,QAAQ,CAACK,MAAM,IAAIA,MAAM;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,iBAAiBA,CAACiC,KAAK,EAAEC,KAAK,EAAE;EACvC,OAAOD,KAAK,CAAC5C,UAAU,KAAK6C,KAAK,CAAC7C,UAAU,GAAG,CAAC,GAAG,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}