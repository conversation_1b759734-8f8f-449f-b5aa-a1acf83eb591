{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * This module is imported by echarts directly.\r\n *\r\n * Notice:\r\n * Always keep this file exists for backward compatibility.\r\n * Because before 4.1.0, dataset is an optional component,\r\n * some users may import this module manually.\r\n */\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { SERIES_LAYOUT_BY_COLUMN } from '../../util/types.js';\nimport { disableTransformOptionMerge, SourceManager } from '../../data/helper/sourceManager.js';\nvar DatasetModel = /** @class */function (_super) {\n  __extends(DatasetModel, _super);\n  function DatasetModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetModel.prototype.init = function (option, parentModel, ecModel) {\n    _super.prototype.init.call(this, option, parentModel, ecModel);\n    this._sourceManager = new SourceManager(this);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.mergeOption = function (newOption, ecModel) {\n    _super.prototype.mergeOption.call(this, newOption, ecModel);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.optionUpdated = function () {\n    this._sourceManager.dirty();\n  };\n  DatasetModel.prototype.getSourceManager = function () {\n    return this._sourceManager;\n  };\n  DatasetModel.type = 'dataset';\n  DatasetModel.defaultOption = {\n    seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN\n  };\n  return DatasetModel;\n}(ComponentModel);\nexport { DatasetModel };\nvar DatasetView = /** @class */function (_super) {\n  __extends(DatasetView, _super);\n  function DatasetView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetView.type = 'dataset';\n  return DatasetView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(DatasetModel);\n  registers.registerComponentView(DatasetView);\n}", "map": {"version": 3, "names": ["__extends", "ComponentModel", "ComponentView", "SERIES_LAYOUT_BY_COLUMN", "disableTransformOptionMerge", "SourceManager", "DatasetModel", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "option", "parentModel", "ecModel", "call", "_sourceManager", "mergeOption", "newOption", "optionUpdated", "dirty", "getSourceManager", "defaultOption", "seriesLayoutBy", "DatasetView", "install", "registers", "registerComponentModel", "registerComponentView"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/dataset/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * This module is imported by echarts directly.\r\n *\r\n * Notice:\r\n * Always keep this file exists for backward compatibility.\r\n * Because before 4.1.0, dataset is an optional component,\r\n * some users may import this module manually.\r\n */\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { SERIES_LAYOUT_BY_COLUMN } from '../../util/types.js';\nimport { disableTransformOptionMerge, SourceManager } from '../../data/helper/sourceManager.js';\nvar DatasetModel = /** @class */function (_super) {\n  __extends(DatasetModel, _super);\n  function DatasetModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetModel.prototype.init = function (option, parentModel, ecModel) {\n    _super.prototype.init.call(this, option, parentModel, ecModel);\n    this._sourceManager = new SourceManager(this);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.mergeOption = function (newOption, ecModel) {\n    _super.prototype.mergeOption.call(this, newOption, ecModel);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.optionUpdated = function () {\n    this._sourceManager.dirty();\n  };\n  DatasetModel.prototype.getSourceManager = function () {\n    return this._sourceManager;\n  };\n  DatasetModel.type = 'dataset';\n  DatasetModel.defaultOption = {\n    seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN\n  };\n  return DatasetModel;\n}(ComponentModel);\nexport { DatasetModel };\nvar DatasetView = /** @class */function (_super) {\n  __extends(DatasetView, _super);\n  function DatasetView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetView.type = 'dataset';\n  return DatasetView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(DatasetModel);\n  registers.registerComponentView(DatasetView);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,2BAA2B,EAAEC,aAAa,QAAQ,oCAAoC;AAC/F,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDP,SAAS,CAACM,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,SAAS;IACtB,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACpET,MAAM,CAACK,SAAS,CAACC,IAAI,CAACI,IAAI,CAAC,IAAI,EAAEH,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC;IAC9D,IAAI,CAACE,cAAc,GAAG,IAAIb,aAAa,CAAC,IAAI,CAAC;IAC7CD,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EACDE,YAAY,CAACM,SAAS,CAACO,WAAW,GAAG,UAAUC,SAAS,EAAEJ,OAAO,EAAE;IACjET,MAAM,CAACK,SAAS,CAACO,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEG,SAAS,EAAEJ,OAAO,CAAC;IAC3DZ,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EACDE,YAAY,CAACM,SAAS,CAACS,aAAa,GAAG,YAAY;IACjD,IAAI,CAACH,cAAc,CAACI,KAAK,CAAC,CAAC;EAC7B,CAAC;EACDhB,YAAY,CAACM,SAAS,CAACW,gBAAgB,GAAG,YAAY;IACpD,OAAO,IAAI,CAACL,cAAc;EAC5B,CAAC;EACDZ,YAAY,CAACK,IAAI,GAAG,SAAS;EAC7BL,YAAY,CAACkB,aAAa,GAAG;IAC3BC,cAAc,EAAEtB;EAClB,CAAC;EACD,OAAOG,YAAY;AACrB,CAAC,CAACL,cAAc,CAAC;AACjB,SAASK,YAAY;AACrB,IAAIoB,WAAW,GAAG,aAAa,UAAUnB,MAAM,EAAE;EAC/CP,SAAS,CAAC0B,WAAW,EAAEnB,MAAM,CAAC;EAC9B,SAASmB,WAAWA,CAAA,EAAG;IACrB,IAAIlB,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,SAAS;IACtB,OAAOH,KAAK;EACd;EACAkB,WAAW,CAACf,IAAI,GAAG,SAAS;EAC5B,OAAOe,WAAW;AACpB,CAAC,CAACxB,aAAa,CAAC;AAChB,OAAO,SAASyB,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACvB,YAAY,CAAC;EAC9CsB,SAAS,CAACE,qBAAqB,CAACJ,WAAW,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}