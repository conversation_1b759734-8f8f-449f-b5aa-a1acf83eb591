{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nexport function rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel) {\n  var axis = axisModel.axis;\n  if (axis.scale.isBlank()) {\n    return;\n  }\n  // TODO: TYPE\n  var splitAreaModel = axisModel.getModel('splitArea');\n  var areaStyleModel = splitAreaModel.getModel('areaStyle');\n  var areaColors = areaStyleModel.get('color');\n  var gridRect = gridModel.coordinateSystem.getRect();\n  var ticksCoords = axis.getTicksCoords({\n    tickModel: splitAreaModel,\n    clamp: true\n  });\n  if (!ticksCoords.length) {\n    return;\n  }\n  // For Making appropriate splitArea animation, the color and anid\n  // should be corresponding to previous one if possible.\n  var areaColorsLen = areaColors.length;\n  var lastSplitAreaColors = inner(axisView).splitAreaColors;\n  var newSplitAreaColors = zrUtil.createHashMap();\n  var colorIndex = 0;\n  if (lastSplitAreaColors) {\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var cIndex = lastSplitAreaColors.get(ticksCoords[i].tickValue);\n      if (cIndex != null) {\n        colorIndex = (cIndex + (areaColorsLen - 1) * i) % areaColorsLen;\n        break;\n      }\n    }\n  }\n  var prev = axis.toGlobalCoord(ticksCoords[0].coord);\n  var areaStyle = areaStyleModel.getAreaStyle();\n  areaColors = zrUtil.isArray(areaColors) ? areaColors : [areaColors];\n  for (var i = 1; i < ticksCoords.length; i++) {\n    var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n    var x = void 0;\n    var y = void 0;\n    var width = void 0;\n    var height = void 0;\n    if (axis.isHorizontal()) {\n      x = prev;\n      y = gridRect.y;\n      width = tickCoord - x;\n      height = gridRect.height;\n      prev = x + width;\n    } else {\n      x = gridRect.x;\n      y = prev;\n      width = gridRect.width;\n      height = tickCoord - y;\n      prev = y + height;\n    }\n    var tickValue = ticksCoords[i - 1].tickValue;\n    tickValue != null && newSplitAreaColors.set(tickValue, colorIndex);\n    axisGroup.add(new graphic.Rect({\n      anid: tickValue != null ? 'area_' + tickValue : null,\n      shape: {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      },\n      style: zrUtil.defaults({\n        fill: areaColors[colorIndex]\n      }, areaStyle),\n      autoBatch: true,\n      silent: true\n    }));\n    colorIndex = (colorIndex + 1) % areaColorsLen;\n  }\n  inner(axisView).splitAreaColors = newSplitAreaColors;\n}\nexport function rectCoordAxisHandleRemove(axisView) {\n  inner(axisView).splitAreaColors = null;\n}", "map": {"version": 3, "names": ["zrUtil", "graphic", "makeInner", "inner", "rectCoordAxisBuildSplitArea", "axisView", "axisGroup", "axisModel", "gridModel", "axis", "scale", "isBlank", "splitAreaModel", "getModel", "areaStyleModel", "areaColors", "get", "gridRect", "coordinateSystem", "getRect", "ticksCoords", "getTicksCoords", "tickModel", "clamp", "length", "areaColorsLen", "lastSplitAreaColors", "splitAreaColors", "newSplitAreaColors", "createHashMap", "colorIndex", "i", "cIndex", "tickValue", "prev", "toGlobalCoord", "coord", "areaStyle", "getAreaStyle", "isArray", "tickCoord", "x", "y", "width", "height", "isHorizontal", "set", "add", "Rect", "anid", "shape", "style", "defaults", "fill", "autoBatch", "silent", "rectCoordAxisHandleRemove"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/axis/axisSplitHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nexport function rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel) {\n  var axis = axisModel.axis;\n  if (axis.scale.isBlank()) {\n    return;\n  }\n  // TODO: TYPE\n  var splitAreaModel = axisModel.getModel('splitArea');\n  var areaStyleModel = splitAreaModel.getModel('areaStyle');\n  var areaColors = areaStyleModel.get('color');\n  var gridRect = gridModel.coordinateSystem.getRect();\n  var ticksCoords = axis.getTicksCoords({\n    tickModel: splitAreaModel,\n    clamp: true\n  });\n  if (!ticksCoords.length) {\n    return;\n  }\n  // For Making appropriate splitArea animation, the color and anid\n  // should be corresponding to previous one if possible.\n  var areaColorsLen = areaColors.length;\n  var lastSplitAreaColors = inner(axisView).splitAreaColors;\n  var newSplitAreaColors = zrUtil.createHashMap();\n  var colorIndex = 0;\n  if (lastSplitAreaColors) {\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var cIndex = lastSplitAreaColors.get(ticksCoords[i].tickValue);\n      if (cIndex != null) {\n        colorIndex = (cIndex + (areaColorsLen - 1) * i) % areaColorsLen;\n        break;\n      }\n    }\n  }\n  var prev = axis.toGlobalCoord(ticksCoords[0].coord);\n  var areaStyle = areaStyleModel.getAreaStyle();\n  areaColors = zrUtil.isArray(areaColors) ? areaColors : [areaColors];\n  for (var i = 1; i < ticksCoords.length; i++) {\n    var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n    var x = void 0;\n    var y = void 0;\n    var width = void 0;\n    var height = void 0;\n    if (axis.isHorizontal()) {\n      x = prev;\n      y = gridRect.y;\n      width = tickCoord - x;\n      height = gridRect.height;\n      prev = x + width;\n    } else {\n      x = gridRect.x;\n      y = prev;\n      width = gridRect.width;\n      height = tickCoord - y;\n      prev = y + height;\n    }\n    var tickValue = ticksCoords[i - 1].tickValue;\n    tickValue != null && newSplitAreaColors.set(tickValue, colorIndex);\n    axisGroup.add(new graphic.Rect({\n      anid: tickValue != null ? 'area_' + tickValue : null,\n      shape: {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      },\n      style: zrUtil.defaults({\n        fill: areaColors[colorIndex]\n      }, areaStyle),\n      autoBatch: true,\n      silent: true\n    }));\n    colorIndex = (colorIndex + 1) % areaColorsLen;\n  }\n  inner(axisView).splitAreaColors = newSplitAreaColors;\n}\nexport function rectCoordAxisHandleRemove(axisView) {\n  inner(axisView).splitAreaColors = null;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,OAAO,SAASE,2BAA2BA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;EACrF,IAAIC,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAIA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE;IACxB;EACF;EACA;EACA,IAAIC,cAAc,GAAGL,SAAS,CAACM,QAAQ,CAAC,WAAW,CAAC;EACpD,IAAIC,cAAc,GAAGF,cAAc,CAACC,QAAQ,CAAC,WAAW,CAAC;EACzD,IAAIE,UAAU,GAAGD,cAAc,CAACE,GAAG,CAAC,OAAO,CAAC;EAC5C,IAAIC,QAAQ,GAAGT,SAAS,CAACU,gBAAgB,CAACC,OAAO,CAAC,CAAC;EACnD,IAAIC,WAAW,GAAGX,IAAI,CAACY,cAAc,CAAC;IACpCC,SAAS,EAAEV,cAAc;IACzBW,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAI,CAACH,WAAW,CAACI,MAAM,EAAE;IACvB;EACF;EACA;EACA;EACA,IAAIC,aAAa,GAAGV,UAAU,CAACS,MAAM;EACrC,IAAIE,mBAAmB,GAAGvB,KAAK,CAACE,QAAQ,CAAC,CAACsB,eAAe;EACzD,IAAIC,kBAAkB,GAAG5B,MAAM,CAAC6B,aAAa,CAAC,CAAC;EAC/C,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIJ,mBAAmB,EAAE;IACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;MAC3C,IAAIC,MAAM,GAAGN,mBAAmB,CAACV,GAAG,CAACI,WAAW,CAACW,CAAC,CAAC,CAACE,SAAS,CAAC;MAC9D,IAAID,MAAM,IAAI,IAAI,EAAE;QAClBF,UAAU,GAAG,CAACE,MAAM,GAAG,CAACP,aAAa,GAAG,CAAC,IAAIM,CAAC,IAAIN,aAAa;QAC/D;MACF;IACF;EACF;EACA,IAAIS,IAAI,GAAGzB,IAAI,CAAC0B,aAAa,CAACf,WAAW,CAAC,CAAC,CAAC,CAACgB,KAAK,CAAC;EACnD,IAAIC,SAAS,GAAGvB,cAAc,CAACwB,YAAY,CAAC,CAAC;EAC7CvB,UAAU,GAAGf,MAAM,CAACuC,OAAO,CAACxB,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;EACnE,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;IAC3C,IAAIS,SAAS,GAAG/B,IAAI,CAAC0B,aAAa,CAACf,WAAW,CAACW,CAAC,CAAC,CAACK,KAAK,CAAC;IACxD,IAAIK,CAAC,GAAG,KAAK,CAAC;IACd,IAAIC,CAAC,GAAG,KAAK,CAAC;IACd,IAAIC,KAAK,GAAG,KAAK,CAAC;IAClB,IAAIC,MAAM,GAAG,KAAK,CAAC;IACnB,IAAInC,IAAI,CAACoC,YAAY,CAAC,CAAC,EAAE;MACvBJ,CAAC,GAAGP,IAAI;MACRQ,CAAC,GAAGzB,QAAQ,CAACyB,CAAC;MACdC,KAAK,GAAGH,SAAS,GAAGC,CAAC;MACrBG,MAAM,GAAG3B,QAAQ,CAAC2B,MAAM;MACxBV,IAAI,GAAGO,CAAC,GAAGE,KAAK;IAClB,CAAC,MAAM;MACLF,CAAC,GAAGxB,QAAQ,CAACwB,CAAC;MACdC,CAAC,GAAGR,IAAI;MACRS,KAAK,GAAG1B,QAAQ,CAAC0B,KAAK;MACtBC,MAAM,GAAGJ,SAAS,GAAGE,CAAC;MACtBR,IAAI,GAAGQ,CAAC,GAAGE,MAAM;IACnB;IACA,IAAIX,SAAS,GAAGb,WAAW,CAACW,CAAC,GAAG,CAAC,CAAC,CAACE,SAAS;IAC5CA,SAAS,IAAI,IAAI,IAAIL,kBAAkB,CAACkB,GAAG,CAACb,SAAS,EAAEH,UAAU,CAAC;IAClExB,SAAS,CAACyC,GAAG,CAAC,IAAI9C,OAAO,CAAC+C,IAAI,CAAC;MAC7BC,IAAI,EAAEhB,SAAS,IAAI,IAAI,GAAG,OAAO,GAAGA,SAAS,GAAG,IAAI;MACpDiB,KAAK,EAAE;QACLT,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACDO,KAAK,EAAEnD,MAAM,CAACoD,QAAQ,CAAC;QACrBC,IAAI,EAAEtC,UAAU,CAACe,UAAU;MAC7B,CAAC,EAAEO,SAAS,CAAC;MACbiB,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHzB,UAAU,GAAG,CAACA,UAAU,GAAG,CAAC,IAAIL,aAAa;EAC/C;EACAtB,KAAK,CAACE,QAAQ,CAAC,CAACsB,eAAe,GAAGC,kBAAkB;AACtD;AACA,OAAO,SAAS4B,yBAAyBA,CAACnD,QAAQ,EAAE;EAClDF,KAAK,CAACE,QAAQ,CAAC,CAACsB,eAAe,GAAG,IAAI;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}