{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as zrColor from 'zrender/lib/tool/color.js';\nimport { linearMap } from '../util/number.js';\nimport { warn } from '../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar CATEGORY_DEFAULT_VISUAL_INDEX = -1;\nvar VisualMapping = /** @class */function () {\n  function VisualMapping(option) {\n    var mappingMethod = option.mappingMethod;\n    var visualType = option.type;\n    var thisOption = this.option = zrUtil.clone(option);\n    this.type = visualType;\n    this.mappingMethod = mappingMethod;\n    this._normalizeData = normalizers[mappingMethod];\n    var visualHandler = VisualMapping.visualHandlers[visualType];\n    this.applyVisual = visualHandler.applyVisual;\n    this.getColorMapper = visualHandler.getColorMapper;\n    this._normalizedToVisual = visualHandler._normalizedToVisual[mappingMethod];\n    if (mappingMethod === 'piecewise') {\n      normalizeVisualRange(thisOption);\n      preprocessForPiecewise(thisOption);\n    } else if (mappingMethod === 'category') {\n      thisOption.categories ? preprocessForSpecifiedCategory(thisOption)\n      // categories is ordinal when thisOption.categories not specified,\n      // which need no more preprocess except normalize visual.\n      : normalizeVisualRange(thisOption, true);\n    } else {\n      // mappingMethod === 'linear' or 'fixed'\n      zrUtil.assert(mappingMethod !== 'linear' || thisOption.dataExtent);\n      normalizeVisualRange(thisOption);\n    }\n  }\n  VisualMapping.prototype.mapValueToVisual = function (value) {\n    var normalized = this._normalizeData(value);\n    return this._normalizedToVisual(normalized, value);\n  };\n  VisualMapping.prototype.getNormalizer = function () {\n    return zrUtil.bind(this._normalizeData, this);\n  };\n  /**\r\n   * List available visual types.\r\n   *\r\n   * @public\r\n   * @return {Array.<string>}\r\n   */\n  VisualMapping.listVisualTypes = function () {\n    return zrUtil.keys(VisualMapping.visualHandlers);\n  };\n  // /**\n  //  * @public\n  //  */\n  // static addVisualHandler(name, handler) {\n  //     visualHandlers[name] = handler;\n  // }\n  /**\r\n   * @public\r\n   */\n  VisualMapping.isValidType = function (visualType) {\n    return VisualMapping.visualHandlers.hasOwnProperty(visualType);\n  };\n  /**\r\n   * Convenient method.\r\n   * Visual can be Object or Array or primary type.\r\n   */\n  VisualMapping.eachVisual = function (visual, callback, context) {\n    if (zrUtil.isObject(visual)) {\n      zrUtil.each(visual, callback, context);\n    } else {\n      callback.call(context, visual);\n    }\n  };\n  VisualMapping.mapVisual = function (visual, callback, context) {\n    var isPrimary;\n    var newVisual = zrUtil.isArray(visual) ? [] : zrUtil.isObject(visual) ? {} : (isPrimary = true, null);\n    VisualMapping.eachVisual(visual, function (v, key) {\n      var newVal = callback.call(context, v, key);\n      isPrimary ? newVisual = newVal : newVisual[key] = newVal;\n    });\n    return newVisual;\n  };\n  /**\r\n   * Retrieve visual properties from given object.\r\n   */\n  VisualMapping.retrieveVisuals = function (obj) {\n    var ret = {};\n    var hasVisual;\n    obj && each(VisualMapping.visualHandlers, function (h, visualType) {\n      if (obj.hasOwnProperty(visualType)) {\n        ret[visualType] = obj[visualType];\n        hasVisual = true;\n      }\n    });\n    return hasVisual ? ret : null;\n  };\n  /**\r\n   * Give order to visual types, considering colorSaturation, colorAlpha depends on color.\r\n   *\r\n   * @public\r\n   * @param {(Object|Array)} visualTypes If Object, like: {color: ..., colorSaturation: ...}\r\n   *                                     IF Array, like: ['color', 'symbol', 'colorSaturation']\r\n   * @return {Array.<string>} Sorted visual types.\r\n   */\n  VisualMapping.prepareVisualTypes = function (visualTypes) {\n    if (zrUtil.isArray(visualTypes)) {\n      visualTypes = visualTypes.slice();\n    } else if (isObject(visualTypes)) {\n      var types_1 = [];\n      each(visualTypes, function (item, type) {\n        types_1.push(type);\n      });\n      visualTypes = types_1;\n    } else {\n      return [];\n    }\n    visualTypes.sort(function (type1, type2) {\n      // color should be front of colorSaturation, colorAlpha, ...\n      // symbol and symbolSize do not matter.\n      return type2 === 'color' && type1 !== 'color' && type1.indexOf('color') === 0 ? 1 : -1;\n    });\n    return visualTypes;\n  };\n  /**\r\n   * 'color', 'colorSaturation', 'colorAlpha', ... are depends on 'color'.\r\n   * Other visuals are only depends on themself.\r\n   */\n  VisualMapping.dependsOn = function (visualType1, visualType2) {\n    return visualType2 === 'color' ? !!(visualType1 && visualType1.indexOf(visualType2) === 0) : visualType1 === visualType2;\n  };\n  /**\r\n   * @param value\r\n   * @param pieceList [{value: ..., interval: [min, max]}, ...]\r\n   *                         Always from small to big.\r\n   * @param findClosestWhenOutside Default to be false\r\n   * @return index\r\n   */\n  VisualMapping.findPieceIndex = function (value, pieceList, findClosestWhenOutside) {\n    var possibleI;\n    var abs = Infinity;\n    // value has the higher priority.\n    for (var i = 0, len = pieceList.length; i < len; i++) {\n      var pieceValue = pieceList[i].value;\n      if (pieceValue != null) {\n        if (pieceValue === value\n        // FIXME\n        // It is supposed to compare value according to value type of dimension,\n        // but currently value type can exactly be string or number.\n        // Compromise for numeric-like string (like '12'), especially\n        // in the case that visualMap.categories is ['22', '33'].\n        || zrUtil.isString(pieceValue) && pieceValue === value + '') {\n          return i;\n        }\n        findClosestWhenOutside && updatePossible(pieceValue, i);\n      }\n    }\n    for (var i = 0, len = pieceList.length; i < len; i++) {\n      var piece = pieceList[i];\n      var interval = piece.interval;\n      var close_1 = piece.close;\n      if (interval) {\n        if (interval[0] === -Infinity) {\n          if (littleThan(close_1[1], value, interval[1])) {\n            return i;\n          }\n        } else if (interval[1] === Infinity) {\n          if (littleThan(close_1[0], interval[0], value)) {\n            return i;\n          }\n        } else if (littleThan(close_1[0], interval[0], value) && littleThan(close_1[1], value, interval[1])) {\n          return i;\n        }\n        findClosestWhenOutside && updatePossible(interval[0], i);\n        findClosestWhenOutside && updatePossible(interval[1], i);\n      }\n    }\n    if (findClosestWhenOutside) {\n      return value === Infinity ? pieceList.length - 1 : value === -Infinity ? 0 : possibleI;\n    }\n    function updatePossible(val, index) {\n      var newAbs = Math.abs(val - value);\n      if (newAbs < abs) {\n        abs = newAbs;\n        possibleI = index;\n      }\n    }\n  };\n  VisualMapping.visualHandlers = {\n    color: {\n      applyVisual: makeApplyVisual('color'),\n      getColorMapper: function () {\n        var thisOption = this.option;\n        return zrUtil.bind(thisOption.mappingMethod === 'category' ? function (value, isNormalized) {\n          !isNormalized && (value = this._normalizeData(value));\n          return doMapCategory.call(this, value);\n        } : function (value, isNormalized, out) {\n          // If output rgb array\n          // which will be much faster and useful in pixel manipulation\n          var returnRGBArray = !!out;\n          !isNormalized && (value = this._normalizeData(value));\n          out = zrColor.fastLerp(value, thisOption.parsedVisual, out);\n          return returnRGBArray ? out : zrColor.stringify(out, 'rgba');\n        }, this);\n      },\n      _normalizedToVisual: {\n        linear: function (normalized) {\n          return zrColor.stringify(zrColor.fastLerp(normalized, this.option.parsedVisual), 'rgba');\n        },\n        category: doMapCategory,\n        piecewise: function (normalized, value) {\n          var result = getSpecifiedVisual.call(this, value);\n          if (result == null) {\n            result = zrColor.stringify(zrColor.fastLerp(normalized, this.option.parsedVisual), 'rgba');\n          }\n          return result;\n        },\n        fixed: doMapFixed\n      }\n    },\n    colorHue: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, value);\n    }),\n    colorSaturation: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, null, value);\n    }),\n    colorLightness: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, null, null, value);\n    }),\n    colorAlpha: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyAlpha(color, value);\n    }),\n    decal: {\n      applyVisual: makeApplyVisual('decal'),\n      _normalizedToVisual: {\n        linear: null,\n        category: doMapCategory,\n        piecewise: null,\n        fixed: null\n      }\n    },\n    opacity: {\n      applyVisual: makeApplyVisual('opacity'),\n      _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n    },\n    liftZ: {\n      applyVisual: makeApplyVisual('liftZ'),\n      _normalizedToVisual: {\n        linear: doMapFixed,\n        category: doMapFixed,\n        piecewise: doMapFixed,\n        fixed: doMapFixed\n      }\n    },\n    symbol: {\n      applyVisual: function (value, getter, setter) {\n        var symbolCfg = this.mapValueToVisual(value);\n        setter('symbol', symbolCfg);\n      },\n      _normalizedToVisual: {\n        linear: doMapToArray,\n        category: doMapCategory,\n        piecewise: function (normalized, value) {\n          var result = getSpecifiedVisual.call(this, value);\n          if (result == null) {\n            result = doMapToArray.call(this, normalized);\n          }\n          return result;\n        },\n        fixed: doMapFixed\n      }\n    },\n    symbolSize: {\n      applyVisual: makeApplyVisual('symbolSize'),\n      _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n    }\n  };\n  return VisualMapping;\n}();\nfunction preprocessForPiecewise(thisOption) {\n  var pieceList = thisOption.pieceList;\n  thisOption.hasSpecialVisual = false;\n  zrUtil.each(pieceList, function (piece, index) {\n    piece.originIndex = index;\n    // piece.visual is \"result visual value\" but not\n    // a visual range, so it does not need to be normalized.\n    if (piece.visual != null) {\n      thisOption.hasSpecialVisual = true;\n    }\n  });\n}\nfunction preprocessForSpecifiedCategory(thisOption) {\n  // Hash categories.\n  var categories = thisOption.categories;\n  var categoryMap = thisOption.categoryMap = {};\n  var visual = thisOption.visual;\n  each(categories, function (cate, index) {\n    categoryMap[cate] = index;\n  });\n  // Process visual map input.\n  if (!zrUtil.isArray(visual)) {\n    var visualArr_1 = [];\n    if (zrUtil.isObject(visual)) {\n      each(visual, function (v, cate) {\n        var index = categoryMap[cate];\n        visualArr_1[index != null ? index : CATEGORY_DEFAULT_VISUAL_INDEX] = v;\n      });\n    } else {\n      // Is primary type, represents default visual.\n      visualArr_1[CATEGORY_DEFAULT_VISUAL_INDEX] = visual;\n    }\n    visual = setVisualToOption(thisOption, visualArr_1);\n  }\n  // Remove categories that has no visual,\n  // then we can mapping them to CATEGORY_DEFAULT_VISUAL_INDEX.\n  for (var i = categories.length - 1; i >= 0; i--) {\n    if (visual[i] == null) {\n      delete categoryMap[categories[i]];\n      categories.pop();\n    }\n  }\n}\nfunction normalizeVisualRange(thisOption, isCategory) {\n  var visual = thisOption.visual;\n  var visualArr = [];\n  if (zrUtil.isObject(visual)) {\n    each(visual, function (v) {\n      visualArr.push(v);\n    });\n  } else if (visual != null) {\n    visualArr.push(visual);\n  }\n  var doNotNeedPair = {\n    color: 1,\n    symbol: 1\n  };\n  if (!isCategory && visualArr.length === 1 && !doNotNeedPair.hasOwnProperty(thisOption.type)) {\n    // Do not care visualArr.length === 0, which is illegal.\n    visualArr[1] = visualArr[0];\n  }\n  setVisualToOption(thisOption, visualArr);\n}\nfunction makePartialColorVisualHandler(applyValue) {\n  return {\n    applyVisual: function (value, getter, setter) {\n      // Only used in HSL\n      var colorChannel = this.mapValueToVisual(value);\n      // Must not be array value\n      setter('color', applyValue(getter('color'), colorChannel));\n    },\n    _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n  };\n}\nfunction doMapToArray(normalized) {\n  var visual = this.option.visual;\n  return visual[Math.round(linearMap(normalized, [0, 1], [0, visual.length - 1], true))] || {}; // TODO {}?\n}\nfunction makeApplyVisual(visualType) {\n  return function (value, getter, setter) {\n    setter(visualType, this.mapValueToVisual(value));\n  };\n}\nfunction doMapCategory(normalized) {\n  var visual = this.option.visual;\n  return visual[this.option.loop && normalized !== CATEGORY_DEFAULT_VISUAL_INDEX ? normalized % visual.length : normalized];\n}\nfunction doMapFixed() {\n  // visual will be convert to array.\n  return this.option.visual[0];\n}\n/**\r\n * Create mapped to numeric visual\r\n */\nfunction createNormalizedToNumericVisual(sourceExtent) {\n  return {\n    linear: function (normalized) {\n      return linearMap(normalized, sourceExtent, this.option.visual, true);\n    },\n    category: doMapCategory,\n    piecewise: function (normalized, value) {\n      var result = getSpecifiedVisual.call(this, value);\n      if (result == null) {\n        result = linearMap(normalized, sourceExtent, this.option.visual, true);\n      }\n      return result;\n    },\n    fixed: doMapFixed\n  };\n}\nfunction getSpecifiedVisual(value) {\n  var thisOption = this.option;\n  var pieceList = thisOption.pieceList;\n  if (thisOption.hasSpecialVisual) {\n    var pieceIndex = VisualMapping.findPieceIndex(value, pieceList);\n    var piece = pieceList[pieceIndex];\n    if (piece && piece.visual) {\n      return piece.visual[this.type];\n    }\n  }\n}\nfunction setVisualToOption(thisOption, visualArr) {\n  thisOption.visual = visualArr;\n  if (thisOption.type === 'color') {\n    thisOption.parsedVisual = zrUtil.map(visualArr, function (item) {\n      var color = zrColor.parse(item);\n      if (!color && process.env.NODE_ENV !== 'production') {\n        warn(\"'\" + item + \"' is an illegal color, fallback to '#000000'\", true);\n      }\n      return color || [0, 0, 0, 1];\n    });\n  }\n  return visualArr;\n}\n/**\r\n * Normalizers by mapping methods.\r\n */\nvar normalizers = {\n  linear: function (value) {\n    return linearMap(value, this.option.dataExtent, [0, 1], true);\n  },\n  piecewise: function (value) {\n    var pieceList = this.option.pieceList;\n    var pieceIndex = VisualMapping.findPieceIndex(value, pieceList, true);\n    if (pieceIndex != null) {\n      return linearMap(pieceIndex, [0, pieceList.length - 1], [0, 1], true);\n    }\n  },\n  category: function (value) {\n    var index = this.option.categories ? this.option.categoryMap[value] : value; // ordinal value\n    return index == null ? CATEGORY_DEFAULT_VISUAL_INDEX : index;\n  },\n  fixed: zrUtil.noop\n};\nfunction littleThan(close, a, b) {\n  return close ? a <= b : a < b;\n}\nexport default VisualMapping;", "map": {"version": 3, "names": ["zrUtil", "zrColor", "linearMap", "warn", "each", "isObject", "CATEGORY_DEFAULT_VISUAL_INDEX", "VisualMapping", "option", "mappingMethod", "visualType", "type", "thisOption", "clone", "_normalizeData", "normalizers", "<PERSON><PERSON><PERSON><PERSON>", "visualHandlers", "applyVisual", "getColorMapper", "_normalizedToVisual", "normalizeVisualRange", "preprocessForPiecewise", "categories", "preprocessForSpecifiedCategory", "assert", "dataExtent", "prototype", "mapValueToVisual", "value", "normalized", "getNormalizer", "bind", "listVisualTypes", "keys", "isValidType", "hasOwnProperty", "eachVisual", "visual", "callback", "context", "call", "mapVisual", "isPrimary", "newVisual", "isArray", "v", "key", "newVal", "retrieveVisuals", "obj", "ret", "hasVisual", "h", "prepareVisualTypes", "visualTypes", "slice", "types_1", "item", "push", "sort", "type1", "type2", "indexOf", "dependsOn", "visualType1", "visualType2", "findPieceIndex", "pieceList", "findClosestWhenOutside", "possibleI", "abs", "Infinity", "i", "len", "length", "pieceValue", "isString", "updatePossible", "piece", "interval", "close_1", "close", "<PERSON><PERSON><PERSON>", "val", "index", "newAbs", "Math", "color", "makeApplyVisual", "isNormalized", "doMapCategory", "out", "returnRGBArray", "fastLerp", "parsedVisual", "stringify", "linear", "category", "piecewise", "result", "getSpecifiedVisual", "fixed", "doMapFixed", "colorHue", "makePartialColorVisualHandler", "modifyHSL", "colorSaturation", "colorLightness", "colorAlpha", "modifyAlpha", "decal", "opacity", "createNormalizedToNumericVisual", "liftZ", "symbol", "getter", "setter", "symbolCfg", "doMapToArray", "symbolSize", "hasSpecialVisual", "originIndex", "categoryMap", "cate", "visualArr_1", "setVisualToOption", "pop", "isCategory", "visualArr", "doNotNeedPair", "applyValue", "colorChannel", "round", "loop", "sourceExtent", "pieceIndex", "map", "parse", "process", "env", "NODE_ENV", "noop", "a", "b"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/visual/VisualMapping.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as zrColor from 'zrender/lib/tool/color.js';\nimport { linearMap } from '../util/number.js';\nimport { warn } from '../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar CATEGORY_DEFAULT_VISUAL_INDEX = -1;\nvar VisualMapping = /** @class */function () {\n  function VisualMapping(option) {\n    var mappingMethod = option.mappingMethod;\n    var visualType = option.type;\n    var thisOption = this.option = zrUtil.clone(option);\n    this.type = visualType;\n    this.mappingMethod = mappingMethod;\n    this._normalizeData = normalizers[mappingMethod];\n    var visualHandler = VisualMapping.visualHandlers[visualType];\n    this.applyVisual = visualHandler.applyVisual;\n    this.getColorMapper = visualHandler.getColorMapper;\n    this._normalizedToVisual = visualHandler._normalizedToVisual[mappingMethod];\n    if (mappingMethod === 'piecewise') {\n      normalizeVisualRange(thisOption);\n      preprocessForPiecewise(thisOption);\n    } else if (mappingMethod === 'category') {\n      thisOption.categories ? preprocessForSpecifiedCategory(thisOption)\n      // categories is ordinal when thisOption.categories not specified,\n      // which need no more preprocess except normalize visual.\n      : normalizeVisualRange(thisOption, true);\n    } else {\n      // mappingMethod === 'linear' or 'fixed'\n      zrUtil.assert(mappingMethod !== 'linear' || thisOption.dataExtent);\n      normalizeVisualRange(thisOption);\n    }\n  }\n  VisualMapping.prototype.mapValueToVisual = function (value) {\n    var normalized = this._normalizeData(value);\n    return this._normalizedToVisual(normalized, value);\n  };\n  VisualMapping.prototype.getNormalizer = function () {\n    return zrUtil.bind(this._normalizeData, this);\n  };\n  /**\r\n   * List available visual types.\r\n   *\r\n   * @public\r\n   * @return {Array.<string>}\r\n   */\n  VisualMapping.listVisualTypes = function () {\n    return zrUtil.keys(VisualMapping.visualHandlers);\n  };\n  // /**\n  //  * @public\n  //  */\n  // static addVisualHandler(name, handler) {\n  //     visualHandlers[name] = handler;\n  // }\n  /**\r\n   * @public\r\n   */\n  VisualMapping.isValidType = function (visualType) {\n    return VisualMapping.visualHandlers.hasOwnProperty(visualType);\n  };\n  /**\r\n   * Convenient method.\r\n   * Visual can be Object or Array or primary type.\r\n   */\n  VisualMapping.eachVisual = function (visual, callback, context) {\n    if (zrUtil.isObject(visual)) {\n      zrUtil.each(visual, callback, context);\n    } else {\n      callback.call(context, visual);\n    }\n  };\n  VisualMapping.mapVisual = function (visual, callback, context) {\n    var isPrimary;\n    var newVisual = zrUtil.isArray(visual) ? [] : zrUtil.isObject(visual) ? {} : (isPrimary = true, null);\n    VisualMapping.eachVisual(visual, function (v, key) {\n      var newVal = callback.call(context, v, key);\n      isPrimary ? newVisual = newVal : newVisual[key] = newVal;\n    });\n    return newVisual;\n  };\n  /**\r\n   * Retrieve visual properties from given object.\r\n   */\n  VisualMapping.retrieveVisuals = function (obj) {\n    var ret = {};\n    var hasVisual;\n    obj && each(VisualMapping.visualHandlers, function (h, visualType) {\n      if (obj.hasOwnProperty(visualType)) {\n        ret[visualType] = obj[visualType];\n        hasVisual = true;\n      }\n    });\n    return hasVisual ? ret : null;\n  };\n  /**\r\n   * Give order to visual types, considering colorSaturation, colorAlpha depends on color.\r\n   *\r\n   * @public\r\n   * @param {(Object|Array)} visualTypes If Object, like: {color: ..., colorSaturation: ...}\r\n   *                                     IF Array, like: ['color', 'symbol', 'colorSaturation']\r\n   * @return {Array.<string>} Sorted visual types.\r\n   */\n  VisualMapping.prepareVisualTypes = function (visualTypes) {\n    if (zrUtil.isArray(visualTypes)) {\n      visualTypes = visualTypes.slice();\n    } else if (isObject(visualTypes)) {\n      var types_1 = [];\n      each(visualTypes, function (item, type) {\n        types_1.push(type);\n      });\n      visualTypes = types_1;\n    } else {\n      return [];\n    }\n    visualTypes.sort(function (type1, type2) {\n      // color should be front of colorSaturation, colorAlpha, ...\n      // symbol and symbolSize do not matter.\n      return type2 === 'color' && type1 !== 'color' && type1.indexOf('color') === 0 ? 1 : -1;\n    });\n    return visualTypes;\n  };\n  /**\r\n   * 'color', 'colorSaturation', 'colorAlpha', ... are depends on 'color'.\r\n   * Other visuals are only depends on themself.\r\n   */\n  VisualMapping.dependsOn = function (visualType1, visualType2) {\n    return visualType2 === 'color' ? !!(visualType1 && visualType1.indexOf(visualType2) === 0) : visualType1 === visualType2;\n  };\n  /**\r\n   * @param value\r\n   * @param pieceList [{value: ..., interval: [min, max]}, ...]\r\n   *                         Always from small to big.\r\n   * @param findClosestWhenOutside Default to be false\r\n   * @return index\r\n   */\n  VisualMapping.findPieceIndex = function (value, pieceList, findClosestWhenOutside) {\n    var possibleI;\n    var abs = Infinity;\n    // value has the higher priority.\n    for (var i = 0, len = pieceList.length; i < len; i++) {\n      var pieceValue = pieceList[i].value;\n      if (pieceValue != null) {\n        if (pieceValue === value\n        // FIXME\n        // It is supposed to compare value according to value type of dimension,\n        // but currently value type can exactly be string or number.\n        // Compromise for numeric-like string (like '12'), especially\n        // in the case that visualMap.categories is ['22', '33'].\n        || zrUtil.isString(pieceValue) && pieceValue === value + '') {\n          return i;\n        }\n        findClosestWhenOutside && updatePossible(pieceValue, i);\n      }\n    }\n    for (var i = 0, len = pieceList.length; i < len; i++) {\n      var piece = pieceList[i];\n      var interval = piece.interval;\n      var close_1 = piece.close;\n      if (interval) {\n        if (interval[0] === -Infinity) {\n          if (littleThan(close_1[1], value, interval[1])) {\n            return i;\n          }\n        } else if (interval[1] === Infinity) {\n          if (littleThan(close_1[0], interval[0], value)) {\n            return i;\n          }\n        } else if (littleThan(close_1[0], interval[0], value) && littleThan(close_1[1], value, interval[1])) {\n          return i;\n        }\n        findClosestWhenOutside && updatePossible(interval[0], i);\n        findClosestWhenOutside && updatePossible(interval[1], i);\n      }\n    }\n    if (findClosestWhenOutside) {\n      return value === Infinity ? pieceList.length - 1 : value === -Infinity ? 0 : possibleI;\n    }\n    function updatePossible(val, index) {\n      var newAbs = Math.abs(val - value);\n      if (newAbs < abs) {\n        abs = newAbs;\n        possibleI = index;\n      }\n    }\n  };\n  VisualMapping.visualHandlers = {\n    color: {\n      applyVisual: makeApplyVisual('color'),\n      getColorMapper: function () {\n        var thisOption = this.option;\n        return zrUtil.bind(thisOption.mappingMethod === 'category' ? function (value, isNormalized) {\n          !isNormalized && (value = this._normalizeData(value));\n          return doMapCategory.call(this, value);\n        } : function (value, isNormalized, out) {\n          // If output rgb array\n          // which will be much faster and useful in pixel manipulation\n          var returnRGBArray = !!out;\n          !isNormalized && (value = this._normalizeData(value));\n          out = zrColor.fastLerp(value, thisOption.parsedVisual, out);\n          return returnRGBArray ? out : zrColor.stringify(out, 'rgba');\n        }, this);\n      },\n      _normalizedToVisual: {\n        linear: function (normalized) {\n          return zrColor.stringify(zrColor.fastLerp(normalized, this.option.parsedVisual), 'rgba');\n        },\n        category: doMapCategory,\n        piecewise: function (normalized, value) {\n          var result = getSpecifiedVisual.call(this, value);\n          if (result == null) {\n            result = zrColor.stringify(zrColor.fastLerp(normalized, this.option.parsedVisual), 'rgba');\n          }\n          return result;\n        },\n        fixed: doMapFixed\n      }\n    },\n    colorHue: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, value);\n    }),\n    colorSaturation: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, null, value);\n    }),\n    colorLightness: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyHSL(color, null, null, value);\n    }),\n    colorAlpha: makePartialColorVisualHandler(function (color, value) {\n      return zrColor.modifyAlpha(color, value);\n    }),\n    decal: {\n      applyVisual: makeApplyVisual('decal'),\n      _normalizedToVisual: {\n        linear: null,\n        category: doMapCategory,\n        piecewise: null,\n        fixed: null\n      }\n    },\n    opacity: {\n      applyVisual: makeApplyVisual('opacity'),\n      _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n    },\n    liftZ: {\n      applyVisual: makeApplyVisual('liftZ'),\n      _normalizedToVisual: {\n        linear: doMapFixed,\n        category: doMapFixed,\n        piecewise: doMapFixed,\n        fixed: doMapFixed\n      }\n    },\n    symbol: {\n      applyVisual: function (value, getter, setter) {\n        var symbolCfg = this.mapValueToVisual(value);\n        setter('symbol', symbolCfg);\n      },\n      _normalizedToVisual: {\n        linear: doMapToArray,\n        category: doMapCategory,\n        piecewise: function (normalized, value) {\n          var result = getSpecifiedVisual.call(this, value);\n          if (result == null) {\n            result = doMapToArray.call(this, normalized);\n          }\n          return result;\n        },\n        fixed: doMapFixed\n      }\n    },\n    symbolSize: {\n      applyVisual: makeApplyVisual('symbolSize'),\n      _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n    }\n  };\n  return VisualMapping;\n}();\nfunction preprocessForPiecewise(thisOption) {\n  var pieceList = thisOption.pieceList;\n  thisOption.hasSpecialVisual = false;\n  zrUtil.each(pieceList, function (piece, index) {\n    piece.originIndex = index;\n    // piece.visual is \"result visual value\" but not\n    // a visual range, so it does not need to be normalized.\n    if (piece.visual != null) {\n      thisOption.hasSpecialVisual = true;\n    }\n  });\n}\nfunction preprocessForSpecifiedCategory(thisOption) {\n  // Hash categories.\n  var categories = thisOption.categories;\n  var categoryMap = thisOption.categoryMap = {};\n  var visual = thisOption.visual;\n  each(categories, function (cate, index) {\n    categoryMap[cate] = index;\n  });\n  // Process visual map input.\n  if (!zrUtil.isArray(visual)) {\n    var visualArr_1 = [];\n    if (zrUtil.isObject(visual)) {\n      each(visual, function (v, cate) {\n        var index = categoryMap[cate];\n        visualArr_1[index != null ? index : CATEGORY_DEFAULT_VISUAL_INDEX] = v;\n      });\n    } else {\n      // Is primary type, represents default visual.\n      visualArr_1[CATEGORY_DEFAULT_VISUAL_INDEX] = visual;\n    }\n    visual = setVisualToOption(thisOption, visualArr_1);\n  }\n  // Remove categories that has no visual,\n  // then we can mapping them to CATEGORY_DEFAULT_VISUAL_INDEX.\n  for (var i = categories.length - 1; i >= 0; i--) {\n    if (visual[i] == null) {\n      delete categoryMap[categories[i]];\n      categories.pop();\n    }\n  }\n}\nfunction normalizeVisualRange(thisOption, isCategory) {\n  var visual = thisOption.visual;\n  var visualArr = [];\n  if (zrUtil.isObject(visual)) {\n    each(visual, function (v) {\n      visualArr.push(v);\n    });\n  } else if (visual != null) {\n    visualArr.push(visual);\n  }\n  var doNotNeedPair = {\n    color: 1,\n    symbol: 1\n  };\n  if (!isCategory && visualArr.length === 1 && !doNotNeedPair.hasOwnProperty(thisOption.type)) {\n    // Do not care visualArr.length === 0, which is illegal.\n    visualArr[1] = visualArr[0];\n  }\n  setVisualToOption(thisOption, visualArr);\n}\nfunction makePartialColorVisualHandler(applyValue) {\n  return {\n    applyVisual: function (value, getter, setter) {\n      // Only used in HSL\n      var colorChannel = this.mapValueToVisual(value);\n      // Must not be array value\n      setter('color', applyValue(getter('color'), colorChannel));\n    },\n    _normalizedToVisual: createNormalizedToNumericVisual([0, 1])\n  };\n}\nfunction doMapToArray(normalized) {\n  var visual = this.option.visual;\n  return visual[Math.round(linearMap(normalized, [0, 1], [0, visual.length - 1], true))] || {}; // TODO {}?\n}\nfunction makeApplyVisual(visualType) {\n  return function (value, getter, setter) {\n    setter(visualType, this.mapValueToVisual(value));\n  };\n}\nfunction doMapCategory(normalized) {\n  var visual = this.option.visual;\n  return visual[this.option.loop && normalized !== CATEGORY_DEFAULT_VISUAL_INDEX ? normalized % visual.length : normalized];\n}\nfunction doMapFixed() {\n  // visual will be convert to array.\n  return this.option.visual[0];\n}\n/**\r\n * Create mapped to numeric visual\r\n */\nfunction createNormalizedToNumericVisual(sourceExtent) {\n  return {\n    linear: function (normalized) {\n      return linearMap(normalized, sourceExtent, this.option.visual, true);\n    },\n    category: doMapCategory,\n    piecewise: function (normalized, value) {\n      var result = getSpecifiedVisual.call(this, value);\n      if (result == null) {\n        result = linearMap(normalized, sourceExtent, this.option.visual, true);\n      }\n      return result;\n    },\n    fixed: doMapFixed\n  };\n}\nfunction getSpecifiedVisual(value) {\n  var thisOption = this.option;\n  var pieceList = thisOption.pieceList;\n  if (thisOption.hasSpecialVisual) {\n    var pieceIndex = VisualMapping.findPieceIndex(value, pieceList);\n    var piece = pieceList[pieceIndex];\n    if (piece && piece.visual) {\n      return piece.visual[this.type];\n    }\n  }\n}\nfunction setVisualToOption(thisOption, visualArr) {\n  thisOption.visual = visualArr;\n  if (thisOption.type === 'color') {\n    thisOption.parsedVisual = zrUtil.map(visualArr, function (item) {\n      var color = zrColor.parse(item);\n      if (!color && process.env.NODE_ENV !== 'production') {\n        warn(\"'\" + item + \"' is an illegal color, fallback to '#000000'\", true);\n      }\n      return color || [0, 0, 0, 1];\n    });\n  }\n  return visualArr;\n}\n/**\r\n * Normalizers by mapping methods.\r\n */\nvar normalizers = {\n  linear: function (value) {\n    return linearMap(value, this.option.dataExtent, [0, 1], true);\n  },\n  piecewise: function (value) {\n    var pieceList = this.option.pieceList;\n    var pieceIndex = VisualMapping.findPieceIndex(value, pieceList, true);\n    if (pieceIndex != null) {\n      return linearMap(pieceIndex, [0, pieceList.length - 1], [0, 1], true);\n    }\n  },\n  category: function (value) {\n    var index = this.option.categories ? this.option.categoryMap[value] : value; // ordinal value\n    return index == null ? CATEGORY_DEFAULT_VISUAL_INDEX : index;\n  },\n  fixed: zrUtil.noop\n};\nfunction littleThan(close, a, b) {\n  return close ? a <= b : a < b;\n}\nexport default VisualMapping;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,2BAA2B;AACpD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB,IAAIC,QAAQ,GAAGL,MAAM,CAACK,QAAQ;AAC9B,IAAIC,6BAA6B,GAAG,CAAC,CAAC;AACtC,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAACC,MAAM,EAAE;IAC7B,IAAIC,aAAa,GAAGD,MAAM,CAACC,aAAa;IACxC,IAAIC,UAAU,GAAGF,MAAM,CAACG,IAAI;IAC5B,IAAIC,UAAU,GAAG,IAAI,CAACJ,MAAM,GAAGR,MAAM,CAACa,KAAK,CAACL,MAAM,CAAC;IACnD,IAAI,CAACG,IAAI,GAAGD,UAAU;IACtB,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACK,cAAc,GAAGC,WAAW,CAACN,aAAa,CAAC;IAChD,IAAIO,aAAa,GAAGT,aAAa,CAACU,cAAc,CAACP,UAAU,CAAC;IAC5D,IAAI,CAACQ,WAAW,GAAGF,aAAa,CAACE,WAAW;IAC5C,IAAI,CAACC,cAAc,GAAGH,aAAa,CAACG,cAAc;IAClD,IAAI,CAACC,mBAAmB,GAAGJ,aAAa,CAACI,mBAAmB,CAACX,aAAa,CAAC;IAC3E,IAAIA,aAAa,KAAK,WAAW,EAAE;MACjCY,oBAAoB,CAACT,UAAU,CAAC;MAChCU,sBAAsB,CAACV,UAAU,CAAC;IACpC,CAAC,MAAM,IAAIH,aAAa,KAAK,UAAU,EAAE;MACvCG,UAAU,CAACW,UAAU,GAAGC,8BAA8B,CAACZ,UAAU;MACjE;MACA;MAAA,EACES,oBAAoB,CAACT,UAAU,EAAE,IAAI,CAAC;IAC1C,CAAC,MAAM;MACL;MACAZ,MAAM,CAACyB,MAAM,CAAChB,aAAa,KAAK,QAAQ,IAAIG,UAAU,CAACc,UAAU,CAAC;MAClEL,oBAAoB,CAACT,UAAU,CAAC;IAClC;EACF;EACAL,aAAa,CAACoB,SAAS,CAACC,gBAAgB,GAAG,UAAUC,KAAK,EAAE;IAC1D,IAAIC,UAAU,GAAG,IAAI,CAAChB,cAAc,CAACe,KAAK,CAAC;IAC3C,OAAO,IAAI,CAACT,mBAAmB,CAACU,UAAU,EAAED,KAAK,CAAC;EACpD,CAAC;EACDtB,aAAa,CAACoB,SAAS,CAACI,aAAa,GAAG,YAAY;IAClD,OAAO/B,MAAM,CAACgC,IAAI,CAAC,IAAI,CAAClB,cAAc,EAAE,IAAI,CAAC;EAC/C,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEP,aAAa,CAAC0B,eAAe,GAAG,YAAY;IAC1C,OAAOjC,MAAM,CAACkC,IAAI,CAAC3B,aAAa,CAACU,cAAc,CAAC;EAClD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA;EACEV,aAAa,CAAC4B,WAAW,GAAG,UAAUzB,UAAU,EAAE;IAChD,OAAOH,aAAa,CAACU,cAAc,CAACmB,cAAc,CAAC1B,UAAU,CAAC;EAChE,CAAC;EACD;AACF;AACA;AACA;EACEH,aAAa,CAAC8B,UAAU,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC9D,IAAIxC,MAAM,CAACK,QAAQ,CAACiC,MAAM,CAAC,EAAE;MAC3BtC,MAAM,CAACI,IAAI,CAACkC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IACxC,CAAC,MAAM;MACLD,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAEF,MAAM,CAAC;IAChC;EACF,CAAC;EACD/B,aAAa,CAACmC,SAAS,GAAG,UAAUJ,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC7D,IAAIG,SAAS;IACb,IAAIC,SAAS,GAAG5C,MAAM,CAAC6C,OAAO,CAACP,MAAM,CAAC,GAAG,EAAE,GAAGtC,MAAM,CAACK,QAAQ,CAACiC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAIK,SAAS,GAAG,IAAI,EAAE,IAAI,CAAC;IACrGpC,aAAa,CAAC8B,UAAU,CAACC,MAAM,EAAE,UAAUQ,CAAC,EAAEC,GAAG,EAAE;MACjD,IAAIC,MAAM,GAAGT,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAEM,CAAC,EAAEC,GAAG,CAAC;MAC3CJ,SAAS,GAAGC,SAAS,GAAGI,MAAM,GAAGJ,SAAS,CAACG,GAAG,CAAC,GAAGC,MAAM;IAC1D,CAAC,CAAC;IACF,OAAOJ,SAAS;EAClB,CAAC;EACD;AACF;AACA;EACErC,aAAa,CAAC0C,eAAe,GAAG,UAAUC,GAAG,EAAE;IAC7C,IAAIC,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIC,SAAS;IACbF,GAAG,IAAI9C,IAAI,CAACG,aAAa,CAACU,cAAc,EAAE,UAAUoC,CAAC,EAAE3C,UAAU,EAAE;MACjE,IAAIwC,GAAG,CAACd,cAAc,CAAC1B,UAAU,CAAC,EAAE;QAClCyC,GAAG,CAACzC,UAAU,CAAC,GAAGwC,GAAG,CAACxC,UAAU,CAAC;QACjC0C,SAAS,GAAG,IAAI;MAClB;IACF,CAAC,CAAC;IACF,OAAOA,SAAS,GAAGD,GAAG,GAAG,IAAI;EAC/B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE5C,aAAa,CAAC+C,kBAAkB,GAAG,UAAUC,WAAW,EAAE;IACxD,IAAIvD,MAAM,CAAC6C,OAAO,CAACU,WAAW,CAAC,EAAE;MAC/BA,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC,CAAC;IACnC,CAAC,MAAM,IAAInD,QAAQ,CAACkD,WAAW,CAAC,EAAE;MAChC,IAAIE,OAAO,GAAG,EAAE;MAChBrD,IAAI,CAACmD,WAAW,EAAE,UAAUG,IAAI,EAAE/C,IAAI,EAAE;QACtC8C,OAAO,CAACE,IAAI,CAAChD,IAAI,CAAC;MACpB,CAAC,CAAC;MACF4C,WAAW,GAAGE,OAAO;IACvB,CAAC,MAAM;MACL,OAAO,EAAE;IACX;IACAF,WAAW,CAACK,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MACvC;MACA;MACA,OAAOA,KAAK,KAAK,OAAO,IAAID,KAAK,KAAK,OAAO,IAAIA,KAAK,CAACE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxF,CAAC,CAAC;IACF,OAAOR,WAAW;EACpB,CAAC;EACD;AACF;AACA;AACA;EACEhD,aAAa,CAACyD,SAAS,GAAG,UAAUC,WAAW,EAAEC,WAAW,EAAE;IAC5D,OAAOA,WAAW,KAAK,OAAO,GAAG,CAAC,EAAED,WAAW,IAAIA,WAAW,CAACF,OAAO,CAACG,WAAW,CAAC,KAAK,CAAC,CAAC,GAAGD,WAAW,KAAKC,WAAW;EAC1H,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACE3D,aAAa,CAAC4D,cAAc,GAAG,UAAUtC,KAAK,EAAEuC,SAAS,EAAEC,sBAAsB,EAAE;IACjF,IAAIC,SAAS;IACb,IAAIC,GAAG,GAAGC,QAAQ;IAClB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,SAAS,CAACO,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,IAAIG,UAAU,GAAGR,SAAS,CAACK,CAAC,CAAC,CAAC5C,KAAK;MACnC,IAAI+C,UAAU,IAAI,IAAI,EAAE;QACtB,IAAIA,UAAU,KAAK/C;QACnB;QACA;QACA;QACA;QACA;QAAA,GACG7B,MAAM,CAAC6E,QAAQ,CAACD,UAAU,CAAC,IAAIA,UAAU,KAAK/C,KAAK,GAAG,EAAE,EAAE;UAC3D,OAAO4C,CAAC;QACV;QACAJ,sBAAsB,IAAIS,cAAc,CAACF,UAAU,EAAEH,CAAC,CAAC;MACzD;IACF;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,SAAS,CAACO,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,IAAIM,KAAK,GAAGX,SAAS,CAACK,CAAC,CAAC;MACxB,IAAIO,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK;MACzB,IAAIF,QAAQ,EAAE;QACZ,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAK,CAACR,QAAQ,EAAE;UAC7B,IAAIW,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAEpD,KAAK,EAAEmD,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9C,OAAOP,CAAC;UACV;QACF,CAAC,MAAM,IAAIO,QAAQ,CAAC,CAAC,CAAC,KAAKR,QAAQ,EAAE;UACnC,IAAIW,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,EAAEnD,KAAK,CAAC,EAAE;YAC9C,OAAO4C,CAAC;UACV;QACF,CAAC,MAAM,IAAIU,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,EAAEnD,KAAK,CAAC,IAAIsD,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAEpD,KAAK,EAAEmD,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;UACnG,OAAOP,CAAC;QACV;QACAJ,sBAAsB,IAAIS,cAAc,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEP,CAAC,CAAC;QACxDJ,sBAAsB,IAAIS,cAAc,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEP,CAAC,CAAC;MAC1D;IACF;IACA,IAAIJ,sBAAsB,EAAE;MAC1B,OAAOxC,KAAK,KAAK2C,QAAQ,GAAGJ,SAAS,CAACO,MAAM,GAAG,CAAC,GAAG9C,KAAK,KAAK,CAAC2C,QAAQ,GAAG,CAAC,GAAGF,SAAS;IACxF;IACA,SAASQ,cAAcA,CAACM,GAAG,EAAEC,KAAK,EAAE;MAClC,IAAIC,MAAM,GAAGC,IAAI,CAAChB,GAAG,CAACa,GAAG,GAAGvD,KAAK,CAAC;MAClC,IAAIyD,MAAM,GAAGf,GAAG,EAAE;QAChBA,GAAG,GAAGe,MAAM;QACZhB,SAAS,GAAGe,KAAK;MACnB;IACF;EACF,CAAC;EACD9E,aAAa,CAACU,cAAc,GAAG;IAC7BuE,KAAK,EAAE;MACLtE,WAAW,EAAEuE,eAAe,CAAC,OAAO,CAAC;MACrCtE,cAAc,EAAE,SAAAA,CAAA,EAAY;QAC1B,IAAIP,UAAU,GAAG,IAAI,CAACJ,MAAM;QAC5B,OAAOR,MAAM,CAACgC,IAAI,CAACpB,UAAU,CAACH,aAAa,KAAK,UAAU,GAAG,UAAUoB,KAAK,EAAE6D,YAAY,EAAE;UAC1F,CAACA,YAAY,KAAK7D,KAAK,GAAG,IAAI,CAACf,cAAc,CAACe,KAAK,CAAC,CAAC;UACrD,OAAO8D,aAAa,CAAClD,IAAI,CAAC,IAAI,EAAEZ,KAAK,CAAC;QACxC,CAAC,GAAG,UAAUA,KAAK,EAAE6D,YAAY,EAAEE,GAAG,EAAE;UACtC;UACA;UACA,IAAIC,cAAc,GAAG,CAAC,CAACD,GAAG;UAC1B,CAACF,YAAY,KAAK7D,KAAK,GAAG,IAAI,CAACf,cAAc,CAACe,KAAK,CAAC,CAAC;UACrD+D,GAAG,GAAG3F,OAAO,CAAC6F,QAAQ,CAACjE,KAAK,EAAEjB,UAAU,CAACmF,YAAY,EAAEH,GAAG,CAAC;UAC3D,OAAOC,cAAc,GAAGD,GAAG,GAAG3F,OAAO,CAAC+F,SAAS,CAACJ,GAAG,EAAE,MAAM,CAAC;QAC9D,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDxE,mBAAmB,EAAE;QACnB6E,MAAM,EAAE,SAAAA,CAAUnE,UAAU,EAAE;UAC5B,OAAO7B,OAAO,CAAC+F,SAAS,CAAC/F,OAAO,CAAC6F,QAAQ,CAAChE,UAAU,EAAE,IAAI,CAACtB,MAAM,CAACuF,YAAY,CAAC,EAAE,MAAM,CAAC;QAC1F,CAAC;QACDG,QAAQ,EAAEP,aAAa;QACvBQ,SAAS,EAAE,SAAAA,CAAUrE,UAAU,EAAED,KAAK,EAAE;UACtC,IAAIuE,MAAM,GAAGC,kBAAkB,CAAC5D,IAAI,CAAC,IAAI,EAAEZ,KAAK,CAAC;UACjD,IAAIuE,MAAM,IAAI,IAAI,EAAE;YAClBA,MAAM,GAAGnG,OAAO,CAAC+F,SAAS,CAAC/F,OAAO,CAAC6F,QAAQ,CAAChE,UAAU,EAAE,IAAI,CAACtB,MAAM,CAACuF,YAAY,CAAC,EAAE,MAAM,CAAC;UAC5F;UACA,OAAOK,MAAM;QACf,CAAC;QACDE,KAAK,EAAEC;MACT;IACF,CAAC;IACDC,QAAQ,EAAEC,6BAA6B,CAAC,UAAUjB,KAAK,EAAE3D,KAAK,EAAE;MAC9D,OAAO5B,OAAO,CAACyG,SAAS,CAAClB,KAAK,EAAE3D,KAAK,CAAC;IACxC,CAAC,CAAC;IACF8E,eAAe,EAAEF,6BAA6B,CAAC,UAAUjB,KAAK,EAAE3D,KAAK,EAAE;MACrE,OAAO5B,OAAO,CAACyG,SAAS,CAAClB,KAAK,EAAE,IAAI,EAAE3D,KAAK,CAAC;IAC9C,CAAC,CAAC;IACF+E,cAAc,EAAEH,6BAA6B,CAAC,UAAUjB,KAAK,EAAE3D,KAAK,EAAE;MACpE,OAAO5B,OAAO,CAACyG,SAAS,CAAClB,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE3D,KAAK,CAAC;IACpD,CAAC,CAAC;IACFgF,UAAU,EAAEJ,6BAA6B,CAAC,UAAUjB,KAAK,EAAE3D,KAAK,EAAE;MAChE,OAAO5B,OAAO,CAAC6G,WAAW,CAACtB,KAAK,EAAE3D,KAAK,CAAC;IAC1C,CAAC,CAAC;IACFkF,KAAK,EAAE;MACL7F,WAAW,EAAEuE,eAAe,CAAC,OAAO,CAAC;MACrCrE,mBAAmB,EAAE;QACnB6E,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAEP,aAAa;QACvBQ,SAAS,EAAE,IAAI;QACfG,KAAK,EAAE;MACT;IACF,CAAC;IACDU,OAAO,EAAE;MACP9F,WAAW,EAAEuE,eAAe,CAAC,SAAS,CAAC;MACvCrE,mBAAmB,EAAE6F,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;IACDC,KAAK,EAAE;MACLhG,WAAW,EAAEuE,eAAe,CAAC,OAAO,CAAC;MACrCrE,mBAAmB,EAAE;QACnB6E,MAAM,EAAEM,UAAU;QAClBL,QAAQ,EAAEK,UAAU;QACpBJ,SAAS,EAAEI,UAAU;QACrBD,KAAK,EAAEC;MACT;IACF,CAAC;IACDY,MAAM,EAAE;MACNjG,WAAW,EAAE,SAAAA,CAAUW,KAAK,EAAEuF,MAAM,EAAEC,MAAM,EAAE;QAC5C,IAAIC,SAAS,GAAG,IAAI,CAAC1F,gBAAgB,CAACC,KAAK,CAAC;QAC5CwF,MAAM,CAAC,QAAQ,EAAEC,SAAS,CAAC;MAC7B,CAAC;MACDlG,mBAAmB,EAAE;QACnB6E,MAAM,EAAEsB,YAAY;QACpBrB,QAAQ,EAAEP,aAAa;QACvBQ,SAAS,EAAE,SAAAA,CAAUrE,UAAU,EAAED,KAAK,EAAE;UACtC,IAAIuE,MAAM,GAAGC,kBAAkB,CAAC5D,IAAI,CAAC,IAAI,EAAEZ,KAAK,CAAC;UACjD,IAAIuE,MAAM,IAAI,IAAI,EAAE;YAClBA,MAAM,GAAGmB,YAAY,CAAC9E,IAAI,CAAC,IAAI,EAAEX,UAAU,CAAC;UAC9C;UACA,OAAOsE,MAAM;QACf,CAAC;QACDE,KAAK,EAAEC;MACT;IACF,CAAC;IACDiB,UAAU,EAAE;MACVtG,WAAW,EAAEuE,eAAe,CAAC,YAAY,CAAC;MAC1CrE,mBAAmB,EAAE6F,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7D;EACF,CAAC;EACD,OAAO1G,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,SAASe,sBAAsBA,CAACV,UAAU,EAAE;EAC1C,IAAIwD,SAAS,GAAGxD,UAAU,CAACwD,SAAS;EACpCxD,UAAU,CAAC6G,gBAAgB,GAAG,KAAK;EACnCzH,MAAM,CAACI,IAAI,CAACgE,SAAS,EAAE,UAAUW,KAAK,EAAEM,KAAK,EAAE;IAC7CN,KAAK,CAAC2C,WAAW,GAAGrC,KAAK;IACzB;IACA;IACA,IAAIN,KAAK,CAACzC,MAAM,IAAI,IAAI,EAAE;MACxB1B,UAAU,CAAC6G,gBAAgB,GAAG,IAAI;IACpC;EACF,CAAC,CAAC;AACJ;AACA,SAASjG,8BAA8BA,CAACZ,UAAU,EAAE;EAClD;EACA,IAAIW,UAAU,GAAGX,UAAU,CAACW,UAAU;EACtC,IAAIoG,WAAW,GAAG/G,UAAU,CAAC+G,WAAW,GAAG,CAAC,CAAC;EAC7C,IAAIrF,MAAM,GAAG1B,UAAU,CAAC0B,MAAM;EAC9BlC,IAAI,CAACmB,UAAU,EAAE,UAAUqG,IAAI,EAAEvC,KAAK,EAAE;IACtCsC,WAAW,CAACC,IAAI,CAAC,GAAGvC,KAAK;EAC3B,CAAC,CAAC;EACF;EACA,IAAI,CAACrF,MAAM,CAAC6C,OAAO,CAACP,MAAM,CAAC,EAAE;IAC3B,IAAIuF,WAAW,GAAG,EAAE;IACpB,IAAI7H,MAAM,CAACK,QAAQ,CAACiC,MAAM,CAAC,EAAE;MAC3BlC,IAAI,CAACkC,MAAM,EAAE,UAAUQ,CAAC,EAAE8E,IAAI,EAAE;QAC9B,IAAIvC,KAAK,GAAGsC,WAAW,CAACC,IAAI,CAAC;QAC7BC,WAAW,CAACxC,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG/E,6BAA6B,CAAC,GAAGwC,CAAC;MACxE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA+E,WAAW,CAACvH,6BAA6B,CAAC,GAAGgC,MAAM;IACrD;IACAA,MAAM,GAAGwF,iBAAiB,CAAClH,UAAU,EAAEiH,WAAW,CAAC;EACrD;EACA;EACA;EACA,KAAK,IAAIpD,CAAC,GAAGlD,UAAU,CAACoD,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,IAAInC,MAAM,CAACmC,CAAC,CAAC,IAAI,IAAI,EAAE;MACrB,OAAOkD,WAAW,CAACpG,UAAU,CAACkD,CAAC,CAAC,CAAC;MACjClD,UAAU,CAACwG,GAAG,CAAC,CAAC;IAClB;EACF;AACF;AACA,SAAS1G,oBAAoBA,CAACT,UAAU,EAAEoH,UAAU,EAAE;EACpD,IAAI1F,MAAM,GAAG1B,UAAU,CAAC0B,MAAM;EAC9B,IAAI2F,SAAS,GAAG,EAAE;EAClB,IAAIjI,MAAM,CAACK,QAAQ,CAACiC,MAAM,CAAC,EAAE;IAC3BlC,IAAI,CAACkC,MAAM,EAAE,UAAUQ,CAAC,EAAE;MACxBmF,SAAS,CAACtE,IAAI,CAACb,CAAC,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIR,MAAM,IAAI,IAAI,EAAE;IACzB2F,SAAS,CAACtE,IAAI,CAACrB,MAAM,CAAC;EACxB;EACA,IAAI4F,aAAa,GAAG;IAClB1C,KAAK,EAAE,CAAC;IACR2B,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACa,UAAU,IAAIC,SAAS,CAACtD,MAAM,KAAK,CAAC,IAAI,CAACuD,aAAa,CAAC9F,cAAc,CAACxB,UAAU,CAACD,IAAI,CAAC,EAAE;IAC3F;IACAsH,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC7B;EACAH,iBAAiB,CAAClH,UAAU,EAAEqH,SAAS,CAAC;AAC1C;AACA,SAASxB,6BAA6BA,CAAC0B,UAAU,EAAE;EACjD,OAAO;IACLjH,WAAW,EAAE,SAAAA,CAAUW,KAAK,EAAEuF,MAAM,EAAEC,MAAM,EAAE;MAC5C;MACA,IAAIe,YAAY,GAAG,IAAI,CAACxG,gBAAgB,CAACC,KAAK,CAAC;MAC/C;MACAwF,MAAM,CAAC,OAAO,EAAEc,UAAU,CAACf,MAAM,CAAC,OAAO,CAAC,EAAEgB,YAAY,CAAC,CAAC;IAC5D,CAAC;IACDhH,mBAAmB,EAAE6F,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7D,CAAC;AACH;AACA,SAASM,YAAYA,CAACzF,UAAU,EAAE;EAChC,IAAIQ,MAAM,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,MAAM;EAC/B,OAAOA,MAAM,CAACiD,IAAI,CAAC8C,KAAK,CAACnI,SAAS,CAAC4B,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEQ,MAAM,CAACqC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChG;AACA,SAASc,eAAeA,CAAC/E,UAAU,EAAE;EACnC,OAAO,UAAUmB,KAAK,EAAEuF,MAAM,EAAEC,MAAM,EAAE;IACtCA,MAAM,CAAC3G,UAAU,EAAE,IAAI,CAACkB,gBAAgB,CAACC,KAAK,CAAC,CAAC;EAClD,CAAC;AACH;AACA,SAAS8D,aAAaA,CAAC7D,UAAU,EAAE;EACjC,IAAIQ,MAAM,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,MAAM;EAC/B,OAAOA,MAAM,CAAC,IAAI,CAAC9B,MAAM,CAAC8H,IAAI,IAAIxG,UAAU,KAAKxB,6BAA6B,GAAGwB,UAAU,GAAGQ,MAAM,CAACqC,MAAM,GAAG7C,UAAU,CAAC;AAC3H;AACA,SAASyE,UAAUA,CAAA,EAAG;EACpB;EACA,OAAO,IAAI,CAAC/F,MAAM,CAAC8B,MAAM,CAAC,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA,SAAS2E,+BAA+BA,CAACsB,YAAY,EAAE;EACrD,OAAO;IACLtC,MAAM,EAAE,SAAAA,CAAUnE,UAAU,EAAE;MAC5B,OAAO5B,SAAS,CAAC4B,UAAU,EAAEyG,YAAY,EAAE,IAAI,CAAC/H,MAAM,CAAC8B,MAAM,EAAE,IAAI,CAAC;IACtE,CAAC;IACD4D,QAAQ,EAAEP,aAAa;IACvBQ,SAAS,EAAE,SAAAA,CAAUrE,UAAU,EAAED,KAAK,EAAE;MACtC,IAAIuE,MAAM,GAAGC,kBAAkB,CAAC5D,IAAI,CAAC,IAAI,EAAEZ,KAAK,CAAC;MACjD,IAAIuE,MAAM,IAAI,IAAI,EAAE;QAClBA,MAAM,GAAGlG,SAAS,CAAC4B,UAAU,EAAEyG,YAAY,EAAE,IAAI,CAAC/H,MAAM,CAAC8B,MAAM,EAAE,IAAI,CAAC;MACxE;MACA,OAAO8D,MAAM;IACf,CAAC;IACDE,KAAK,EAAEC;EACT,CAAC;AACH;AACA,SAASF,kBAAkBA,CAACxE,KAAK,EAAE;EACjC,IAAIjB,UAAU,GAAG,IAAI,CAACJ,MAAM;EAC5B,IAAI4D,SAAS,GAAGxD,UAAU,CAACwD,SAAS;EACpC,IAAIxD,UAAU,CAAC6G,gBAAgB,EAAE;IAC/B,IAAIe,UAAU,GAAGjI,aAAa,CAAC4D,cAAc,CAACtC,KAAK,EAAEuC,SAAS,CAAC;IAC/D,IAAIW,KAAK,GAAGX,SAAS,CAACoE,UAAU,CAAC;IACjC,IAAIzD,KAAK,IAAIA,KAAK,CAACzC,MAAM,EAAE;MACzB,OAAOyC,KAAK,CAACzC,MAAM,CAAC,IAAI,CAAC3B,IAAI,CAAC;IAChC;EACF;AACF;AACA,SAASmH,iBAAiBA,CAAClH,UAAU,EAAEqH,SAAS,EAAE;EAChDrH,UAAU,CAAC0B,MAAM,GAAG2F,SAAS;EAC7B,IAAIrH,UAAU,CAACD,IAAI,KAAK,OAAO,EAAE;IAC/BC,UAAU,CAACmF,YAAY,GAAG/F,MAAM,CAACyI,GAAG,CAACR,SAAS,EAAE,UAAUvE,IAAI,EAAE;MAC9D,IAAI8B,KAAK,GAAGvF,OAAO,CAACyI,KAAK,CAAChF,IAAI,CAAC;MAC/B,IAAI,CAAC8B,KAAK,IAAImD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACnD1I,IAAI,CAAC,GAAG,GAAGuD,IAAI,GAAG,8CAA8C,EAAE,IAAI,CAAC;MACzE;MACA,OAAO8B,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC,CAAC;EACJ;EACA,OAAOyC,SAAS;AAClB;AACA;AACA;AACA;AACA,IAAIlH,WAAW,GAAG;EAChBkF,MAAM,EAAE,SAAAA,CAAUpE,KAAK,EAAE;IACvB,OAAO3B,SAAS,CAAC2B,KAAK,EAAE,IAAI,CAACrB,MAAM,CAACkB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EAC/D,CAAC;EACDyE,SAAS,EAAE,SAAAA,CAAUtE,KAAK,EAAE;IAC1B,IAAIuC,SAAS,GAAG,IAAI,CAAC5D,MAAM,CAAC4D,SAAS;IACrC,IAAIoE,UAAU,GAAGjI,aAAa,CAAC4D,cAAc,CAACtC,KAAK,EAAEuC,SAAS,EAAE,IAAI,CAAC;IACrE,IAAIoE,UAAU,IAAI,IAAI,EAAE;MACtB,OAAOtI,SAAS,CAACsI,UAAU,EAAE,CAAC,CAAC,EAAEpE,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IACvE;EACF,CAAC;EACDuB,QAAQ,EAAE,SAAAA,CAAUrE,KAAK,EAAE;IACzB,IAAIwD,KAAK,GAAG,IAAI,CAAC7E,MAAM,CAACe,UAAU,GAAG,IAAI,CAACf,MAAM,CAACmH,WAAW,CAAC9F,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;IAC7E,OAAOwD,KAAK,IAAI,IAAI,GAAG/E,6BAA6B,GAAG+E,KAAK;EAC9D,CAAC;EACDiB,KAAK,EAAEtG,MAAM,CAAC8I;AAChB,CAAC;AACD,SAAS3D,UAAUA,CAACD,KAAK,EAAE6D,CAAC,EAAEC,CAAC,EAAE;EAC/B,OAAO9D,KAAK,GAAG6D,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAGC,CAAC;AAC/B;AACA,eAAezI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}