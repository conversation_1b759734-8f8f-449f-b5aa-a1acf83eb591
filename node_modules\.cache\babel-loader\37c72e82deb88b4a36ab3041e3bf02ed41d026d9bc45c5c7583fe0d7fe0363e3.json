{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport FunnelView from './FunnelView.js';\nimport FunnelSeriesModel from './FunnelSeries.js';\nimport funnelLayout from './funnelLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(FunnelView);\n  registers.registerSeriesModel(FunnelSeriesModel);\n  registers.registerLayout(funnelLayout);\n  registers.registerProcessor(dataFilter('funnel'));\n}", "map": {"version": 3, "names": ["FunnelView", "FunnelSeriesModel", "funnelLayout", "dataFilter", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerProcessor"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/funnel/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport FunnelView from './FunnelView.js';\nimport FunnelSeriesModel from './FunnelSeries.js';\nimport funnelLayout from './funnelLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(FunnelView);\n  registers.registerSeriesModel(FunnelSeriesModel);\n  registers.registerLayout(funnelLayout);\n  registers.registerProcessor(dataFilter('funnel'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,iBAAiB,MAAM,mBAAmB;AACjD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACN,UAAU,CAAC;EACvCK,SAAS,CAACE,mBAAmB,CAACN,iBAAiB,CAAC;EAChDI,SAAS,CAACG,cAAc,CAACN,YAAY,CAAC;EACtCG,SAAS,CAACI,iBAAiB,CAACN,UAAU,CAAC,QAAQ,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}