{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LineSeries from './LineSeries.js';\nimport LineView from './LineView.js';\n// In case developer forget to include grid component\nimport layoutPoints from '../../layout/points.js';\nimport dataSample from '../../processor/dataSample.js';\nexport function install(registers) {\n  registers.registerChartView(LineView);\n  registers.registerSeriesModel(LineSeries);\n  registers.registerLayout(layoutPoints('line', true));\n  registers.registerVisual({\n    seriesType: 'line',\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      // Visual coding for legend\n      var lineStyle = seriesModel.getModel('lineStyle').getLineStyle();\n      if (lineStyle && !lineStyle.stroke) {\n        // Fill in visual should be palette color if\n        // has color callback\n        lineStyle.stroke = data.getVisual('style').fill;\n      }\n      data.setVisual('legendLineStyle', lineStyle);\n    }\n  });\n  // Down sample after filter\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample('line'));\n}", "map": {"version": 3, "names": ["LineSeries", "LineView", "layoutPoints", "dataSample", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerVisual", "seriesType", "reset", "seriesModel", "data", "getData", "lineStyle", "getModel", "getLineStyle", "stroke", "getVisual", "fill", "setVisual", "registerProcessor", "PRIORITY", "PROCESSOR", "STATISTIC"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/line/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LineSeries from './LineSeries.js';\nimport LineView from './LineView.js';\n// In case developer forget to include grid component\nimport layoutPoints from '../../layout/points.js';\nimport dataSample from '../../processor/dataSample.js';\nexport function install(registers) {\n  registers.registerChartView(LineView);\n  registers.registerSeriesModel(LineSeries);\n  registers.registerLayout(layoutPoints('line', true));\n  registers.registerVisual({\n    seriesType: 'line',\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      // Visual coding for legend\n      var lineStyle = seriesModel.getModel('lineStyle').getLineStyle();\n      if (lineStyle && !lineStyle.stroke) {\n        // Fill in visual should be palette color if\n        // has color callback\n        lineStyle.stroke = data.getVisual('style').fill;\n      }\n      data.setVisual('legendLineStyle', lineStyle);\n    }\n  });\n  // Down sample after filter\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample('line'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC;AACA,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACL,QAAQ,CAAC;EACrCI,SAAS,CAACE,mBAAmB,CAACP,UAAU,CAAC;EACzCK,SAAS,CAACG,cAAc,CAACN,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EACpDG,SAAS,CAACI,cAAc,CAAC;IACvBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAE;MAC5B,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;MAChC;MACA,IAAIC,SAAS,GAAGH,WAAW,CAACI,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAChE,IAAIF,SAAS,IAAI,CAACA,SAAS,CAACG,MAAM,EAAE;QAClC;QACA;QACAH,SAAS,CAACG,MAAM,GAAGL,IAAI,CAACM,SAAS,CAAC,OAAO,CAAC,CAACC,IAAI;MACjD;MACAP,IAAI,CAACQ,SAAS,CAAC,iBAAiB,EAAEN,SAAS,CAAC;IAC9C;EACF,CAAC,CAAC;EACF;EACAV,SAAS,CAACiB,iBAAiB,CAACjB,SAAS,CAACkB,QAAQ,CAACC,SAAS,CAACC,SAAS,EAAEtB,UAAU,CAAC,MAAM,CAAC,CAAC;AACzF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}