{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\nimport { GraphicComponentModel } from './GraphicModel.js';\nimport { GraphicComponentView } from './GraphicView.js';\nexport function install(registers) {\n  registers.registerComponentModel(GraphicComponentModel);\n  registers.registerComponentView(GraphicComponentView);\n  registers.registerPreprocessor(function (option) {\n    var graphicOption = option.graphic;\n    // Convert\n    // {graphic: [{left: 10, type: 'circle'}, ...]}\n    // or\n    // {graphic: {left: 10, type: 'circle'}}\n    // to\n    // {graphic: [{elements: [{left: 10, type: 'circle'}, ...]}]}\n    if (isArray(graphicOption)) {\n      if (!graphicOption[0] || !graphicOption[0].elements) {\n        option.graphic = [{\n          elements: graphicOption\n        }];\n      } else {\n        // Only one graphic instance can be instantiated. (We don't\n        // want that too many views are created in echarts._viewMap.)\n        option.graphic = [option.graphic[0]];\n      }\n    } else if (graphicOption && !graphicOption.elements) {\n      option.graphic = [{\n        elements: [graphicOption]\n      }];\n    }\n  });\n}", "map": {"version": 3, "names": ["isArray", "GraphicComponentModel", "GraphicComponentView", "install", "registers", "registerComponentModel", "registerComponentView", "registerPreprocessor", "option", "graphicOption", "graphic", "elements"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/graphic/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\nimport { GraphicComponentModel } from './GraphicModel.js';\nimport { GraphicComponentView } from './GraphicView.js';\nexport function install(registers) {\n  registers.registerComponentModel(GraphicComponentModel);\n  registers.registerComponentView(GraphicComponentView);\n  registers.registerPreprocessor(function (option) {\n    var graphicOption = option.graphic;\n    // Convert\n    // {graphic: [{left: 10, type: 'circle'}, ...]}\n    // or\n    // {graphic: {left: 10, type: 'circle'}}\n    // to\n    // {graphic: [{elements: [{left: 10, type: 'circle'}, ...]}]}\n    if (isArray(graphicOption)) {\n      if (!graphicOption[0] || !graphicOption[0].elements) {\n        option.graphic = [{\n          elements: graphicOption\n        }];\n      } else {\n        // Only one graphic instance can be instantiated. (We don't\n        // want that too many views are created in echarts._viewMap.)\n        option.graphic = [option.graphic[0]];\n      }\n    } else if (graphicOption && !graphicOption.elements) {\n      option.graphic = [{\n        elements: [graphicOption]\n      }];\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,0BAA0B;AAClD,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,SAASC,oBAAoB,QAAQ,kBAAkB;AACvD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACJ,qBAAqB,CAAC;EACvDG,SAAS,CAACE,qBAAqB,CAACJ,oBAAoB,CAAC;EACrDE,SAAS,CAACG,oBAAoB,CAAC,UAAUC,MAAM,EAAE;IAC/C,IAAIC,aAAa,GAAGD,MAAM,CAACE,OAAO;IAClC;IACA;IACA;IACA;IACA;IACA;IACA,IAAIV,OAAO,CAACS,aAAa,CAAC,EAAE;MAC1B,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAACE,QAAQ,EAAE;QACnDH,MAAM,CAACE,OAAO,GAAG,CAAC;UAChBC,QAAQ,EAAEF;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;QACAD,MAAM,CAACE,OAAO,GAAG,CAACF,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,MAAM,IAAID,aAAa,IAAI,CAACA,aAAa,CAACE,QAAQ,EAAE;MACnDH,MAAM,CAACE,OAAO,GAAG,CAAC;QAChBC,QAAQ,EAAE,CAACF,aAAa;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}