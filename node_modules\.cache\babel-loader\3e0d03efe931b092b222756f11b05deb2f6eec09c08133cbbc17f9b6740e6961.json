{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar TooltipModel = /** @class */function (_super) {\n  __extends(TooltipModel, _super);\n  function TooltipModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipModel.type;\n    return _this;\n  }\n  TooltipModel.type = 'tooltip';\n  TooltipModel.dependencies = ['axisPointer'];\n  TooltipModel.defaultOption = {\n    // zlevel: 0,\n    z: 60,\n    show: true,\n    // tooltip main content\n    showContent: true,\n    // 'trigger' only works on coordinate system.\n    // 'item' | 'axis' | 'none'\n    trigger: 'item',\n    // 'click' | 'mousemove' | 'none'\n    triggerOn: 'mousemove|click',\n    alwaysShowContent: false,\n    displayMode: 'single',\n    renderMode: 'auto',\n    // whether restraint content inside viewRect.\n    // If renderMode: 'richText', default true.\n    // If renderMode: 'html', defaut false (for backward compat).\n    confine: null,\n    showDelay: 0,\n    hideDelay: 100,\n    // Animation transition time, unit is second\n    transitionDuration: 0.4,\n    enterable: false,\n    backgroundColor: '#fff',\n    // box shadow\n    shadowBlur: 10,\n    shadowColor: 'rgba(0, 0, 0, .2)',\n    shadowOffsetX: 1,\n    shadowOffsetY: 2,\n    // tooltip border radius, unit is px, default is 4\n    borderRadius: 4,\n    // tooltip border width, unit is px, default is 0 (no border)\n    borderWidth: 1,\n    // Tooltip inside padding, default is 5 for all direction\n    // Array is allowed to set up, right, bottom, left, same with css\n    // The default value: See `tooltip/tooltipMarkup.ts#getPaddingFromTooltipModel`.\n    padding: null,\n    // Extra css text\n    extraCssText: '',\n    // axis indicator, trigger by axis\n    axisPointer: {\n      // default is line\n      // legal values: 'line' | 'shadow' | 'cross'\n      type: 'line',\n      // Valid when type is line, appoint tooltip line locate on which line. Optional\n      // legal values: 'x' | 'y' | 'angle' | 'radius' | 'auto'\n      // default is 'auto', chose the axis which type is category.\n      // for multiply y axis, cartesian coord chose x axis, polar chose angle axis\n      axis: 'auto',\n      animation: 'auto',\n      animationDurationUpdate: 200,\n      animationEasingUpdate: 'exponentialOut',\n      crossStyle: {\n        color: '#999',\n        width: 1,\n        type: 'dashed',\n        // TODO formatter\n        textStyle: {}\n      }\n      // lineStyle and shadowStyle should not be specified here,\n      // otherwise it will always override those styles on option.axisPointer.\n    },\n    textStyle: {\n      color: '#666',\n      fontSize: 14\n    }\n  };\n  return TooltipModel;\n}(ComponentModel);\nexport default TooltipModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "TooltipModel", "_super", "_this", "apply", "arguments", "type", "dependencies", "defaultOption", "z", "show", "showContent", "trigger", "triggerOn", "alwaysS<PERSON><PERSON><PERSON>nt", "displayMode", "renderMode", "confine", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "enterable", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "borderRadius", "borderWidth", "padding", "extraCssText", "axisPointer", "axis", "animation", "animationDurationUpdate", "animationEasingUpdate", "crossStyle", "color", "width", "textStyle", "fontSize"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/tooltip/TooltipModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar TooltipModel = /** @class */function (_super) {\n  __extends(TooltipModel, _super);\n  function TooltipModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipModel.type;\n    return _this;\n  }\n  TooltipModel.type = 'tooltip';\n  TooltipModel.dependencies = ['axisPointer'];\n  TooltipModel.defaultOption = {\n    // zlevel: 0,\n    z: 60,\n    show: true,\n    // tooltip main content\n    showContent: true,\n    // 'trigger' only works on coordinate system.\n    // 'item' | 'axis' | 'none'\n    trigger: 'item',\n    // 'click' | 'mousemove' | 'none'\n    triggerOn: 'mousemove|click',\n    alwaysShowContent: false,\n    displayMode: 'single',\n    renderMode: 'auto',\n    // whether restraint content inside viewRect.\n    // If renderMode: 'richText', default true.\n    // If renderMode: 'html', defaut false (for backward compat).\n    confine: null,\n    showDelay: 0,\n    hideDelay: 100,\n    // Animation transition time, unit is second\n    transitionDuration: 0.4,\n    enterable: false,\n    backgroundColor: '#fff',\n    // box shadow\n    shadowBlur: 10,\n    shadowColor: 'rgba(0, 0, 0, .2)',\n    shadowOffsetX: 1,\n    shadowOffsetY: 2,\n    // tooltip border radius, unit is px, default is 4\n    borderRadius: 4,\n    // tooltip border width, unit is px, default is 0 (no border)\n    borderWidth: 1,\n    // Tooltip inside padding, default is 5 for all direction\n    // Array is allowed to set up, right, bottom, left, same with css\n    // The default value: See `tooltip/tooltipMarkup.ts#getPaddingFromTooltipModel`.\n    padding: null,\n    // Extra css text\n    extraCssText: '',\n    // axis indicator, trigger by axis\n    axisPointer: {\n      // default is line\n      // legal values: 'line' | 'shadow' | 'cross'\n      type: 'line',\n      // Valid when type is line, appoint tooltip line locate on which line. Optional\n      // legal values: 'x' | 'y' | 'angle' | 'radius' | 'auto'\n      // default is 'auto', chose the axis which type is category.\n      // for multiply y axis, cartesian coord chose x axis, polar chose angle axis\n      axis: 'auto',\n      animation: 'auto',\n      animationDurationUpdate: 200,\n      animationEasingUpdate: 'exponentialOut',\n      crossStyle: {\n        color: '#999',\n        width: 1,\n        type: 'dashed',\n        // TODO formatter\n        textStyle: {}\n      }\n      // lineStyle and shadowStyle should not be specified here,\n      // otherwise it will always override those styles on option.axisPointer.\n    },\n    textStyle: {\n      color: '#666',\n      fontSize: 14\n    }\n  };\n  return TooltipModel;\n}(ComponentModel);\nexport default TooltipModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACK,IAAI,GAAG,SAAS;EAC7BL,YAAY,CAACM,YAAY,GAAG,CAAC,aAAa,CAAC;EAC3CN,YAAY,CAACO,aAAa,GAAG;IAC3B;IACAC,CAAC,EAAE,EAAE;IACLC,IAAI,EAAE,IAAI;IACV;IACAC,WAAW,EAAE,IAAI;IACjB;IACA;IACAC,OAAO,EAAE,MAAM;IACf;IACAC,SAAS,EAAE,iBAAiB;IAC5BC,iBAAiB,EAAE,KAAK;IACxBC,WAAW,EAAE,QAAQ;IACrBC,UAAU,EAAE,MAAM;IAClB;IACA;IACA;IACAC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,GAAG;IACd;IACAC,kBAAkB,EAAE,GAAG;IACvBC,SAAS,EAAE,KAAK;IAChBC,eAAe,EAAE,MAAM;IACvB;IACAC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,mBAAmB;IAChCC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChB;IACAC,YAAY,EAAE,CAAC;IACf;IACAC,WAAW,EAAE,CAAC;IACd;IACA;IACA;IACAC,OAAO,EAAE,IAAI;IACb;IACAC,YAAY,EAAE,EAAE;IAChB;IACAC,WAAW,EAAE;MACX;MACA;MACAzB,IAAI,EAAE,MAAM;MACZ;MACA;MACA;MACA;MACA0B,IAAI,EAAE,MAAM;MACZC,SAAS,EAAE,MAAM;MACjBC,uBAAuB,EAAE,GAAG;MAC5BC,qBAAqB,EAAE,gBAAgB;MACvCC,UAAU,EAAE;QACVC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,CAAC;QACRhC,IAAI,EAAE,QAAQ;QACd;QACAiC,SAAS,EAAE,CAAC;MACd;MACA;MACA;IACF,CAAC;IACDA,SAAS,EAAE;MACTF,KAAK,EAAE,MAAM;MACbG,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,OAAOvC,YAAY;AACrB,CAAC,CAACD,cAAc,CAAC;AACjB,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}