{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nexport default function parallelPreprocessor(option) {\n  createParallelIfNeeded(option);\n  mergeAxisOptionFromParallel(option);\n}\n/**\r\n * Create a parallel coordinate if not exists.\r\n * @inner\r\n */\nfunction createParallelIfNeeded(option) {\n  if (option.parallel) {\n    return;\n  }\n  var hasParallelSeries = false;\n  zrUtil.each(option.series, function (seriesOpt) {\n    if (seriesOpt && seriesOpt.type === 'parallel') {\n      hasParallelSeries = true;\n    }\n  });\n  if (hasParallelSeries) {\n    option.parallel = [{}];\n  }\n}\n/**\r\n * Merge aixs definition from parallel option (if exists) to axis option.\r\n * @inner\r\n */\nfunction mergeAxisOptionFromParallel(option) {\n  var axes = modelUtil.normalizeToArray(option.parallelAxis);\n  zrUtil.each(axes, function (axisOption) {\n    if (!zrUtil.isObject(axisOption)) {\n      return;\n    }\n    var parallelIndex = axisOption.parallelIndex || 0;\n    var parallelOption = modelUtil.normalizeToArray(option.parallel)[parallelIndex];\n    if (parallelOption && parallelOption.parallelAxisDefault) {\n      zrUtil.merge(axisOption, parallelOption.parallelAxisDefault, false);\n    }\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "modelUtil", "parallelPreprocessor", "option", "createParallelIfNeeded", "mergeAxisOptionFromParallel", "parallel", "hasParallelSeries", "each", "series", "seriesOpt", "type", "axes", "normalizeToArray", "parallelAxis", "axisOption", "isObject", "parallelIndex", "parallelOption", "parallelAxisDefault", "merge"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/parallel/parallelPreprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nexport default function parallelPreprocessor(option) {\n  createParallelIfNeeded(option);\n  mergeAxisOptionFromParallel(option);\n}\n/**\r\n * Create a parallel coordinate if not exists.\r\n * @inner\r\n */\nfunction createParallelIfNeeded(option) {\n  if (option.parallel) {\n    return;\n  }\n  var hasParallelSeries = false;\n  zrUtil.each(option.series, function (seriesOpt) {\n    if (seriesOpt && seriesOpt.type === 'parallel') {\n      hasParallelSeries = true;\n    }\n  });\n  if (hasParallelSeries) {\n    option.parallel = [{}];\n  }\n}\n/**\r\n * Merge aixs definition from parallel option (if exists) to axis option.\r\n * @inner\r\n */\nfunction mergeAxisOptionFromParallel(option) {\n  var axes = modelUtil.normalizeToArray(option.parallelAxis);\n  zrUtil.each(axes, function (axisOption) {\n    if (!zrUtil.isObject(axisOption)) {\n      return;\n    }\n    var parallelIndex = axisOption.parallelIndex || 0;\n    var parallelOption = modelUtil.normalizeToArray(option.parallel)[parallelIndex];\n    if (parallelOption && parallelOption.parallelAxisDefault) {\n      zrUtil.merge(axisOption, parallelOption.parallelAxisDefault, false);\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,eAAe,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EACnDC,sBAAsB,CAACD,MAAM,CAAC;EAC9BE,2BAA2B,CAACF,MAAM,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACD,MAAM,EAAE;EACtC,IAAIA,MAAM,CAACG,QAAQ,EAAE;IACnB;EACF;EACA,IAAIC,iBAAiB,GAAG,KAAK;EAC7BP,MAAM,CAACQ,IAAI,CAACL,MAAM,CAACM,MAAM,EAAE,UAAUC,SAAS,EAAE;IAC9C,IAAIA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,UAAU,EAAE;MAC9CJ,iBAAiB,GAAG,IAAI;IAC1B;EACF,CAAC,CAAC;EACF,IAAIA,iBAAiB,EAAE;IACrBJ,MAAM,CAACG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB;AACF;AACA;AACA;AACA;AACA;AACA,SAASD,2BAA2BA,CAACF,MAAM,EAAE;EAC3C,IAAIS,IAAI,GAAGX,SAAS,CAACY,gBAAgB,CAACV,MAAM,CAACW,YAAY,CAAC;EAC1Dd,MAAM,CAACQ,IAAI,CAACI,IAAI,EAAE,UAAUG,UAAU,EAAE;IACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAACD,UAAU,CAAC,EAAE;MAChC;IACF;IACA,IAAIE,aAAa,GAAGF,UAAU,CAACE,aAAa,IAAI,CAAC;IACjD,IAAIC,cAAc,GAAGjB,SAAS,CAACY,gBAAgB,CAACV,MAAM,CAACG,QAAQ,CAAC,CAACW,aAAa,CAAC;IAC/E,IAAIC,cAAc,IAAIA,cAAc,CAACC,mBAAmB,EAAE;MACxDnB,MAAM,CAACoB,KAAK,CAACL,UAAU,EAAEG,cAAc,CAACC,mBAAmB,EAAE,KAAK,CAAC;IACrE;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}