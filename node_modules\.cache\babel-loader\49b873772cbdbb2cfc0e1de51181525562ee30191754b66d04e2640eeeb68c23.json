{"ast": null, "code": "var DEFAULT_MIN_MERGE = 32;\nvar DEFAULT_MIN_GALLOPING = 7;\nfunction minRunLength(n) {\n  var r = 0;\n  while (n >= DEFAULT_MIN_MERGE) {\n    r |= n & 1;\n    n >>= 1;\n  }\n  return n + r;\n}\nfunction makeAscendingRun(array, lo, hi, compare) {\n  var runHi = lo + 1;\n  if (runHi === hi) {\n    return 1;\n  }\n  if (compare(array[runHi++], array[lo]) < 0) {\n    while (runHi < hi && compare(array[runHi], array[runHi - 1]) < 0) {\n      runHi++;\n    }\n    reverseRun(array, lo, runHi);\n  } else {\n    while (runHi < hi && compare(array[runHi], array[runHi - 1]) >= 0) {\n      runHi++;\n    }\n  }\n  return runHi - lo;\n}\nfunction reverseRun(array, lo, hi) {\n  hi--;\n  while (lo < hi) {\n    var t = array[lo];\n    array[lo++] = array[hi];\n    array[hi--] = t;\n  }\n}\nfunction binaryInsertionSort(array, lo, hi, start, compare) {\n  if (start === lo) {\n    start++;\n  }\n  for (; start < hi; start++) {\n    var pivot = array[start];\n    var left = lo;\n    var right = start;\n    var mid;\n    while (left < right) {\n      mid = left + right >>> 1;\n      if (compare(pivot, array[mid]) < 0) {\n        right = mid;\n      } else {\n        left = mid + 1;\n      }\n    }\n    var n = start - left;\n    switch (n) {\n      case 3:\n        array[left + 3] = array[left + 2];\n      case 2:\n        array[left + 2] = array[left + 1];\n      case 1:\n        array[left + 1] = array[left];\n        break;\n      default:\n        while (n > 0) {\n          array[left + n] = array[left + n - 1];\n          n--;\n        }\n    }\n    array[left] = pivot;\n  }\n}\nfunction gallopLeft(value, array, start, length, hint, compare) {\n  var lastOffset = 0;\n  var maxOffset = 0;\n  var offset = 1;\n  if (compare(value, array[start + hint]) > 0) {\n    maxOffset = length - hint;\n    while (offset < maxOffset && compare(value, array[start + hint + offset]) > 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    lastOffset += hint;\n    offset += hint;\n  } else {\n    maxOffset = hint + 1;\n    while (offset < maxOffset && compare(value, array[start + hint - offset]) <= 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    var tmp = lastOffset;\n    lastOffset = hint - offset;\n    offset = hint - tmp;\n  }\n  lastOffset++;\n  while (lastOffset < offset) {\n    var m = lastOffset + (offset - lastOffset >>> 1);\n    if (compare(value, array[start + m]) > 0) {\n      lastOffset = m + 1;\n    } else {\n      offset = m;\n    }\n  }\n  return offset;\n}\nfunction gallopRight(value, array, start, length, hint, compare) {\n  var lastOffset = 0;\n  var maxOffset = 0;\n  var offset = 1;\n  if (compare(value, array[start + hint]) < 0) {\n    maxOffset = hint + 1;\n    while (offset < maxOffset && compare(value, array[start + hint - offset]) < 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    var tmp = lastOffset;\n    lastOffset = hint - offset;\n    offset = hint - tmp;\n  } else {\n    maxOffset = length - hint;\n    while (offset < maxOffset && compare(value, array[start + hint + offset]) >= 0) {\n      lastOffset = offset;\n      offset = (offset << 1) + 1;\n      if (offset <= 0) {\n        offset = maxOffset;\n      }\n    }\n    if (offset > maxOffset) {\n      offset = maxOffset;\n    }\n    lastOffset += hint;\n    offset += hint;\n  }\n  lastOffset++;\n  while (lastOffset < offset) {\n    var m = lastOffset + (offset - lastOffset >>> 1);\n    if (compare(value, array[start + m]) < 0) {\n      offset = m;\n    } else {\n      lastOffset = m + 1;\n    }\n  }\n  return offset;\n}\nfunction TimSort(array, compare) {\n  var minGallop = DEFAULT_MIN_GALLOPING;\n  var runStart;\n  var runLength;\n  var stackSize = 0;\n  var tmp = [];\n  runStart = [];\n  runLength = [];\n  function pushRun(_runStart, _runLength) {\n    runStart[stackSize] = _runStart;\n    runLength[stackSize] = _runLength;\n    stackSize += 1;\n  }\n  function mergeRuns() {\n    while (stackSize > 1) {\n      var n = stackSize - 2;\n      if (n >= 1 && runLength[n - 1] <= runLength[n] + runLength[n + 1] || n >= 2 && runLength[n - 2] <= runLength[n] + runLength[n - 1]) {\n        if (runLength[n - 1] < runLength[n + 1]) {\n          n--;\n        }\n      } else if (runLength[n] > runLength[n + 1]) {\n        break;\n      }\n      mergeAt(n);\n    }\n  }\n  function forceMergeRuns() {\n    while (stackSize > 1) {\n      var n = stackSize - 2;\n      if (n > 0 && runLength[n - 1] < runLength[n + 1]) {\n        n--;\n      }\n      mergeAt(n);\n    }\n  }\n  function mergeAt(i) {\n    var start1 = runStart[i];\n    var length1 = runLength[i];\n    var start2 = runStart[i + 1];\n    var length2 = runLength[i + 1];\n    runLength[i] = length1 + length2;\n    if (i === stackSize - 3) {\n      runStart[i + 1] = runStart[i + 2];\n      runLength[i + 1] = runLength[i + 2];\n    }\n    stackSize--;\n    var k = gallopRight(array[start2], array, start1, length1, 0, compare);\n    start1 += k;\n    length1 -= k;\n    if (length1 === 0) {\n      return;\n    }\n    length2 = gallopLeft(array[start1 + length1 - 1], array, start2, length2, length2 - 1, compare);\n    if (length2 === 0) {\n      return;\n    }\n    if (length1 <= length2) {\n      mergeLow(start1, length1, start2, length2);\n    } else {\n      mergeHigh(start1, length1, start2, length2);\n    }\n  }\n  function mergeLow(start1, length1, start2, length2) {\n    var i = 0;\n    for (i = 0; i < length1; i++) {\n      tmp[i] = array[start1 + i];\n    }\n    var cursor1 = 0;\n    var cursor2 = start2;\n    var dest = start1;\n    array[dest++] = array[cursor2++];\n    if (--length2 === 0) {\n      for (i = 0; i < length1; i++) {\n        array[dest + i] = tmp[cursor1 + i];\n      }\n      return;\n    }\n    if (length1 === 1) {\n      for (i = 0; i < length2; i++) {\n        array[dest + i] = array[cursor2 + i];\n      }\n      array[dest + length2] = tmp[cursor1];\n      return;\n    }\n    var _minGallop = minGallop;\n    var count1;\n    var count2;\n    var exit;\n    while (1) {\n      count1 = 0;\n      count2 = 0;\n      exit = false;\n      do {\n        if (compare(array[cursor2], tmp[cursor1]) < 0) {\n          array[dest++] = array[cursor2++];\n          count2++;\n          count1 = 0;\n          if (--length2 === 0) {\n            exit = true;\n            break;\n          }\n        } else {\n          array[dest++] = tmp[cursor1++];\n          count1++;\n          count2 = 0;\n          if (--length1 === 1) {\n            exit = true;\n            break;\n          }\n        }\n      } while ((count1 | count2) < _minGallop);\n      if (exit) {\n        break;\n      }\n      do {\n        count1 = gallopRight(array[cursor2], tmp, cursor1, length1, 0, compare);\n        if (count1 !== 0) {\n          for (i = 0; i < count1; i++) {\n            array[dest + i] = tmp[cursor1 + i];\n          }\n          dest += count1;\n          cursor1 += count1;\n          length1 -= count1;\n          if (length1 <= 1) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest++] = array[cursor2++];\n        if (--length2 === 0) {\n          exit = true;\n          break;\n        }\n        count2 = gallopLeft(tmp[cursor1], array, cursor2, length2, 0, compare);\n        if (count2 !== 0) {\n          for (i = 0; i < count2; i++) {\n            array[dest + i] = array[cursor2 + i];\n          }\n          dest += count2;\n          cursor2 += count2;\n          length2 -= count2;\n          if (length2 === 0) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest++] = tmp[cursor1++];\n        if (--length1 === 1) {\n          exit = true;\n          break;\n        }\n        _minGallop--;\n      } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n      if (exit) {\n        break;\n      }\n      if (_minGallop < 0) {\n        _minGallop = 0;\n      }\n      _minGallop += 2;\n    }\n    minGallop = _minGallop;\n    minGallop < 1 && (minGallop = 1);\n    if (length1 === 1) {\n      for (i = 0; i < length2; i++) {\n        array[dest + i] = array[cursor2 + i];\n      }\n      array[dest + length2] = tmp[cursor1];\n    } else if (length1 === 0) {\n      throw new Error();\n    } else {\n      for (i = 0; i < length1; i++) {\n        array[dest + i] = tmp[cursor1 + i];\n      }\n    }\n  }\n  function mergeHigh(start1, length1, start2, length2) {\n    var i = 0;\n    for (i = 0; i < length2; i++) {\n      tmp[i] = array[start2 + i];\n    }\n    var cursor1 = start1 + length1 - 1;\n    var cursor2 = length2 - 1;\n    var dest = start2 + length2 - 1;\n    var customCursor = 0;\n    var customDest = 0;\n    array[dest--] = array[cursor1--];\n    if (--length1 === 0) {\n      customCursor = dest - (length2 - 1);\n      for (i = 0; i < length2; i++) {\n        array[customCursor + i] = tmp[i];\n      }\n      return;\n    }\n    if (length2 === 1) {\n      dest -= length1;\n      cursor1 -= length1;\n      customDest = dest + 1;\n      customCursor = cursor1 + 1;\n      for (i = length1 - 1; i >= 0; i--) {\n        array[customDest + i] = array[customCursor + i];\n      }\n      array[dest] = tmp[cursor2];\n      return;\n    }\n    var _minGallop = minGallop;\n    while (true) {\n      var count1 = 0;\n      var count2 = 0;\n      var exit = false;\n      do {\n        if (compare(tmp[cursor2], array[cursor1]) < 0) {\n          array[dest--] = array[cursor1--];\n          count1++;\n          count2 = 0;\n          if (--length1 === 0) {\n            exit = true;\n            break;\n          }\n        } else {\n          array[dest--] = tmp[cursor2--];\n          count2++;\n          count1 = 0;\n          if (--length2 === 1) {\n            exit = true;\n            break;\n          }\n        }\n      } while ((count1 | count2) < _minGallop);\n      if (exit) {\n        break;\n      }\n      do {\n        count1 = length1 - gallopRight(tmp[cursor2], array, start1, length1, length1 - 1, compare);\n        if (count1 !== 0) {\n          dest -= count1;\n          cursor1 -= count1;\n          length1 -= count1;\n          customDest = dest + 1;\n          customCursor = cursor1 + 1;\n          for (i = count1 - 1; i >= 0; i--) {\n            array[customDest + i] = array[customCursor + i];\n          }\n          if (length1 === 0) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest--] = tmp[cursor2--];\n        if (--length2 === 1) {\n          exit = true;\n          break;\n        }\n        count2 = length2 - gallopLeft(array[cursor1], tmp, 0, length2, length2 - 1, compare);\n        if (count2 !== 0) {\n          dest -= count2;\n          cursor2 -= count2;\n          length2 -= count2;\n          customDest = dest + 1;\n          customCursor = cursor2 + 1;\n          for (i = 0; i < count2; i++) {\n            array[customDest + i] = tmp[customCursor + i];\n          }\n          if (length2 <= 1) {\n            exit = true;\n            break;\n          }\n        }\n        array[dest--] = array[cursor1--];\n        if (--length1 === 0) {\n          exit = true;\n          break;\n        }\n        _minGallop--;\n      } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n      if (exit) {\n        break;\n      }\n      if (_minGallop < 0) {\n        _minGallop = 0;\n      }\n      _minGallop += 2;\n    }\n    minGallop = _minGallop;\n    if (minGallop < 1) {\n      minGallop = 1;\n    }\n    if (length2 === 1) {\n      dest -= length1;\n      cursor1 -= length1;\n      customDest = dest + 1;\n      customCursor = cursor1 + 1;\n      for (i = length1 - 1; i >= 0; i--) {\n        array[customDest + i] = array[customCursor + i];\n      }\n      array[dest] = tmp[cursor2];\n    } else if (length2 === 0) {\n      throw new Error();\n    } else {\n      customCursor = dest - (length2 - 1);\n      for (i = 0; i < length2; i++) {\n        array[customCursor + i] = tmp[i];\n      }\n    }\n  }\n  return {\n    mergeRuns: mergeRuns,\n    forceMergeRuns: forceMergeRuns,\n    pushRun: pushRun\n  };\n}\nexport default function sort(array, compare, lo, hi) {\n  if (!lo) {\n    lo = 0;\n  }\n  if (!hi) {\n    hi = array.length;\n  }\n  var remaining = hi - lo;\n  if (remaining < 2) {\n    return;\n  }\n  var runLength = 0;\n  if (remaining < DEFAULT_MIN_MERGE) {\n    runLength = makeAscendingRun(array, lo, hi, compare);\n    binaryInsertionSort(array, lo, hi, lo + runLength, compare);\n    return;\n  }\n  var ts = TimSort(array, compare);\n  var minRun = minRunLength(remaining);\n  do {\n    runLength = makeAscendingRun(array, lo, hi, compare);\n    if (runLength < minRun) {\n      var force = remaining;\n      if (force > minRun) {\n        force = minRun;\n      }\n      binaryInsertionSort(array, lo, lo + force, lo + runLength, compare);\n      runLength = force;\n    }\n    ts.pushRun(lo, runLength);\n    ts.mergeRuns();\n    remaining -= runLength;\n    lo += runLength;\n  } while (remaining !== 0);\n  ts.forceMergeRuns();\n}", "map": {"version": 3, "names": ["DEFAULT_MIN_MERGE", "DEFAULT_MIN_GALLOPING", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "r", "makeAscendingRun", "array", "lo", "hi", "compare", "runHi", "reverseRun", "t", "binaryInsertionSort", "start", "pivot", "left", "right", "mid", "gallopLeft", "value", "length", "hint", "lastOffset", "maxOffset", "offset", "tmp", "m", "gallopRight", "<PERSON><PERSON><PERSON>", "minGallop", "runStart", "<PERSON><PERSON><PERSON><PERSON>", "stackSize", "pushRun", "_runStart", "_runLength", "mergeRuns", "mergeAt", "forceMergeRuns", "i", "start1", "length1", "start2", "length2", "k", "mergeLow", "mergeHigh", "cursor1", "cursor2", "dest", "_minGallop", "count1", "count2", "exit", "Error", "customCursor", "customDest", "sort", "remaining", "ts", "minRun", "force"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/core/timsort.js"], "sourcesContent": ["var DEFAULT_MIN_MERGE = 32;\nvar DEFAULT_MIN_GALLOPING = 7;\nfunction minRunLength(n) {\n    var r = 0;\n    while (n >= DEFAULT_MIN_MERGE) {\n        r |= n & 1;\n        n >>= 1;\n    }\n    return n + r;\n}\nfunction makeAscendingRun(array, lo, hi, compare) {\n    var runHi = lo + 1;\n    if (runHi === hi) {\n        return 1;\n    }\n    if (compare(array[runHi++], array[lo]) < 0) {\n        while (runHi < hi && compare(array[runHi], array[runHi - 1]) < 0) {\n            runHi++;\n        }\n        reverseRun(array, lo, runHi);\n    }\n    else {\n        while (runHi < hi && compare(array[runHi], array[runHi - 1]) >= 0) {\n            runHi++;\n        }\n    }\n    return runHi - lo;\n}\nfunction reverseRun(array, lo, hi) {\n    hi--;\n    while (lo < hi) {\n        var t = array[lo];\n        array[lo++] = array[hi];\n        array[hi--] = t;\n    }\n}\nfunction binaryInsertionSort(array, lo, hi, start, compare) {\n    if (start === lo) {\n        start++;\n    }\n    for (; start < hi; start++) {\n        var pivot = array[start];\n        var left = lo;\n        var right = start;\n        var mid;\n        while (left < right) {\n            mid = left + right >>> 1;\n            if (compare(pivot, array[mid]) < 0) {\n                right = mid;\n            }\n            else {\n                left = mid + 1;\n            }\n        }\n        var n = start - left;\n        switch (n) {\n            case 3:\n                array[left + 3] = array[left + 2];\n            case 2:\n                array[left + 2] = array[left + 1];\n            case 1:\n                array[left + 1] = array[left];\n                break;\n            default:\n                while (n > 0) {\n                    array[left + n] = array[left + n - 1];\n                    n--;\n                }\n        }\n        array[left] = pivot;\n    }\n}\nfunction gallopLeft(value, array, start, length, hint, compare) {\n    var lastOffset = 0;\n    var maxOffset = 0;\n    var offset = 1;\n    if (compare(value, array[start + hint]) > 0) {\n        maxOffset = length - hint;\n        while (offset < maxOffset && compare(value, array[start + hint + offset]) > 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        lastOffset += hint;\n        offset += hint;\n    }\n    else {\n        maxOffset = hint + 1;\n        while (offset < maxOffset && compare(value, array[start + hint - offset]) <= 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        var tmp = lastOffset;\n        lastOffset = hint - offset;\n        offset = hint - tmp;\n    }\n    lastOffset++;\n    while (lastOffset < offset) {\n        var m = lastOffset + (offset - lastOffset >>> 1);\n        if (compare(value, array[start + m]) > 0) {\n            lastOffset = m + 1;\n        }\n        else {\n            offset = m;\n        }\n    }\n    return offset;\n}\nfunction gallopRight(value, array, start, length, hint, compare) {\n    var lastOffset = 0;\n    var maxOffset = 0;\n    var offset = 1;\n    if (compare(value, array[start + hint]) < 0) {\n        maxOffset = hint + 1;\n        while (offset < maxOffset && compare(value, array[start + hint - offset]) < 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        var tmp = lastOffset;\n        lastOffset = hint - offset;\n        offset = hint - tmp;\n    }\n    else {\n        maxOffset = length - hint;\n        while (offset < maxOffset && compare(value, array[start + hint + offset]) >= 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        lastOffset += hint;\n        offset += hint;\n    }\n    lastOffset++;\n    while (lastOffset < offset) {\n        var m = lastOffset + (offset - lastOffset >>> 1);\n        if (compare(value, array[start + m]) < 0) {\n            offset = m;\n        }\n        else {\n            lastOffset = m + 1;\n        }\n    }\n    return offset;\n}\nfunction TimSort(array, compare) {\n    var minGallop = DEFAULT_MIN_GALLOPING;\n    var runStart;\n    var runLength;\n    var stackSize = 0;\n    var tmp = [];\n    runStart = [];\n    runLength = [];\n    function pushRun(_runStart, _runLength) {\n        runStart[stackSize] = _runStart;\n        runLength[stackSize] = _runLength;\n        stackSize += 1;\n    }\n    function mergeRuns() {\n        while (stackSize > 1) {\n            var n = stackSize - 2;\n            if ((n >= 1 && runLength[n - 1] <= runLength[n] + runLength[n + 1])\n                || (n >= 2 && runLength[n - 2] <= runLength[n] + runLength[n - 1])) {\n                if (runLength[n - 1] < runLength[n + 1]) {\n                    n--;\n                }\n            }\n            else if (runLength[n] > runLength[n + 1]) {\n                break;\n            }\n            mergeAt(n);\n        }\n    }\n    function forceMergeRuns() {\n        while (stackSize > 1) {\n            var n = stackSize - 2;\n            if (n > 0 && runLength[n - 1] < runLength[n + 1]) {\n                n--;\n            }\n            mergeAt(n);\n        }\n    }\n    function mergeAt(i) {\n        var start1 = runStart[i];\n        var length1 = runLength[i];\n        var start2 = runStart[i + 1];\n        var length2 = runLength[i + 1];\n        runLength[i] = length1 + length2;\n        if (i === stackSize - 3) {\n            runStart[i + 1] = runStart[i + 2];\n            runLength[i + 1] = runLength[i + 2];\n        }\n        stackSize--;\n        var k = gallopRight(array[start2], array, start1, length1, 0, compare);\n        start1 += k;\n        length1 -= k;\n        if (length1 === 0) {\n            return;\n        }\n        length2 = gallopLeft(array[start1 + length1 - 1], array, start2, length2, length2 - 1, compare);\n        if (length2 === 0) {\n            return;\n        }\n        if (length1 <= length2) {\n            mergeLow(start1, length1, start2, length2);\n        }\n        else {\n            mergeHigh(start1, length1, start2, length2);\n        }\n    }\n    function mergeLow(start1, length1, start2, length2) {\n        var i = 0;\n        for (i = 0; i < length1; i++) {\n            tmp[i] = array[start1 + i];\n        }\n        var cursor1 = 0;\n        var cursor2 = start2;\n        var dest = start1;\n        array[dest++] = array[cursor2++];\n        if (--length2 === 0) {\n            for (i = 0; i < length1; i++) {\n                array[dest + i] = tmp[cursor1 + i];\n            }\n            return;\n        }\n        if (length1 === 1) {\n            for (i = 0; i < length2; i++) {\n                array[dest + i] = array[cursor2 + i];\n            }\n            array[dest + length2] = tmp[cursor1];\n            return;\n        }\n        var _minGallop = minGallop;\n        var count1;\n        var count2;\n        var exit;\n        while (1) {\n            count1 = 0;\n            count2 = 0;\n            exit = false;\n            do {\n                if (compare(array[cursor2], tmp[cursor1]) < 0) {\n                    array[dest++] = array[cursor2++];\n                    count2++;\n                    count1 = 0;\n                    if (--length2 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                else {\n                    array[dest++] = tmp[cursor1++];\n                    count1++;\n                    count2 = 0;\n                    if (--length1 === 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n            } while ((count1 | count2) < _minGallop);\n            if (exit) {\n                break;\n            }\n            do {\n                count1 = gallopRight(array[cursor2], tmp, cursor1, length1, 0, compare);\n                if (count1 !== 0) {\n                    for (i = 0; i < count1; i++) {\n                        array[dest + i] = tmp[cursor1 + i];\n                    }\n                    dest += count1;\n                    cursor1 += count1;\n                    length1 -= count1;\n                    if (length1 <= 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest++] = array[cursor2++];\n                if (--length2 === 0) {\n                    exit = true;\n                    break;\n                }\n                count2 = gallopLeft(tmp[cursor1], array, cursor2, length2, 0, compare);\n                if (count2 !== 0) {\n                    for (i = 0; i < count2; i++) {\n                        array[dest + i] = array[cursor2 + i];\n                    }\n                    dest += count2;\n                    cursor2 += count2;\n                    length2 -= count2;\n                    if (length2 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest++] = tmp[cursor1++];\n                if (--length1 === 1) {\n                    exit = true;\n                    break;\n                }\n                _minGallop--;\n            } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n            if (exit) {\n                break;\n            }\n            if (_minGallop < 0) {\n                _minGallop = 0;\n            }\n            _minGallop += 2;\n        }\n        minGallop = _minGallop;\n        minGallop < 1 && (minGallop = 1);\n        if (length1 === 1) {\n            for (i = 0; i < length2; i++) {\n                array[dest + i] = array[cursor2 + i];\n            }\n            array[dest + length2] = tmp[cursor1];\n        }\n        else if (length1 === 0) {\n            throw new Error();\n        }\n        else {\n            for (i = 0; i < length1; i++) {\n                array[dest + i] = tmp[cursor1 + i];\n            }\n        }\n    }\n    function mergeHigh(start1, length1, start2, length2) {\n        var i = 0;\n        for (i = 0; i < length2; i++) {\n            tmp[i] = array[start2 + i];\n        }\n        var cursor1 = start1 + length1 - 1;\n        var cursor2 = length2 - 1;\n        var dest = start2 + length2 - 1;\n        var customCursor = 0;\n        var customDest = 0;\n        array[dest--] = array[cursor1--];\n        if (--length1 === 0) {\n            customCursor = dest - (length2 - 1);\n            for (i = 0; i < length2; i++) {\n                array[customCursor + i] = tmp[i];\n            }\n            return;\n        }\n        if (length2 === 1) {\n            dest -= length1;\n            cursor1 -= length1;\n            customDest = dest + 1;\n            customCursor = cursor1 + 1;\n            for (i = length1 - 1; i >= 0; i--) {\n                array[customDest + i] = array[customCursor + i];\n            }\n            array[dest] = tmp[cursor2];\n            return;\n        }\n        var _minGallop = minGallop;\n        while (true) {\n            var count1 = 0;\n            var count2 = 0;\n            var exit = false;\n            do {\n                if (compare(tmp[cursor2], array[cursor1]) < 0) {\n                    array[dest--] = array[cursor1--];\n                    count1++;\n                    count2 = 0;\n                    if (--length1 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                else {\n                    array[dest--] = tmp[cursor2--];\n                    count2++;\n                    count1 = 0;\n                    if (--length2 === 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n            } while ((count1 | count2) < _minGallop);\n            if (exit) {\n                break;\n            }\n            do {\n                count1 = length1 - gallopRight(tmp[cursor2], array, start1, length1, length1 - 1, compare);\n                if (count1 !== 0) {\n                    dest -= count1;\n                    cursor1 -= count1;\n                    length1 -= count1;\n                    customDest = dest + 1;\n                    customCursor = cursor1 + 1;\n                    for (i = count1 - 1; i >= 0; i--) {\n                        array[customDest + i] = array[customCursor + i];\n                    }\n                    if (length1 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest--] = tmp[cursor2--];\n                if (--length2 === 1) {\n                    exit = true;\n                    break;\n                }\n                count2 = length2 - gallopLeft(array[cursor1], tmp, 0, length2, length2 - 1, compare);\n                if (count2 !== 0) {\n                    dest -= count2;\n                    cursor2 -= count2;\n                    length2 -= count2;\n                    customDest = dest + 1;\n                    customCursor = cursor2 + 1;\n                    for (i = 0; i < count2; i++) {\n                        array[customDest + i] = tmp[customCursor + i];\n                    }\n                    if (length2 <= 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest--] = array[cursor1--];\n                if (--length1 === 0) {\n                    exit = true;\n                    break;\n                }\n                _minGallop--;\n            } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n            if (exit) {\n                break;\n            }\n            if (_minGallop < 0) {\n                _minGallop = 0;\n            }\n            _minGallop += 2;\n        }\n        minGallop = _minGallop;\n        if (minGallop < 1) {\n            minGallop = 1;\n        }\n        if (length2 === 1) {\n            dest -= length1;\n            cursor1 -= length1;\n            customDest = dest + 1;\n            customCursor = cursor1 + 1;\n            for (i = length1 - 1; i >= 0; i--) {\n                array[customDest + i] = array[customCursor + i];\n            }\n            array[dest] = tmp[cursor2];\n        }\n        else if (length2 === 0) {\n            throw new Error();\n        }\n        else {\n            customCursor = dest - (length2 - 1);\n            for (i = 0; i < length2; i++) {\n                array[customCursor + i] = tmp[i];\n            }\n        }\n    }\n    return {\n        mergeRuns: mergeRuns,\n        forceMergeRuns: forceMergeRuns,\n        pushRun: pushRun\n    };\n}\nexport default function sort(array, compare, lo, hi) {\n    if (!lo) {\n        lo = 0;\n    }\n    if (!hi) {\n        hi = array.length;\n    }\n    var remaining = hi - lo;\n    if (remaining < 2) {\n        return;\n    }\n    var runLength = 0;\n    if (remaining < DEFAULT_MIN_MERGE) {\n        runLength = makeAscendingRun(array, lo, hi, compare);\n        binaryInsertionSort(array, lo, hi, lo + runLength, compare);\n        return;\n    }\n    var ts = TimSort(array, compare);\n    var minRun = minRunLength(remaining);\n    do {\n        runLength = makeAscendingRun(array, lo, hi, compare);\n        if (runLength < minRun) {\n            var force = remaining;\n            if (force > minRun) {\n                force = minRun;\n            }\n            binaryInsertionSort(array, lo, lo + force, lo + runLength, compare);\n            runLength = force;\n        }\n        ts.pushRun(lo, runLength);\n        ts.mergeRuns();\n        remaining -= runLength;\n        lo += runLength;\n    } while (remaining !== 0);\n    ts.forceMergeRuns();\n}\n"], "mappings": "AAAA,IAAIA,iBAAiB,GAAG,EAAE;AAC1B,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,SAASC,YAAYA,CAACC,CAAC,EAAE;EACrB,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOD,CAAC,IAAIH,iBAAiB,EAAE;IAC3BI,CAAC,IAAID,CAAC,GAAG,CAAC;IACVA,CAAC,KAAK,CAAC;EACX;EACA,OAAOA,CAAC,GAAGC,CAAC;AAChB;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAC9C,IAAIC,KAAK,GAAGH,EAAE,GAAG,CAAC;EAClB,IAAIG,KAAK,KAAKF,EAAE,EAAE;IACd,OAAO,CAAC;EACZ;EACA,IAAIC,OAAO,CAACH,KAAK,CAACI,KAAK,EAAE,CAAC,EAAEJ,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE;IACxC,OAAOG,KAAK,GAAGF,EAAE,IAAIC,OAAO,CAACH,KAAK,CAACI,KAAK,CAAC,EAAEJ,KAAK,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC9DA,KAAK,EAAE;IACX;IACAC,UAAU,CAACL,KAAK,EAAEC,EAAE,EAAEG,KAAK,CAAC;EAChC,CAAC,MACI;IACD,OAAOA,KAAK,GAAGF,EAAE,IAAIC,OAAO,CAACH,KAAK,CAACI,KAAK,CAAC,EAAEJ,KAAK,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAC/DA,KAAK,EAAE;IACX;EACJ;EACA,OAAOA,KAAK,GAAGH,EAAE;AACrB;AACA,SAASI,UAAUA,CAACL,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC/BA,EAAE,EAAE;EACJ,OAAOD,EAAE,GAAGC,EAAE,EAAE;IACZ,IAAII,CAAC,GAAGN,KAAK,CAACC,EAAE,CAAC;IACjBD,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGD,KAAK,CAACE,EAAE,CAAC;IACvBF,KAAK,CAACE,EAAE,EAAE,CAAC,GAAGI,CAAC;EACnB;AACJ;AACA,SAASC,mBAAmBA,CAACP,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEM,KAAK,EAAEL,OAAO,EAAE;EACxD,IAAIK,KAAK,KAAKP,EAAE,EAAE;IACdO,KAAK,EAAE;EACX;EACA,OAAOA,KAAK,GAAGN,EAAE,EAAEM,KAAK,EAAE,EAAE;IACxB,IAAIC,KAAK,GAAGT,KAAK,CAACQ,KAAK,CAAC;IACxB,IAAIE,IAAI,GAAGT,EAAE;IACb,IAAIU,KAAK,GAAGH,KAAK;IACjB,IAAII,GAAG;IACP,OAAOF,IAAI,GAAGC,KAAK,EAAE;MACjBC,GAAG,GAAGF,IAAI,GAAGC,KAAK,KAAK,CAAC;MACxB,IAAIR,OAAO,CAACM,KAAK,EAAET,KAAK,CAACY,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;QAChCD,KAAK,GAAGC,GAAG;MACf,CAAC,MACI;QACDF,IAAI,GAAGE,GAAG,GAAG,CAAC;MAClB;IACJ;IACA,IAAIf,CAAC,GAAGW,KAAK,GAAGE,IAAI;IACpB,QAAQb,CAAC;MACL,KAAK,CAAC;QACFG,KAAK,CAACU,IAAI,GAAG,CAAC,CAAC,GAAGV,KAAK,CAACU,IAAI,GAAG,CAAC,CAAC;MACrC,KAAK,CAAC;QACFV,KAAK,CAACU,IAAI,GAAG,CAAC,CAAC,GAAGV,KAAK,CAACU,IAAI,GAAG,CAAC,CAAC;MACrC,KAAK,CAAC;QACFV,KAAK,CAACU,IAAI,GAAG,CAAC,CAAC,GAAGV,KAAK,CAACU,IAAI,CAAC;QAC7B;MACJ;QACI,OAAOb,CAAC,GAAG,CAAC,EAAE;UACVG,KAAK,CAACU,IAAI,GAAGb,CAAC,CAAC,GAAGG,KAAK,CAACU,IAAI,GAAGb,CAAC,GAAG,CAAC,CAAC;UACrCA,CAAC,EAAE;QACP;IACR;IACAG,KAAK,CAACU,IAAI,CAAC,GAAGD,KAAK;EACvB;AACJ;AACA,SAASI,UAAUA,CAACC,KAAK,EAAEd,KAAK,EAAEQ,KAAK,EAAEO,MAAM,EAAEC,IAAI,EAAEb,OAAO,EAAE;EAC5D,IAAIc,UAAU,GAAG,CAAC;EAClB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIhB,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzCE,SAAS,GAAGH,MAAM,GAAGC,IAAI;IACzB,OAAOG,MAAM,GAAGD,SAAS,IAAIf,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,GAAGG,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MAC3EF,UAAU,GAAGE,MAAM;MACnBA,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAIA,MAAM,IAAI,CAAC,EAAE;QACbA,MAAM,GAAGD,SAAS;MACtB;IACJ;IACA,IAAIC,MAAM,GAAGD,SAAS,EAAE;MACpBC,MAAM,GAAGD,SAAS;IACtB;IACAD,UAAU,IAAID,IAAI;IAClBG,MAAM,IAAIH,IAAI;EAClB,CAAC,MACI;IACDE,SAAS,GAAGF,IAAI,GAAG,CAAC;IACpB,OAAOG,MAAM,GAAGD,SAAS,IAAIf,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,GAAGG,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MAC5EF,UAAU,GAAGE,MAAM;MACnBA,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAIA,MAAM,IAAI,CAAC,EAAE;QACbA,MAAM,GAAGD,SAAS;MACtB;IACJ;IACA,IAAIC,MAAM,GAAGD,SAAS,EAAE;MACpBC,MAAM,GAAGD,SAAS;IACtB;IACA,IAAIE,GAAG,GAAGH,UAAU;IACpBA,UAAU,GAAGD,IAAI,GAAGG,MAAM;IAC1BA,MAAM,GAAGH,IAAI,GAAGI,GAAG;EACvB;EACAH,UAAU,EAAE;EACZ,OAAOA,UAAU,GAAGE,MAAM,EAAE;IACxB,IAAIE,CAAC,GAAGJ,UAAU,IAAIE,MAAM,GAAGF,UAAU,KAAK,CAAC,CAAC;IAChD,IAAId,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACtCJ,UAAU,GAAGI,CAAC,GAAG,CAAC;IACtB,CAAC,MACI;MACDF,MAAM,GAAGE,CAAC;IACd;EACJ;EACA,OAAOF,MAAM;AACjB;AACA,SAASG,WAAWA,CAACR,KAAK,EAAEd,KAAK,EAAEQ,KAAK,EAAEO,MAAM,EAAEC,IAAI,EAAEb,OAAO,EAAE;EAC7D,IAAIc,UAAU,GAAG,CAAC;EAClB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIhB,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzCE,SAAS,GAAGF,IAAI,GAAG,CAAC;IACpB,OAAOG,MAAM,GAAGD,SAAS,IAAIf,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,GAAGG,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MAC3EF,UAAU,GAAGE,MAAM;MACnBA,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAIA,MAAM,IAAI,CAAC,EAAE;QACbA,MAAM,GAAGD,SAAS;MACtB;IACJ;IACA,IAAIC,MAAM,GAAGD,SAAS,EAAE;MACpBC,MAAM,GAAGD,SAAS;IACtB;IACA,IAAIE,GAAG,GAAGH,UAAU;IACpBA,UAAU,GAAGD,IAAI,GAAGG,MAAM;IAC1BA,MAAM,GAAGH,IAAI,GAAGI,GAAG;EACvB,CAAC,MACI;IACDF,SAAS,GAAGH,MAAM,GAAGC,IAAI;IACzB,OAAOG,MAAM,GAAGD,SAAS,IAAIf,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGQ,IAAI,GAAGG,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MAC5EF,UAAU,GAAGE,MAAM;MACnBA,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAIA,MAAM,IAAI,CAAC,EAAE;QACbA,MAAM,GAAGD,SAAS;MACtB;IACJ;IACA,IAAIC,MAAM,GAAGD,SAAS,EAAE;MACpBC,MAAM,GAAGD,SAAS;IACtB;IACAD,UAAU,IAAID,IAAI;IAClBG,MAAM,IAAIH,IAAI;EAClB;EACAC,UAAU,EAAE;EACZ,OAAOA,UAAU,GAAGE,MAAM,EAAE;IACxB,IAAIE,CAAC,GAAGJ,UAAU,IAAIE,MAAM,GAAGF,UAAU,KAAK,CAAC,CAAC;IAChD,IAAId,OAAO,CAACW,KAAK,EAAEd,KAAK,CAACQ,KAAK,GAAGa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACtCF,MAAM,GAAGE,CAAC;IACd,CAAC,MACI;MACDJ,UAAU,GAAGI,CAAC,GAAG,CAAC;IACtB;EACJ;EACA,OAAOF,MAAM;AACjB;AACA,SAASI,OAAOA,CAACvB,KAAK,EAAEG,OAAO,EAAE;EAC7B,IAAIqB,SAAS,GAAG7B,qBAAqB;EACrC,IAAI8B,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIP,GAAG,GAAG,EAAE;EACZK,QAAQ,GAAG,EAAE;EACbC,SAAS,GAAG,EAAE;EACd,SAASE,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;IACpCL,QAAQ,CAACE,SAAS,CAAC,GAAGE,SAAS;IAC/BH,SAAS,CAACC,SAAS,CAAC,GAAGG,UAAU;IACjCH,SAAS,IAAI,CAAC;EAClB;EACA,SAASI,SAASA,CAAA,EAAG;IACjB,OAAOJ,SAAS,GAAG,CAAC,EAAE;MAClB,IAAI9B,CAAC,GAAG8B,SAAS,GAAG,CAAC;MACrB,IAAK9B,CAAC,IAAI,CAAC,IAAI6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAI6B,SAAS,CAAC7B,CAAC,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAC1DA,CAAC,IAAI,CAAC,IAAI6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAI6B,SAAS,CAAC7B,CAAC,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAE,EAAE;QACpE,IAAI6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,EAAE;UACrCA,CAAC,EAAE;QACP;MACJ,CAAC,MACI,IAAI6B,SAAS,CAAC7B,CAAC,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,EAAE;QACtC;MACJ;MACAmC,OAAO,CAACnC,CAAC,CAAC;IACd;EACJ;EACA,SAASoC,cAAcA,CAAA,EAAG;IACtB,OAAON,SAAS,GAAG,CAAC,EAAE;MAClB,IAAI9B,CAAC,GAAG8B,SAAS,GAAG,CAAC;MACrB,IAAI9B,CAAC,GAAG,CAAC,IAAI6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAG6B,SAAS,CAAC7B,CAAC,GAAG,CAAC,CAAC,EAAE;QAC9CA,CAAC,EAAE;MACP;MACAmC,OAAO,CAACnC,CAAC,CAAC;IACd;EACJ;EACA,SAASmC,OAAOA,CAACE,CAAC,EAAE;IAChB,IAAIC,MAAM,GAAGV,QAAQ,CAACS,CAAC,CAAC;IACxB,IAAIE,OAAO,GAAGV,SAAS,CAACQ,CAAC,CAAC;IAC1B,IAAIG,MAAM,GAAGZ,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAII,OAAO,GAAGZ,SAAS,CAACQ,CAAC,GAAG,CAAC,CAAC;IAC9BR,SAAS,CAACQ,CAAC,CAAC,GAAGE,OAAO,GAAGE,OAAO;IAChC,IAAIJ,CAAC,KAAKP,SAAS,GAAG,CAAC,EAAE;MACrBF,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC;MACjCR,SAAS,CAACQ,CAAC,GAAG,CAAC,CAAC,GAAGR,SAAS,CAACQ,CAAC,GAAG,CAAC,CAAC;IACvC;IACAP,SAAS,EAAE;IACX,IAAIY,CAAC,GAAGjB,WAAW,CAACtB,KAAK,CAACqC,MAAM,CAAC,EAAErC,KAAK,EAAEmC,MAAM,EAAEC,OAAO,EAAE,CAAC,EAAEjC,OAAO,CAAC;IACtEgC,MAAM,IAAII,CAAC;IACXH,OAAO,IAAIG,CAAC;IACZ,IAAIH,OAAO,KAAK,CAAC,EAAE;MACf;IACJ;IACAE,OAAO,GAAGzB,UAAU,CAACb,KAAK,CAACmC,MAAM,GAAGC,OAAO,GAAG,CAAC,CAAC,EAAEpC,KAAK,EAAEqC,MAAM,EAAEC,OAAO,EAAEA,OAAO,GAAG,CAAC,EAAEnC,OAAO,CAAC;IAC/F,IAAImC,OAAO,KAAK,CAAC,EAAE;MACf;IACJ;IACA,IAAIF,OAAO,IAAIE,OAAO,EAAE;MACpBE,QAAQ,CAACL,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAC9C,CAAC,MACI;MACDG,SAAS,CAACN,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAC/C;EACJ;EACA,SAASE,QAAQA,CAACL,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAChD,IAAIJ,CAAC,GAAG,CAAC;IACT,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,EAAEF,CAAC,EAAE,EAAE;MAC1Bd,GAAG,CAACc,CAAC,CAAC,GAAGlC,KAAK,CAACmC,MAAM,GAAGD,CAAC,CAAC;IAC9B;IACA,IAAIQ,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAGN,MAAM;IACpB,IAAIO,IAAI,GAAGT,MAAM;IACjBnC,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC2C,OAAO,EAAE,CAAC;IAChC,IAAI,EAAEL,OAAO,KAAK,CAAC,EAAE;MACjB,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,EAAEF,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGd,GAAG,CAACsB,OAAO,GAAGR,CAAC,CAAC;MACtC;MACA;IACJ;IACA,IAAIE,OAAO,KAAK,CAAC,EAAE;MACf,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,EAAEJ,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGlC,KAAK,CAAC2C,OAAO,GAAGT,CAAC,CAAC;MACxC;MACAlC,KAAK,CAAC4C,IAAI,GAAGN,OAAO,CAAC,GAAGlB,GAAG,CAACsB,OAAO,CAAC;MACpC;IACJ;IACA,IAAIG,UAAU,GAAGrB,SAAS;IAC1B,IAAIsB,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,IAAI;IACR,OAAO,CAAC,EAAE;MACNF,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVC,IAAI,GAAG,KAAK;MACZ,GAAG;QACC,IAAI7C,OAAO,CAACH,KAAK,CAAC2C,OAAO,CAAC,EAAEvB,GAAG,CAACsB,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;UAC3C1C,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC2C,OAAO,EAAE,CAAC;UAChCI,MAAM,EAAE;UACRD,MAAM,GAAG,CAAC;UACV,IAAI,EAAER,OAAO,KAAK,CAAC,EAAE;YACjBU,IAAI,GAAG,IAAI;YACX;UACJ;QACJ,CAAC,MACI;UACDhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAGxB,GAAG,CAACsB,OAAO,EAAE,CAAC;UAC9BI,MAAM,EAAE;UACRC,MAAM,GAAG,CAAC;UACV,IAAI,EAAEX,OAAO,KAAK,CAAC,EAAE;YACjBY,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;MACJ,CAAC,QAAQ,CAACF,MAAM,GAAGC,MAAM,IAAIF,UAAU;MACvC,IAAIG,IAAI,EAAE;QACN;MACJ;MACA,GAAG;QACCF,MAAM,GAAGxB,WAAW,CAACtB,KAAK,CAAC2C,OAAO,CAAC,EAAEvB,GAAG,EAAEsB,OAAO,EAAEN,OAAO,EAAE,CAAC,EAAEjC,OAAO,CAAC;QACvE,IAAI2C,MAAM,KAAK,CAAC,EAAE;UACd,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,EAAEZ,CAAC,EAAE,EAAE;YACzBlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGd,GAAG,CAACsB,OAAO,GAAGR,CAAC,CAAC;UACtC;UACAU,IAAI,IAAIE,MAAM;UACdJ,OAAO,IAAII,MAAM;UACjBV,OAAO,IAAIU,MAAM;UACjB,IAAIV,OAAO,IAAI,CAAC,EAAE;YACdY,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;QACAhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC2C,OAAO,EAAE,CAAC;QAChC,IAAI,EAAEL,OAAO,KAAK,CAAC,EAAE;UACjBU,IAAI,GAAG,IAAI;UACX;QACJ;QACAD,MAAM,GAAGlC,UAAU,CAACO,GAAG,CAACsB,OAAO,CAAC,EAAE1C,KAAK,EAAE2C,OAAO,EAAEL,OAAO,EAAE,CAAC,EAAEnC,OAAO,CAAC;QACtE,IAAI4C,MAAM,KAAK,CAAC,EAAE;UACd,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,MAAM,EAAEb,CAAC,EAAE,EAAE;YACzBlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGlC,KAAK,CAAC2C,OAAO,GAAGT,CAAC,CAAC;UACxC;UACAU,IAAI,IAAIG,MAAM;UACdJ,OAAO,IAAII,MAAM;UACjBT,OAAO,IAAIS,MAAM;UACjB,IAAIT,OAAO,KAAK,CAAC,EAAE;YACfU,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;QACAhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAGxB,GAAG,CAACsB,OAAO,EAAE,CAAC;QAC9B,IAAI,EAAEN,OAAO,KAAK,CAAC,EAAE;UACjBY,IAAI,GAAG,IAAI;UACX;QACJ;QACAH,UAAU,EAAE;MAChB,CAAC,QAAQC,MAAM,IAAInD,qBAAqB,IAAIoD,MAAM,IAAIpD,qBAAqB;MAC3E,IAAIqD,IAAI,EAAE;QACN;MACJ;MACA,IAAIH,UAAU,GAAG,CAAC,EAAE;QAChBA,UAAU,GAAG,CAAC;MAClB;MACAA,UAAU,IAAI,CAAC;IACnB;IACArB,SAAS,GAAGqB,UAAU;IACtBrB,SAAS,GAAG,CAAC,KAAKA,SAAS,GAAG,CAAC,CAAC;IAChC,IAAIY,OAAO,KAAK,CAAC,EAAE;MACf,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,EAAEJ,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGlC,KAAK,CAAC2C,OAAO,GAAGT,CAAC,CAAC;MACxC;MACAlC,KAAK,CAAC4C,IAAI,GAAGN,OAAO,CAAC,GAAGlB,GAAG,CAACsB,OAAO,CAAC;IACxC,CAAC,MACI,IAAIN,OAAO,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIa,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACD,KAAKf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,EAAEF,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAAC4C,IAAI,GAAGV,CAAC,CAAC,GAAGd,GAAG,CAACsB,OAAO,GAAGR,CAAC,CAAC;MACtC;IACJ;EACJ;EACA,SAASO,SAASA,CAACN,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACjD,IAAIJ,CAAC,GAAG,CAAC;IACT,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,EAAEJ,CAAC,EAAE,EAAE;MAC1Bd,GAAG,CAACc,CAAC,CAAC,GAAGlC,KAAK,CAACqC,MAAM,GAAGH,CAAC,CAAC;IAC9B;IACA,IAAIQ,OAAO,GAAGP,MAAM,GAAGC,OAAO,GAAG,CAAC;IAClC,IAAIO,OAAO,GAAGL,OAAO,GAAG,CAAC;IACzB,IAAIM,IAAI,GAAGP,MAAM,GAAGC,OAAO,GAAG,CAAC;IAC/B,IAAIY,YAAY,GAAG,CAAC;IACpB,IAAIC,UAAU,GAAG,CAAC;IAClBnD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC0C,OAAO,EAAE,CAAC;IAChC,IAAI,EAAEN,OAAO,KAAK,CAAC,EAAE;MACjBc,YAAY,GAAGN,IAAI,IAAIN,OAAO,GAAG,CAAC,CAAC;MACnC,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,EAAEJ,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAACkD,YAAY,GAAGhB,CAAC,CAAC,GAAGd,GAAG,CAACc,CAAC,CAAC;MACpC;MACA;IACJ;IACA,IAAII,OAAO,KAAK,CAAC,EAAE;MACfM,IAAI,IAAIR,OAAO;MACfM,OAAO,IAAIN,OAAO;MAClBe,UAAU,GAAGP,IAAI,GAAG,CAAC;MACrBM,YAAY,GAAGR,OAAO,GAAG,CAAC;MAC1B,KAAKR,CAAC,GAAGE,OAAO,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/BlC,KAAK,CAACmD,UAAU,GAAGjB,CAAC,CAAC,GAAGlC,KAAK,CAACkD,YAAY,GAAGhB,CAAC,CAAC;MACnD;MACAlC,KAAK,CAAC4C,IAAI,CAAC,GAAGxB,GAAG,CAACuB,OAAO,CAAC;MAC1B;IACJ;IACA,IAAIE,UAAU,GAAGrB,SAAS;IAC1B,OAAO,IAAI,EAAE;MACT,IAAIsB,MAAM,GAAG,CAAC;MACd,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,IAAI,GAAG,KAAK;MAChB,GAAG;QACC,IAAI7C,OAAO,CAACiB,GAAG,CAACuB,OAAO,CAAC,EAAE3C,KAAK,CAAC0C,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;UAC3C1C,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC0C,OAAO,EAAE,CAAC;UAChCI,MAAM,EAAE;UACRC,MAAM,GAAG,CAAC;UACV,IAAI,EAAEX,OAAO,KAAK,CAAC,EAAE;YACjBY,IAAI,GAAG,IAAI;YACX;UACJ;QACJ,CAAC,MACI;UACDhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAGxB,GAAG,CAACuB,OAAO,EAAE,CAAC;UAC9BI,MAAM,EAAE;UACRD,MAAM,GAAG,CAAC;UACV,IAAI,EAAER,OAAO,KAAK,CAAC,EAAE;YACjBU,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;MACJ,CAAC,QAAQ,CAACF,MAAM,GAAGC,MAAM,IAAIF,UAAU;MACvC,IAAIG,IAAI,EAAE;QACN;MACJ;MACA,GAAG;QACCF,MAAM,GAAGV,OAAO,GAAGd,WAAW,CAACF,GAAG,CAACuB,OAAO,CAAC,EAAE3C,KAAK,EAAEmC,MAAM,EAAEC,OAAO,EAAEA,OAAO,GAAG,CAAC,EAAEjC,OAAO,CAAC;QAC1F,IAAI2C,MAAM,KAAK,CAAC,EAAE;UACdF,IAAI,IAAIE,MAAM;UACdJ,OAAO,IAAII,MAAM;UACjBV,OAAO,IAAIU,MAAM;UACjBK,UAAU,GAAGP,IAAI,GAAG,CAAC;UACrBM,YAAY,GAAGR,OAAO,GAAG,CAAC;UAC1B,KAAKR,CAAC,GAAGY,MAAM,GAAG,CAAC,EAAEZ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC9BlC,KAAK,CAACmD,UAAU,GAAGjB,CAAC,CAAC,GAAGlC,KAAK,CAACkD,YAAY,GAAGhB,CAAC,CAAC;UACnD;UACA,IAAIE,OAAO,KAAK,CAAC,EAAE;YACfY,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;QACAhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAGxB,GAAG,CAACuB,OAAO,EAAE,CAAC;QAC9B,IAAI,EAAEL,OAAO,KAAK,CAAC,EAAE;UACjBU,IAAI,GAAG,IAAI;UACX;QACJ;QACAD,MAAM,GAAGT,OAAO,GAAGzB,UAAU,CAACb,KAAK,CAAC0C,OAAO,CAAC,EAAEtB,GAAG,EAAE,CAAC,EAAEkB,OAAO,EAAEA,OAAO,GAAG,CAAC,EAAEnC,OAAO,CAAC;QACpF,IAAI4C,MAAM,KAAK,CAAC,EAAE;UACdH,IAAI,IAAIG,MAAM;UACdJ,OAAO,IAAII,MAAM;UACjBT,OAAO,IAAIS,MAAM;UACjBI,UAAU,GAAGP,IAAI,GAAG,CAAC;UACrBM,YAAY,GAAGP,OAAO,GAAG,CAAC;UAC1B,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,MAAM,EAAEb,CAAC,EAAE,EAAE;YACzBlC,KAAK,CAACmD,UAAU,GAAGjB,CAAC,CAAC,GAAGd,GAAG,CAAC8B,YAAY,GAAGhB,CAAC,CAAC;UACjD;UACA,IAAII,OAAO,IAAI,CAAC,EAAE;YACdU,IAAI,GAAG,IAAI;YACX;UACJ;QACJ;QACAhD,KAAK,CAAC4C,IAAI,EAAE,CAAC,GAAG5C,KAAK,CAAC0C,OAAO,EAAE,CAAC;QAChC,IAAI,EAAEN,OAAO,KAAK,CAAC,EAAE;UACjBY,IAAI,GAAG,IAAI;UACX;QACJ;QACAH,UAAU,EAAE;MAChB,CAAC,QAAQC,MAAM,IAAInD,qBAAqB,IAAIoD,MAAM,IAAIpD,qBAAqB;MAC3E,IAAIqD,IAAI,EAAE;QACN;MACJ;MACA,IAAIH,UAAU,GAAG,CAAC,EAAE;QAChBA,UAAU,GAAG,CAAC;MAClB;MACAA,UAAU,IAAI,CAAC;IACnB;IACArB,SAAS,GAAGqB,UAAU;IACtB,IAAIrB,SAAS,GAAG,CAAC,EAAE;MACfA,SAAS,GAAG,CAAC;IACjB;IACA,IAAIc,OAAO,KAAK,CAAC,EAAE;MACfM,IAAI,IAAIR,OAAO;MACfM,OAAO,IAAIN,OAAO;MAClBe,UAAU,GAAGP,IAAI,GAAG,CAAC;MACrBM,YAAY,GAAGR,OAAO,GAAG,CAAC;MAC1B,KAAKR,CAAC,GAAGE,OAAO,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/BlC,KAAK,CAACmD,UAAU,GAAGjB,CAAC,CAAC,GAAGlC,KAAK,CAACkD,YAAY,GAAGhB,CAAC,CAAC;MACnD;MACAlC,KAAK,CAAC4C,IAAI,CAAC,GAAGxB,GAAG,CAACuB,OAAO,CAAC;IAC9B,CAAC,MACI,IAAIL,OAAO,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIW,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACDC,YAAY,GAAGN,IAAI,IAAIN,OAAO,GAAG,CAAC,CAAC;MACnC,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,EAAEJ,CAAC,EAAE,EAAE;QAC1BlC,KAAK,CAACkD,YAAY,GAAGhB,CAAC,CAAC,GAAGd,GAAG,CAACc,CAAC,CAAC;MACpC;IACJ;EACJ;EACA,OAAO;IACHH,SAAS,EAAEA,SAAS;IACpBE,cAAc,EAAEA,cAAc;IAC9BL,OAAO,EAAEA;EACb,CAAC;AACL;AACA,eAAe,SAASwB,IAAIA,CAACpD,KAAK,EAAEG,OAAO,EAAEF,EAAE,EAAEC,EAAE,EAAE;EACjD,IAAI,CAACD,EAAE,EAAE;IACLA,EAAE,GAAG,CAAC;EACV;EACA,IAAI,CAACC,EAAE,EAAE;IACLA,EAAE,GAAGF,KAAK,CAACe,MAAM;EACrB;EACA,IAAIsC,SAAS,GAAGnD,EAAE,GAAGD,EAAE;EACvB,IAAIoD,SAAS,GAAG,CAAC,EAAE;IACf;EACJ;EACA,IAAI3B,SAAS,GAAG,CAAC;EACjB,IAAI2B,SAAS,GAAG3D,iBAAiB,EAAE;IAC/BgC,SAAS,GAAG3B,gBAAgB,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,CAAC;IACpDI,mBAAmB,CAACP,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAED,EAAE,GAAGyB,SAAS,EAAEvB,OAAO,CAAC;IAC3D;EACJ;EACA,IAAImD,EAAE,GAAG/B,OAAO,CAACvB,KAAK,EAAEG,OAAO,CAAC;EAChC,IAAIoD,MAAM,GAAG3D,YAAY,CAACyD,SAAS,CAAC;EACpC,GAAG;IACC3B,SAAS,GAAG3B,gBAAgB,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,CAAC;IACpD,IAAIuB,SAAS,GAAG6B,MAAM,EAAE;MACpB,IAAIC,KAAK,GAAGH,SAAS;MACrB,IAAIG,KAAK,GAAGD,MAAM,EAAE;QAChBC,KAAK,GAAGD,MAAM;MAClB;MACAhD,mBAAmB,CAACP,KAAK,EAAEC,EAAE,EAAEA,EAAE,GAAGuD,KAAK,EAAEvD,EAAE,GAAGyB,SAAS,EAAEvB,OAAO,CAAC;MACnEuB,SAAS,GAAG8B,KAAK;IACrB;IACAF,EAAE,CAAC1B,OAAO,CAAC3B,EAAE,EAAEyB,SAAS,CAAC;IACzB4B,EAAE,CAACvB,SAAS,CAAC,CAAC;IACdsB,SAAS,IAAI3B,SAAS;IACtBzB,EAAE,IAAIyB,SAAS;EACnB,CAAC,QAAQ2B,SAAS,KAAK,CAAC;EACxBC,EAAE,CAACrB,cAAc,CAAC,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}