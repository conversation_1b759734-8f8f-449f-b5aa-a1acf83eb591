{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { hasOwn, assert, isString, retrieve2, retrieve3, defaults, each, indexOf } from 'zrender/lib/core/util.js';\nimport * as graphicUtil from '../../util/graphic.js';\nimport { setDefaultStateProxy, toggleHoverEmphasis } from '../../util/states.js';\nimport * as labelStyleHelper from '../../label/labelStyle.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { getLayoutOnAxis } from '../../layout/barGrid.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport prepareCartesian2d from '../../coord/cartesian/prepareCustom.js';\nimport prepareGeo from '../../coord/geo/prepareCustom.js';\nimport prepareSingleAxis from '../../coord/single/prepareCustom.js';\nimport preparePolar from '../../coord/polar/prepareCustom.js';\nimport prepareCalendar from '../../coord/calendar/prepareCustom.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { convertToEC4StyleForCustomSerise, isEC4CompatibleStyle, convertFromEC4CompatibleStyle, warnDeprecated } from '../../util/styleCompat.js';\nimport { throwError } from '../../util/log.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { STYLE_VISUAL_TYPE, NON_STYLE_VISUAL_PROPS, customInnerStore } from './CustomSeries.js';\nimport { applyLeaveTransition, applyUpdateTransition } from '../../animation/customGraphicTransition.js';\nimport { applyKeyframeAnimation, stopPreviousKeyframeAnimationAndRestore } from '../../animation/customGraphicKeyframeAnimation.js';\nvar EMPHASIS = 'emphasis';\nvar NORMAL = 'normal';\nvar BLUR = 'blur';\nvar SELECT = 'select';\nvar STATES = [NORMAL, EMPHASIS, BLUR, SELECT];\nvar PATH_ITEM_STYLE = {\n  normal: ['itemStyle'],\n  emphasis: [EMPHASIS, 'itemStyle'],\n  blur: [BLUR, 'itemStyle'],\n  select: [SELECT, 'itemStyle']\n};\nvar PATH_LABEL = {\n  normal: ['label'],\n  emphasis: [EMPHASIS, 'label'],\n  blur: [BLUR, 'label'],\n  select: [SELECT, 'label']\n};\nvar DEFAULT_TRANSITION = ['x', 'y'];\n// Use prefix to avoid index to be the same as el.name,\n// which will cause weird update animation.\nvar GROUP_DIFF_PREFIX = 'e\\0\\0';\nvar attachedTxInfoTmp = {\n  normal: {},\n  emphasis: {},\n  blur: {},\n  select: {}\n};\n/**\r\n * To reduce total package size of each coordinate systems, the modules `prepareCustom`\r\n * of each coordinate systems are not required by each coordinate systems directly, but\r\n * required by the module `custom`.\r\n *\r\n * prepareInfoForCustomSeries {Function}: optional\r\n *     @return {Object} {coordSys: {...}, api: {\r\n *         coord: function (data, clamp) {}, // return point in global.\r\n *         size: function (dataSize, dataItem) {} // return size of each axis in coordSys.\r\n *     }}\r\n */\nvar prepareCustoms = {\n  cartesian2d: prepareCartesian2d,\n  geo: prepareGeo,\n  single: prepareSingleAxis,\n  polar: preparePolar,\n  calendar: prepareCalendar\n};\nfunction isPath(el) {\n  return el instanceof graphicUtil.Path;\n}\nfunction isDisplayable(el) {\n  return el instanceof Displayable;\n}\nfunction copyElement(sourceEl, targetEl) {\n  targetEl.copyTransform(sourceEl);\n  if (isDisplayable(targetEl) && isDisplayable(sourceEl)) {\n    targetEl.setStyle(sourceEl.style);\n    targetEl.z = sourceEl.z;\n    targetEl.z2 = sourceEl.z2;\n    targetEl.zlevel = sourceEl.zlevel;\n    targetEl.invisible = sourceEl.invisible;\n    targetEl.ignore = sourceEl.ignore;\n    if (isPath(targetEl) && isPath(sourceEl)) {\n      targetEl.setShape(sourceEl.shape);\n    }\n  }\n}\nvar CustomChartView = /** @class */function (_super) {\n  __extends(CustomChartView, _super);\n  function CustomChartView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomChartView.type;\n    return _this;\n  }\n  CustomChartView.prototype.render = function (customSeries, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var oldData = this._data;\n    var data = customSeries.getData();\n    var group = this.group;\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    if (!oldData) {\n      // Previous render is incremental render or first render.\n      // Needs remove the incremental rendered elements.\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      createOrUpdateItem(api, null, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && applyLeaveTransition(el, customInnerStore(el).option, customSeries);\n    }).update(function (newIdx, oldIdx) {\n      var oldEl = oldData.getItemGraphicEl(oldIdx);\n      createOrUpdateItem(api, oldEl, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).execute();\n    // Do clipping\n    var clipPath = customSeries.get('clip', true) ? createClipPath(customSeries.coordinateSystem, false, customSeries) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n  };\n  CustomChartView.prototype.incrementalPrepareRender = function (customSeries, ecModel, api) {\n    this.group.removeAll();\n    this._data = null;\n  };\n  CustomChartView.prototype.incrementalRender = function (params, customSeries, ecModel, api, payload) {\n    var data = customSeries.getData();\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    var progressiveEls = this._progressiveEls = [];\n    function setIncrementalAndHoverLayer(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = params.start; idx < params.end; idx++) {\n      var el = createOrUpdateItem(null, null, idx, renderItem(idx, payload), customSeries, this.group, data);\n      if (el) {\n        el.traverse(setIncrementalAndHoverLayer);\n        progressiveEls.push(el);\n      }\n    }\n  };\n  CustomChartView.prototype.eachRendered = function (cb) {\n    graphicUtil.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  CustomChartView.prototype.filterForExposedEvent = function (eventType, query, targetEl, packedEvent) {\n    var elementName = query.element;\n    if (elementName == null || targetEl.name === elementName) {\n      return true;\n    }\n    // Enable to give a name on a group made by `renderItem`, and listen\n    // events that are triggered by its descendents.\n    while ((targetEl = targetEl.__hostTarget || targetEl.parent) && targetEl !== this.group) {\n      if (targetEl.name === elementName) {\n        return true;\n      }\n    }\n    return false;\n  };\n  CustomChartView.type = 'custom';\n  return CustomChartView;\n}(ChartView);\nexport default CustomChartView;\nfunction createEl(elOption) {\n  var graphicType = elOption.type;\n  var el;\n  // Those graphic elements are not shapes. They should not be\n  // overwritten by users, so do them first.\n  if (graphicType === 'path') {\n    var shape = elOption.shape;\n    // Using pathRect brings convenience to users sacle svg path.\n    var pathRect = shape.width != null && shape.height != null ? {\n      x: shape.x || 0,\n      y: shape.y || 0,\n      width: shape.width,\n      height: shape.height\n    } : null;\n    var pathData = getPathData(shape);\n    // Path is also used for icon, so layout 'center' by default.\n    el = graphicUtil.makePath(pathData, null, pathRect, shape.layout || 'center');\n    customInnerStore(el).customPathData = pathData;\n  } else if (graphicType === 'image') {\n    el = new graphicUtil.Image({});\n    customInnerStore(el).customImagePath = elOption.style.image;\n  } else if (graphicType === 'text') {\n    el = new graphicUtil.Text({});\n    // customInnerStore(el).customText = (elOption.style as TextStyleProps).text;\n  } else if (graphicType === 'group') {\n    el = new graphicUtil.Group();\n  } else if (graphicType === 'compoundPath') {\n    throw new Error('\"compoundPath\" is not supported yet.');\n  } else {\n    var Clz = graphicUtil.getShapeClass(graphicType);\n    if (!Clz) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'graphic type \"' + graphicType + '\" can not be found.';\n      }\n      throwError(errMsg);\n    }\n    el = new Clz();\n  }\n  customInnerStore(el).customGraphicType = graphicType;\n  el.name = elOption.name;\n  // Compat ec4: the default z2 lift is 1. If changing the number,\n  // some cases probably be broken: hierarchy layout along z, like circle packing,\n  // where emphasis only intending to modify color/border rather than lift z2.\n  el.z2EmphasisLift = 1;\n  el.z2SelectLift = 1;\n  return el;\n}\nfunction updateElNormal(\n// Can be null/undefined\napi, el, dataIndex, elOption, attachedTxInfo, seriesModel, isInit) {\n  // Stop and restore before update any other attributes.\n  stopPreviousKeyframeAnimationAndRestore(el);\n  var txCfgOpt = attachedTxInfo && attachedTxInfo.normal.cfg;\n  if (txCfgOpt) {\n    // PENDING: whether use user object directly rather than clone?\n    // TODO:5.0 textConfig transition animation?\n    el.setTextConfig(txCfgOpt);\n  }\n  // Default transition ['x', 'y']\n  if (elOption && elOption.transition == null) {\n    elOption.transition = DEFAULT_TRANSITION;\n  }\n  // Do some normalization on style.\n  var styleOpt = elOption && elOption.style;\n  if (styleOpt) {\n    if (el.type === 'text') {\n      var textOptionStyle = styleOpt;\n      // Compatible with ec4: if `textFill` or `textStroke` exists use them.\n      hasOwn(textOptionStyle, 'textFill') && (textOptionStyle.fill = textOptionStyle.textFill);\n      hasOwn(textOptionStyle, 'textStroke') && (textOptionStyle.stroke = textOptionStyle.textStroke);\n    }\n    var decalPattern = void 0;\n    var decalObj = isPath(el) ? styleOpt.decal : null;\n    if (api && decalObj) {\n      decalObj.dirty = true;\n      decalPattern = createOrUpdatePatternFromDecal(decalObj, api);\n    }\n    // Always overwrite in case user specify this prop.\n    styleOpt.__decalPattern = decalPattern;\n  }\n  if (isDisplayable(el)) {\n    if (styleOpt) {\n      var decalPattern = styleOpt.__decalPattern;\n      if (decalPattern) {\n        styleOpt.decal = decalPattern;\n      }\n    }\n  }\n  applyUpdateTransition(el, elOption, seriesModel, {\n    dataIndex: dataIndex,\n    isInit: isInit,\n    clearStyle: true\n  });\n  applyKeyframeAnimation(el, elOption.keyframeAnimation, seriesModel);\n}\nfunction updateElOnState(state, el, elStateOpt, styleOpt, attachedTxInfo) {\n  var elDisplayable = el.isGroup ? null : el;\n  var txCfgOpt = attachedTxInfo && attachedTxInfo[state].cfg;\n  // PENDING:5.0 support customize scale change and transition animation?\n  if (elDisplayable) {\n    // By default support auto lift color when hover whether `emphasis` specified.\n    var stateObj = elDisplayable.ensureState(state);\n    if (styleOpt === false) {\n      var existingEmphasisState = elDisplayable.getState(state);\n      if (existingEmphasisState) {\n        existingEmphasisState.style = null;\n      }\n    } else {\n      // style is needed to enable default emphasis.\n      stateObj.style = styleOpt || null;\n    }\n    // If `elOption.styleEmphasis` or `elOption.emphasis.style` is `false`,\n    // remove hover style.\n    // If `elOption.textConfig` or `elOption.emphasis.textConfig` is null/undefined, it does not\n    // make sense. So for simplicity, we do not ditinguish `hasOwnProperty` and null/undefined.\n    if (txCfgOpt) {\n      stateObj.textConfig = txCfgOpt;\n    }\n    setDefaultStateProxy(elDisplayable);\n  }\n}\nfunction updateZ(el, elOption, seriesModel) {\n  // Group not support textContent and not support z yet.\n  if (el.isGroup) {\n    return;\n  }\n  var elDisplayable = el;\n  var currentZ = seriesModel.currentZ;\n  var currentZLevel = seriesModel.currentZLevel;\n  // Always erase.\n  elDisplayable.z = currentZ;\n  elDisplayable.zlevel = currentZLevel;\n  // z2 must not be null/undefined, otherwise sort error may occur.\n  var optZ2 = elOption.z2;\n  optZ2 != null && (elDisplayable.z2 = optZ2 || 0);\n  for (var i = 0; i < STATES.length; i++) {\n    updateZForEachState(elDisplayable, elOption, STATES[i]);\n  }\n}\nfunction updateZForEachState(elDisplayable, elOption, state) {\n  var isNormal = state === NORMAL;\n  var elStateOpt = isNormal ? elOption : retrieveStateOption(elOption, state);\n  var optZ2 = elStateOpt ? elStateOpt.z2 : null;\n  var stateObj;\n  if (optZ2 != null) {\n    // Do not `ensureState` until required.\n    stateObj = isNormal ? elDisplayable : elDisplayable.ensureState(state);\n    stateObj.z2 = optZ2 || 0;\n  }\n}\nfunction makeRenderItem(customSeries, data, ecModel, api) {\n  var renderItem = customSeries.get('renderItem');\n  var coordSys = customSeries.coordinateSystem;\n  var prepareResult = {};\n  if (coordSys) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(renderItem, 'series.render is required.');\n      assert(coordSys.prepareCustoms || prepareCustoms[coordSys.type], 'This coordSys does not support custom series.');\n    }\n    // `coordSys.prepareCustoms` is used for external coord sys like bmap.\n    prepareResult = coordSys.prepareCustoms ? coordSys.prepareCustoms(coordSys) : prepareCustoms[coordSys.type](coordSys);\n  }\n  var userAPI = defaults({\n    getWidth: api.getWidth,\n    getHeight: api.getHeight,\n    getZr: api.getZr,\n    getDevicePixelRatio: api.getDevicePixelRatio,\n    value: value,\n    style: style,\n    ordinalRawValue: ordinalRawValue,\n    styleEmphasis: styleEmphasis,\n    visual: visual,\n    barLayout: barLayout,\n    currentSeriesIndices: currentSeriesIndices,\n    font: font\n  }, prepareResult.api || {});\n  var userParams = {\n    // The life cycle of context: current round of rendering.\n    // The global life cycle is probably not necessary, because\n    // user can store global status by themselves.\n    context: {},\n    seriesId: customSeries.id,\n    seriesName: customSeries.name,\n    seriesIndex: customSeries.seriesIndex,\n    coordSys: prepareResult.coordSys,\n    dataInsideLength: data.count(),\n    encode: wrapEncodeDef(customSeries.getData())\n  };\n  // If someday intending to refactor them to a class, should consider do not\n  // break change: currently these attribute member are encapsulated in a closure\n  // so that do not need to force user to call these method with a scope.\n  // Do not support call `api` asynchronously without dataIndexInside input.\n  var currDataIndexInside;\n  var currItemModel;\n  var currItemStyleModels = {};\n  var currLabelModels = {};\n  var seriesItemStyleModels = {};\n  var seriesLabelModels = {};\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    seriesItemStyleModels[stateName] = customSeries.getModel(PATH_ITEM_STYLE[stateName]);\n    seriesLabelModels[stateName] = customSeries.getModel(PATH_LABEL[stateName]);\n  }\n  function getItemModel(dataIndexInside) {\n    return dataIndexInside === currDataIndexInside ? currItemModel || (currItemModel = data.getItemModel(dataIndexInside)) : data.getItemModel(dataIndexInside);\n  }\n  function getItemStyleModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesItemStyleModels[state] : dataIndexInside === currDataIndexInside ? currItemStyleModels[state] || (currItemStyleModels[state] = getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state])) : getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state]);\n  }\n  function getLabelModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesLabelModels[state] : dataIndexInside === currDataIndexInside ? currLabelModels[state] || (currLabelModels[state] = getItemModel(dataIndexInside).getModel(PATH_LABEL[state])) : getItemModel(dataIndexInside).getModel(PATH_LABEL[state]);\n  }\n  return function (dataIndexInside, payload) {\n    currDataIndexInside = dataIndexInside;\n    currItemModel = null;\n    currItemStyleModels = {};\n    currLabelModels = {};\n    return renderItem && renderItem(defaults({\n      dataIndexInside: dataIndexInside,\n      dataIndex: data.getRawIndex(dataIndexInside),\n      // Can be used for optimization when zoom or roam.\n      actionType: payload ? payload.type : null\n    }, userParams), userAPI);\n  };\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function value(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    return data.getStore().get(data.getDimensionIndex(dim || 0), dataIndexInside);\n  }\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function ordinalRawValue(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    dim = dim || 0;\n    var dimInfo = data.getDimensionInfo(dim);\n    if (!dimInfo) {\n      var dimIndex = data.getDimensionIndex(dim);\n      return dimIndex >= 0 ? data.getStore().get(dimIndex, dataIndexInside) : undefined;\n    }\n    var val = data.get(dimInfo.name, dataIndexInside);\n    var ordinalMeta = dimInfo && dimInfo.ordinalMeta;\n    return ordinalMeta ? ordinalMeta.categories[val] : val;\n  }\n  /**\r\n   * @deprecated The original intention of `api.style` is enable to set itemStyle\r\n   * like other series. But it is not necessary and not easy to give a strict definition\r\n   * of what it returns. And since echarts5 it needs to be make compat work. So\r\n   * deprecates it since echarts5.\r\n   *\r\n   * By default, `visual` is applied to style (to support visualMap).\r\n   * `visual.color` is applied at `fill`. If user want apply visual.color on `stroke`,\r\n   * it can be implemented as:\r\n   * `api.style({stroke: api.visual('color'), fill: null})`;\r\n   *\r\n   * [Compat]: since ec5, RectText has been separated from its hosts el.\r\n   * so `api.style()` will only return the style from `itemStyle` but not handle `label`\r\n   * any more. But `series.label` config is never published in doc.\r\n   * We still compat it in `api.style()`. But not encourage to use it and will still not\r\n   * to pulish it to doc.\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function style(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.style', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var style = data.getItemVisual(dataIndexInside, 'style');\n    var visualColor = style && style.fill;\n    var opacity = style && style.opacity;\n    var itemStyle = getItemStyleModel(dataIndexInside, NORMAL).getItemStyle();\n    visualColor != null && (itemStyle.fill = visualColor);\n    opacity != null && (itemStyle.opacity = opacity);\n    var opt = {\n      inheritColor: isString(visualColor) ? visualColor : '#000'\n    };\n    var labelModel = getLabelModel(dataIndexInside, NORMAL);\n    // Now that the feature of \"auto adjust text fill/stroke\" has been migrated to zrender\n    // since ec5, we should set `isAttached` as `false` here and make compat in\n    // `convertToEC4StyleForCustomSerise`.\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, opt, false, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve2(customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, opt, false);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  /**\r\n   * @deprecated The reason see `api.style()`\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function styleEmphasis(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.styleEmphasis', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var itemStyle = getItemStyleModel(dataIndexInside, EMPHASIS).getItemStyle();\n    var labelModel = getLabelModel(dataIndexInside, EMPHASIS);\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, null, true, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve3(customSeries.getFormattedLabel(dataIndexInside, EMPHASIS), customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, null, true);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  function applyUserPropsAfter(itemStyle, extra) {\n    for (var key in extra) {\n      if (hasOwn(extra, key)) {\n        itemStyle[key] = extra[key];\n      }\n    }\n  }\n  function preFetchFromExtra(extra, itemStyle) {\n    // A trick to retrieve those props firstly, which are used to\n    // apply auto inside fill/stroke in `convertToEC4StyleForCustomSerise`.\n    // (It's not reasonable but only for a degree of compat)\n    if (extra) {\n      extra.textFill && (itemStyle.textFill = extra.textFill);\n      extra.textPosition && (itemStyle.textPosition = extra.textPosition);\n    }\n  }\n  /**\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function visual(visualType, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    if (hasOwn(STYLE_VISUAL_TYPE, visualType)) {\n      var style_1 = data.getItemVisual(dataIndexInside, 'style');\n      return style_1 ? style_1[STYLE_VISUAL_TYPE[visualType]] : null;\n    }\n    // Only support these visuals. Other visual might be inner tricky\n    // for performance (like `style`), do not expose to users.\n    if (hasOwn(NON_STYLE_VISUAL_PROPS, visualType)) {\n      return data.getItemVisual(dataIndexInside, visualType);\n    }\n  }\n  /**\r\n   * @public\r\n   * @return If not support, return undefined.\r\n   */\n  function barLayout(opt) {\n    if (coordSys.type === 'cartesian2d') {\n      var baseAxis = coordSys.getBaseAxis();\n      return getLayoutOnAxis(defaults({\n        axis: baseAxis\n      }, opt));\n    }\n  }\n  /**\r\n   * @public\r\n   */\n  function currentSeriesIndices() {\n    return ecModel.getCurrentSeriesIndices();\n  }\n  /**\r\n   * @public\r\n   * @return font string\r\n   */\n  function font(opt) {\n    return labelStyleHelper.getFont(opt, ecModel);\n  }\n}\nfunction wrapEncodeDef(data) {\n  var encodeDef = {};\n  each(data.dimensions, function (dimName) {\n    var dimInfo = data.getDimensionInfo(dimName);\n    if (!dimInfo.isExtraCoord) {\n      var coordDim = dimInfo.coordDim;\n      var dataDims = encodeDef[coordDim] = encodeDef[coordDim] || [];\n      dataDims[dimInfo.coordDimIndex] = data.getDimensionIndex(dimName);\n    }\n  });\n  return encodeDef;\n}\nfunction createOrUpdateItem(api, existsEl, dataIndex, elOption, seriesModel, group, data) {\n  // [Rule]\n  // If `renderItem` returns `null`/`undefined`/`false`, remove the previous el if existing.\n  //     (It seems that violate the \"merge\" principle, but most of users probably intuitively\n  //     regard \"return;\" as \"show nothing element whatever\", so make a exception to meet the\n  //     most cases.)\n  // The rule or \"merge\" see [STRATEGY_MERGE].\n  // If `elOption` is `null`/`undefined`/`false` (when `renderItem` returns nothing).\n  if (!elOption) {\n    group.remove(existsEl);\n    return;\n  }\n  var el = doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group);\n  el && data.setItemGraphicEl(dataIndex, el);\n  el && toggleHoverEmphasis(el, elOption.focus, elOption.blurScope, elOption.emphasisDisabled);\n  return el;\n}\nfunction doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(elOption, 'should not have an null/undefined element setting');\n  }\n  var toBeReplacedIdx = -1;\n  var oldEl = existsEl;\n  if (existsEl && doesElNeedRecreate(existsEl, elOption, seriesModel)\n  // || (\n  //     // PENDING: even in one-to-one mapping case, if el is marked as morph,\n  //     // do not sure whether the el will be mapped to another el with different\n  //     // hierarchy in Group tree. So always recreate el rather than reuse the el.\n  //     morphHelper && morphHelper.isOneToOneFrom(el)\n  // )\n  ) {\n    // Should keep at the original index, otherwise \"merge by index\" will be incorrect.\n    toBeReplacedIdx = indexOf(group.childrenRef(), existsEl);\n    existsEl = null;\n  }\n  var isInit = !existsEl;\n  var el = existsEl;\n  if (!el) {\n    el = createEl(elOption);\n    if (oldEl) {\n      copyElement(oldEl, el);\n    }\n  } else {\n    // FIMXE:NEXT unified clearState?\n    // If in some case the performance issue arised, consider\n    // do not clearState but update cached normal state directly.\n    el.clearStates();\n  }\n  // Need to set morph: false explictly to disable automatically morphing.\n  if (elOption.morph === false) {\n    el.disableMorphing = true;\n  } else if (el.disableMorphing) {\n    el.disableMorphing = false;\n  }\n  attachedTxInfoTmp.normal.cfg = attachedTxInfoTmp.normal.conOpt = attachedTxInfoTmp.emphasis.cfg = attachedTxInfoTmp.emphasis.conOpt = attachedTxInfoTmp.blur.cfg = attachedTxInfoTmp.blur.conOpt = attachedTxInfoTmp.select.cfg = attachedTxInfoTmp.select.conOpt = null;\n  attachedTxInfoTmp.isLegacy = false;\n  doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfoTmp);\n  doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit);\n  updateElNormal(api, el, dataIndex, elOption, attachedTxInfoTmp, seriesModel, isInit);\n  // `elOption.info` enables user to mount some info on\n  // elements and use them in event handlers.\n  // Update them only when user specified, otherwise, remain.\n  hasOwn(elOption, 'info') && (customInnerStore(el).info = elOption.info);\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    if (stateName !== NORMAL) {\n      var otherStateOpt = retrieveStateOption(elOption, stateName);\n      var otherStyleOpt = retrieveStyleOptionOnState(elOption, otherStateOpt, stateName);\n      updateElOnState(stateName, el, otherStateOpt, otherStyleOpt, attachedTxInfoTmp);\n    }\n  }\n  updateZ(el, elOption, seriesModel);\n  if (elOption.type === 'group') {\n    mergeChildren(api, el, dataIndex, elOption, seriesModel);\n  }\n  if (toBeReplacedIdx >= 0) {\n    group.replaceAt(el, toBeReplacedIdx);\n  } else {\n    group.add(el);\n  }\n  return el;\n}\n// `el` must not be null/undefined.\nfunction doesElNeedRecreate(el, elOption, seriesModel) {\n  var elInner = customInnerStore(el);\n  var elOptionType = elOption.type;\n  var elOptionShape = elOption.shape;\n  var elOptionStyle = elOption.style;\n  return (\n    // Always create new if universal transition is enabled.\n    // Because we do transition after render. It needs to know what old element is. Replacement will loose it.\n    seriesModel.isUniversalTransitionEnabled()\n    // If `elOptionType` is `null`, follow the merge principle.\n    || elOptionType != null && elOptionType !== elInner.customGraphicType || elOptionType === 'path' && hasOwnPathData(elOptionShape) && getPathData(elOptionShape) !== elInner.customPathData || elOptionType === 'image' && hasOwn(elOptionStyle, 'image') && elOptionStyle.image !== elInner.customImagePath\n    // // FIXME test and remove this restriction?\n    // || (elOptionType === 'text'\n    //     && hasOwn(elOptionStyle, 'text')\n    //     && (elOptionStyle as TextStyleProps).text !== elInner.customText\n    // )\n  );\n}\nfunction doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit) {\n  // Based on the \"merge\" principle, if no clipPath provided,\n  // do nothing. The exists clip will be totally removed only if\n  // `el.clipPath` is `false`. Otherwise it will be merged/replaced.\n  var clipPathOpt = elOption.clipPath;\n  if (clipPathOpt === false) {\n    if (el && el.getClipPath()) {\n      el.removeClipPath();\n    }\n  } else if (clipPathOpt) {\n    var clipPath = el.getClipPath();\n    if (clipPath && doesElNeedRecreate(clipPath, clipPathOpt, seriesModel)) {\n      clipPath = null;\n    }\n    if (!clipPath) {\n      clipPath = createEl(clipPathOpt);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(isPath(clipPath), 'Only any type of `path` can be used in `clipPath`, rather than ' + clipPath.type + '.');\n      }\n      el.setClipPath(clipPath);\n    }\n    updateElNormal(null, clipPath, dataIndex, clipPathOpt, null, seriesModel, isInit);\n  }\n  // If not define `clipPath` in option, do nothing unnecessary.\n}\nfunction doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfo) {\n  // Group does not support textContent temporarily until necessary.\n  if (el.isGroup) {\n    return;\n  }\n  // Normal must be called before emphasis, for `isLegacy` detection.\n  processTxInfo(elOption, null, attachedTxInfo);\n  processTxInfo(elOption, EMPHASIS, attachedTxInfo);\n  // If `elOption.textConfig` or `elOption.textContent` is null/undefined, it does not make sense.\n  // So for simplicity, if \"elOption hasOwnProperty of them but be null/undefined\", we do not\n  // trade them as set to null to el.\n  // Especially:\n  // `elOption.textContent: false` means remove textContent.\n  // `elOption.textContent.emphasis.style: false` means remove the style from emphasis state.\n  var txConOptNormal = attachedTxInfo.normal.conOpt;\n  var txConOptEmphasis = attachedTxInfo.emphasis.conOpt;\n  var txConOptBlur = attachedTxInfo.blur.conOpt;\n  var txConOptSelect = attachedTxInfo.select.conOpt;\n  if (txConOptNormal != null || txConOptEmphasis != null || txConOptSelect != null || txConOptBlur != null) {\n    var textContent = el.getTextContent();\n    if (txConOptNormal === false) {\n      textContent && el.removeTextContent();\n    } else {\n      txConOptNormal = attachedTxInfo.normal.conOpt = txConOptNormal || {\n        type: 'text'\n      };\n      if (!textContent) {\n        textContent = createEl(txConOptNormal);\n        el.setTextContent(textContent);\n      } else {\n        // If in some case the performance issue arised, consider\n        // do not clearState but update cached normal state directly.\n        textContent.clearStates();\n      }\n      updateElNormal(null, textContent, dataIndex, txConOptNormal, null, seriesModel, isInit);\n      var txConStlOptNormal = txConOptNormal && txConOptNormal.style;\n      for (var i = 0; i < STATES.length; i++) {\n        var stateName = STATES[i];\n        if (stateName !== NORMAL) {\n          var txConOptOtherState = attachedTxInfo[stateName].conOpt;\n          updateElOnState(stateName, textContent, txConOptOtherState, retrieveStyleOptionOnState(txConOptNormal, txConOptOtherState, stateName), null);\n        }\n      }\n      txConStlOptNormal ? textContent.dirty() : textContent.markRedraw();\n    }\n  }\n}\nfunction processTxInfo(elOption, state, attachedTxInfo) {\n  var stateOpt = !state ? elOption : retrieveStateOption(elOption, state);\n  var styleOpt = !state ? elOption.style : retrieveStyleOptionOnState(elOption, stateOpt, EMPHASIS);\n  var elType = elOption.type;\n  var txCfg = stateOpt ? stateOpt.textConfig : null;\n  var txConOptNormal = elOption.textContent;\n  var txConOpt = !txConOptNormal ? null : !state ? txConOptNormal : retrieveStateOption(txConOptNormal, state);\n  if (styleOpt && (\n  // Because emphasis style has little info to detect legacy,\n  // if normal is legacy, emphasis is trade as legacy.\n  attachedTxInfo.isLegacy || isEC4CompatibleStyle(styleOpt, elType, !!txCfg, !!txConOpt))) {\n    attachedTxInfo.isLegacy = true;\n    var convertResult = convertFromEC4CompatibleStyle(styleOpt, elType, !state);\n    // Explicitly specified `textConfig` and `textContent` has higher priority than\n    // the ones generated by legacy style. Otherwise if users use them and `api.style`\n    // at the same time, they not both work and hardly to known why.\n    if (!txCfg && convertResult.textConfig) {\n      txCfg = convertResult.textConfig;\n    }\n    if (!txConOpt && convertResult.textContent) {\n      txConOpt = convertResult.textContent;\n    }\n  }\n  if (!state && txConOpt) {\n    var txConOptNormal_1 = txConOpt;\n    // `textContent: {type: 'text'}`, the \"type\" is easy to be missing. So we tolerate it.\n    !txConOptNormal_1.type && (txConOptNormal_1.type = 'text');\n    if (process.env.NODE_ENV !== 'production') {\n      // Do not tolerate incorrcet type for forward compat.\n      assert(txConOptNormal_1.type === 'text', 'textContent.type must be \"text\"');\n    }\n  }\n  var info = !state ? attachedTxInfo.normal : attachedTxInfo[state];\n  info.cfg = txCfg;\n  info.conOpt = txConOpt;\n}\nfunction retrieveStateOption(elOption, state) {\n  return !state ? elOption : elOption ? elOption[state] : null;\n}\nfunction retrieveStyleOptionOnState(stateOptionNormal, stateOption, state) {\n  var style = stateOption && stateOption.style;\n  if (style == null && state === EMPHASIS && stateOptionNormal) {\n    style = stateOptionNormal.styleEmphasis;\n  }\n  return style;\n}\n// Usage:\n// (1) By default, `elOption.$mergeChildren` is `'byIndex'`, which indicates\n//     that the existing children will not be removed, and enables the feature\n//     that update some of the props of some of the children simply by construct\n//     the returned children of `renderItem` like:\n//     `var children = group.children = []; children[3] = {opacity: 0.5};`\n// (2) If `elOption.$mergeChildren` is `'byName'`, add/update/remove children\n//     by child.name. But that might be lower performance.\n// (3) If `elOption.$mergeChildren` is `false`, the existing children will be\n//     replaced totally.\n// (4) If `!elOption.children`, following the \"merge\" principle, nothing will\n//     happen.\n// (5) If `elOption.$mergeChildren` is not `false` neither `'byName'` and the\n//     `el` is a group, and if any of the new child is null, it means to remove\n//     the element at the same index, if exists. On the other hand, if the new\n//     child is and empty object `{}`, it means to keep the element not changed.\n//\n// For implementation simpleness, do not provide a direct way to remove single\n// child (otherwise the total indices of the children array have to be modified).\n// User can remove a single child by setting its `ignore` to `true`.\nfunction mergeChildren(api, el, dataIndex, elOption, seriesModel) {\n  var newChildren = elOption.children;\n  var newLen = newChildren ? newChildren.length : 0;\n  var mergeChildren = elOption.$mergeChildren;\n  // `diffChildrenByName` has been deprecated.\n  var byName = mergeChildren === 'byName' || elOption.diffChildrenByName;\n  var notMerge = mergeChildren === false;\n  // For better performance on roam update, only enter if necessary.\n  if (!newLen && !byName && !notMerge) {\n    return;\n  }\n  if (byName) {\n    diffGroupChildren({\n      api: api,\n      oldChildren: el.children() || [],\n      newChildren: newChildren || [],\n      dataIndex: dataIndex,\n      seriesModel: seriesModel,\n      group: el\n    });\n    return;\n  }\n  notMerge && el.removeAll();\n  // Mapping children of a group simply by index, which\n  // might be better performance.\n  var index = 0;\n  for (; index < newLen; index++) {\n    var newChild = newChildren[index];\n    var oldChild = el.childAt(index);\n    if (newChild) {\n      if (newChild.ignore == null) {\n        // The old child is set to be ignored if null (see comments\n        // below). So we need to set ignore to be false back.\n        newChild.ignore = false;\n      }\n      doCreateOrUpdateEl(api, oldChild, dataIndex, newChild, seriesModel, el);\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(oldChild, 'renderItem should not return a group containing elements' + ' as null/undefined/{} if they do not exist before.');\n      }\n      // If the new element option is null, it means to remove the old\n      // element. But we cannot really remove the element from the group\n      // directly, because the element order may not be stable when this\n      // element is added back. So we set the element to be ignored.\n      oldChild.ignore = true;\n    }\n  }\n  for (var i = el.childCount() - 1; i >= index; i--) {\n    var child = el.childAt(i);\n    removeChildFromGroup(el, child, seriesModel);\n  }\n}\nfunction removeChildFromGroup(group, child, seriesModel) {\n  // Do not support leave elements that are not mentioned in the latest\n  // `renderItem` return. Otherwise users may not have a clear and simple\n  // concept that how to control all of the elements.\n  child && applyLeaveTransition(child, customInnerStore(group).option, seriesModel);\n}\nfunction diffGroupChildren(context) {\n  new DataDiffer(context.oldChildren, context.newChildren, getKey, getKey, context).add(processAddUpdate).update(processAddUpdate).remove(processRemove).execute();\n}\nfunction getKey(item, idx) {\n  var name = item && item.name;\n  return name != null ? name : GROUP_DIFF_PREFIX + idx;\n}\nfunction processAddUpdate(newIndex, oldIndex) {\n  var context = this.context;\n  var childOption = newIndex != null ? context.newChildren[newIndex] : null;\n  var child = oldIndex != null ? context.oldChildren[oldIndex] : null;\n  doCreateOrUpdateEl(context.api, child, context.dataIndex, childOption, context.seriesModel, context.group);\n}\nfunction processRemove(oldIndex) {\n  var context = this.context;\n  var child = context.oldChildren[oldIndex];\n  child && applyLeaveTransition(child, customInnerStore(child).option, context.seriesModel);\n}\n/**\r\n * @return SVG Path data.\r\n */\nfunction getPathData(shape) {\n  // \"d\" follows the SVG convention.\n  return shape && (shape.pathData || shape.d);\n}\nfunction hasOwnPathData(shape) {\n  return shape && (hasOwn(shape, 'pathData') || hasOwn(shape, 'd'));\n}", "map": {"version": 3, "names": ["__extends", "hasOwn", "assert", "isString", "retrieve2", "retrieve3", "defaults", "each", "indexOf", "graphicUtil", "setDefaultStateProxy", "toggleHoverEmphasis", "labelStyleHelper", "getDefaultLabel", "getLayoutOnAxis", "<PERSON><PERSON><PERSON><PERSON>", "ChartView", "createClipPath", "prepareCartesian2d", "prepareGeo", "prepareSingleAxis", "preparePolar", "prepareCalendar", "Displayable", "convertToEC4StyleForCustomSerise", "isEC4CompatibleStyle", "convertFromEC4CompatibleStyle", "warnDeprecated", "throwError", "createOrUpdatePatternFromDecal", "STYLE_VISUAL_TYPE", "NON_STYLE_VISUAL_PROPS", "customInnerStore", "applyLeaveTransition", "applyUpdateTransition", "applyKeyframeAnimation", "stopPreviousKeyframeAnimationAndRestore", "EMPHASIS", "NORMAL", "BLUR", "SELECT", "STATES", "PATH_ITEM_STYLE", "normal", "emphasis", "blur", "select", "PATH_LABEL", "DEFAULT_TRANSITION", "GROUP_DIFF_PREFIX", "attachedTxInfoTmp", "prepareCustoms", "cartesian2d", "geo", "single", "polar", "calendar", "isPath", "el", "Path", "isDisplayable", "copyElement", "sourceEl", "targetEl", "copyTransform", "setStyle", "style", "z", "z2", "zlevel", "invisible", "ignore", "setShape", "shape", "CustomChartView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "customSeries", "ecModel", "api", "payload", "_progressiveEls", "oldData", "_data", "data", "getData", "group", "renderItem", "makeRenderItem", "removeAll", "diff", "add", "newIdx", "createOrUpdateItem", "remove", "oldIdx", "getItemGraphicEl", "option", "update", "oldEl", "execute", "clipPath", "get", "coordinateSystem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeClip<PERSON>ath", "incrementalPrepareRender", "incrementalRender", "params", "progressiveEls", "setIncrementalAndHoverLayer", "isGroup", "incremental", "ensureState", "hoverLayer", "idx", "start", "end", "traverse", "push", "eachRendered", "cb", "traverseElements", "filterForExposedEvent", "eventType", "query", "packedEvent", "elementName", "element", "name", "__host<PERSON><PERSON>get", "parent", "createEl", "elOption", "graphicType", "pathRect", "width", "height", "x", "y", "pathData", "getPathData", "<PERSON><PERSON><PERSON>", "layout", "customPathData", "Image", "customImagePath", "image", "Text", "Group", "Error", "Clz", "getShapeClass", "errMsg", "process", "env", "NODE_ENV", "customGraphicType", "z2EmphasisLift", "z2SelectLift", "updateElNormal", "dataIndex", "attachedTxInfo", "seriesModel", "isInit", "txCfgOpt", "cfg", "setTextConfig", "transition", "styleOpt", "textOptionStyle", "fill", "textFill", "stroke", "textStroke", "decalPattern", "decalObj", "decal", "dirty", "__decal<PERSON>attern", "clearStyle", "keyframeAnimation", "updateElOnState", "state", "elStateOpt", "elDisplayable", "stateObj", "existingEmphasisState", "getState", "textConfig", "updateZ", "currentZ", "currentZLevel", "optZ2", "i", "length", "updateZForEachState", "isNormal", "retrieveStateOption", "coordSys", "prepareResult", "userAPI", "getWidth", "getHeight", "getZr", "getDevicePixelRatio", "value", "ordinalRawValue", "styleEmphasis", "visual", "barLayout", "currentSeriesIndices", "font", "userParams", "context", "seriesId", "id", "seriesName", "seriesIndex", "dataInsideLength", "count", "encode", "wrapEncodeDef", "currDataIndexInside", "currItemModel", "currItemStyleModels", "currLabelModels", "seriesItemStyleModels", "seriesLabelModels", "stateName", "getModel", "getItemModel", "dataIndexInside", "getItemStyleModel", "hasItemOption", "getLabelModel", "getRawIndex", "actionType", "dim", "getStore", "getDimensionIndex", "dimInfo", "getDimensionInfo", "dimIndex", "undefined", "val", "ordinalMeta", "categories", "userProps", "getItemVisual", "visualColor", "opacity", "itemStyle", "getItemStyle", "opt", "inheritColor", "labelModel", "textStyle", "createTextStyle", "text", "getShallow", "getFormattedLabel", "createTextConfig", "preFetchFromExtra", "applyUserPropsAfter", "legacy", "extra", "key", "textPosition", "visualType", "style_1", "baseAxis", "getBaseAxis", "axis", "getCurrentSeriesIndices", "getFont", "encodeDef", "dimensions", "dimName", "isExtraCoord", "coordDim", "dataDims", "coordDimIndex", "existsEl", "doCreateOrUpdateEl", "setItemGraphicEl", "focus", "blurScope", "emphasisDisabled", "toBeReplacedIdx", "doesElNeedRecreate", "childrenRef", "clearStates", "morph", "disableMorphing", "conOpt", "isLegacy", "doCreateOrUpdateAttachedTx", "doCreateOrUpdateClipPath", "info", "otherStateOpt", "otherStyleOpt", "retrieveStyleOptionOnState", "mergeChildren", "replaceAt", "elInner", "elOptionType", "elOptionShape", "elOptionStyle", "isUniversalTransitionEnabled", "hasOwnPathData", "clipPathOpt", "getClipPath", "processTxInfo", "txConOptNormal", "txConOptEmphasis", "txConOptBlur", "txConOptSelect", "textContent", "getTextContent", "removeTextContent", "setTextContent", "txConStlOptNormal", "txConOptOtherState", "mark<PERSON><PERSON><PERSON>", "stateOpt", "elType", "txCfg", "txConOpt", "convertResult", "txConOptNormal_1", "stateOptionNormal", "stateOption", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "newLen", "$mergeChildren", "by<PERSON><PERSON>", "diffChildrenByName", "notMerge", "diffGroupChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "childAt", "childCount", "child", "removeChildFromGroup", "<PERSON><PERSON><PERSON>", "processAddUpdate", "processRemove", "item", "newIndex", "oldIndex", "childOption", "d"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/custom/CustomView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { hasOwn, assert, isString, retrieve2, retrieve3, defaults, each, indexOf } from 'zrender/lib/core/util.js';\nimport * as graphicUtil from '../../util/graphic.js';\nimport { setDefaultStateProxy, toggleHoverEmphasis } from '../../util/states.js';\nimport * as labelStyleHelper from '../../label/labelStyle.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { getLayoutOnAxis } from '../../layout/barGrid.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport prepareCartesian2d from '../../coord/cartesian/prepareCustom.js';\nimport prepareGeo from '../../coord/geo/prepareCustom.js';\nimport prepareSingleAxis from '../../coord/single/prepareCustom.js';\nimport preparePolar from '../../coord/polar/prepareCustom.js';\nimport prepareCalendar from '../../coord/calendar/prepareCustom.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { convertToEC4StyleForCustomSerise, isEC4CompatibleStyle, convertFromEC4CompatibleStyle, warnDeprecated } from '../../util/styleCompat.js';\nimport { throwError } from '../../util/log.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { STYLE_VISUAL_TYPE, NON_STYLE_VISUAL_PROPS, customInnerStore } from './CustomSeries.js';\nimport { applyLeaveTransition, applyUpdateTransition } from '../../animation/customGraphicTransition.js';\nimport { applyKeyframeAnimation, stopPreviousKeyframeAnimationAndRestore } from '../../animation/customGraphicKeyframeAnimation.js';\nvar EMPHASIS = 'emphasis';\nvar NORMAL = 'normal';\nvar BLUR = 'blur';\nvar SELECT = 'select';\nvar STATES = [NORMAL, EMPHASIS, BLUR, SELECT];\nvar PATH_ITEM_STYLE = {\n  normal: ['itemStyle'],\n  emphasis: [EMPHASIS, 'itemStyle'],\n  blur: [BLUR, 'itemStyle'],\n  select: [SELECT, 'itemStyle']\n};\nvar PATH_LABEL = {\n  normal: ['label'],\n  emphasis: [EMPHASIS, 'label'],\n  blur: [BLUR, 'label'],\n  select: [SELECT, 'label']\n};\nvar DEFAULT_TRANSITION = ['x', 'y'];\n// Use prefix to avoid index to be the same as el.name,\n// which will cause weird update animation.\nvar GROUP_DIFF_PREFIX = 'e\\0\\0';\nvar attachedTxInfoTmp = {\n  normal: {},\n  emphasis: {},\n  blur: {},\n  select: {}\n};\n/**\r\n * To reduce total package size of each coordinate systems, the modules `prepareCustom`\r\n * of each coordinate systems are not required by each coordinate systems directly, but\r\n * required by the module `custom`.\r\n *\r\n * prepareInfoForCustomSeries {Function}: optional\r\n *     @return {Object} {coordSys: {...}, api: {\r\n *         coord: function (data, clamp) {}, // return point in global.\r\n *         size: function (dataSize, dataItem) {} // return size of each axis in coordSys.\r\n *     }}\r\n */\nvar prepareCustoms = {\n  cartesian2d: prepareCartesian2d,\n  geo: prepareGeo,\n  single: prepareSingleAxis,\n  polar: preparePolar,\n  calendar: prepareCalendar\n};\nfunction isPath(el) {\n  return el instanceof graphicUtil.Path;\n}\nfunction isDisplayable(el) {\n  return el instanceof Displayable;\n}\nfunction copyElement(sourceEl, targetEl) {\n  targetEl.copyTransform(sourceEl);\n  if (isDisplayable(targetEl) && isDisplayable(sourceEl)) {\n    targetEl.setStyle(sourceEl.style);\n    targetEl.z = sourceEl.z;\n    targetEl.z2 = sourceEl.z2;\n    targetEl.zlevel = sourceEl.zlevel;\n    targetEl.invisible = sourceEl.invisible;\n    targetEl.ignore = sourceEl.ignore;\n    if (isPath(targetEl) && isPath(sourceEl)) {\n      targetEl.setShape(sourceEl.shape);\n    }\n  }\n}\nvar CustomChartView = /** @class */function (_super) {\n  __extends(CustomChartView, _super);\n  function CustomChartView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomChartView.type;\n    return _this;\n  }\n  CustomChartView.prototype.render = function (customSeries, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var oldData = this._data;\n    var data = customSeries.getData();\n    var group = this.group;\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    if (!oldData) {\n      // Previous render is incremental render or first render.\n      // Needs remove the incremental rendered elements.\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      createOrUpdateItem(api, null, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && applyLeaveTransition(el, customInnerStore(el).option, customSeries);\n    }).update(function (newIdx, oldIdx) {\n      var oldEl = oldData.getItemGraphicEl(oldIdx);\n      createOrUpdateItem(api, oldEl, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).execute();\n    // Do clipping\n    var clipPath = customSeries.get('clip', true) ? createClipPath(customSeries.coordinateSystem, false, customSeries) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n  };\n  CustomChartView.prototype.incrementalPrepareRender = function (customSeries, ecModel, api) {\n    this.group.removeAll();\n    this._data = null;\n  };\n  CustomChartView.prototype.incrementalRender = function (params, customSeries, ecModel, api, payload) {\n    var data = customSeries.getData();\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    var progressiveEls = this._progressiveEls = [];\n    function setIncrementalAndHoverLayer(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = params.start; idx < params.end; idx++) {\n      var el = createOrUpdateItem(null, null, idx, renderItem(idx, payload), customSeries, this.group, data);\n      if (el) {\n        el.traverse(setIncrementalAndHoverLayer);\n        progressiveEls.push(el);\n      }\n    }\n  };\n  CustomChartView.prototype.eachRendered = function (cb) {\n    graphicUtil.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  CustomChartView.prototype.filterForExposedEvent = function (eventType, query, targetEl, packedEvent) {\n    var elementName = query.element;\n    if (elementName == null || targetEl.name === elementName) {\n      return true;\n    }\n    // Enable to give a name on a group made by `renderItem`, and listen\n    // events that are triggered by its descendents.\n    while ((targetEl = targetEl.__hostTarget || targetEl.parent) && targetEl !== this.group) {\n      if (targetEl.name === elementName) {\n        return true;\n      }\n    }\n    return false;\n  };\n  CustomChartView.type = 'custom';\n  return CustomChartView;\n}(ChartView);\nexport default CustomChartView;\nfunction createEl(elOption) {\n  var graphicType = elOption.type;\n  var el;\n  // Those graphic elements are not shapes. They should not be\n  // overwritten by users, so do them first.\n  if (graphicType === 'path') {\n    var shape = elOption.shape;\n    // Using pathRect brings convenience to users sacle svg path.\n    var pathRect = shape.width != null && shape.height != null ? {\n      x: shape.x || 0,\n      y: shape.y || 0,\n      width: shape.width,\n      height: shape.height\n    } : null;\n    var pathData = getPathData(shape);\n    // Path is also used for icon, so layout 'center' by default.\n    el = graphicUtil.makePath(pathData, null, pathRect, shape.layout || 'center');\n    customInnerStore(el).customPathData = pathData;\n  } else if (graphicType === 'image') {\n    el = new graphicUtil.Image({});\n    customInnerStore(el).customImagePath = elOption.style.image;\n  } else if (graphicType === 'text') {\n    el = new graphicUtil.Text({});\n    // customInnerStore(el).customText = (elOption.style as TextStyleProps).text;\n  } else if (graphicType === 'group') {\n    el = new graphicUtil.Group();\n  } else if (graphicType === 'compoundPath') {\n    throw new Error('\"compoundPath\" is not supported yet.');\n  } else {\n    var Clz = graphicUtil.getShapeClass(graphicType);\n    if (!Clz) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'graphic type \"' + graphicType + '\" can not be found.';\n      }\n      throwError(errMsg);\n    }\n    el = new Clz();\n  }\n  customInnerStore(el).customGraphicType = graphicType;\n  el.name = elOption.name;\n  // Compat ec4: the default z2 lift is 1. If changing the number,\n  // some cases probably be broken: hierarchy layout along z, like circle packing,\n  // where emphasis only intending to modify color/border rather than lift z2.\n  el.z2EmphasisLift = 1;\n  el.z2SelectLift = 1;\n  return el;\n}\nfunction updateElNormal(\n// Can be null/undefined\napi, el, dataIndex, elOption, attachedTxInfo, seriesModel, isInit) {\n  // Stop and restore before update any other attributes.\n  stopPreviousKeyframeAnimationAndRestore(el);\n  var txCfgOpt = attachedTxInfo && attachedTxInfo.normal.cfg;\n  if (txCfgOpt) {\n    // PENDING: whether use user object directly rather than clone?\n    // TODO:5.0 textConfig transition animation?\n    el.setTextConfig(txCfgOpt);\n  }\n  // Default transition ['x', 'y']\n  if (elOption && elOption.transition == null) {\n    elOption.transition = DEFAULT_TRANSITION;\n  }\n  // Do some normalization on style.\n  var styleOpt = elOption && elOption.style;\n  if (styleOpt) {\n    if (el.type === 'text') {\n      var textOptionStyle = styleOpt;\n      // Compatible with ec4: if `textFill` or `textStroke` exists use them.\n      hasOwn(textOptionStyle, 'textFill') && (textOptionStyle.fill = textOptionStyle.textFill);\n      hasOwn(textOptionStyle, 'textStroke') && (textOptionStyle.stroke = textOptionStyle.textStroke);\n    }\n    var decalPattern = void 0;\n    var decalObj = isPath(el) ? styleOpt.decal : null;\n    if (api && decalObj) {\n      decalObj.dirty = true;\n      decalPattern = createOrUpdatePatternFromDecal(decalObj, api);\n    }\n    // Always overwrite in case user specify this prop.\n    styleOpt.__decalPattern = decalPattern;\n  }\n  if (isDisplayable(el)) {\n    if (styleOpt) {\n      var decalPattern = styleOpt.__decalPattern;\n      if (decalPattern) {\n        styleOpt.decal = decalPattern;\n      }\n    }\n  }\n  applyUpdateTransition(el, elOption, seriesModel, {\n    dataIndex: dataIndex,\n    isInit: isInit,\n    clearStyle: true\n  });\n  applyKeyframeAnimation(el, elOption.keyframeAnimation, seriesModel);\n}\nfunction updateElOnState(state, el, elStateOpt, styleOpt, attachedTxInfo) {\n  var elDisplayable = el.isGroup ? null : el;\n  var txCfgOpt = attachedTxInfo && attachedTxInfo[state].cfg;\n  // PENDING:5.0 support customize scale change and transition animation?\n  if (elDisplayable) {\n    // By default support auto lift color when hover whether `emphasis` specified.\n    var stateObj = elDisplayable.ensureState(state);\n    if (styleOpt === false) {\n      var existingEmphasisState = elDisplayable.getState(state);\n      if (existingEmphasisState) {\n        existingEmphasisState.style = null;\n      }\n    } else {\n      // style is needed to enable default emphasis.\n      stateObj.style = styleOpt || null;\n    }\n    // If `elOption.styleEmphasis` or `elOption.emphasis.style` is `false`,\n    // remove hover style.\n    // If `elOption.textConfig` or `elOption.emphasis.textConfig` is null/undefined, it does not\n    // make sense. So for simplicity, we do not ditinguish `hasOwnProperty` and null/undefined.\n    if (txCfgOpt) {\n      stateObj.textConfig = txCfgOpt;\n    }\n    setDefaultStateProxy(elDisplayable);\n  }\n}\nfunction updateZ(el, elOption, seriesModel) {\n  // Group not support textContent and not support z yet.\n  if (el.isGroup) {\n    return;\n  }\n  var elDisplayable = el;\n  var currentZ = seriesModel.currentZ;\n  var currentZLevel = seriesModel.currentZLevel;\n  // Always erase.\n  elDisplayable.z = currentZ;\n  elDisplayable.zlevel = currentZLevel;\n  // z2 must not be null/undefined, otherwise sort error may occur.\n  var optZ2 = elOption.z2;\n  optZ2 != null && (elDisplayable.z2 = optZ2 || 0);\n  for (var i = 0; i < STATES.length; i++) {\n    updateZForEachState(elDisplayable, elOption, STATES[i]);\n  }\n}\nfunction updateZForEachState(elDisplayable, elOption, state) {\n  var isNormal = state === NORMAL;\n  var elStateOpt = isNormal ? elOption : retrieveStateOption(elOption, state);\n  var optZ2 = elStateOpt ? elStateOpt.z2 : null;\n  var stateObj;\n  if (optZ2 != null) {\n    // Do not `ensureState` until required.\n    stateObj = isNormal ? elDisplayable : elDisplayable.ensureState(state);\n    stateObj.z2 = optZ2 || 0;\n  }\n}\nfunction makeRenderItem(customSeries, data, ecModel, api) {\n  var renderItem = customSeries.get('renderItem');\n  var coordSys = customSeries.coordinateSystem;\n  var prepareResult = {};\n  if (coordSys) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(renderItem, 'series.render is required.');\n      assert(coordSys.prepareCustoms || prepareCustoms[coordSys.type], 'This coordSys does not support custom series.');\n    }\n    // `coordSys.prepareCustoms` is used for external coord sys like bmap.\n    prepareResult = coordSys.prepareCustoms ? coordSys.prepareCustoms(coordSys) : prepareCustoms[coordSys.type](coordSys);\n  }\n  var userAPI = defaults({\n    getWidth: api.getWidth,\n    getHeight: api.getHeight,\n    getZr: api.getZr,\n    getDevicePixelRatio: api.getDevicePixelRatio,\n    value: value,\n    style: style,\n    ordinalRawValue: ordinalRawValue,\n    styleEmphasis: styleEmphasis,\n    visual: visual,\n    barLayout: barLayout,\n    currentSeriesIndices: currentSeriesIndices,\n    font: font\n  }, prepareResult.api || {});\n  var userParams = {\n    // The life cycle of context: current round of rendering.\n    // The global life cycle is probably not necessary, because\n    // user can store global status by themselves.\n    context: {},\n    seriesId: customSeries.id,\n    seriesName: customSeries.name,\n    seriesIndex: customSeries.seriesIndex,\n    coordSys: prepareResult.coordSys,\n    dataInsideLength: data.count(),\n    encode: wrapEncodeDef(customSeries.getData())\n  };\n  // If someday intending to refactor them to a class, should consider do not\n  // break change: currently these attribute member are encapsulated in a closure\n  // so that do not need to force user to call these method with a scope.\n  // Do not support call `api` asynchronously without dataIndexInside input.\n  var currDataIndexInside;\n  var currItemModel;\n  var currItemStyleModels = {};\n  var currLabelModels = {};\n  var seriesItemStyleModels = {};\n  var seriesLabelModels = {};\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    seriesItemStyleModels[stateName] = customSeries.getModel(PATH_ITEM_STYLE[stateName]);\n    seriesLabelModels[stateName] = customSeries.getModel(PATH_LABEL[stateName]);\n  }\n  function getItemModel(dataIndexInside) {\n    return dataIndexInside === currDataIndexInside ? currItemModel || (currItemModel = data.getItemModel(dataIndexInside)) : data.getItemModel(dataIndexInside);\n  }\n  function getItemStyleModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesItemStyleModels[state] : dataIndexInside === currDataIndexInside ? currItemStyleModels[state] || (currItemStyleModels[state] = getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state])) : getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state]);\n  }\n  function getLabelModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesLabelModels[state] : dataIndexInside === currDataIndexInside ? currLabelModels[state] || (currLabelModels[state] = getItemModel(dataIndexInside).getModel(PATH_LABEL[state])) : getItemModel(dataIndexInside).getModel(PATH_LABEL[state]);\n  }\n  return function (dataIndexInside, payload) {\n    currDataIndexInside = dataIndexInside;\n    currItemModel = null;\n    currItemStyleModels = {};\n    currLabelModels = {};\n    return renderItem && renderItem(defaults({\n      dataIndexInside: dataIndexInside,\n      dataIndex: data.getRawIndex(dataIndexInside),\n      // Can be used for optimization when zoom or roam.\n      actionType: payload ? payload.type : null\n    }, userParams), userAPI);\n  };\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function value(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    return data.getStore().get(data.getDimensionIndex(dim || 0), dataIndexInside);\n  }\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function ordinalRawValue(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    dim = dim || 0;\n    var dimInfo = data.getDimensionInfo(dim);\n    if (!dimInfo) {\n      var dimIndex = data.getDimensionIndex(dim);\n      return dimIndex >= 0 ? data.getStore().get(dimIndex, dataIndexInside) : undefined;\n    }\n    var val = data.get(dimInfo.name, dataIndexInside);\n    var ordinalMeta = dimInfo && dimInfo.ordinalMeta;\n    return ordinalMeta ? ordinalMeta.categories[val] : val;\n  }\n  /**\r\n   * @deprecated The original intention of `api.style` is enable to set itemStyle\r\n   * like other series. But it is not necessary and not easy to give a strict definition\r\n   * of what it returns. And since echarts5 it needs to be make compat work. So\r\n   * deprecates it since echarts5.\r\n   *\r\n   * By default, `visual` is applied to style (to support visualMap).\r\n   * `visual.color` is applied at `fill`. If user want apply visual.color on `stroke`,\r\n   * it can be implemented as:\r\n   * `api.style({stroke: api.visual('color'), fill: null})`;\r\n   *\r\n   * [Compat]: since ec5, RectText has been separated from its hosts el.\r\n   * so `api.style()` will only return the style from `itemStyle` but not handle `label`\r\n   * any more. But `series.label` config is never published in doc.\r\n   * We still compat it in `api.style()`. But not encourage to use it and will still not\r\n   * to pulish it to doc.\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function style(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.style', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var style = data.getItemVisual(dataIndexInside, 'style');\n    var visualColor = style && style.fill;\n    var opacity = style && style.opacity;\n    var itemStyle = getItemStyleModel(dataIndexInside, NORMAL).getItemStyle();\n    visualColor != null && (itemStyle.fill = visualColor);\n    opacity != null && (itemStyle.opacity = opacity);\n    var opt = {\n      inheritColor: isString(visualColor) ? visualColor : '#000'\n    };\n    var labelModel = getLabelModel(dataIndexInside, NORMAL);\n    // Now that the feature of \"auto adjust text fill/stroke\" has been migrated to zrender\n    // since ec5, we should set `isAttached` as `false` here and make compat in\n    // `convertToEC4StyleForCustomSerise`.\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, opt, false, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve2(customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, opt, false);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  /**\r\n   * @deprecated The reason see `api.style()`\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function styleEmphasis(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.styleEmphasis', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var itemStyle = getItemStyleModel(dataIndexInside, EMPHASIS).getItemStyle();\n    var labelModel = getLabelModel(dataIndexInside, EMPHASIS);\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, null, true, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve3(customSeries.getFormattedLabel(dataIndexInside, EMPHASIS), customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, null, true);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  function applyUserPropsAfter(itemStyle, extra) {\n    for (var key in extra) {\n      if (hasOwn(extra, key)) {\n        itemStyle[key] = extra[key];\n      }\n    }\n  }\n  function preFetchFromExtra(extra, itemStyle) {\n    // A trick to retrieve those props firstly, which are used to\n    // apply auto inside fill/stroke in `convertToEC4StyleForCustomSerise`.\n    // (It's not reasonable but only for a degree of compat)\n    if (extra) {\n      extra.textFill && (itemStyle.textFill = extra.textFill);\n      extra.textPosition && (itemStyle.textPosition = extra.textPosition);\n    }\n  }\n  /**\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function visual(visualType, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    if (hasOwn(STYLE_VISUAL_TYPE, visualType)) {\n      var style_1 = data.getItemVisual(dataIndexInside, 'style');\n      return style_1 ? style_1[STYLE_VISUAL_TYPE[visualType]] : null;\n    }\n    // Only support these visuals. Other visual might be inner tricky\n    // for performance (like `style`), do not expose to users.\n    if (hasOwn(NON_STYLE_VISUAL_PROPS, visualType)) {\n      return data.getItemVisual(dataIndexInside, visualType);\n    }\n  }\n  /**\r\n   * @public\r\n   * @return If not support, return undefined.\r\n   */\n  function barLayout(opt) {\n    if (coordSys.type === 'cartesian2d') {\n      var baseAxis = coordSys.getBaseAxis();\n      return getLayoutOnAxis(defaults({\n        axis: baseAxis\n      }, opt));\n    }\n  }\n  /**\r\n   * @public\r\n   */\n  function currentSeriesIndices() {\n    return ecModel.getCurrentSeriesIndices();\n  }\n  /**\r\n   * @public\r\n   * @return font string\r\n   */\n  function font(opt) {\n    return labelStyleHelper.getFont(opt, ecModel);\n  }\n}\nfunction wrapEncodeDef(data) {\n  var encodeDef = {};\n  each(data.dimensions, function (dimName) {\n    var dimInfo = data.getDimensionInfo(dimName);\n    if (!dimInfo.isExtraCoord) {\n      var coordDim = dimInfo.coordDim;\n      var dataDims = encodeDef[coordDim] = encodeDef[coordDim] || [];\n      dataDims[dimInfo.coordDimIndex] = data.getDimensionIndex(dimName);\n    }\n  });\n  return encodeDef;\n}\nfunction createOrUpdateItem(api, existsEl, dataIndex, elOption, seriesModel, group, data) {\n  // [Rule]\n  // If `renderItem` returns `null`/`undefined`/`false`, remove the previous el if existing.\n  //     (It seems that violate the \"merge\" principle, but most of users probably intuitively\n  //     regard \"return;\" as \"show nothing element whatever\", so make a exception to meet the\n  //     most cases.)\n  // The rule or \"merge\" see [STRATEGY_MERGE].\n  // If `elOption` is `null`/`undefined`/`false` (when `renderItem` returns nothing).\n  if (!elOption) {\n    group.remove(existsEl);\n    return;\n  }\n  var el = doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group);\n  el && data.setItemGraphicEl(dataIndex, el);\n  el && toggleHoverEmphasis(el, elOption.focus, elOption.blurScope, elOption.emphasisDisabled);\n  return el;\n}\nfunction doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(elOption, 'should not have an null/undefined element setting');\n  }\n  var toBeReplacedIdx = -1;\n  var oldEl = existsEl;\n  if (existsEl && doesElNeedRecreate(existsEl, elOption, seriesModel)\n  // || (\n  //     // PENDING: even in one-to-one mapping case, if el is marked as morph,\n  //     // do not sure whether the el will be mapped to another el with different\n  //     // hierarchy in Group tree. So always recreate el rather than reuse the el.\n  //     morphHelper && morphHelper.isOneToOneFrom(el)\n  // )\n  ) {\n    // Should keep at the original index, otherwise \"merge by index\" will be incorrect.\n    toBeReplacedIdx = indexOf(group.childrenRef(), existsEl);\n    existsEl = null;\n  }\n  var isInit = !existsEl;\n  var el = existsEl;\n  if (!el) {\n    el = createEl(elOption);\n    if (oldEl) {\n      copyElement(oldEl, el);\n    }\n  } else {\n    // FIMXE:NEXT unified clearState?\n    // If in some case the performance issue arised, consider\n    // do not clearState but update cached normal state directly.\n    el.clearStates();\n  }\n  // Need to set morph: false explictly to disable automatically morphing.\n  if (elOption.morph === false) {\n    el.disableMorphing = true;\n  } else if (el.disableMorphing) {\n    el.disableMorphing = false;\n  }\n  attachedTxInfoTmp.normal.cfg = attachedTxInfoTmp.normal.conOpt = attachedTxInfoTmp.emphasis.cfg = attachedTxInfoTmp.emphasis.conOpt = attachedTxInfoTmp.blur.cfg = attachedTxInfoTmp.blur.conOpt = attachedTxInfoTmp.select.cfg = attachedTxInfoTmp.select.conOpt = null;\n  attachedTxInfoTmp.isLegacy = false;\n  doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfoTmp);\n  doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit);\n  updateElNormal(api, el, dataIndex, elOption, attachedTxInfoTmp, seriesModel, isInit);\n  // `elOption.info` enables user to mount some info on\n  // elements and use them in event handlers.\n  // Update them only when user specified, otherwise, remain.\n  hasOwn(elOption, 'info') && (customInnerStore(el).info = elOption.info);\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    if (stateName !== NORMAL) {\n      var otherStateOpt = retrieveStateOption(elOption, stateName);\n      var otherStyleOpt = retrieveStyleOptionOnState(elOption, otherStateOpt, stateName);\n      updateElOnState(stateName, el, otherStateOpt, otherStyleOpt, attachedTxInfoTmp);\n    }\n  }\n  updateZ(el, elOption, seriesModel);\n  if (elOption.type === 'group') {\n    mergeChildren(api, el, dataIndex, elOption, seriesModel);\n  }\n  if (toBeReplacedIdx >= 0) {\n    group.replaceAt(el, toBeReplacedIdx);\n  } else {\n    group.add(el);\n  }\n  return el;\n}\n// `el` must not be null/undefined.\nfunction doesElNeedRecreate(el, elOption, seriesModel) {\n  var elInner = customInnerStore(el);\n  var elOptionType = elOption.type;\n  var elOptionShape = elOption.shape;\n  var elOptionStyle = elOption.style;\n  return (\n    // Always create new if universal transition is enabled.\n    // Because we do transition after render. It needs to know what old element is. Replacement will loose it.\n    seriesModel.isUniversalTransitionEnabled()\n    // If `elOptionType` is `null`, follow the merge principle.\n    || elOptionType != null && elOptionType !== elInner.customGraphicType || elOptionType === 'path' && hasOwnPathData(elOptionShape) && getPathData(elOptionShape) !== elInner.customPathData || elOptionType === 'image' && hasOwn(elOptionStyle, 'image') && elOptionStyle.image !== elInner.customImagePath\n    // // FIXME test and remove this restriction?\n    // || (elOptionType === 'text'\n    //     && hasOwn(elOptionStyle, 'text')\n    //     && (elOptionStyle as TextStyleProps).text !== elInner.customText\n    // )\n  );\n}\nfunction doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit) {\n  // Based on the \"merge\" principle, if no clipPath provided,\n  // do nothing. The exists clip will be totally removed only if\n  // `el.clipPath` is `false`. Otherwise it will be merged/replaced.\n  var clipPathOpt = elOption.clipPath;\n  if (clipPathOpt === false) {\n    if (el && el.getClipPath()) {\n      el.removeClipPath();\n    }\n  } else if (clipPathOpt) {\n    var clipPath = el.getClipPath();\n    if (clipPath && doesElNeedRecreate(clipPath, clipPathOpt, seriesModel)) {\n      clipPath = null;\n    }\n    if (!clipPath) {\n      clipPath = createEl(clipPathOpt);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(isPath(clipPath), 'Only any type of `path` can be used in `clipPath`, rather than ' + clipPath.type + '.');\n      }\n      el.setClipPath(clipPath);\n    }\n    updateElNormal(null, clipPath, dataIndex, clipPathOpt, null, seriesModel, isInit);\n  }\n  // If not define `clipPath` in option, do nothing unnecessary.\n}\nfunction doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfo) {\n  // Group does not support textContent temporarily until necessary.\n  if (el.isGroup) {\n    return;\n  }\n  // Normal must be called before emphasis, for `isLegacy` detection.\n  processTxInfo(elOption, null, attachedTxInfo);\n  processTxInfo(elOption, EMPHASIS, attachedTxInfo);\n  // If `elOption.textConfig` or `elOption.textContent` is null/undefined, it does not make sense.\n  // So for simplicity, if \"elOption hasOwnProperty of them but be null/undefined\", we do not\n  // trade them as set to null to el.\n  // Especially:\n  // `elOption.textContent: false` means remove textContent.\n  // `elOption.textContent.emphasis.style: false` means remove the style from emphasis state.\n  var txConOptNormal = attachedTxInfo.normal.conOpt;\n  var txConOptEmphasis = attachedTxInfo.emphasis.conOpt;\n  var txConOptBlur = attachedTxInfo.blur.conOpt;\n  var txConOptSelect = attachedTxInfo.select.conOpt;\n  if (txConOptNormal != null || txConOptEmphasis != null || txConOptSelect != null || txConOptBlur != null) {\n    var textContent = el.getTextContent();\n    if (txConOptNormal === false) {\n      textContent && el.removeTextContent();\n    } else {\n      txConOptNormal = attachedTxInfo.normal.conOpt = txConOptNormal || {\n        type: 'text'\n      };\n      if (!textContent) {\n        textContent = createEl(txConOptNormal);\n        el.setTextContent(textContent);\n      } else {\n        // If in some case the performance issue arised, consider\n        // do not clearState but update cached normal state directly.\n        textContent.clearStates();\n      }\n      updateElNormal(null, textContent, dataIndex, txConOptNormal, null, seriesModel, isInit);\n      var txConStlOptNormal = txConOptNormal && txConOptNormal.style;\n      for (var i = 0; i < STATES.length; i++) {\n        var stateName = STATES[i];\n        if (stateName !== NORMAL) {\n          var txConOptOtherState = attachedTxInfo[stateName].conOpt;\n          updateElOnState(stateName, textContent, txConOptOtherState, retrieveStyleOptionOnState(txConOptNormal, txConOptOtherState, stateName), null);\n        }\n      }\n      txConStlOptNormal ? textContent.dirty() : textContent.markRedraw();\n    }\n  }\n}\nfunction processTxInfo(elOption, state, attachedTxInfo) {\n  var stateOpt = !state ? elOption : retrieveStateOption(elOption, state);\n  var styleOpt = !state ? elOption.style : retrieveStyleOptionOnState(elOption, stateOpt, EMPHASIS);\n  var elType = elOption.type;\n  var txCfg = stateOpt ? stateOpt.textConfig : null;\n  var txConOptNormal = elOption.textContent;\n  var txConOpt = !txConOptNormal ? null : !state ? txConOptNormal : retrieveStateOption(txConOptNormal, state);\n  if (styleOpt && (\n  // Because emphasis style has little info to detect legacy,\n  // if normal is legacy, emphasis is trade as legacy.\n  attachedTxInfo.isLegacy || isEC4CompatibleStyle(styleOpt, elType, !!txCfg, !!txConOpt))) {\n    attachedTxInfo.isLegacy = true;\n    var convertResult = convertFromEC4CompatibleStyle(styleOpt, elType, !state);\n    // Explicitly specified `textConfig` and `textContent` has higher priority than\n    // the ones generated by legacy style. Otherwise if users use them and `api.style`\n    // at the same time, they not both work and hardly to known why.\n    if (!txCfg && convertResult.textConfig) {\n      txCfg = convertResult.textConfig;\n    }\n    if (!txConOpt && convertResult.textContent) {\n      txConOpt = convertResult.textContent;\n    }\n  }\n  if (!state && txConOpt) {\n    var txConOptNormal_1 = txConOpt;\n    // `textContent: {type: 'text'}`, the \"type\" is easy to be missing. So we tolerate it.\n    !txConOptNormal_1.type && (txConOptNormal_1.type = 'text');\n    if (process.env.NODE_ENV !== 'production') {\n      // Do not tolerate incorrcet type for forward compat.\n      assert(txConOptNormal_1.type === 'text', 'textContent.type must be \"text\"');\n    }\n  }\n  var info = !state ? attachedTxInfo.normal : attachedTxInfo[state];\n  info.cfg = txCfg;\n  info.conOpt = txConOpt;\n}\nfunction retrieveStateOption(elOption, state) {\n  return !state ? elOption : elOption ? elOption[state] : null;\n}\nfunction retrieveStyleOptionOnState(stateOptionNormal, stateOption, state) {\n  var style = stateOption && stateOption.style;\n  if (style == null && state === EMPHASIS && stateOptionNormal) {\n    style = stateOptionNormal.styleEmphasis;\n  }\n  return style;\n}\n// Usage:\n// (1) By default, `elOption.$mergeChildren` is `'byIndex'`, which indicates\n//     that the existing children will not be removed, and enables the feature\n//     that update some of the props of some of the children simply by construct\n//     the returned children of `renderItem` like:\n//     `var children = group.children = []; children[3] = {opacity: 0.5};`\n// (2) If `elOption.$mergeChildren` is `'byName'`, add/update/remove children\n//     by child.name. But that might be lower performance.\n// (3) If `elOption.$mergeChildren` is `false`, the existing children will be\n//     replaced totally.\n// (4) If `!elOption.children`, following the \"merge\" principle, nothing will\n//     happen.\n// (5) If `elOption.$mergeChildren` is not `false` neither `'byName'` and the\n//     `el` is a group, and if any of the new child is null, it means to remove\n//     the element at the same index, if exists. On the other hand, if the new\n//     child is and empty object `{}`, it means to keep the element not changed.\n//\n// For implementation simpleness, do not provide a direct way to remove single\n// child (otherwise the total indices of the children array have to be modified).\n// User can remove a single child by setting its `ignore` to `true`.\nfunction mergeChildren(api, el, dataIndex, elOption, seriesModel) {\n  var newChildren = elOption.children;\n  var newLen = newChildren ? newChildren.length : 0;\n  var mergeChildren = elOption.$mergeChildren;\n  // `diffChildrenByName` has been deprecated.\n  var byName = mergeChildren === 'byName' || elOption.diffChildrenByName;\n  var notMerge = mergeChildren === false;\n  // For better performance on roam update, only enter if necessary.\n  if (!newLen && !byName && !notMerge) {\n    return;\n  }\n  if (byName) {\n    diffGroupChildren({\n      api: api,\n      oldChildren: el.children() || [],\n      newChildren: newChildren || [],\n      dataIndex: dataIndex,\n      seriesModel: seriesModel,\n      group: el\n    });\n    return;\n  }\n  notMerge && el.removeAll();\n  // Mapping children of a group simply by index, which\n  // might be better performance.\n  var index = 0;\n  for (; index < newLen; index++) {\n    var newChild = newChildren[index];\n    var oldChild = el.childAt(index);\n    if (newChild) {\n      if (newChild.ignore == null) {\n        // The old child is set to be ignored if null (see comments\n        // below). So we need to set ignore to be false back.\n        newChild.ignore = false;\n      }\n      doCreateOrUpdateEl(api, oldChild, dataIndex, newChild, seriesModel, el);\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(oldChild, 'renderItem should not return a group containing elements' + ' as null/undefined/{} if they do not exist before.');\n      }\n      // If the new element option is null, it means to remove the old\n      // element. But we cannot really remove the element from the group\n      // directly, because the element order may not be stable when this\n      // element is added back. So we set the element to be ignored.\n      oldChild.ignore = true;\n    }\n  }\n  for (var i = el.childCount() - 1; i >= index; i--) {\n    var child = el.childAt(i);\n    removeChildFromGroup(el, child, seriesModel);\n  }\n}\nfunction removeChildFromGroup(group, child, seriesModel) {\n  // Do not support leave elements that are not mentioned in the latest\n  // `renderItem` return. Otherwise users may not have a clear and simple\n  // concept that how to control all of the elements.\n  child && applyLeaveTransition(child, customInnerStore(group).option, seriesModel);\n}\nfunction diffGroupChildren(context) {\n  new DataDiffer(context.oldChildren, context.newChildren, getKey, getKey, context).add(processAddUpdate).update(processAddUpdate).remove(processRemove).execute();\n}\nfunction getKey(item, idx) {\n  var name = item && item.name;\n  return name != null ? name : GROUP_DIFF_PREFIX + idx;\n}\nfunction processAddUpdate(newIndex, oldIndex) {\n  var context = this.context;\n  var childOption = newIndex != null ? context.newChildren[newIndex] : null;\n  var child = oldIndex != null ? context.oldChildren[oldIndex] : null;\n  doCreateOrUpdateEl(context.api, child, context.dataIndex, childOption, context.seriesModel, context.group);\n}\nfunction processRemove(oldIndex) {\n  var context = this.context;\n  var child = context.oldChildren[oldIndex];\n  child && applyLeaveTransition(child, customInnerStore(child).option, context.seriesModel);\n}\n/**\r\n * @return SVG Path data.\r\n */\nfunction getPathData(shape) {\n  // \"d\" follows the SVG convention.\n  return shape && (shape.pathData || shape.d);\n}\nfunction hasOwnPathData(shape) {\n  return shape && (hasOwn(shape, 'pathData') || hasOwn(shape, 'd'));\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,QAAQ,0BAA0B;AAClH,OAAO,KAAKC,WAAW,MAAM,uBAAuB;AACpD,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAChF,OAAO,KAAKC,gBAAgB,MAAM,2BAA2B;AAC7D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,cAAc,QAAQ,yCAAyC;AACxE,OAAOC,kBAAkB,MAAM,wCAAwC;AACvE,OAAOC,UAAU,MAAM,kCAAkC;AACzD,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,gCAAgC,EAAEC,oBAAoB,EAAEC,6BAA6B,EAAEC,cAAc,QAAQ,2BAA2B;AACjJ,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC/F,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,4CAA4C;AACxG,SAASC,sBAAsB,EAAEC,uCAAuC,QAAQ,mDAAmD;AACnI,IAAIC,QAAQ,GAAG,UAAU;AACzB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,MAAM,GAAG,CAACH,MAAM,EAAED,QAAQ,EAAEE,IAAI,EAAEC,MAAM,CAAC;AAC7C,IAAIE,eAAe,GAAG;EACpBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,QAAQ,EAAE,CAACP,QAAQ,EAAE,WAAW,CAAC;EACjCQ,IAAI,EAAE,CAACN,IAAI,EAAE,WAAW,CAAC;EACzBO,MAAM,EAAE,CAACN,MAAM,EAAE,WAAW;AAC9B,CAAC;AACD,IAAIO,UAAU,GAAG;EACfJ,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,QAAQ,EAAE,CAACP,QAAQ,EAAE,OAAO,CAAC;EAC7BQ,IAAI,EAAE,CAACN,IAAI,EAAE,OAAO,CAAC;EACrBO,MAAM,EAAE,CAACN,MAAM,EAAE,OAAO;AAC1B,CAAC;AACD,IAAIQ,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACnC;AACA;AACA,IAAIC,iBAAiB,GAAG,OAAO;AAC/B,IAAIC,iBAAiB,GAAG;EACtBP,MAAM,EAAE,CAAC,CAAC;EACVC,QAAQ,EAAE,CAAC,CAAC;EACZC,IAAI,EAAE,CAAC,CAAC;EACRC,MAAM,EAAE,CAAC;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,cAAc,GAAG;EACnBC,WAAW,EAAElC,kBAAkB;EAC/BmC,GAAG,EAAElC,UAAU;EACfmC,MAAM,EAAElC,iBAAiB;EACzBmC,KAAK,EAAElC,YAAY;EACnBmC,QAAQ,EAAElC;AACZ,CAAC;AACD,SAASmC,MAAMA,CAACC,EAAE,EAAE;EAClB,OAAOA,EAAE,YAAYjD,WAAW,CAACkD,IAAI;AACvC;AACA,SAASC,aAAaA,CAACF,EAAE,EAAE;EACzB,OAAOA,EAAE,YAAYnC,WAAW;AAClC;AACA,SAASsC,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACvCA,QAAQ,CAACC,aAAa,CAACF,QAAQ,CAAC;EAChC,IAAIF,aAAa,CAACG,QAAQ,CAAC,IAAIH,aAAa,CAACE,QAAQ,CAAC,EAAE;IACtDC,QAAQ,CAACE,QAAQ,CAACH,QAAQ,CAACI,KAAK,CAAC;IACjCH,QAAQ,CAACI,CAAC,GAAGL,QAAQ,CAACK,CAAC;IACvBJ,QAAQ,CAACK,EAAE,GAAGN,QAAQ,CAACM,EAAE;IACzBL,QAAQ,CAACM,MAAM,GAAGP,QAAQ,CAACO,MAAM;IACjCN,QAAQ,CAACO,SAAS,GAAGR,QAAQ,CAACQ,SAAS;IACvCP,QAAQ,CAACQ,MAAM,GAAGT,QAAQ,CAACS,MAAM;IACjC,IAAId,MAAM,CAACM,QAAQ,CAAC,IAAIN,MAAM,CAACK,QAAQ,CAAC,EAAE;MACxCC,QAAQ,CAACS,QAAQ,CAACV,QAAQ,CAACW,KAAK,CAAC;IACnC;EACF;AACF;AACA,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnD3E,SAAS,CAAC0E,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAChF;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIC,IAAI,GAAGP,YAAY,CAACQ,OAAO,CAAC,CAAC;IACjC,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,UAAU,GAAGC,cAAc,CAACX,YAAY,EAAEO,IAAI,EAAEN,OAAO,EAAEC,GAAG,CAAC;IACjE,IAAI,CAACG,OAAO,EAAE;MACZ;MACA;MACAI,KAAK,CAACG,SAAS,CAAC,CAAC;IACnB;IACAL,IAAI,CAACM,IAAI,CAACR,OAAO,CAAC,CAACS,GAAG,CAAC,UAAUC,MAAM,EAAE;MACvCC,kBAAkB,CAACd,GAAG,EAAE,IAAI,EAAEa,MAAM,EAAEL,UAAU,CAACK,MAAM,EAAEZ,OAAO,CAAC,EAAEH,YAAY,EAAES,KAAK,EAAEF,IAAI,CAAC;IAC/F,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUC,MAAM,EAAE;MAC1B,IAAI1C,EAAE,GAAG6B,OAAO,CAACc,gBAAgB,CAACD,MAAM,CAAC;MACzC1C,EAAE,IAAIzB,oBAAoB,CAACyB,EAAE,EAAE1B,gBAAgB,CAAC0B,EAAE,CAAC,CAAC4C,MAAM,EAAEpB,YAAY,CAAC;IAC3E,CAAC,CAAC,CAACqB,MAAM,CAAC,UAAUN,MAAM,EAAEG,MAAM,EAAE;MAClC,IAAII,KAAK,GAAGjB,OAAO,CAACc,gBAAgB,CAACD,MAAM,CAAC;MAC5CF,kBAAkB,CAACd,GAAG,EAAEoB,KAAK,EAAEP,MAAM,EAAEL,UAAU,CAACK,MAAM,EAAEZ,OAAO,CAAC,EAAEH,YAAY,EAAES,KAAK,EAAEF,IAAI,CAAC;IAChG,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC;IACZ;IACA,IAAIC,QAAQ,GAAGxB,YAAY,CAACyB,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG1F,cAAc,CAACiE,YAAY,CAAC0B,gBAAgB,EAAE,KAAK,EAAE1B,YAAY,CAAC,GAAG,IAAI;IACzH,IAAIwB,QAAQ,EAAE;MACZf,KAAK,CAACkB,WAAW,CAACH,QAAQ,CAAC;IAC7B,CAAC,MAAM;MACLf,KAAK,CAACmB,cAAc,CAAC,CAAC;IACxB;IACA,IAAI,CAACtB,KAAK,GAAGC,IAAI;EACnB,CAAC;EACDf,eAAe,CAACM,SAAS,CAAC+B,wBAAwB,GAAG,UAAU7B,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACzF,IAAI,CAACO,KAAK,CAACG,SAAS,CAAC,CAAC;IACtB,IAAI,CAACN,KAAK,GAAG,IAAI;EACnB,CAAC;EACDd,eAAe,CAACM,SAAS,CAACgC,iBAAiB,GAAG,UAAUC,MAAM,EAAE/B,YAAY,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACnG,IAAII,IAAI,GAAGP,YAAY,CAACQ,OAAO,CAAC,CAAC;IACjC,IAAIE,UAAU,GAAGC,cAAc,CAACX,YAAY,EAAEO,IAAI,EAAEN,OAAO,EAAEC,GAAG,CAAC;IACjE,IAAI8B,cAAc,GAAG,IAAI,CAAC5B,eAAe,GAAG,EAAE;IAC9C,SAAS6B,2BAA2BA,CAACzD,EAAE,EAAE;MACvC,IAAI,CAACA,EAAE,CAAC0D,OAAO,EAAE;QACf1D,EAAE,CAAC2D,WAAW,GAAG,IAAI;QACrB3D,EAAE,CAAC4D,WAAW,CAAC,UAAU,CAAC,CAACC,UAAU,GAAG,IAAI;MAC9C;IACF;IACA,KAAK,IAAIC,GAAG,GAAGP,MAAM,CAACQ,KAAK,EAAED,GAAG,GAAGP,MAAM,CAACS,GAAG,EAAEF,GAAG,EAAE,EAAE;MACpD,IAAI9D,EAAE,GAAGwC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAEsB,GAAG,EAAE5B,UAAU,CAAC4B,GAAG,EAAEnC,OAAO,CAAC,EAAEH,YAAY,EAAE,IAAI,CAACS,KAAK,EAAEF,IAAI,CAAC;MACtG,IAAI/B,EAAE,EAAE;QACNA,EAAE,CAACiE,QAAQ,CAACR,2BAA2B,CAAC;QACxCD,cAAc,CAACU,IAAI,CAAClE,EAAE,CAAC;MACzB;IACF;EACF,CAAC;EACDgB,eAAe,CAACM,SAAS,CAAC6C,YAAY,GAAG,UAAUC,EAAE,EAAE;IACrDrH,WAAW,CAACsH,gBAAgB,CAAC,IAAI,CAACzC,eAAe,IAAI,IAAI,CAACK,KAAK,EAAEmC,EAAE,CAAC;EACtE,CAAC;EACDpD,eAAe,CAACM,SAAS,CAACgD,qBAAqB,GAAG,UAAUC,SAAS,EAAEC,KAAK,EAAEnE,QAAQ,EAAEoE,WAAW,EAAE;IACnG,IAAIC,WAAW,GAAGF,KAAK,CAACG,OAAO;IAC/B,IAAID,WAAW,IAAI,IAAI,IAAIrE,QAAQ,CAACuE,IAAI,KAAKF,WAAW,EAAE;MACxD,OAAO,IAAI;IACb;IACA;IACA;IACA,OAAO,CAACrE,QAAQ,GAAGA,QAAQ,CAACwE,YAAY,IAAIxE,QAAQ,CAACyE,MAAM,KAAKzE,QAAQ,KAAK,IAAI,CAAC4B,KAAK,EAAE;MACvF,IAAI5B,QAAQ,CAACuE,IAAI,KAAKF,WAAW,EAAE;QACjC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EACD1D,eAAe,CAACK,IAAI,GAAG,QAAQ;EAC/B,OAAOL,eAAe;AACxB,CAAC,CAAC1D,SAAS,CAAC;AACZ,eAAe0D,eAAe;AAC9B,SAAS+D,QAAQA,CAACC,QAAQ,EAAE;EAC1B,IAAIC,WAAW,GAAGD,QAAQ,CAAC3D,IAAI;EAC/B,IAAIrB,EAAE;EACN;EACA;EACA,IAAIiF,WAAW,KAAK,MAAM,EAAE;IAC1B,IAAIlE,KAAK,GAAGiE,QAAQ,CAACjE,KAAK;IAC1B;IACA,IAAImE,QAAQ,GAAGnE,KAAK,CAACoE,KAAK,IAAI,IAAI,IAAIpE,KAAK,CAACqE,MAAM,IAAI,IAAI,GAAG;MAC3DC,CAAC,EAAEtE,KAAK,CAACsE,CAAC,IAAI,CAAC;MACfC,CAAC,EAAEvE,KAAK,CAACuE,CAAC,IAAI,CAAC;MACfH,KAAK,EAAEpE,KAAK,CAACoE,KAAK;MAClBC,MAAM,EAAErE,KAAK,CAACqE;IAChB,CAAC,GAAG,IAAI;IACR,IAAIG,QAAQ,GAAGC,WAAW,CAACzE,KAAK,CAAC;IACjC;IACAf,EAAE,GAAGjD,WAAW,CAAC0I,QAAQ,CAACF,QAAQ,EAAE,IAAI,EAAEL,QAAQ,EAAEnE,KAAK,CAAC2E,MAAM,IAAI,QAAQ,CAAC;IAC7EpH,gBAAgB,CAAC0B,EAAE,CAAC,CAAC2F,cAAc,GAAGJ,QAAQ;EAChD,CAAC,MAAM,IAAIN,WAAW,KAAK,OAAO,EAAE;IAClCjF,EAAE,GAAG,IAAIjD,WAAW,CAAC6I,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9BtH,gBAAgB,CAAC0B,EAAE,CAAC,CAAC6F,eAAe,GAAGb,QAAQ,CAACxE,KAAK,CAACsF,KAAK;EAC7D,CAAC,MAAM,IAAIb,WAAW,KAAK,MAAM,EAAE;IACjCjF,EAAE,GAAG,IAAIjD,WAAW,CAACgJ,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,MAAM,IAAId,WAAW,KAAK,OAAO,EAAE;IAClCjF,EAAE,GAAG,IAAIjD,WAAW,CAACiJ,KAAK,CAAC,CAAC;EAC9B,CAAC,MAAM,IAAIf,WAAW,KAAK,cAAc,EAAE;IACzC,MAAM,IAAIgB,KAAK,CAAC,sCAAsC,CAAC;EACzD,CAAC,MAAM;IACL,IAAIC,GAAG,GAAGnJ,WAAW,CAACoJ,aAAa,CAAClB,WAAW,CAAC;IAChD,IAAI,CAACiB,GAAG,EAAE;MACR,IAAIE,MAAM,GAAG,EAAE;MACf,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,MAAM,GAAG,gBAAgB,GAAGnB,WAAW,GAAG,qBAAqB;MACjE;MACA/G,UAAU,CAACkI,MAAM,CAAC;IACpB;IACApG,EAAE,GAAG,IAAIkG,GAAG,CAAC,CAAC;EAChB;EACA5H,gBAAgB,CAAC0B,EAAE,CAAC,CAACwG,iBAAiB,GAAGvB,WAAW;EACpDjF,EAAE,CAAC4E,IAAI,GAAGI,QAAQ,CAACJ,IAAI;EACvB;EACA;EACA;EACA5E,EAAE,CAACyG,cAAc,GAAG,CAAC;EACrBzG,EAAE,CAAC0G,YAAY,GAAG,CAAC;EACnB,OAAO1G,EAAE;AACX;AACA,SAAS2G,cAAcA;AACvB;AACAjF,GAAG,EAAE1B,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE6B,cAAc,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACjE;EACArI,uCAAuC,CAACsB,EAAE,CAAC;EAC3C,IAAIgH,QAAQ,GAAGH,cAAc,IAAIA,cAAc,CAAC5H,MAAM,CAACgI,GAAG;EAC1D,IAAID,QAAQ,EAAE;IACZ;IACA;IACAhH,EAAE,CAACkH,aAAa,CAACF,QAAQ,CAAC;EAC5B;EACA;EACA,IAAIhC,QAAQ,IAAIA,QAAQ,CAACmC,UAAU,IAAI,IAAI,EAAE;IAC3CnC,QAAQ,CAACmC,UAAU,GAAG7H,kBAAkB;EAC1C;EACA;EACA,IAAI8H,QAAQ,GAAGpC,QAAQ,IAAIA,QAAQ,CAACxE,KAAK;EACzC,IAAI4G,QAAQ,EAAE;IACZ,IAAIpH,EAAE,CAACqB,IAAI,KAAK,MAAM,EAAE;MACtB,IAAIgG,eAAe,GAAGD,QAAQ;MAC9B;MACA7K,MAAM,CAAC8K,eAAe,EAAE,UAAU,CAAC,KAAKA,eAAe,CAACC,IAAI,GAAGD,eAAe,CAACE,QAAQ,CAAC;MACxFhL,MAAM,CAAC8K,eAAe,EAAE,YAAY,CAAC,KAAKA,eAAe,CAACG,MAAM,GAAGH,eAAe,CAACI,UAAU,CAAC;IAChG;IACA,IAAIC,YAAY,GAAG,KAAK,CAAC;IACzB,IAAIC,QAAQ,GAAG5H,MAAM,CAACC,EAAE,CAAC,GAAGoH,QAAQ,CAACQ,KAAK,GAAG,IAAI;IACjD,IAAIlG,GAAG,IAAIiG,QAAQ,EAAE;MACnBA,QAAQ,CAACE,KAAK,GAAG,IAAI;MACrBH,YAAY,GAAGvJ,8BAA8B,CAACwJ,QAAQ,EAAEjG,GAAG,CAAC;IAC9D;IACA;IACA0F,QAAQ,CAACU,cAAc,GAAGJ,YAAY;EACxC;EACA,IAAIxH,aAAa,CAACF,EAAE,CAAC,EAAE;IACrB,IAAIoH,QAAQ,EAAE;MACZ,IAAIM,YAAY,GAAGN,QAAQ,CAACU,cAAc;MAC1C,IAAIJ,YAAY,EAAE;QAChBN,QAAQ,CAACQ,KAAK,GAAGF,YAAY;MAC/B;IACF;EACF;EACAlJ,qBAAqB,CAACwB,EAAE,EAAEgF,QAAQ,EAAE8B,WAAW,EAAE;IAC/CF,SAAS,EAAEA,SAAS;IACpBG,MAAM,EAAEA,MAAM;IACdgB,UAAU,EAAE;EACd,CAAC,CAAC;EACFtJ,sBAAsB,CAACuB,EAAE,EAAEgF,QAAQ,CAACgD,iBAAiB,EAAElB,WAAW,CAAC;AACrE;AACA,SAASmB,eAAeA,CAACC,KAAK,EAAElI,EAAE,EAAEmI,UAAU,EAAEf,QAAQ,EAAEP,cAAc,EAAE;EACxE,IAAIuB,aAAa,GAAGpI,EAAE,CAAC0D,OAAO,GAAG,IAAI,GAAG1D,EAAE;EAC1C,IAAIgH,QAAQ,GAAGH,cAAc,IAAIA,cAAc,CAACqB,KAAK,CAAC,CAACjB,GAAG;EAC1D;EACA,IAAImB,aAAa,EAAE;IACjB;IACA,IAAIC,QAAQ,GAAGD,aAAa,CAACxE,WAAW,CAACsE,KAAK,CAAC;IAC/C,IAAId,QAAQ,KAAK,KAAK,EAAE;MACtB,IAAIkB,qBAAqB,GAAGF,aAAa,CAACG,QAAQ,CAACL,KAAK,CAAC;MACzD,IAAII,qBAAqB,EAAE;QACzBA,qBAAqB,CAAC9H,KAAK,GAAG,IAAI;MACpC;IACF,CAAC,MAAM;MACL;MACA6H,QAAQ,CAAC7H,KAAK,GAAG4G,QAAQ,IAAI,IAAI;IACnC;IACA;IACA;IACA;IACA;IACA,IAAIJ,QAAQ,EAAE;MACZqB,QAAQ,CAACG,UAAU,GAAGxB,QAAQ;IAChC;IACAhK,oBAAoB,CAACoL,aAAa,CAAC;EACrC;AACF;AACA,SAASK,OAAOA,CAACzI,EAAE,EAAEgF,QAAQ,EAAE8B,WAAW,EAAE;EAC1C;EACA,IAAI9G,EAAE,CAAC0D,OAAO,EAAE;IACd;EACF;EACA,IAAI0E,aAAa,GAAGpI,EAAE;EACtB,IAAI0I,QAAQ,GAAG5B,WAAW,CAAC4B,QAAQ;EACnC,IAAIC,aAAa,GAAG7B,WAAW,CAAC6B,aAAa;EAC7C;EACAP,aAAa,CAAC3H,CAAC,GAAGiI,QAAQ;EAC1BN,aAAa,CAACzH,MAAM,GAAGgI,aAAa;EACpC;EACA,IAAIC,KAAK,GAAG5D,QAAQ,CAACtE,EAAE;EACvBkI,KAAK,IAAI,IAAI,KAAKR,aAAa,CAAC1H,EAAE,GAAGkI,KAAK,IAAI,CAAC,CAAC;EAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9J,MAAM,CAAC+J,MAAM,EAAED,CAAC,EAAE,EAAE;IACtCE,mBAAmB,CAACX,aAAa,EAAEpD,QAAQ,EAAEjG,MAAM,CAAC8J,CAAC,CAAC,CAAC;EACzD;AACF;AACA,SAASE,mBAAmBA,CAACX,aAAa,EAAEpD,QAAQ,EAAEkD,KAAK,EAAE;EAC3D,IAAIc,QAAQ,GAAGd,KAAK,KAAKtJ,MAAM;EAC/B,IAAIuJ,UAAU,GAAGa,QAAQ,GAAGhE,QAAQ,GAAGiE,mBAAmB,CAACjE,QAAQ,EAAEkD,KAAK,CAAC;EAC3E,IAAIU,KAAK,GAAGT,UAAU,GAAGA,UAAU,CAACzH,EAAE,GAAG,IAAI;EAC7C,IAAI2H,QAAQ;EACZ,IAAIO,KAAK,IAAI,IAAI,EAAE;IACjB;IACAP,QAAQ,GAAGW,QAAQ,GAAGZ,aAAa,GAAGA,aAAa,CAACxE,WAAW,CAACsE,KAAK,CAAC;IACtEG,QAAQ,CAAC3H,EAAE,GAAGkI,KAAK,IAAI,CAAC;EAC1B;AACF;AACA,SAASzG,cAAcA,CAACX,YAAY,EAAEO,IAAI,EAAEN,OAAO,EAAEC,GAAG,EAAE;EACxD,IAAIQ,UAAU,GAAGV,YAAY,CAACyB,GAAG,CAAC,YAAY,CAAC;EAC/C,IAAIiG,QAAQ,GAAG1H,YAAY,CAAC0B,gBAAgB;EAC5C,IAAIiG,aAAa,GAAG,CAAC,CAAC;EACtB,IAAID,QAAQ,EAAE;IACZ,IAAI7C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC/J,MAAM,CAAC0F,UAAU,EAAE,4BAA4B,CAAC;MAChD1F,MAAM,CAAC0M,QAAQ,CAACzJ,cAAc,IAAIA,cAAc,CAACyJ,QAAQ,CAAC7H,IAAI,CAAC,EAAE,+CAA+C,CAAC;IACnH;IACA;IACA8H,aAAa,GAAGD,QAAQ,CAACzJ,cAAc,GAAGyJ,QAAQ,CAACzJ,cAAc,CAACyJ,QAAQ,CAAC,GAAGzJ,cAAc,CAACyJ,QAAQ,CAAC7H,IAAI,CAAC,CAAC6H,QAAQ,CAAC;EACvH;EACA,IAAIE,OAAO,GAAGxM,QAAQ,CAAC;IACrByM,QAAQ,EAAE3H,GAAG,CAAC2H,QAAQ;IACtBC,SAAS,EAAE5H,GAAG,CAAC4H,SAAS;IACxBC,KAAK,EAAE7H,GAAG,CAAC6H,KAAK;IAChBC,mBAAmB,EAAE9H,GAAG,CAAC8H,mBAAmB;IAC5CC,KAAK,EAAEA,KAAK;IACZjJ,KAAK,EAAEA,KAAK;IACZkJ,eAAe,EAAEA,eAAe;IAChCC,aAAa,EAAEA,aAAa;IAC5BC,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBC,oBAAoB,EAAEA,oBAAoB;IAC1CC,IAAI,EAAEA;EACR,CAAC,EAAEZ,aAAa,CAACzH,GAAG,IAAI,CAAC,CAAC,CAAC;EAC3B,IAAIsI,UAAU,GAAG;IACf;IACA;IACA;IACAC,OAAO,EAAE,CAAC,CAAC;IACXC,QAAQ,EAAE1I,YAAY,CAAC2I,EAAE;IACzBC,UAAU,EAAE5I,YAAY,CAACoD,IAAI;IAC7ByF,WAAW,EAAE7I,YAAY,CAAC6I,WAAW;IACrCnB,QAAQ,EAAEC,aAAa,CAACD,QAAQ;IAChCoB,gBAAgB,EAAEvI,IAAI,CAACwI,KAAK,CAAC,CAAC;IAC9BC,MAAM,EAAEC,aAAa,CAACjJ,YAAY,CAACQ,OAAO,CAAC,CAAC;EAC9C,CAAC;EACD;EACA;EACA;EACA;EACA,IAAI0I,mBAAmB;EACvB,IAAIC,aAAa;EACjB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAC5B,IAAIC,eAAe,GAAG,CAAC,CAAC;EACxB,IAAIC,qBAAqB,GAAG,CAAC,CAAC;EAC9B,IAAIC,iBAAiB,GAAG,CAAC,CAAC;EAC1B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9J,MAAM,CAAC+J,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAImC,SAAS,GAAGjM,MAAM,CAAC8J,CAAC,CAAC;IACzBiC,qBAAqB,CAACE,SAAS,CAAC,GAAGxJ,YAAY,CAACyJ,QAAQ,CAACjM,eAAe,CAACgM,SAAS,CAAC,CAAC;IACpFD,iBAAiB,CAACC,SAAS,CAAC,GAAGxJ,YAAY,CAACyJ,QAAQ,CAAC5L,UAAU,CAAC2L,SAAS,CAAC,CAAC;EAC7E;EACA,SAASE,YAAYA,CAACC,eAAe,EAAE;IACrC,OAAOA,eAAe,KAAKT,mBAAmB,GAAGC,aAAa,KAAKA,aAAa,GAAG5I,IAAI,CAACmJ,YAAY,CAACC,eAAe,CAAC,CAAC,GAAGpJ,IAAI,CAACmJ,YAAY,CAACC,eAAe,CAAC;EAC7J;EACA,SAASC,iBAAiBA,CAACD,eAAe,EAAEjD,KAAK,EAAE;IACjD,OAAO,CAACnG,IAAI,CAACsJ,aAAa,GAAGP,qBAAqB,CAAC5C,KAAK,CAAC,GAAGiD,eAAe,KAAKT,mBAAmB,GAAGE,mBAAmB,CAAC1C,KAAK,CAAC,KAAK0C,mBAAmB,CAAC1C,KAAK,CAAC,GAAGgD,YAAY,CAACC,eAAe,CAAC,CAACF,QAAQ,CAACjM,eAAe,CAACkJ,KAAK,CAAC,CAAC,CAAC,GAAGgD,YAAY,CAACC,eAAe,CAAC,CAACF,QAAQ,CAACjM,eAAe,CAACkJ,KAAK,CAAC,CAAC;EACpS;EACA,SAASoD,aAAaA,CAACH,eAAe,EAAEjD,KAAK,EAAE;IAC7C,OAAO,CAACnG,IAAI,CAACsJ,aAAa,GAAGN,iBAAiB,CAAC7C,KAAK,CAAC,GAAGiD,eAAe,KAAKT,mBAAmB,GAAGG,eAAe,CAAC3C,KAAK,CAAC,KAAK2C,eAAe,CAAC3C,KAAK,CAAC,GAAGgD,YAAY,CAACC,eAAe,CAAC,CAACF,QAAQ,CAAC5L,UAAU,CAAC6I,KAAK,CAAC,CAAC,CAAC,GAAGgD,YAAY,CAACC,eAAe,CAAC,CAACF,QAAQ,CAAC5L,UAAU,CAAC6I,KAAK,CAAC,CAAC;EAC9Q;EACA,OAAO,UAAUiD,eAAe,EAAExJ,OAAO,EAAE;IACzC+I,mBAAmB,GAAGS,eAAe;IACrCR,aAAa,GAAG,IAAI;IACpBC,mBAAmB,GAAG,CAAC,CAAC;IACxBC,eAAe,GAAG,CAAC,CAAC;IACpB,OAAO3I,UAAU,IAAIA,UAAU,CAACtF,QAAQ,CAAC;MACvCuO,eAAe,EAAEA,eAAe;MAChCvE,SAAS,EAAE7E,IAAI,CAACwJ,WAAW,CAACJ,eAAe,CAAC;MAC5C;MACAK,UAAU,EAAE7J,OAAO,GAAGA,OAAO,CAACN,IAAI,GAAG;IACvC,CAAC,EAAE2I,UAAU,CAAC,EAAEZ,OAAO,CAAC;EAC1B,CAAC;EACD;AACF;AACA;AACA;AACA;EACE,SAASK,KAAKA,CAACgC,GAAG,EAAEN,eAAe,EAAE;IACnCA,eAAe,IAAI,IAAI,KAAKA,eAAe,GAAGT,mBAAmB,CAAC;IAClE,OAAO3I,IAAI,CAAC2J,QAAQ,CAAC,CAAC,CAACzI,GAAG,CAAClB,IAAI,CAAC4J,iBAAiB,CAACF,GAAG,IAAI,CAAC,CAAC,EAAEN,eAAe,CAAC;EAC/E;EACA;AACF;AACA;AACA;AACA;EACE,SAASzB,eAAeA,CAAC+B,GAAG,EAAEN,eAAe,EAAE;IAC7CA,eAAe,IAAI,IAAI,KAAKA,eAAe,GAAGT,mBAAmB,CAAC;IAClEe,GAAG,GAAGA,GAAG,IAAI,CAAC;IACd,IAAIG,OAAO,GAAG7J,IAAI,CAAC8J,gBAAgB,CAACJ,GAAG,CAAC;IACxC,IAAI,CAACG,OAAO,EAAE;MACZ,IAAIE,QAAQ,GAAG/J,IAAI,CAAC4J,iBAAiB,CAACF,GAAG,CAAC;MAC1C,OAAOK,QAAQ,IAAI,CAAC,GAAG/J,IAAI,CAAC2J,QAAQ,CAAC,CAAC,CAACzI,GAAG,CAAC6I,QAAQ,EAAEX,eAAe,CAAC,GAAGY,SAAS;IACnF;IACA,IAAIC,GAAG,GAAGjK,IAAI,CAACkB,GAAG,CAAC2I,OAAO,CAAChH,IAAI,EAAEuG,eAAe,CAAC;IACjD,IAAIc,WAAW,GAAGL,OAAO,IAAIA,OAAO,CAACK,WAAW;IAChD,OAAOA,WAAW,GAAGA,WAAW,CAACC,UAAU,CAACF,GAAG,CAAC,GAAGA,GAAG;EACxD;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASxL,KAAKA,CAAC2L,SAAS,EAAEhB,eAAe,EAAE;IACzC,IAAI9E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCtI,cAAc,CAAC,WAAW,EAAE,8CAA8C,CAAC;IAC7E;IACAkN,eAAe,IAAI,IAAI,KAAKA,eAAe,GAAGT,mBAAmB,CAAC;IAClE,IAAIlK,KAAK,GAAGuB,IAAI,CAACqK,aAAa,CAACjB,eAAe,EAAE,OAAO,CAAC;IACxD,IAAIkB,WAAW,GAAG7L,KAAK,IAAIA,KAAK,CAAC8G,IAAI;IACrC,IAAIgF,OAAO,GAAG9L,KAAK,IAAIA,KAAK,CAAC8L,OAAO;IACpC,IAAIC,SAAS,GAAGnB,iBAAiB,CAACD,eAAe,EAAEvM,MAAM,CAAC,CAAC4N,YAAY,CAAC,CAAC;IACzEH,WAAW,IAAI,IAAI,KAAKE,SAAS,CAACjF,IAAI,GAAG+E,WAAW,CAAC;IACrDC,OAAO,IAAI,IAAI,KAAKC,SAAS,CAACD,OAAO,GAAGA,OAAO,CAAC;IAChD,IAAIG,GAAG,GAAG;MACRC,YAAY,EAAEjQ,QAAQ,CAAC4P,WAAW,CAAC,GAAGA,WAAW,GAAG;IACtD,CAAC;IACD,IAAIM,UAAU,GAAGrB,aAAa,CAACH,eAAe,EAAEvM,MAAM,CAAC;IACvD;IACA;IACA;IACA,IAAIgO,SAAS,GAAG1P,gBAAgB,CAAC2P,eAAe,CAACF,UAAU,EAAE,IAAI,EAAEF,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;IACpFG,SAAS,CAACE,IAAI,GAAGH,UAAU,CAACI,UAAU,CAAC,MAAM,CAAC,GAAGrQ,SAAS,CAAC8E,YAAY,CAACwL,iBAAiB,CAAC7B,eAAe,EAAEvM,MAAM,CAAC,EAAEzB,eAAe,CAAC4E,IAAI,EAAEoJ,eAAe,CAAC,CAAC,GAAG,IAAI;IAClK,IAAI3C,UAAU,GAAGtL,gBAAgB,CAAC+P,gBAAgB,CAACN,UAAU,EAAEF,GAAG,EAAE,KAAK,CAAC;IAC1ES,iBAAiB,CAACf,SAAS,EAAEI,SAAS,CAAC;IACvCA,SAAS,GAAGzO,gCAAgC,CAACyO,SAAS,EAAEK,SAAS,EAAEpE,UAAU,CAAC;IAC9E2D,SAAS,IAAIgB,mBAAmB,CAACZ,SAAS,EAAEJ,SAAS,CAAC;IACtDI,SAAS,CAACa,MAAM,GAAG,IAAI;IACvB,OAAOb,SAAS;EAClB;EACA;AACF;AACA;AACA;AACA;EACE,SAAS5C,aAAaA,CAACwC,SAAS,EAAEhB,eAAe,EAAE;IACjD,IAAI9E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCtI,cAAc,CAAC,mBAAmB,EAAE,8CAA8C,CAAC;IACrF;IACAkN,eAAe,IAAI,IAAI,KAAKA,eAAe,GAAGT,mBAAmB,CAAC;IAClE,IAAI6B,SAAS,GAAGnB,iBAAiB,CAACD,eAAe,EAAExM,QAAQ,CAAC,CAAC6N,YAAY,CAAC,CAAC;IAC3E,IAAIG,UAAU,GAAGrB,aAAa,CAACH,eAAe,EAAExM,QAAQ,CAAC;IACzD,IAAIiO,SAAS,GAAG1P,gBAAgB,CAAC2P,eAAe,CAACF,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACpFC,SAAS,CAACE,IAAI,GAAGH,UAAU,CAACI,UAAU,CAAC,MAAM,CAAC,GAAGpQ,SAAS,CAAC6E,YAAY,CAACwL,iBAAiB,CAAC7B,eAAe,EAAExM,QAAQ,CAAC,EAAE6C,YAAY,CAACwL,iBAAiB,CAAC7B,eAAe,EAAEvM,MAAM,CAAC,EAAEzB,eAAe,CAAC4E,IAAI,EAAEoJ,eAAe,CAAC,CAAC,GAAG,IAAI;IAC7N,IAAI3C,UAAU,GAAGtL,gBAAgB,CAAC+P,gBAAgB,CAACN,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1EO,iBAAiB,CAACf,SAAS,EAAEI,SAAS,CAAC;IACvCA,SAAS,GAAGzO,gCAAgC,CAACyO,SAAS,EAAEK,SAAS,EAAEpE,UAAU,CAAC;IAC9E2D,SAAS,IAAIgB,mBAAmB,CAACZ,SAAS,EAAEJ,SAAS,CAAC;IACtDI,SAAS,CAACa,MAAM,GAAG,IAAI;IACvB,OAAOb,SAAS;EAClB;EACA,SAASY,mBAAmBA,CAACZ,SAAS,EAAEc,KAAK,EAAE;IAC7C,KAAK,IAAIC,GAAG,IAAID,KAAK,EAAE;MACrB,IAAI9Q,MAAM,CAAC8Q,KAAK,EAAEC,GAAG,CAAC,EAAE;QACtBf,SAAS,CAACe,GAAG,CAAC,GAAGD,KAAK,CAACC,GAAG,CAAC;MAC7B;IACF;EACF;EACA,SAASJ,iBAAiBA,CAACG,KAAK,EAAEd,SAAS,EAAE;IAC3C;IACA;IACA;IACA,IAAIc,KAAK,EAAE;MACTA,KAAK,CAAC9F,QAAQ,KAAKgF,SAAS,CAAChF,QAAQ,GAAG8F,KAAK,CAAC9F,QAAQ,CAAC;MACvD8F,KAAK,CAACE,YAAY,KAAKhB,SAAS,CAACgB,YAAY,GAAGF,KAAK,CAACE,YAAY,CAAC;IACrE;EACF;EACA;AACF;AACA;AACA;EACE,SAAS3D,MAAMA,CAAC4D,UAAU,EAAErC,eAAe,EAAE;IAC3CA,eAAe,IAAI,IAAI,KAAKA,eAAe,GAAGT,mBAAmB,CAAC;IAClE,IAAInO,MAAM,CAAC6B,iBAAiB,EAAEoP,UAAU,CAAC,EAAE;MACzC,IAAIC,OAAO,GAAG1L,IAAI,CAACqK,aAAa,CAACjB,eAAe,EAAE,OAAO,CAAC;MAC1D,OAAOsC,OAAO,GAAGA,OAAO,CAACrP,iBAAiB,CAACoP,UAAU,CAAC,CAAC,GAAG,IAAI;IAChE;IACA;IACA;IACA,IAAIjR,MAAM,CAAC8B,sBAAsB,EAAEmP,UAAU,CAAC,EAAE;MAC9C,OAAOzL,IAAI,CAACqK,aAAa,CAACjB,eAAe,EAAEqC,UAAU,CAAC;IACxD;EACF;EACA;AACF;AACA;AACA;EACE,SAAS3D,SAASA,CAAC4C,GAAG,EAAE;IACtB,IAAIvD,QAAQ,CAAC7H,IAAI,KAAK,aAAa,EAAE;MACnC,IAAIqM,QAAQ,GAAGxE,QAAQ,CAACyE,WAAW,CAAC,CAAC;MACrC,OAAOvQ,eAAe,CAACR,QAAQ,CAAC;QAC9BgR,IAAI,EAAEF;MACR,CAAC,EAAEjB,GAAG,CAAC,CAAC;IACV;EACF;EACA;AACF;AACA;EACE,SAAS3C,oBAAoBA,CAAA,EAAG;IAC9B,OAAOrI,OAAO,CAACoM,uBAAuB,CAAC,CAAC;EAC1C;EACA;AACF;AACA;AACA;EACE,SAAS9D,IAAIA,CAAC0C,GAAG,EAAE;IACjB,OAAOvP,gBAAgB,CAAC4Q,OAAO,CAACrB,GAAG,EAAEhL,OAAO,CAAC;EAC/C;AACF;AACA,SAASgJ,aAAaA,CAAC1I,IAAI,EAAE;EAC3B,IAAIgM,SAAS,GAAG,CAAC,CAAC;EAClBlR,IAAI,CAACkF,IAAI,CAACiM,UAAU,EAAE,UAAUC,OAAO,EAAE;IACvC,IAAIrC,OAAO,GAAG7J,IAAI,CAAC8J,gBAAgB,CAACoC,OAAO,CAAC;IAC5C,IAAI,CAACrC,OAAO,CAACsC,YAAY,EAAE;MACzB,IAAIC,QAAQ,GAAGvC,OAAO,CAACuC,QAAQ;MAC/B,IAAIC,QAAQ,GAAGL,SAAS,CAACI,QAAQ,CAAC,GAAGJ,SAAS,CAACI,QAAQ,CAAC,IAAI,EAAE;MAC9DC,QAAQ,CAACxC,OAAO,CAACyC,aAAa,CAAC,GAAGtM,IAAI,CAAC4J,iBAAiB,CAACsC,OAAO,CAAC;IACnE;EACF,CAAC,CAAC;EACF,OAAOF,SAAS;AAClB;AACA,SAASvL,kBAAkBA,CAACd,GAAG,EAAE4M,QAAQ,EAAE1H,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAE7E,KAAK,EAAEF,IAAI,EAAE;EACxF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACiD,QAAQ,EAAE;IACb/C,KAAK,CAACQ,MAAM,CAAC6L,QAAQ,CAAC;IACtB;EACF;EACA,IAAItO,EAAE,GAAGuO,kBAAkB,CAAC7M,GAAG,EAAE4M,QAAQ,EAAE1H,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAE7E,KAAK,CAAC;EACnFjC,EAAE,IAAI+B,IAAI,CAACyM,gBAAgB,CAAC5H,SAAS,EAAE5G,EAAE,CAAC;EAC1CA,EAAE,IAAI/C,mBAAmB,CAAC+C,EAAE,EAAEgF,QAAQ,CAACyJ,KAAK,EAAEzJ,QAAQ,CAAC0J,SAAS,EAAE1J,QAAQ,CAAC2J,gBAAgB,CAAC;EAC5F,OAAO3O,EAAE;AACX;AACA,SAASuO,kBAAkBA,CAAC7M,GAAG,EAAE4M,QAAQ,EAAE1H,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAE7E,KAAK,EAAE;EAClF,IAAIoE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC/J,MAAM,CAACwI,QAAQ,EAAE,mDAAmD,CAAC;EACvE;EACA,IAAI4J,eAAe,GAAG,CAAC,CAAC;EACxB,IAAI9L,KAAK,GAAGwL,QAAQ;EACpB,IAAIA,QAAQ,IAAIO,kBAAkB,CAACP,QAAQ,EAAEtJ,QAAQ,EAAE8B,WAAW;EAClE;EACA;EACA;EACA;EACA;EACA;EAAA,EACE;IACA;IACA8H,eAAe,GAAG9R,OAAO,CAACmF,KAAK,CAAC6M,WAAW,CAAC,CAAC,EAAER,QAAQ,CAAC;IACxDA,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIvH,MAAM,GAAG,CAACuH,QAAQ;EACtB,IAAItO,EAAE,GAAGsO,QAAQ;EACjB,IAAI,CAACtO,EAAE,EAAE;IACPA,EAAE,GAAG+E,QAAQ,CAACC,QAAQ,CAAC;IACvB,IAAIlC,KAAK,EAAE;MACT3C,WAAW,CAAC2C,KAAK,EAAE9C,EAAE,CAAC;IACxB;EACF,CAAC,MAAM;IACL;IACA;IACA;IACAA,EAAE,CAAC+O,WAAW,CAAC,CAAC;EAClB;EACA;EACA,IAAI/J,QAAQ,CAACgK,KAAK,KAAK,KAAK,EAAE;IAC5BhP,EAAE,CAACiP,eAAe,GAAG,IAAI;EAC3B,CAAC,MAAM,IAAIjP,EAAE,CAACiP,eAAe,EAAE;IAC7BjP,EAAE,CAACiP,eAAe,GAAG,KAAK;EAC5B;EACAzP,iBAAiB,CAACP,MAAM,CAACgI,GAAG,GAAGzH,iBAAiB,CAACP,MAAM,CAACiQ,MAAM,GAAG1P,iBAAiB,CAACN,QAAQ,CAAC+H,GAAG,GAAGzH,iBAAiB,CAACN,QAAQ,CAACgQ,MAAM,GAAG1P,iBAAiB,CAACL,IAAI,CAAC8H,GAAG,GAAGzH,iBAAiB,CAACL,IAAI,CAAC+P,MAAM,GAAG1P,iBAAiB,CAACJ,MAAM,CAAC6H,GAAG,GAAGzH,iBAAiB,CAACJ,MAAM,CAAC8P,MAAM,GAAG,IAAI;EACxQ1P,iBAAiB,CAAC2P,QAAQ,GAAG,KAAK;EAClCC,0BAA0B,CAACpP,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAEC,MAAM,EAAEvH,iBAAiB,CAAC;EAC3F6P,wBAAwB,CAACrP,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAEC,MAAM,CAAC;EACtEJ,cAAc,CAACjF,GAAG,EAAE1B,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAExF,iBAAiB,EAAEsH,WAAW,EAAEC,MAAM,CAAC;EACpF;EACA;EACA;EACAxK,MAAM,CAACyI,QAAQ,EAAE,MAAM,CAAC,KAAK1G,gBAAgB,CAAC0B,EAAE,CAAC,CAACsP,IAAI,GAAGtK,QAAQ,CAACsK,IAAI,CAAC;EACvE,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9J,MAAM,CAAC+J,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAImC,SAAS,GAAGjM,MAAM,CAAC8J,CAAC,CAAC;IACzB,IAAImC,SAAS,KAAKpM,MAAM,EAAE;MACxB,IAAI2Q,aAAa,GAAGtG,mBAAmB,CAACjE,QAAQ,EAAEgG,SAAS,CAAC;MAC5D,IAAIwE,aAAa,GAAGC,0BAA0B,CAACzK,QAAQ,EAAEuK,aAAa,EAAEvE,SAAS,CAAC;MAClF/C,eAAe,CAAC+C,SAAS,EAAEhL,EAAE,EAAEuP,aAAa,EAAEC,aAAa,EAAEhQ,iBAAiB,CAAC;IACjF;EACF;EACAiJ,OAAO,CAACzI,EAAE,EAAEgF,QAAQ,EAAE8B,WAAW,CAAC;EAClC,IAAI9B,QAAQ,CAAC3D,IAAI,KAAK,OAAO,EAAE;IAC7BqO,aAAa,CAAChO,GAAG,EAAE1B,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,CAAC;EAC1D;EACA,IAAI8H,eAAe,IAAI,CAAC,EAAE;IACxB3M,KAAK,CAAC0N,SAAS,CAAC3P,EAAE,EAAE4O,eAAe,CAAC;EACtC,CAAC,MAAM;IACL3M,KAAK,CAACK,GAAG,CAACtC,EAAE,CAAC;EACf;EACA,OAAOA,EAAE;AACX;AACA;AACA,SAAS6O,kBAAkBA,CAAC7O,EAAE,EAAEgF,QAAQ,EAAE8B,WAAW,EAAE;EACrD,IAAI8I,OAAO,GAAGtR,gBAAgB,CAAC0B,EAAE,CAAC;EAClC,IAAI6P,YAAY,GAAG7K,QAAQ,CAAC3D,IAAI;EAChC,IAAIyO,aAAa,GAAG9K,QAAQ,CAACjE,KAAK;EAClC,IAAIgP,aAAa,GAAG/K,QAAQ,CAACxE,KAAK;EAClC;IACE;IACA;IACAsG,WAAW,CAACkJ,4BAA4B,CAAC;IACzC;IAAA,GACGH,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAKD,OAAO,CAACpJ,iBAAiB,IAAIqJ,YAAY,KAAK,MAAM,IAAII,cAAc,CAACH,aAAa,CAAC,IAAItK,WAAW,CAACsK,aAAa,CAAC,KAAKF,OAAO,CAACjK,cAAc,IAAIkK,YAAY,KAAK,OAAO,IAAItT,MAAM,CAACwT,aAAa,EAAE,OAAO,CAAC,IAAIA,aAAa,CAACjK,KAAK,KAAK8J,OAAO,CAAC/J;IAC5R;IACA;IACA;IACA;IACA;EAAA;AAEJ;AACA,SAASwJ,wBAAwBA,CAACrP,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAEC,MAAM,EAAE;EAC9E;EACA;EACA;EACA,IAAImJ,WAAW,GAAGlL,QAAQ,CAAChC,QAAQ;EACnC,IAAIkN,WAAW,KAAK,KAAK,EAAE;IACzB,IAAIlQ,EAAE,IAAIA,EAAE,CAACmQ,WAAW,CAAC,CAAC,EAAE;MAC1BnQ,EAAE,CAACoD,cAAc,CAAC,CAAC;IACrB;EACF,CAAC,MAAM,IAAI8M,WAAW,EAAE;IACtB,IAAIlN,QAAQ,GAAGhD,EAAE,CAACmQ,WAAW,CAAC,CAAC;IAC/B,IAAInN,QAAQ,IAAI6L,kBAAkB,CAAC7L,QAAQ,EAAEkN,WAAW,EAAEpJ,WAAW,CAAC,EAAE;MACtE9D,QAAQ,GAAG,IAAI;IACjB;IACA,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAG+B,QAAQ,CAACmL,WAAW,CAAC;MAChC,IAAI7J,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC/J,MAAM,CAACuD,MAAM,CAACiD,QAAQ,CAAC,EAAE,iEAAiE,GAAGA,QAAQ,CAAC3B,IAAI,GAAG,GAAG,CAAC;MACnH;MACArB,EAAE,CAACmD,WAAW,CAACH,QAAQ,CAAC;IAC1B;IACA2D,cAAc,CAAC,IAAI,EAAE3D,QAAQ,EAAE4D,SAAS,EAAEsJ,WAAW,EAAE,IAAI,EAAEpJ,WAAW,EAAEC,MAAM,CAAC;EACnF;EACA;AACF;AACA,SAASqI,0BAA0BA,CAACpP,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAEC,MAAM,EAAEF,cAAc,EAAE;EAChG;EACA,IAAI7G,EAAE,CAAC0D,OAAO,EAAE;IACd;EACF;EACA;EACA0M,aAAa,CAACpL,QAAQ,EAAE,IAAI,EAAE6B,cAAc,CAAC;EAC7CuJ,aAAa,CAACpL,QAAQ,EAAErG,QAAQ,EAAEkI,cAAc,CAAC;EACjD;EACA;EACA;EACA;EACA;EACA;EACA,IAAIwJ,cAAc,GAAGxJ,cAAc,CAAC5H,MAAM,CAACiQ,MAAM;EACjD,IAAIoB,gBAAgB,GAAGzJ,cAAc,CAAC3H,QAAQ,CAACgQ,MAAM;EACrD,IAAIqB,YAAY,GAAG1J,cAAc,CAAC1H,IAAI,CAAC+P,MAAM;EAC7C,IAAIsB,cAAc,GAAG3J,cAAc,CAACzH,MAAM,CAAC8P,MAAM;EACjD,IAAImB,cAAc,IAAI,IAAI,IAAIC,gBAAgB,IAAI,IAAI,IAAIE,cAAc,IAAI,IAAI,IAAID,YAAY,IAAI,IAAI,EAAE;IACxG,IAAIE,WAAW,GAAGzQ,EAAE,CAAC0Q,cAAc,CAAC,CAAC;IACrC,IAAIL,cAAc,KAAK,KAAK,EAAE;MAC5BI,WAAW,IAAIzQ,EAAE,CAAC2Q,iBAAiB,CAAC,CAAC;IACvC,CAAC,MAAM;MACLN,cAAc,GAAGxJ,cAAc,CAAC5H,MAAM,CAACiQ,MAAM,GAAGmB,cAAc,IAAI;QAChEhP,IAAI,EAAE;MACR,CAAC;MACD,IAAI,CAACoP,WAAW,EAAE;QAChBA,WAAW,GAAG1L,QAAQ,CAACsL,cAAc,CAAC;QACtCrQ,EAAE,CAAC4Q,cAAc,CAACH,WAAW,CAAC;MAChC,CAAC,MAAM;QACL;QACA;QACAA,WAAW,CAAC1B,WAAW,CAAC,CAAC;MAC3B;MACApI,cAAc,CAAC,IAAI,EAAE8J,WAAW,EAAE7J,SAAS,EAAEyJ,cAAc,EAAE,IAAI,EAAEvJ,WAAW,EAAEC,MAAM,CAAC;MACvF,IAAI8J,iBAAiB,GAAGR,cAAc,IAAIA,cAAc,CAAC7P,KAAK;MAC9D,KAAK,IAAIqI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9J,MAAM,CAAC+J,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAImC,SAAS,GAAGjM,MAAM,CAAC8J,CAAC,CAAC;QACzB,IAAImC,SAAS,KAAKpM,MAAM,EAAE;UACxB,IAAIkS,kBAAkB,GAAGjK,cAAc,CAACmE,SAAS,CAAC,CAACkE,MAAM;UACzDjH,eAAe,CAAC+C,SAAS,EAAEyF,WAAW,EAAEK,kBAAkB,EAAErB,0BAA0B,CAACY,cAAc,EAAES,kBAAkB,EAAE9F,SAAS,CAAC,EAAE,IAAI,CAAC;QAC9I;MACF;MACA6F,iBAAiB,GAAGJ,WAAW,CAAC5I,KAAK,CAAC,CAAC,GAAG4I,WAAW,CAACM,UAAU,CAAC,CAAC;IACpE;EACF;AACF;AACA,SAASX,aAAaA,CAACpL,QAAQ,EAAEkD,KAAK,EAAErB,cAAc,EAAE;EACtD,IAAImK,QAAQ,GAAG,CAAC9I,KAAK,GAAGlD,QAAQ,GAAGiE,mBAAmB,CAACjE,QAAQ,EAAEkD,KAAK,CAAC;EACvE,IAAId,QAAQ,GAAG,CAACc,KAAK,GAAGlD,QAAQ,CAACxE,KAAK,GAAGiP,0BAA0B,CAACzK,QAAQ,EAAEgM,QAAQ,EAAErS,QAAQ,CAAC;EACjG,IAAIsS,MAAM,GAAGjM,QAAQ,CAAC3D,IAAI;EAC1B,IAAI6P,KAAK,GAAGF,QAAQ,GAAGA,QAAQ,CAACxI,UAAU,GAAG,IAAI;EACjD,IAAI6H,cAAc,GAAGrL,QAAQ,CAACyL,WAAW;EACzC,IAAIU,QAAQ,GAAG,CAACd,cAAc,GAAG,IAAI,GAAG,CAACnI,KAAK,GAAGmI,cAAc,GAAGpH,mBAAmB,CAACoH,cAAc,EAAEnI,KAAK,CAAC;EAC5G,IAAId,QAAQ;EACZ;EACA;EACAP,cAAc,CAACsI,QAAQ,IAAIpR,oBAAoB,CAACqJ,QAAQ,EAAE6J,MAAM,EAAE,CAAC,CAACC,KAAK,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IACvFtK,cAAc,CAACsI,QAAQ,GAAG,IAAI;IAC9B,IAAIiC,aAAa,GAAGpT,6BAA6B,CAACoJ,QAAQ,EAAE6J,MAAM,EAAE,CAAC/I,KAAK,CAAC;IAC3E;IACA;IACA;IACA,IAAI,CAACgJ,KAAK,IAAIE,aAAa,CAAC5I,UAAU,EAAE;MACtC0I,KAAK,GAAGE,aAAa,CAAC5I,UAAU;IAClC;IACA,IAAI,CAAC2I,QAAQ,IAAIC,aAAa,CAACX,WAAW,EAAE;MAC1CU,QAAQ,GAAGC,aAAa,CAACX,WAAW;IACtC;EACF;EACA,IAAI,CAACvI,KAAK,IAAIiJ,QAAQ,EAAE;IACtB,IAAIE,gBAAgB,GAAGF,QAAQ;IAC/B;IACA,CAACE,gBAAgB,CAAChQ,IAAI,KAAKgQ,gBAAgB,CAAChQ,IAAI,GAAG,MAAM,CAAC;IAC1D,IAAIgF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA/J,MAAM,CAAC6U,gBAAgB,CAAChQ,IAAI,KAAK,MAAM,EAAE,iCAAiC,CAAC;IAC7E;EACF;EACA,IAAIiO,IAAI,GAAG,CAACpH,KAAK,GAAGrB,cAAc,CAAC5H,MAAM,GAAG4H,cAAc,CAACqB,KAAK,CAAC;EACjEoH,IAAI,CAACrI,GAAG,GAAGiK,KAAK;EAChB5B,IAAI,CAACJ,MAAM,GAAGiC,QAAQ;AACxB;AACA,SAASlI,mBAAmBA,CAACjE,QAAQ,EAAEkD,KAAK,EAAE;EAC5C,OAAO,CAACA,KAAK,GAAGlD,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,CAACkD,KAAK,CAAC,GAAG,IAAI;AAC9D;AACA,SAASuH,0BAA0BA,CAAC6B,iBAAiB,EAAEC,WAAW,EAAErJ,KAAK,EAAE;EACzE,IAAI1H,KAAK,GAAG+Q,WAAW,IAAIA,WAAW,CAAC/Q,KAAK;EAC5C,IAAIA,KAAK,IAAI,IAAI,IAAI0H,KAAK,KAAKvJ,QAAQ,IAAI2S,iBAAiB,EAAE;IAC5D9Q,KAAK,GAAG8Q,iBAAiB,CAAC3H,aAAa;EACzC;EACA,OAAOnJ,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkP,aAAaA,CAAChO,GAAG,EAAE1B,EAAE,EAAE4G,SAAS,EAAE5B,QAAQ,EAAE8B,WAAW,EAAE;EAChE,IAAI0K,WAAW,GAAGxM,QAAQ,CAACyM,QAAQ;EACnC,IAAIC,MAAM,GAAGF,WAAW,GAAGA,WAAW,CAAC1I,MAAM,GAAG,CAAC;EACjD,IAAI4G,aAAa,GAAG1K,QAAQ,CAAC2M,cAAc;EAC3C;EACA,IAAIC,MAAM,GAAGlC,aAAa,KAAK,QAAQ,IAAI1K,QAAQ,CAAC6M,kBAAkB;EACtE,IAAIC,QAAQ,GAAGpC,aAAa,KAAK,KAAK;EACtC;EACA,IAAI,CAACgC,MAAM,IAAI,CAACE,MAAM,IAAI,CAACE,QAAQ,EAAE;IACnC;EACF;EACA,IAAIF,MAAM,EAAE;IACVG,iBAAiB,CAAC;MAChBrQ,GAAG,EAAEA,GAAG;MACRsQ,WAAW,EAAEhS,EAAE,CAACyR,QAAQ,CAAC,CAAC,IAAI,EAAE;MAChCD,WAAW,EAAEA,WAAW,IAAI,EAAE;MAC9B5K,SAAS,EAAEA,SAAS;MACpBE,WAAW,EAAEA,WAAW;MACxB7E,KAAK,EAAEjC;IACT,CAAC,CAAC;IACF;EACF;EACA8R,QAAQ,IAAI9R,EAAE,CAACoC,SAAS,CAAC,CAAC;EAC1B;EACA;EACA,IAAI6P,KAAK,GAAG,CAAC;EACb,OAAOA,KAAK,GAAGP,MAAM,EAAEO,KAAK,EAAE,EAAE;IAC9B,IAAIC,QAAQ,GAAGV,WAAW,CAACS,KAAK,CAAC;IACjC,IAAIE,QAAQ,GAAGnS,EAAE,CAACoS,OAAO,CAACH,KAAK,CAAC;IAChC,IAAIC,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAACrR,MAAM,IAAI,IAAI,EAAE;QAC3B;QACA;QACAqR,QAAQ,CAACrR,MAAM,GAAG,KAAK;MACzB;MACA0N,kBAAkB,CAAC7M,GAAG,EAAEyQ,QAAQ,EAAEvL,SAAS,EAAEsL,QAAQ,EAAEpL,WAAW,EAAE9G,EAAE,CAAC;IACzE,CAAC,MAAM;MACL,IAAIqG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC/J,MAAM,CAAC2V,QAAQ,EAAE,0DAA0D,GAAG,oDAAoD,CAAC;MACrI;MACA;MACA;MACA;MACA;MACAA,QAAQ,CAACtR,MAAM,GAAG,IAAI;IACxB;EACF;EACA,KAAK,IAAIgI,CAAC,GAAG7I,EAAE,CAACqS,UAAU,CAAC,CAAC,GAAG,CAAC,EAAExJ,CAAC,IAAIoJ,KAAK,EAAEpJ,CAAC,EAAE,EAAE;IACjD,IAAIyJ,KAAK,GAAGtS,EAAE,CAACoS,OAAO,CAACvJ,CAAC,CAAC;IACzB0J,oBAAoB,CAACvS,EAAE,EAAEsS,KAAK,EAAExL,WAAW,CAAC;EAC9C;AACF;AACA,SAASyL,oBAAoBA,CAACtQ,KAAK,EAAEqQ,KAAK,EAAExL,WAAW,EAAE;EACvD;EACA;EACA;EACAwL,KAAK,IAAI/T,oBAAoB,CAAC+T,KAAK,EAAEhU,gBAAgB,CAAC2D,KAAK,CAAC,CAACW,MAAM,EAAEkE,WAAW,CAAC;AACnF;AACA,SAASiL,iBAAiBA,CAAC9H,OAAO,EAAE;EAClC,IAAI5M,UAAU,CAAC4M,OAAO,CAAC+H,WAAW,EAAE/H,OAAO,CAACuH,WAAW,EAAEgB,MAAM,EAAEA,MAAM,EAAEvI,OAAO,CAAC,CAAC3H,GAAG,CAACmQ,gBAAgB,CAAC,CAAC5P,MAAM,CAAC4P,gBAAgB,CAAC,CAAChQ,MAAM,CAACiQ,aAAa,CAAC,CAAC3P,OAAO,CAAC,CAAC;AAClK;AACA,SAASyP,MAAMA,CAACG,IAAI,EAAE7O,GAAG,EAAE;EACzB,IAAIc,IAAI,GAAG+N,IAAI,IAAIA,IAAI,CAAC/N,IAAI;EAC5B,OAAOA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGrF,iBAAiB,GAAGuE,GAAG;AACtD;AACA,SAAS2O,gBAAgBA,CAACG,QAAQ,EAAEC,QAAQ,EAAE;EAC5C,IAAI5I,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAI6I,WAAW,GAAGF,QAAQ,IAAI,IAAI,GAAG3I,OAAO,CAACuH,WAAW,CAACoB,QAAQ,CAAC,GAAG,IAAI;EACzE,IAAIN,KAAK,GAAGO,QAAQ,IAAI,IAAI,GAAG5I,OAAO,CAAC+H,WAAW,CAACa,QAAQ,CAAC,GAAG,IAAI;EACnEtE,kBAAkB,CAACtE,OAAO,CAACvI,GAAG,EAAE4Q,KAAK,EAAErI,OAAO,CAACrD,SAAS,EAAEkM,WAAW,EAAE7I,OAAO,CAACnD,WAAW,EAAEmD,OAAO,CAAChI,KAAK,CAAC;AAC5G;AACA,SAASyQ,aAAaA,CAACG,QAAQ,EAAE;EAC/B,IAAI5I,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAIqI,KAAK,GAAGrI,OAAO,CAAC+H,WAAW,CAACa,QAAQ,CAAC;EACzCP,KAAK,IAAI/T,oBAAoB,CAAC+T,KAAK,EAAEhU,gBAAgB,CAACgU,KAAK,CAAC,CAAC1P,MAAM,EAAEqH,OAAO,CAACnD,WAAW,CAAC;AAC3F;AACA;AACA;AACA;AACA,SAAStB,WAAWA,CAACzE,KAAK,EAAE;EAC1B;EACA,OAAOA,KAAK,KAAKA,KAAK,CAACwE,QAAQ,IAAIxE,KAAK,CAACgS,CAAC,CAAC;AAC7C;AACA,SAAS9C,cAAcA,CAAClP,KAAK,EAAE;EAC7B,OAAOA,KAAK,KAAKxE,MAAM,CAACwE,KAAK,EAAE,UAAU,CAAC,IAAIxE,MAAM,CAACwE,KAAK,EAAE,GAAG,CAAC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}