{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../../chart/helper/SymbolDraw.js';\nimport * as numberUtil from '../../util/number.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isFunction, map, filter, curry, extend } from 'zrender/lib/core/util.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nfunction updateMarkerLayout(mpData, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  mpData.each(function (idx) {\n    var itemModel = mpData.getItemModel(idx);\n    var point;\n    var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n    var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n    if (!isNaN(xPx) && !isNaN(yPx)) {\n      point = [xPx, yPx];\n    }\n    // Chart like bar may have there own marker positioning logic\n    else if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));\n    } else if (coordSys) {\n      var x = mpData.get(coordSys.dimensions[0], idx);\n      var y = mpData.get(coordSys.dimensions[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n    mpData.setItemLayout(idx, point);\n  });\n}\nvar MarkPointView = /** @class */function (_super) {\n  __extends(MarkPointView, _super);\n  function MarkPointView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointView.type;\n    return _this;\n  }\n  MarkPointView.prototype.updateTransform = function (markPointModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mpModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markPoint');\n      if (mpModel) {\n        updateMarkerLayout(mpModel.getData(), seriesModel, api);\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkPointView.prototype.renderSeries = function (seriesModel, mpModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var symbolDrawMap = this.markerGroupMap;\n    var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw());\n    var mpData = createData(coordSys, seriesModel, mpModel);\n    // FIXME\n    mpModel.setData(mpData);\n    updateMarkerLayout(mpModel.getData(), seriesModel, api);\n    mpData.each(function (idx) {\n      var itemModel = mpData.getItemModel(idx);\n      var symbol = itemModel.getShallow('symbol');\n      var symbolSize = itemModel.getShallow('symbolSize');\n      var symbolRotate = itemModel.getShallow('symbolRotate');\n      var symbolOffset = itemModel.getShallow('symbolOffset');\n      var symbolKeepAspect = itemModel.getShallow('symbolKeepAspect');\n      // TODO: refactor needed: single data item should not support callback function\n      if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {\n        var rawIdx = mpModel.getRawValue(idx);\n        var dataParams = mpModel.getDataParams(idx);\n        if (isFunction(symbol)) {\n          symbol = symbol(rawIdx, dataParams);\n        }\n        if (isFunction(symbolSize)) {\n          // FIXME 这里不兼容 ECharts 2.x，2.x 貌似参数是整个数据？\n          symbolSize = symbolSize(rawIdx, dataParams);\n        }\n        if (isFunction(symbolRotate)) {\n          symbolRotate = symbolRotate(rawIdx, dataParams);\n        }\n        if (isFunction(symbolOffset)) {\n          symbolOffset = symbolOffset(rawIdx, dataParams);\n        }\n      }\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n      }\n      mpData.setItemVisual(idx, {\n        symbol: symbol,\n        symbolSize: symbolSize,\n        symbolRotate: symbolRotate,\n        symbolOffset: symbolOffset,\n        symbolKeepAspect: symbolKeepAspect,\n        style: style\n      });\n    });\n    // TODO Text are wrong\n    symbolDraw.updateData(mpData);\n    this.group.add(symbolDraw.group);\n    // Set host model for tooltip\n    // FIXME\n    mpData.eachItemGraphicEl(function (el) {\n      el.traverse(function (child) {\n        getECData(child).dataModel = mpModel;\n      });\n    });\n    this.markKeep(symbolDraw);\n    symbolDraw.group.silent = mpModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkPointView.type = 'markPoint';\n  return MarkPointView;\n}(MarkerView);\nfunction createData(coordSys, seriesModel, mpModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var mpData = new SeriesData(coordDimsInfos, mpModel);\n  var dataOpt = map(mpModel.get('data'), curry(markerHelper.dataTransform, seriesModel));\n  if (coordSys) {\n    dataOpt = filter(dataOpt, curry(markerHelper.dataFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  mpData.initData(dataOpt, null, dimValueGetter);\n  return mpData;\n}\nexport default MarkPointView;", "map": {"version": 3, "names": ["__extends", "SymbolDraw", "numberUtil", "SeriesData", "marker<PERSON>elper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFunction", "map", "filter", "curry", "extend", "getECData", "getVisualFromData", "updateMarkerLayout", "mpData", "seriesModel", "api", "coordSys", "coordinateSystem", "each", "idx", "itemModel", "getItemModel", "point", "xPx", "parsePercent", "get", "getWidth", "yPx", "getHeight", "isNaN", "getMarkerPosition", "getV<PERSON>ues", "dimensions", "x", "y", "dataToPoint", "setItemLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "prototype", "updateTransform", "markPointModel", "ecModel", "eachSeries", "mpModel", "getMarkerModelFromSeries", "getData", "markerGroupMap", "id", "updateLayout", "renderSeries", "seriesId", "seriesData", "symbolDrawMap", "symbolDraw", "set", "createData", "setData", "symbol", "getShallow", "symbolSize", "symbolRotate", "symbolOffset", "symbolKeepAspect", "rawIdx", "getRawValue", "dataParams", "getDataParams", "style", "getModel", "getItemStyle", "color", "fill", "setItemVisual", "updateData", "group", "add", "eachItemGraphicEl", "el", "traverse", "child", "dataModel", "<PERSON><PERSON><PERSON>", "silent", "coordDimsInfos", "coordDim", "info", "getDimensionInfo", "mapDimension", "name", "ordinalMeta", "dataOpt", "dataTransform", "dataFilter", "dimValueGetter", "createMarkerDimValueGetter", "initData"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/marker/MarkPointView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../../chart/helper/SymbolDraw.js';\nimport * as numberUtil from '../../util/number.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isFunction, map, filter, curry, extend } from 'zrender/lib/core/util.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nfunction updateMarkerLayout(mpData, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  mpData.each(function (idx) {\n    var itemModel = mpData.getItemModel(idx);\n    var point;\n    var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n    var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n    if (!isNaN(xPx) && !isNaN(yPx)) {\n      point = [xPx, yPx];\n    }\n    // Chart like bar may have there own marker positioning logic\n    else if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));\n    } else if (coordSys) {\n      var x = mpData.get(coordSys.dimensions[0], idx);\n      var y = mpData.get(coordSys.dimensions[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n    mpData.setItemLayout(idx, point);\n  });\n}\nvar MarkPointView = /** @class */function (_super) {\n  __extends(MarkPointView, _super);\n  function MarkPointView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointView.type;\n    return _this;\n  }\n  MarkPointView.prototype.updateTransform = function (markPointModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mpModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markPoint');\n      if (mpModel) {\n        updateMarkerLayout(mpModel.getData(), seriesModel, api);\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkPointView.prototype.renderSeries = function (seriesModel, mpModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var symbolDrawMap = this.markerGroupMap;\n    var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw());\n    var mpData = createData(coordSys, seriesModel, mpModel);\n    // FIXME\n    mpModel.setData(mpData);\n    updateMarkerLayout(mpModel.getData(), seriesModel, api);\n    mpData.each(function (idx) {\n      var itemModel = mpData.getItemModel(idx);\n      var symbol = itemModel.getShallow('symbol');\n      var symbolSize = itemModel.getShallow('symbolSize');\n      var symbolRotate = itemModel.getShallow('symbolRotate');\n      var symbolOffset = itemModel.getShallow('symbolOffset');\n      var symbolKeepAspect = itemModel.getShallow('symbolKeepAspect');\n      // TODO: refactor needed: single data item should not support callback function\n      if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {\n        var rawIdx = mpModel.getRawValue(idx);\n        var dataParams = mpModel.getDataParams(idx);\n        if (isFunction(symbol)) {\n          symbol = symbol(rawIdx, dataParams);\n        }\n        if (isFunction(symbolSize)) {\n          // FIXME 这里不兼容 ECharts 2.x，2.x 貌似参数是整个数据？\n          symbolSize = symbolSize(rawIdx, dataParams);\n        }\n        if (isFunction(symbolRotate)) {\n          symbolRotate = symbolRotate(rawIdx, dataParams);\n        }\n        if (isFunction(symbolOffset)) {\n          symbolOffset = symbolOffset(rawIdx, dataParams);\n        }\n      }\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n      }\n      mpData.setItemVisual(idx, {\n        symbol: symbol,\n        symbolSize: symbolSize,\n        symbolRotate: symbolRotate,\n        symbolOffset: symbolOffset,\n        symbolKeepAspect: symbolKeepAspect,\n        style: style\n      });\n    });\n    // TODO Text are wrong\n    symbolDraw.updateData(mpData);\n    this.group.add(symbolDraw.group);\n    // Set host model for tooltip\n    // FIXME\n    mpData.eachItemGraphicEl(function (el) {\n      el.traverse(function (child) {\n        getECData(child).dataModel = mpModel;\n      });\n    });\n    this.markKeep(symbolDraw);\n    symbolDraw.group.silent = mpModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkPointView.type = 'markPoint';\n  return MarkPointView;\n}(MarkerView);\nfunction createData(coordSys, seriesModel, mpModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var mpData = new SeriesData(coordDimsInfos, mpModel);\n  var dataOpt = map(mpModel.get('data'), curry(markerHelper.dataTransform, seriesModel));\n  if (coordSys) {\n    dataOpt = filter(dataOpt, curry(markerHelper.dataFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  mpData.initData(dataOpt, null, dimValueGetter);\n  return mpData;\n}\nexport default MarkPointView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,kCAAkC;AACzD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,UAAU,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,0BAA0B;AACjF,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAE;EACpD,IAAIC,QAAQ,GAAGF,WAAW,CAACG,gBAAgB;EAC3CJ,MAAM,CAACK,IAAI,CAAC,UAAUC,GAAG,EAAE;IACzB,IAAIC,SAAS,GAAGP,MAAM,CAACQ,YAAY,CAACF,GAAG,CAAC;IACxC,IAAIG,KAAK;IACT,IAAIC,GAAG,GAAGvB,UAAU,CAACwB,YAAY,CAACJ,SAAS,CAACK,GAAG,CAAC,GAAG,CAAC,EAAEV,GAAG,CAACW,QAAQ,CAAC,CAAC,CAAC;IACrE,IAAIC,GAAG,GAAG3B,UAAU,CAACwB,YAAY,CAACJ,SAAS,CAACK,GAAG,CAAC,GAAG,CAAC,EAAEV,GAAG,CAACa,SAAS,CAAC,CAAC,CAAC;IACtE,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC,IAAI,CAACM,KAAK,CAACF,GAAG,CAAC,EAAE;MAC9BL,KAAK,GAAG,CAACC,GAAG,EAAEI,GAAG,CAAC;IACpB;IACA;IAAA,KACK,IAAIb,WAAW,CAACgB,iBAAiB,EAAE;MACtC;MACAR,KAAK,GAAGR,WAAW,CAACgB,iBAAiB,CAACjB,MAAM,CAACkB,SAAS,CAAClB,MAAM,CAACmB,UAAU,EAAEb,GAAG,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIH,QAAQ,EAAE;MACnB,IAAIiB,CAAC,GAAGpB,MAAM,CAACY,GAAG,CAACT,QAAQ,CAACgB,UAAU,CAAC,CAAC,CAAC,EAAEb,GAAG,CAAC;MAC/C,IAAIe,CAAC,GAAGrB,MAAM,CAACY,GAAG,CAACT,QAAQ,CAACgB,UAAU,CAAC,CAAC,CAAC,EAAEb,GAAG,CAAC;MAC/CG,KAAK,GAAGN,QAAQ,CAACmB,WAAW,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;IACtC;IACA;IACA,IAAI,CAACL,KAAK,CAACN,GAAG,CAAC,EAAE;MACfD,KAAK,CAAC,CAAC,CAAC,GAAGC,GAAG;IAChB;IACA,IAAI,CAACM,KAAK,CAACF,GAAG,CAAC,EAAE;MACfL,KAAK,CAAC,CAAC,CAAC,GAAGK,GAAG;IAChB;IACAd,MAAM,CAACuB,aAAa,CAACjB,GAAG,EAAEG,KAAK,CAAC;EAClC,CAAC,CAAC;AACJ;AACA,IAAIe,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDxC,SAAS,CAACuC,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/B,OAAOH,KAAK;EACd;EACAF,aAAa,CAACM,SAAS,CAACC,eAAe,GAAG,UAAUC,cAAc,EAAEC,OAAO,EAAE/B,GAAG,EAAE;IAChF+B,OAAO,CAACC,UAAU,CAAC,UAAUjC,WAAW,EAAE;MACxC,IAAIkC,OAAO,GAAG5C,WAAW,CAAC6C,wBAAwB,CAACnC,WAAW,EAAE,WAAW,CAAC;MAC5E,IAAIkC,OAAO,EAAE;QACXpC,kBAAkB,CAACoC,OAAO,CAACE,OAAO,CAAC,CAAC,EAAEpC,WAAW,EAAEC,GAAG,CAAC;QACvD,IAAI,CAACoC,cAAc,CAAC1B,GAAG,CAACX,WAAW,CAACsC,EAAE,CAAC,CAACC,YAAY,CAAC,CAAC;MACxD;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDhB,aAAa,CAACM,SAAS,CAACW,YAAY,GAAG,UAAUxC,WAAW,EAAEkC,OAAO,EAAEF,OAAO,EAAE/B,GAAG,EAAE;IACnF,IAAIC,QAAQ,GAAGF,WAAW,CAACG,gBAAgB;IAC3C,IAAIsC,QAAQ,GAAGzC,WAAW,CAACsC,EAAE;IAC7B,IAAII,UAAU,GAAG1C,WAAW,CAACoC,OAAO,CAAC,CAAC;IACtC,IAAIO,aAAa,GAAG,IAAI,CAACN,cAAc;IACvC,IAAIO,UAAU,GAAGD,aAAa,CAAChC,GAAG,CAAC8B,QAAQ,CAAC,IAAIE,aAAa,CAACE,GAAG,CAACJ,QAAQ,EAAE,IAAIxD,UAAU,CAAC,CAAC,CAAC;IAC7F,IAAIc,MAAM,GAAG+C,UAAU,CAAC5C,QAAQ,EAAEF,WAAW,EAAEkC,OAAO,CAAC;IACvD;IACAA,OAAO,CAACa,OAAO,CAAChD,MAAM,CAAC;IACvBD,kBAAkB,CAACoC,OAAO,CAACE,OAAO,CAAC,CAAC,EAAEpC,WAAW,EAAEC,GAAG,CAAC;IACvDF,MAAM,CAACK,IAAI,CAAC,UAAUC,GAAG,EAAE;MACzB,IAAIC,SAAS,GAAGP,MAAM,CAACQ,YAAY,CAACF,GAAG,CAAC;MACxC,IAAI2C,MAAM,GAAG1C,SAAS,CAAC2C,UAAU,CAAC,QAAQ,CAAC;MAC3C,IAAIC,UAAU,GAAG5C,SAAS,CAAC2C,UAAU,CAAC,YAAY,CAAC;MACnD,IAAIE,YAAY,GAAG7C,SAAS,CAAC2C,UAAU,CAAC,cAAc,CAAC;MACvD,IAAIG,YAAY,GAAG9C,SAAS,CAAC2C,UAAU,CAAC,cAAc,CAAC;MACvD,IAAII,gBAAgB,GAAG/C,SAAS,CAAC2C,UAAU,CAAC,kBAAkB,CAAC;MAC/D;MACA,IAAI1D,UAAU,CAACyD,MAAM,CAAC,IAAIzD,UAAU,CAAC2D,UAAU,CAAC,IAAI3D,UAAU,CAAC4D,YAAY,CAAC,IAAI5D,UAAU,CAAC6D,YAAY,CAAC,EAAE;QACxG,IAAIE,MAAM,GAAGpB,OAAO,CAACqB,WAAW,CAAClD,GAAG,CAAC;QACrC,IAAImD,UAAU,GAAGtB,OAAO,CAACuB,aAAa,CAACpD,GAAG,CAAC;QAC3C,IAAId,UAAU,CAACyD,MAAM,CAAC,EAAE;UACtBA,MAAM,GAAGA,MAAM,CAACM,MAAM,EAAEE,UAAU,CAAC;QACrC;QACA,IAAIjE,UAAU,CAAC2D,UAAU,CAAC,EAAE;UAC1B;UACAA,UAAU,GAAGA,UAAU,CAACI,MAAM,EAAEE,UAAU,CAAC;QAC7C;QACA,IAAIjE,UAAU,CAAC4D,YAAY,CAAC,EAAE;UAC5BA,YAAY,GAAGA,YAAY,CAACG,MAAM,EAAEE,UAAU,CAAC;QACjD;QACA,IAAIjE,UAAU,CAAC6D,YAAY,CAAC,EAAE;UAC5BA,YAAY,GAAGA,YAAY,CAACE,MAAM,EAAEE,UAAU,CAAC;QACjD;MACF;MACA,IAAIE,KAAK,GAAGpD,SAAS,CAACqD,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC1D,IAAIC,KAAK,GAAGhE,iBAAiB,CAAC6C,UAAU,EAAE,OAAO,CAAC;MAClD,IAAI,CAACgB,KAAK,CAACI,IAAI,EAAE;QACfJ,KAAK,CAACI,IAAI,GAAGD,KAAK;MACpB;MACA9D,MAAM,CAACgE,aAAa,CAAC1D,GAAG,EAAE;QACxB2C,MAAM,EAAEA,MAAM;QACdE,UAAU,EAAEA,UAAU;QACtBC,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA,YAAY;QAC1BC,gBAAgB,EAAEA,gBAAgB;QAClCK,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACAd,UAAU,CAACoB,UAAU,CAACjE,MAAM,CAAC;IAC7B,IAAI,CAACkE,KAAK,CAACC,GAAG,CAACtB,UAAU,CAACqB,KAAK,CAAC;IAChC;IACA;IACAlE,MAAM,CAACoE,iBAAiB,CAAC,UAAUC,EAAE,EAAE;MACrCA,EAAE,CAACC,QAAQ,CAAC,UAAUC,KAAK,EAAE;QAC3B1E,SAAS,CAAC0E,KAAK,CAAC,CAACC,SAAS,GAAGrC,OAAO;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACsC,QAAQ,CAAC5B,UAAU,CAAC;IACzBA,UAAU,CAACqB,KAAK,CAACQ,MAAM,GAAGvC,OAAO,CAACvB,GAAG,CAAC,QAAQ,CAAC,IAAIX,WAAW,CAACW,GAAG,CAAC,QAAQ,CAAC;EAC9E,CAAC;EACDY,aAAa,CAACK,IAAI,GAAG,WAAW;EAChC,OAAOL,aAAa;AACtB,CAAC,CAAClC,UAAU,CAAC;AACb,SAASyD,UAAUA,CAAC5C,QAAQ,EAAEF,WAAW,EAAEkC,OAAO,EAAE;EAClD,IAAIwC,cAAc;EAClB,IAAIxE,QAAQ,EAAE;IACZwE,cAAc,GAAGlF,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACgB,UAAU,EAAE,UAAUyD,QAAQ,EAAE;MACxE,IAAIC,IAAI,GAAG5E,WAAW,CAACoC,OAAO,CAAC,CAAC,CAACyC,gBAAgB,CAAC7E,WAAW,CAACoC,OAAO,CAAC,CAAC,CAAC0C,YAAY,CAACH,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;MACrG;MACA,OAAOhF,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEiF,IAAI,CAAC,EAAE;QAC9BG,IAAI,EAAEJ,QAAQ;QACd;QACAK,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,cAAc,GAAG,CAAC;MAChBK,IAAI,EAAE,OAAO;MACbnD,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EACA,IAAI7B,MAAM,GAAG,IAAIZ,UAAU,CAACuF,cAAc,EAAExC,OAAO,CAAC;EACpD,IAAI+C,OAAO,GAAGzF,GAAG,CAAC0C,OAAO,CAACvB,GAAG,CAAC,MAAM,CAAC,EAAEjB,KAAK,CAACN,YAAY,CAAC8F,aAAa,EAAElF,WAAW,CAAC,CAAC;EACtF,IAAIE,QAAQ,EAAE;IACZ+E,OAAO,GAAGxF,MAAM,CAACwF,OAAO,EAAEvF,KAAK,CAACN,YAAY,CAAC+F,UAAU,EAAEjF,QAAQ,CAAC,CAAC;EACrE;EACA,IAAIkF,cAAc,GAAGhG,YAAY,CAACiG,0BAA0B,CAAC,CAAC,CAACnF,QAAQ,EAAEwE,cAAc,CAAC;EACxF3E,MAAM,CAACuF,QAAQ,CAACL,OAAO,EAAE,IAAI,EAAEG,cAAc,CAAC;EAC9C,OAAOrF,MAAM;AACf;AACA,eAAewB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}