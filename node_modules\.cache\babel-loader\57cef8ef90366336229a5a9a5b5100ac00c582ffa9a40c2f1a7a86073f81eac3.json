{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport * as numberUtil from '../../util/number.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nvar ParallelAxisModel = /** @class */function (_super) {\n  __extends(ParallelAxisModel, _super);\n  function ParallelAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisModel.type;\n    /**\r\n     * @readOnly\r\n     */\n    _this.activeIntervals = [];\n    return _this;\n  }\n  ParallelAxisModel.prototype.getAreaSelectStyle = function () {\n    return makeStyleMapper([['fill', 'color'], ['lineWidth', 'borderWidth'], ['stroke', 'borderColor'], ['width', 'width'], ['opacity', 'opacity']\n    // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n    // So do not transfer decal directly.\n    ])(this.getModel('areaSelectStyle'));\n  };\n  /**\r\n   * The code of this feature is put on AxisModel but not ParallelAxis,\r\n   * because axisModel can be alive after echarts updating but instance of\r\n   * ParallelAxis having been disposed. this._activeInterval should be kept\r\n   * when action dispatched (i.e. legend click).\r\n   *\r\n   * @param intervals `interval.length === 0` means set all active.\r\n   */\n  ParallelAxisModel.prototype.setActiveIntervals = function (intervals) {\n    var activeIntervals = this.activeIntervals = zrUtil.clone(intervals);\n    // Normalize\n    if (activeIntervals) {\n      for (var i = activeIntervals.length - 1; i >= 0; i--) {\n        numberUtil.asc(activeIntervals[i]);\n      }\n    }\n  };\n  /**\r\n   * @param value When only attempting detect whether 'no activeIntervals set',\r\n   *        `value` is not needed to be input.\r\n   */\n  ParallelAxisModel.prototype.getActiveState = function (value) {\n    var activeIntervals = this.activeIntervals;\n    if (!activeIntervals.length) {\n      return 'normal';\n    }\n    if (value == null || isNaN(+value)) {\n      return 'inactive';\n    }\n    // Simple optimization\n    if (activeIntervals.length === 1) {\n      var interval = activeIntervals[0];\n      if (interval[0] <= value && value <= interval[1]) {\n        return 'active';\n      }\n    } else {\n      for (var i = 0, len = activeIntervals.length; i < len; i++) {\n        if (activeIntervals[i][0] <= value && value <= activeIntervals[i][1]) {\n          return 'active';\n        }\n      }\n    }\n    return 'inactive';\n  };\n  return ParallelAxisModel;\n}(ComponentModel);\nzrUtil.mixin(ParallelAxisModel, AxisModelCommonMixin);\nexport default ParallelAxisModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ComponentModel", "makeStyleMapper", "numberUtil", "AxisModelCommonMixin", "ParallelAxisModel", "_super", "_this", "apply", "arguments", "type", "activeIntervals", "prototype", "getAreaSelectStyle", "getModel", "setActiveIntervals", "intervals", "clone", "i", "length", "asc", "getActiveState", "value", "isNaN", "interval", "len", "mixin"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/parallel/AxisModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport * as numberUtil from '../../util/number.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nvar ParallelAxisModel = /** @class */function (_super) {\n  __extends(ParallelAxisModel, _super);\n  function ParallelAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisModel.type;\n    /**\r\n     * @readOnly\r\n     */\n    _this.activeIntervals = [];\n    return _this;\n  }\n  ParallelAxisModel.prototype.getAreaSelectStyle = function () {\n    return makeStyleMapper([['fill', 'color'], ['lineWidth', 'borderWidth'], ['stroke', 'borderColor'], ['width', 'width'], ['opacity', 'opacity']\n    // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n    // So do not transfer decal directly.\n    ])(this.getModel('areaSelectStyle'));\n  };\n  /**\r\n   * The code of this feature is put on AxisModel but not ParallelAxis,\r\n   * because axisModel can be alive after echarts updating but instance of\r\n   * ParallelAxis having been disposed. this._activeInterval should be kept\r\n   * when action dispatched (i.e. legend click).\r\n   *\r\n   * @param intervals `interval.length === 0` means set all active.\r\n   */\n  ParallelAxisModel.prototype.setActiveIntervals = function (intervals) {\n    var activeIntervals = this.activeIntervals = zrUtil.clone(intervals);\n    // Normalize\n    if (activeIntervals) {\n      for (var i = activeIntervals.length - 1; i >= 0; i--) {\n        numberUtil.asc(activeIntervals[i]);\n      }\n    }\n  };\n  /**\r\n   * @param value When only attempting detect whether 'no activeIntervals set',\r\n   *        `value` is not needed to be input.\r\n   */\n  ParallelAxisModel.prototype.getActiveState = function (value) {\n    var activeIntervals = this.activeIntervals;\n    if (!activeIntervals.length) {\n      return 'normal';\n    }\n    if (value == null || isNaN(+value)) {\n      return 'inactive';\n    }\n    // Simple optimization\n    if (activeIntervals.length === 1) {\n      var interval = activeIntervals[0];\n      if (interval[0] <= value && value <= interval[1]) {\n        return 'active';\n      }\n    } else {\n      for (var i = 0, len = activeIntervals.length; i < len; i++) {\n        if (activeIntervals[i][0] <= value && value <= activeIntervals[i][1]) {\n          return 'active';\n        }\n      }\n    }\n    return 'inactive';\n  };\n  return ParallelAxisModel;\n}(ComponentModel);\nzrUtil.mixin(ParallelAxisModel, AxisModelCommonMixin);\nexport default ParallelAxisModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDP,SAAS,CAACM,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnC;AACJ;AACA;IACIH,KAAK,CAACI,eAAe,GAAG,EAAE;IAC1B,OAAOJ,KAAK;EACd;EACAF,iBAAiB,CAACO,SAAS,CAACC,kBAAkB,GAAG,YAAY;IAC3D,OAAOX,eAAe,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS;IAC7I;IACA;IAAA,CACC,CAAC,CAAC,IAAI,CAACY,QAAQ,CAAC,iBAAiB,CAAC,CAAC;EACtC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACET,iBAAiB,CAACO,SAAS,CAACG,kBAAkB,GAAG,UAAUC,SAAS,EAAE;IACpE,IAAIL,eAAe,GAAG,IAAI,CAACA,eAAe,GAAGX,MAAM,CAACiB,KAAK,CAACD,SAAS,CAAC;IACpE;IACA,IAAIL,eAAe,EAAE;MACnB,KAAK,IAAIO,CAAC,GAAGP,eAAe,CAACQ,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpDf,UAAU,CAACiB,GAAG,CAACT,eAAe,CAACO,CAAC,CAAC,CAAC;MACpC;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;EACEb,iBAAiB,CAACO,SAAS,CAACS,cAAc,GAAG,UAAUC,KAAK,EAAE;IAC5D,IAAIX,eAAe,GAAG,IAAI,CAACA,eAAe;IAC1C,IAAI,CAACA,eAAe,CAACQ,MAAM,EAAE;MAC3B,OAAO,QAAQ;IACjB;IACA,IAAIG,KAAK,IAAI,IAAI,IAAIC,KAAK,CAAC,CAACD,KAAK,CAAC,EAAE;MAClC,OAAO,UAAU;IACnB;IACA;IACA,IAAIX,eAAe,CAACQ,MAAM,KAAK,CAAC,EAAE;MAChC,IAAIK,QAAQ,GAAGb,eAAe,CAAC,CAAC,CAAC;MACjC,IAAIa,QAAQ,CAAC,CAAC,CAAC,IAAIF,KAAK,IAAIA,KAAK,IAAIE,QAAQ,CAAC,CAAC,CAAC,EAAE;QAChD,OAAO,QAAQ;MACjB;IACF,CAAC,MAAM;MACL,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGd,eAAe,CAACQ,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;QAC1D,IAAIP,eAAe,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAII,KAAK,IAAIA,KAAK,IAAIX,eAAe,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpE,OAAO,QAAQ;QACjB;MACF;IACF;IACA,OAAO,UAAU;EACnB,CAAC;EACD,OAAOb,iBAAiB;AAC1B,CAAC,CAACJ,cAAc,CAAC;AACjBD,MAAM,CAAC0B,KAAK,CAACrB,iBAAiB,EAAED,oBAAoB,CAAC;AACrD,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}