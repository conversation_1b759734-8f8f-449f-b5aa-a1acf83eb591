{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport * as helper from './helper.js';\nimport * as modelUtil from '../../util/model.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { setAsHighDownDispatcher } from '../../util/states.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { findEventDispatcher } from '../../util/event.js';\nvar linearMap = numberUtil.linearMap;\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\n// Arbitrary value\nvar HOVER_LINK_SIZE = 12;\nvar HOVER_LINK_OUT = 6;\n// Notice:\n// Any \"interval\" should be by the order of [low, high].\n// \"handle0\" (handleIndex === 0) maps to\n// low data value: this._dataInterval[0] and has low coord.\n// \"handle1\" (handleIndex === 1) maps to\n// high data value: this._dataInterval[1] and has high coord.\n// The logic of transform is implemented in this._createBarGroup.\nvar ContinuousView = /** @class */function (_super) {\n  __extends(ContinuousView, _super);\n  function ContinuousView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousView.type;\n    _this._shapes = {};\n    _this._dataInterval = [];\n    _this._handleEnds = [];\n    _this._hoverLinkDataIndices = [];\n    return _this;\n  }\n  ContinuousView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.call(this, ecModel, api);\n    this._hoverLinkFromSeriesMouseOver = zrUtil.bind(this._hoverLinkFromSeriesMouseOver, this);\n    this._hideIndicator = zrUtil.bind(this._hideIndicator, this);\n  };\n  ContinuousView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {\n    if (!payload || payload.type !== 'selectDataRange' || payload.from !== this.uid) {\n      this._buildView();\n    }\n  };\n  ContinuousView.prototype._buildView = function () {\n    this.group.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var thisGroup = this.group;\n    this._orient = visualMapModel.get('orient');\n    this._useHandle = visualMapModel.get('calculable');\n    this._resetInterval();\n    this._renderBar(thisGroup);\n    var dataRangeText = visualMapModel.get('text');\n    this._renderEndsText(thisGroup, dataRangeText, 0);\n    this._renderEndsText(thisGroup, dataRangeText, 1);\n    // Do this for background size calculation.\n    this._updateView(true);\n    // After updating view, inner shapes is built completely,\n    // and then background can be rendered.\n    this.renderBackground(thisGroup);\n    // Real update view\n    this._updateView();\n    this._enableHoverLinkToSeries();\n    this._enableHoverLinkFromSeries();\n    this.positionGroup(thisGroup);\n  };\n  ContinuousView.prototype._renderEndsText = function (group, dataRangeText, endsIndex) {\n    if (!dataRangeText) {\n      return;\n    }\n    // Compatible with ec2, text[0] map to high value, text[1] map low value.\n    var text = dataRangeText[1 - endsIndex];\n    text = text != null ? text + '' : '';\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var itemSize = visualMapModel.itemSize;\n    var barGroup = this._shapes.mainGroup;\n    var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);\n    var align = this._applyTransform(endsIndex === 0 ? 'bottom' : 'top', barGroup);\n    var orient = this._orient;\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    this.group.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: position[0],\n        y: position[1],\n        verticalAlign: orient === 'horizontal' ? 'middle' : align,\n        align: orient === 'horizontal' ? align : 'center',\n        text: text\n      })\n    }));\n  };\n  ContinuousView.prototype._renderBar = function (targetGroup) {\n    var visualMapModel = this.visualMapModel;\n    var shapes = this._shapes;\n    var itemSize = visualMapModel.itemSize;\n    var orient = this._orient;\n    var useHandle = this._useHandle;\n    var itemAlign = helper.getItemAlign(visualMapModel, this.api, itemSize);\n    var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);\n    var gradientBarGroup = new graphic.Group();\n    mainGroup.add(gradientBarGroup);\n    // Bar\n    gradientBarGroup.add(shapes.outOfRange = createPolygon());\n    gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, zrUtil.bind(this._dragHandle, this, 'all', false), zrUtil.bind(this._dragHandle, this, 'all', true)));\n    // A border radius clip.\n    gradientBarGroup.setClipPath(new graphic.Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: itemSize[0],\n        height: itemSize[1],\n        r: 3\n      }\n    }));\n    var textRect = visualMapModel.textStyleModel.getTextRect('国');\n    var textSize = mathMax(textRect.width, textRect.height);\n    // Handle\n    if (useHandle) {\n      shapes.handleThumbs = [];\n      shapes.handleLabels = [];\n      shapes.handleLabelPoints = [];\n      this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);\n      this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);\n    }\n    this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);\n    targetGroup.add(mainGroup);\n  };\n  ContinuousView.prototype._createHandle = function (visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {\n    var onDrift = zrUtil.bind(this._dragHandle, this, handleIndex, false);\n    var onDragEnd = zrUtil.bind(this._dragHandle, this, handleIndex, true);\n    var handleSize = parsePercent(visualMapModel.get('handleSize'), itemSize[0]);\n    var handleThumb = createSymbol(visualMapModel.get('handleIcon'), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);\n    var cursor = getCursor(this._orient);\n    handleThumb.attr({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      ondragend: onDragEnd,\n      onmousemove: function (e) {\n        eventTool.stop(e.event);\n      }\n    });\n    handleThumb.x = itemSize[0] / 2;\n    handleThumb.useStyle(visualMapModel.getModel('handleStyle').getItemStyle());\n    handleThumb.setStyle({\n      strokeNoScale: true,\n      strokeFirst: true\n    });\n    handleThumb.style.lineWidth *= 2;\n    handleThumb.ensureState('emphasis').style = visualMapModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n    setAsHighDownDispatcher(handleThumb, true);\n    mainGroup.add(handleThumb);\n    // Text is always horizontal layout but should not be effected by\n    // transform (orient/inverse). So label is built separately but not\n    // use zrender/graphic/helper/RectText, and is located based on view\n    // group (according to handleLabelPoint) but not barGroup.\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var handleLabel = new graphic.Text({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      onmousemove: function (e) {\n        // For mobile device, prevent screen slider on the button.\n        eventTool.stop(e.event);\n      },\n      ondragend: onDragEnd,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    handleLabel.ensureState('blur').style = {\n      opacity: 0.1\n    };\n    handleLabel.stateTransition = {\n      duration: 200\n    };\n    this.group.add(handleLabel);\n    var handleLabelPoint = [handleSize, 0];\n    var shapes = this._shapes;\n    shapes.handleThumbs[handleIndex] = handleThumb;\n    shapes.handleLabelPoints[handleIndex] = handleLabelPoint;\n    shapes.handleLabels[handleIndex] = handleLabel;\n  };\n  ContinuousView.prototype._createIndicator = function (visualMapModel, mainGroup, itemSize, textSize, orient) {\n    var scale = parsePercent(visualMapModel.get('indicatorSize'), itemSize[0]);\n    var indicator = createSymbol(visualMapModel.get('indicatorIcon'), -scale / 2, -scale / 2, scale, scale, null, true);\n    indicator.attr({\n      cursor: 'move',\n      invisible: true,\n      silent: true,\n      x: itemSize[0] / 2\n    });\n    var indicatorStyle = visualMapModel.getModel('indicatorStyle').getItemStyle();\n    if (indicator instanceof ZRImage) {\n      var pathStyle = indicator.style;\n      indicator.useStyle(zrUtil.extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, indicatorStyle));\n    } else {\n      indicator.useStyle(indicatorStyle);\n    }\n    mainGroup.add(indicator);\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var indicatorLabel = new graphic.Text({\n      silent: true,\n      invisible: true,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    this.group.add(indicatorLabel);\n    var indicatorLabelPoint = [(orient === 'horizontal' ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];\n    var shapes = this._shapes;\n    shapes.indicator = indicator;\n    shapes.indicatorLabel = indicatorLabel;\n    shapes.indicatorLabelPoint = indicatorLabelPoint;\n    this._firstShowIndicator = true;\n  };\n  ContinuousView.prototype._dragHandle = function (handleIndex, isEnd,\n  // dx is event from ondragend if isEnd is true. It's not used\n  dx, dy) {\n    if (!this._useHandle) {\n      return;\n    }\n    this._dragging = !isEnd;\n    if (!isEnd) {\n      // Transform dx, dy to bar coordination.\n      var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);\n      this._updateInterval(handleIndex, vertex[1]);\n      this._hideIndicator();\n      // Considering realtime, update view should be executed\n      // before dispatch action.\n      this._updateView();\n    }\n    // dragEnd do not dispatch action when realtime.\n    if (isEnd === !this.visualMapModel.get('realtime')) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'selectDataRange',\n        from: this.uid,\n        visualMapId: this.visualMapModel.id,\n        selected: this._dataInterval.slice()\n      });\n    }\n    if (isEnd) {\n      !this._hovering && this._clearHoverLinkToSeries();\n    } else if (useHoverLinkOnHandle(this.visualMapModel)) {\n      this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);\n    }\n  };\n  ContinuousView.prototype._resetInterval = function () {\n    var visualMapModel = this.visualMapModel;\n    var dataInterval = this._dataInterval = visualMapModel.getSelected();\n    var dataExtent = visualMapModel.getExtent();\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];\n  };\n  /**\r\n   * @private\r\n   * @param {(number|string)} handleIndex 0 or 1 or 'all'\r\n   * @param {number} dx\r\n   * @param {number} dy\r\n   */\n  ContinuousView.prototype._updateInterval = function (handleIndex, delta) {\n    delta = delta || 0;\n    var visualMapModel = this.visualMapModel;\n    var handleEnds = this._handleEnds;\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    sliderMove(delta, handleEnds, sizeExtent, handleIndex,\n    // cross is forbidden\n    0);\n    var dataExtent = visualMapModel.getExtent();\n    // Update data interval.\n    this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];\n  };\n  ContinuousView.prototype._updateView = function (forSketch) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var shapes = this._shapes;\n    var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];\n    var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;\n    var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, 'inRange');\n    var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, 'outOfRange');\n    shapes.inRange.setStyle({\n      fill: visualInRange.barColor\n      // opacity: visualInRange.opacity\n    }).setShape('points', visualInRange.barPoints);\n    shapes.outOfRange.setStyle({\n      fill: visualOutOfRange.barColor\n      // opacity: visualOutOfRange.opacity\n    }).setShape('points', visualOutOfRange.barPoints);\n    this._updateHandle(inRangeHandleEnds, visualInRange);\n  };\n  ContinuousView.prototype._createBarVisual = function (dataInterval, dataExtent, handleEnds, forceState) {\n    var opts = {\n      forceState: forceState,\n      convertOpacityToAlpha: true\n    };\n    var colorStops = this._makeColorGradient(dataInterval, opts);\n    var symbolSizes = [this.getControllerVisual(dataInterval[0], 'symbolSize', opts), this.getControllerVisual(dataInterval[1], 'symbolSize', opts)];\n    var barPoints = this._createBarPoints(handleEnds, symbolSizes);\n    return {\n      barColor: new LinearGradient(0, 0, 0, 1, colorStops),\n      barPoints: barPoints,\n      handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]\n    };\n  };\n  ContinuousView.prototype._makeColorGradient = function (dataInterval, opts) {\n    // Considering colorHue, which is not linear, so we have to sample\n    // to calculate gradient color stops, but not only calculate head\n    // and tail.\n    var sampleNumber = 100; // Arbitrary value.\n    var colorStops = [];\n    var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[0], 'color', opts),\n      offset: 0\n    });\n    for (var i = 1; i < sampleNumber; i++) {\n      var currValue = dataInterval[0] + step * i;\n      if (currValue > dataInterval[1]) {\n        break;\n      }\n      colorStops.push({\n        color: this.getControllerVisual(currValue, 'color', opts),\n        offset: i / sampleNumber\n      });\n    }\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[1], 'color', opts),\n      offset: 1\n    });\n    return colorStops;\n  };\n  ContinuousView.prototype._createBarPoints = function (handleEnds, symbolSizes) {\n    var itemSize = this.visualMapModel.itemSize;\n    return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];\n  };\n  ContinuousView.prototype._createBarGroup = function (itemAlign) {\n    var orient = this._orient;\n    var inverse = this.visualMapModel.get('inverse');\n    return new graphic.Group(orient === 'horizontal' && !inverse ? {\n      scaleX: itemAlign === 'bottom' ? 1 : -1,\n      rotation: Math.PI / 2\n    } : orient === 'horizontal' && inverse ? {\n      scaleX: itemAlign === 'bottom' ? -1 : 1,\n      rotation: -Math.PI / 2\n    } : orient === 'vertical' && !inverse ? {\n      scaleX: itemAlign === 'left' ? 1 : -1,\n      scaleY: -1\n    } : {\n      scaleX: itemAlign === 'left' ? 1 : -1\n    });\n  };\n  ContinuousView.prototype._updateHandle = function (handleEnds, visualInRange) {\n    if (!this._useHandle) {\n      return;\n    }\n    var shapes = this._shapes;\n    var visualMapModel = this.visualMapModel;\n    var handleThumbs = shapes.handleThumbs;\n    var handleLabels = shapes.handleLabels;\n    var itemSize = visualMapModel.itemSize;\n    var dataExtent = visualMapModel.getExtent();\n    var align = this._applyTransform('left', shapes.mainGroup);\n    each([0, 1], function (handleIndex) {\n      var handleThumb = handleThumbs[handleIndex];\n      handleThumb.setStyle('fill', visualInRange.handlesColor[handleIndex]);\n      handleThumb.y = handleEnds[handleIndex];\n      var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);\n      var symbolSize = this.getControllerVisual(val, 'symbolSize');\n      handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];\n      handleThumb.x = itemSize[0] - symbolSize / 2;\n      // Update handle label position.\n      var textPoint = graphic.applyTransform(shapes.handleLabelPoints[handleIndex], graphic.getTransform(handleThumb, this.group));\n      if (this._orient === 'horizontal') {\n        // If visualMap controls symbol size, an additional offset needs to be added to labels to avoid collision at minimum size.\n        // Offset reaches value of 0 at \"maximum\" position, so maximum position is not altered at all.\n        var minimumOffset = align === 'left' || align === 'top' ? (itemSize[0] - symbolSize) / 2 : (itemSize[0] - symbolSize) / -2;\n        textPoint[1] += minimumOffset;\n      }\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),\n        verticalAlign: 'middle',\n        align: this._orient === 'vertical' ? this._applyTransform('left', shapes.mainGroup) : 'center'\n      });\n    }, this);\n  };\n  ContinuousView.prototype._showIndicator = function (cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var itemSize = visualMapModel.itemSize;\n    var sizeExtent = [0, itemSize[1]];\n    var shapes = this._shapes;\n    var indicator = shapes.indicator;\n    if (!indicator) {\n      return;\n    }\n    indicator.attr('invisible', false);\n    var opts = {\n      convertOpacityToAlpha: true\n    };\n    var color = this.getControllerVisual(cursorValue, 'color', opts);\n    var symbolSize = this.getControllerVisual(cursorValue, 'symbolSize');\n    var y = linearMap(cursorValue, dataExtent, sizeExtent, true);\n    var x = itemSize[0] - symbolSize / 2;\n    var oldIndicatorPos = {\n      x: indicator.x,\n      y: indicator.y\n    };\n    // Update handle label position.\n    indicator.y = y;\n    indicator.x = x;\n    var textPoint = graphic.applyTransform(shapes.indicatorLabelPoint, graphic.getTransform(indicator, this.group));\n    var indicatorLabel = shapes.indicatorLabel;\n    indicatorLabel.attr('invisible', false);\n    var align = this._applyTransform('left', shapes.mainGroup);\n    var orient = this._orient;\n    var isHorizontal = orient === 'horizontal';\n    indicatorLabel.setStyle({\n      text: (rangeSymbol ? rangeSymbol : '') + visualMapModel.formatValueText(textValue),\n      verticalAlign: isHorizontal ? align : 'middle',\n      align: isHorizontal ? 'center' : align\n    });\n    var indicatorNewProps = {\n      x: x,\n      y: y,\n      style: {\n        fill: color\n      }\n    };\n    var labelNewProps = {\n      style: {\n        x: textPoint[0],\n        y: textPoint[1]\n      }\n    };\n    if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {\n      var animationCfg = {\n        duration: 100,\n        easing: 'cubicInOut',\n        additive: true\n      };\n      indicator.x = oldIndicatorPos.x;\n      indicator.y = oldIndicatorPos.y;\n      indicator.animateTo(indicatorNewProps, animationCfg);\n      indicatorLabel.animateTo(labelNewProps, animationCfg);\n    } else {\n      indicator.attr(indicatorNewProps);\n      indicatorLabel.attr(labelNewProps);\n    }\n    this._firstShowIndicator = false;\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.enterBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._enableHoverLinkToSeries = function () {\n    var self = this;\n    this._shapes.mainGroup.on('mousemove', function (e) {\n      self._hovering = true;\n      if (!self._dragging) {\n        var itemSize = self.visualMapModel.itemSize;\n        var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true);\n        // For hover link show when hover handle, which might be\n        // below or upper than sizeExtent.\n        pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);\n        self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);\n      }\n    }).on('mouseout', function () {\n      // When mouse is out of handle, hoverLink still need\n      // to be displayed when realtime is set as false.\n      self._hovering = false;\n      !self._dragging && self._clearHoverLinkToSeries();\n    });\n  };\n  ContinuousView.prototype._enableHoverLinkFromSeries = function () {\n    var zr = this.api.getZr();\n    if (this.visualMapModel.option.hoverLink) {\n      zr.on('mouseover', this._hoverLinkFromSeriesMouseOver, this);\n      zr.on('mouseout', this._hideIndicator, this);\n    } else {\n      this._clearHoverLinkFromSeries();\n    }\n  };\n  ContinuousView.prototype._doHoverLinkToSeries = function (cursorPos, hoverOnBar) {\n    var visualMapModel = this.visualMapModel;\n    var itemSize = visualMapModel.itemSize;\n    if (!visualMapModel.option.hoverLink) {\n      return;\n    }\n    var sizeExtent = [0, itemSize[1]];\n    var dataExtent = visualMapModel.getExtent();\n    // For hover link show when hover handle, which might be below or upper than sizeExtent.\n    cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);\n    var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);\n    var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];\n    var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);\n    var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)];\n    // Consider data range is out of visualMap range, see test/visualMap-continuous.html,\n    // where china and india has very large population.\n    hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);\n    hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity);\n    // Do not show indicator when mouse is over handle,\n    // otherwise labels overlap, especially when dragging.\n    if (hoverOnBar) {\n      if (valueRange[0] === -Infinity) {\n        this._showIndicator(cursorValue, valueRange[1], '< ', halfHoverLinkSize);\n      } else if (valueRange[1] === Infinity) {\n        this._showIndicator(cursorValue, valueRange[0], '> ', halfHoverLinkSize);\n      } else {\n        this._showIndicator(cursorValue, cursorValue, '≈ ', halfHoverLinkSize);\n      }\n    }\n    // When realtime is set as false, handles, which are in barGroup,\n    // also trigger hoverLink, which help user to realize where they\n    // focus on when dragging. (see test/heatmap-large.html)\n    // When realtime is set as true, highlight will not show when hover\n    // handle, because the label on handle, which displays a exact value\n    // but not range, might mislead users.\n    var oldBatch = this._hoverLinkDataIndices;\n    var newBatch = [];\n    if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {\n      newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);\n    }\n    var resultBatches = modelUtil.compressBatches(oldBatch, newBatch);\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(resultBatches[0], visualMapModel));\n    this._dispatchHighDown('highlight', helper.makeHighDownBatch(resultBatches[1], visualMapModel));\n  };\n  ContinuousView.prototype._hoverLinkFromSeriesMouseOver = function (e) {\n    var ecData;\n    findEventDispatcher(e.target, function (target) {\n      var currECData = getECData(target);\n      if (currECData.dataIndex != null) {\n        ecData = currECData;\n        return true;\n      }\n    }, true);\n    if (!ecData) {\n      return;\n    }\n    var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);\n    var visualMapModel = this.visualMapModel;\n    if (!visualMapModel.isTargetSeries(dataModel)) {\n      return;\n    }\n    var data = dataModel.getData(ecData.dataType);\n    var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);\n    if (!isNaN(value)) {\n      this._showIndicator(value, value);\n    }\n  };\n  ContinuousView.prototype._hideIndicator = function () {\n    var shapes = this._shapes;\n    shapes.indicator && shapes.indicator.attr('invisible', true);\n    shapes.indicatorLabel && shapes.indicatorLabel.attr('invisible', true);\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.leaveBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._clearHoverLinkToSeries = function () {\n    this._hideIndicator();\n    var indices = this._hoverLinkDataIndices;\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(indices, this.visualMapModel));\n    indices.length = 0;\n  };\n  ContinuousView.prototype._clearHoverLinkFromSeries = function () {\n    this._hideIndicator();\n    var zr = this.api.getZr();\n    zr.off('mouseover', this._hoverLinkFromSeriesMouseOver);\n    zr.off('mouseout', this._hideIndicator);\n  };\n  ContinuousView.prototype._applyTransform = function (vertex, element, inverse, global) {\n    var transform = graphic.getTransform(element, global ? null : this.group);\n    return zrUtil.isArray(vertex) ? graphic.applyTransform(vertex, transform, inverse) : graphic.transformDirection(vertex, transform, inverse);\n  };\n  // TODO: TYPE more specified payload types.\n  ContinuousView.prototype._dispatchHighDown = function (type, batch) {\n    batch && batch.length && this.api.dispatchAction({\n      type: type,\n      batch: batch\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousView.prototype.dispose = function () {\n    this._clearHoverLinkFromSeries();\n    this._clearHoverLinkToSeries();\n  };\n  ContinuousView.type = 'visualMap.continuous';\n  return ContinuousView;\n}(VisualMapView);\nfunction createPolygon(points, cursor, onDrift, onDragEnd) {\n  return new graphic.Polygon({\n    shape: {\n      points: points\n    },\n    draggable: !!onDrift,\n    cursor: cursor,\n    drift: onDrift,\n    onmousemove: function (e) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n    },\n    ondragend: onDragEnd\n  });\n}\nfunction getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {\n  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;\n  var hoverLinkDataSize = visualMapModel.get('hoverLinkDataSize');\n  if (hoverLinkDataSize) {\n    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;\n  }\n  return halfHoverLinkSize;\n}\nfunction useHoverLinkOnHandle(visualMapModel) {\n  var hoverLinkOnHandle = visualMapModel.get('hoverLinkOnHandle');\n  return !!(hoverLinkOnHandle == null ? visualMapModel.get('realtime') : hoverLinkOnHandle);\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default ContinuousView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "LinearGradient", "eventTool", "VisualMapView", "graphic", "numberUtil", "slider<PERSON><PERSON>", "helper", "modelUtil", "parsePercent", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "createSymbol", "ZRImage", "getECData", "createTextStyle", "find<PERSON>vent<PERSON><PERSON><PERSON><PERSON><PERSON>", "linearMap", "each", "mathMin", "Math", "min", "mathMax", "max", "HOVER_LINK_SIZE", "HOVER_LINK_OUT", "Continuous<PERSON><PERSON>w", "_super", "_this", "apply", "arguments", "type", "_shapes", "_dataInterval", "_handleEnds", "_hoverLinkDataIndices", "prototype", "init", "ecModel", "api", "call", "_hoverLinkFromSeriesMouseOver", "bind", "_hideIndicator", "doR<PERSON>", "visualMapModel", "payload", "from", "uid", "_buildView", "group", "removeAll", "thisGroup", "_orient", "get", "_useHandle", "_resetInterval", "_renderBar", "dataRangeText", "_renderEndsText", "_updateView", "renderBackground", "_enableHoverLinkToSeries", "_enableHoverLinkFromSeries", "positionGroup", "endsIndex", "text", "textGap", "itemSize", "barGroup", "mainGroup", "position", "_applyTransform", "align", "orient", "textStyleModel", "add", "Text", "style", "x", "y", "verticalAlign", "targetGroup", "shapes", "useHandle", "itemAlign", "getItemAlign", "_createBarGroup", "gradientBarGroup", "Group", "outOfRange", "createPolygon", "inRange", "getCursor", "_drag<PERSON><PERSON>le", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rect", "shape", "width", "height", "r", "textRect", "getTextRect", "textSize", "handleThumbs", "handleLabels", "handleLabelPoints", "_createHandle", "_createIndicator", "handleIndex", "onDrift", "onDragEnd", "handleSize", "handleThumb", "cursor", "attr", "draggable", "drift", "ondragend", "<PERSON><PERSON><PERSON><PERSON>", "e", "stop", "event", "useStyle", "getModel", "getItemStyle", "setStyle", "strokeNoScale", "<PERSON><PERSON><PERSON><PERSON>", "lineWidth", "ensureState", "handleLabel", "opacity", "stateTransition", "duration", "handleLabelPoint", "scale", "indicator", "invisible", "silent", "indicatorStyle", "pathStyle", "extend", "image", "indicatorLabel", "indicatorLabelPoint", "_firstShowIndicator", "isEnd", "dx", "dy", "_dragging", "vertex", "_updateInterval", "dispatchAction", "visualMapId", "id", "selected", "slice", "_hovering", "_clearHoverLinkToSeries", "useHoverLinkOnHandle", "_doHoverLinkToSeries", "dataInterval", "getSelected", "dataExtent", "getExtent", "sizeExtent", "delta", "handleEnds", "forSketch", "outOfRangeHandleEnds", "inRangeHandleEnds", "visualInRange", "_createBarVisual", "visualOutOfRange", "fill", "barColor", "setShape", "barPoints", "_update<PERSON><PERSON>le", "forceState", "opts", "convertOpacityToAlpha", "colorStops", "_makeColorGradient", "symbolSizes", "getControllerVisual", "_createBarPoints", "handlesColor", "color", "length", "sampleNumber", "step", "push", "offset", "i", "currValue", "inverse", "scaleX", "rotation", "PI", "scaleY", "val", "symbolSize", "textPoint", "applyTransform", "getTransform", "minimumOffset", "formatValueText", "_showIndicator", "cursor<PERSON><PERSON>ue", "textValue", "rangeSymbol", "halfHoverLinkSize", "oldIndicatorPos", "isHorizontal", "indicatorNewProps", "labelNewProps", "isAnimationEnabled", "animationCfg", "easing", "additive", "animateTo", "enterBlur", "self", "on", "pos", "offsetX", "offsetY", "zr", "getZr", "option", "hoverLink", "_clearHoverLinkFromSeries", "cursorPos", "hoverOnBar", "getHalfHoverLinkSize", "hoverRange", "valueRange", "Infinity", "oldBatch", "newBatch", "findTargetDataIndices", "resultBatches", "compressBatches", "_dispatchHighDown", "makeHighDownBatch", "ecData", "target", "currECData", "dataIndex", "dataModel", "getSeriesByIndex", "seriesIndex", "isTargetSeries", "data", "getData", "dataType", "value", "getStore", "getDataDimensionIndex", "isNaN", "leaveBlur", "indices", "off", "element", "global", "transform", "isArray", "transformDirection", "batch", "dispose", "points", "Polygon", "hoverLinkDataSize", "hoverLinkOnHandle"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/visualMap/ContinuousView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport * as helper from './helper.js';\nimport * as modelUtil from '../../util/model.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { setAsHighDownDispatcher } from '../../util/states.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { findEventDispatcher } from '../../util/event.js';\nvar linearMap = numberUtil.linearMap;\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\n// Arbitrary value\nvar HOVER_LINK_SIZE = 12;\nvar HOVER_LINK_OUT = 6;\n// Notice:\n// Any \"interval\" should be by the order of [low, high].\n// \"handle0\" (handleIndex === 0) maps to\n// low data value: this._dataInterval[0] and has low coord.\n// \"handle1\" (handleIndex === 1) maps to\n// high data value: this._dataInterval[1] and has high coord.\n// The logic of transform is implemented in this._createBarGroup.\nvar ContinuousView = /** @class */function (_super) {\n  __extends(ContinuousView, _super);\n  function ContinuousView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousView.type;\n    _this._shapes = {};\n    _this._dataInterval = [];\n    _this._handleEnds = [];\n    _this._hoverLinkDataIndices = [];\n    return _this;\n  }\n  ContinuousView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.call(this, ecModel, api);\n    this._hoverLinkFromSeriesMouseOver = zrUtil.bind(this._hoverLinkFromSeriesMouseOver, this);\n    this._hideIndicator = zrUtil.bind(this._hideIndicator, this);\n  };\n  ContinuousView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {\n    if (!payload || payload.type !== 'selectDataRange' || payload.from !== this.uid) {\n      this._buildView();\n    }\n  };\n  ContinuousView.prototype._buildView = function () {\n    this.group.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var thisGroup = this.group;\n    this._orient = visualMapModel.get('orient');\n    this._useHandle = visualMapModel.get('calculable');\n    this._resetInterval();\n    this._renderBar(thisGroup);\n    var dataRangeText = visualMapModel.get('text');\n    this._renderEndsText(thisGroup, dataRangeText, 0);\n    this._renderEndsText(thisGroup, dataRangeText, 1);\n    // Do this for background size calculation.\n    this._updateView(true);\n    // After updating view, inner shapes is built completely,\n    // and then background can be rendered.\n    this.renderBackground(thisGroup);\n    // Real update view\n    this._updateView();\n    this._enableHoverLinkToSeries();\n    this._enableHoverLinkFromSeries();\n    this.positionGroup(thisGroup);\n  };\n  ContinuousView.prototype._renderEndsText = function (group, dataRangeText, endsIndex) {\n    if (!dataRangeText) {\n      return;\n    }\n    // Compatible with ec2, text[0] map to high value, text[1] map low value.\n    var text = dataRangeText[1 - endsIndex];\n    text = text != null ? text + '' : '';\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var itemSize = visualMapModel.itemSize;\n    var barGroup = this._shapes.mainGroup;\n    var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);\n    var align = this._applyTransform(endsIndex === 0 ? 'bottom' : 'top', barGroup);\n    var orient = this._orient;\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    this.group.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: position[0],\n        y: position[1],\n        verticalAlign: orient === 'horizontal' ? 'middle' : align,\n        align: orient === 'horizontal' ? align : 'center',\n        text: text\n      })\n    }));\n  };\n  ContinuousView.prototype._renderBar = function (targetGroup) {\n    var visualMapModel = this.visualMapModel;\n    var shapes = this._shapes;\n    var itemSize = visualMapModel.itemSize;\n    var orient = this._orient;\n    var useHandle = this._useHandle;\n    var itemAlign = helper.getItemAlign(visualMapModel, this.api, itemSize);\n    var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);\n    var gradientBarGroup = new graphic.Group();\n    mainGroup.add(gradientBarGroup);\n    // Bar\n    gradientBarGroup.add(shapes.outOfRange = createPolygon());\n    gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, zrUtil.bind(this._dragHandle, this, 'all', false), zrUtil.bind(this._dragHandle, this, 'all', true)));\n    // A border radius clip.\n    gradientBarGroup.setClipPath(new graphic.Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: itemSize[0],\n        height: itemSize[1],\n        r: 3\n      }\n    }));\n    var textRect = visualMapModel.textStyleModel.getTextRect('国');\n    var textSize = mathMax(textRect.width, textRect.height);\n    // Handle\n    if (useHandle) {\n      shapes.handleThumbs = [];\n      shapes.handleLabels = [];\n      shapes.handleLabelPoints = [];\n      this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);\n      this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);\n    }\n    this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);\n    targetGroup.add(mainGroup);\n  };\n  ContinuousView.prototype._createHandle = function (visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {\n    var onDrift = zrUtil.bind(this._dragHandle, this, handleIndex, false);\n    var onDragEnd = zrUtil.bind(this._dragHandle, this, handleIndex, true);\n    var handleSize = parsePercent(visualMapModel.get('handleSize'), itemSize[0]);\n    var handleThumb = createSymbol(visualMapModel.get('handleIcon'), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);\n    var cursor = getCursor(this._orient);\n    handleThumb.attr({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      ondragend: onDragEnd,\n      onmousemove: function (e) {\n        eventTool.stop(e.event);\n      }\n    });\n    handleThumb.x = itemSize[0] / 2;\n    handleThumb.useStyle(visualMapModel.getModel('handleStyle').getItemStyle());\n    handleThumb.setStyle({\n      strokeNoScale: true,\n      strokeFirst: true\n    });\n    handleThumb.style.lineWidth *= 2;\n    handleThumb.ensureState('emphasis').style = visualMapModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n    setAsHighDownDispatcher(handleThumb, true);\n    mainGroup.add(handleThumb);\n    // Text is always horizontal layout but should not be effected by\n    // transform (orient/inverse). So label is built separately but not\n    // use zrender/graphic/helper/RectText, and is located based on view\n    // group (according to handleLabelPoint) but not barGroup.\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var handleLabel = new graphic.Text({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      onmousemove: function (e) {\n        // For mobile device, prevent screen slider on the button.\n        eventTool.stop(e.event);\n      },\n      ondragend: onDragEnd,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    handleLabel.ensureState('blur').style = {\n      opacity: 0.1\n    };\n    handleLabel.stateTransition = {\n      duration: 200\n    };\n    this.group.add(handleLabel);\n    var handleLabelPoint = [handleSize, 0];\n    var shapes = this._shapes;\n    shapes.handleThumbs[handleIndex] = handleThumb;\n    shapes.handleLabelPoints[handleIndex] = handleLabelPoint;\n    shapes.handleLabels[handleIndex] = handleLabel;\n  };\n  ContinuousView.prototype._createIndicator = function (visualMapModel, mainGroup, itemSize, textSize, orient) {\n    var scale = parsePercent(visualMapModel.get('indicatorSize'), itemSize[0]);\n    var indicator = createSymbol(visualMapModel.get('indicatorIcon'), -scale / 2, -scale / 2, scale, scale, null, true);\n    indicator.attr({\n      cursor: 'move',\n      invisible: true,\n      silent: true,\n      x: itemSize[0] / 2\n    });\n    var indicatorStyle = visualMapModel.getModel('indicatorStyle').getItemStyle();\n    if (indicator instanceof ZRImage) {\n      var pathStyle = indicator.style;\n      indicator.useStyle(zrUtil.extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, indicatorStyle));\n    } else {\n      indicator.useStyle(indicatorStyle);\n    }\n    mainGroup.add(indicator);\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var indicatorLabel = new graphic.Text({\n      silent: true,\n      invisible: true,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    this.group.add(indicatorLabel);\n    var indicatorLabelPoint = [(orient === 'horizontal' ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];\n    var shapes = this._shapes;\n    shapes.indicator = indicator;\n    shapes.indicatorLabel = indicatorLabel;\n    shapes.indicatorLabelPoint = indicatorLabelPoint;\n    this._firstShowIndicator = true;\n  };\n  ContinuousView.prototype._dragHandle = function (handleIndex, isEnd,\n  // dx is event from ondragend if isEnd is true. It's not used\n  dx, dy) {\n    if (!this._useHandle) {\n      return;\n    }\n    this._dragging = !isEnd;\n    if (!isEnd) {\n      // Transform dx, dy to bar coordination.\n      var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);\n      this._updateInterval(handleIndex, vertex[1]);\n      this._hideIndicator();\n      // Considering realtime, update view should be executed\n      // before dispatch action.\n      this._updateView();\n    }\n    // dragEnd do not dispatch action when realtime.\n    if (isEnd === !this.visualMapModel.get('realtime')) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'selectDataRange',\n        from: this.uid,\n        visualMapId: this.visualMapModel.id,\n        selected: this._dataInterval.slice()\n      });\n    }\n    if (isEnd) {\n      !this._hovering && this._clearHoverLinkToSeries();\n    } else if (useHoverLinkOnHandle(this.visualMapModel)) {\n      this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);\n    }\n  };\n  ContinuousView.prototype._resetInterval = function () {\n    var visualMapModel = this.visualMapModel;\n    var dataInterval = this._dataInterval = visualMapModel.getSelected();\n    var dataExtent = visualMapModel.getExtent();\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];\n  };\n  /**\r\n   * @private\r\n   * @param {(number|string)} handleIndex 0 or 1 or 'all'\r\n   * @param {number} dx\r\n   * @param {number} dy\r\n   */\n  ContinuousView.prototype._updateInterval = function (handleIndex, delta) {\n    delta = delta || 0;\n    var visualMapModel = this.visualMapModel;\n    var handleEnds = this._handleEnds;\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    sliderMove(delta, handleEnds, sizeExtent, handleIndex,\n    // cross is forbidden\n    0);\n    var dataExtent = visualMapModel.getExtent();\n    // Update data interval.\n    this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];\n  };\n  ContinuousView.prototype._updateView = function (forSketch) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var shapes = this._shapes;\n    var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];\n    var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;\n    var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, 'inRange');\n    var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, 'outOfRange');\n    shapes.inRange.setStyle({\n      fill: visualInRange.barColor\n      // opacity: visualInRange.opacity\n    }).setShape('points', visualInRange.barPoints);\n    shapes.outOfRange.setStyle({\n      fill: visualOutOfRange.barColor\n      // opacity: visualOutOfRange.opacity\n    }).setShape('points', visualOutOfRange.barPoints);\n    this._updateHandle(inRangeHandleEnds, visualInRange);\n  };\n  ContinuousView.prototype._createBarVisual = function (dataInterval, dataExtent, handleEnds, forceState) {\n    var opts = {\n      forceState: forceState,\n      convertOpacityToAlpha: true\n    };\n    var colorStops = this._makeColorGradient(dataInterval, opts);\n    var symbolSizes = [this.getControllerVisual(dataInterval[0], 'symbolSize', opts), this.getControllerVisual(dataInterval[1], 'symbolSize', opts)];\n    var barPoints = this._createBarPoints(handleEnds, symbolSizes);\n    return {\n      barColor: new LinearGradient(0, 0, 0, 1, colorStops),\n      barPoints: barPoints,\n      handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]\n    };\n  };\n  ContinuousView.prototype._makeColorGradient = function (dataInterval, opts) {\n    // Considering colorHue, which is not linear, so we have to sample\n    // to calculate gradient color stops, but not only calculate head\n    // and tail.\n    var sampleNumber = 100; // Arbitrary value.\n    var colorStops = [];\n    var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[0], 'color', opts),\n      offset: 0\n    });\n    for (var i = 1; i < sampleNumber; i++) {\n      var currValue = dataInterval[0] + step * i;\n      if (currValue > dataInterval[1]) {\n        break;\n      }\n      colorStops.push({\n        color: this.getControllerVisual(currValue, 'color', opts),\n        offset: i / sampleNumber\n      });\n    }\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[1], 'color', opts),\n      offset: 1\n    });\n    return colorStops;\n  };\n  ContinuousView.prototype._createBarPoints = function (handleEnds, symbolSizes) {\n    var itemSize = this.visualMapModel.itemSize;\n    return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];\n  };\n  ContinuousView.prototype._createBarGroup = function (itemAlign) {\n    var orient = this._orient;\n    var inverse = this.visualMapModel.get('inverse');\n    return new graphic.Group(orient === 'horizontal' && !inverse ? {\n      scaleX: itemAlign === 'bottom' ? 1 : -1,\n      rotation: Math.PI / 2\n    } : orient === 'horizontal' && inverse ? {\n      scaleX: itemAlign === 'bottom' ? -1 : 1,\n      rotation: -Math.PI / 2\n    } : orient === 'vertical' && !inverse ? {\n      scaleX: itemAlign === 'left' ? 1 : -1,\n      scaleY: -1\n    } : {\n      scaleX: itemAlign === 'left' ? 1 : -1\n    });\n  };\n  ContinuousView.prototype._updateHandle = function (handleEnds, visualInRange) {\n    if (!this._useHandle) {\n      return;\n    }\n    var shapes = this._shapes;\n    var visualMapModel = this.visualMapModel;\n    var handleThumbs = shapes.handleThumbs;\n    var handleLabels = shapes.handleLabels;\n    var itemSize = visualMapModel.itemSize;\n    var dataExtent = visualMapModel.getExtent();\n    var align = this._applyTransform('left', shapes.mainGroup);\n    each([0, 1], function (handleIndex) {\n      var handleThumb = handleThumbs[handleIndex];\n      handleThumb.setStyle('fill', visualInRange.handlesColor[handleIndex]);\n      handleThumb.y = handleEnds[handleIndex];\n      var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);\n      var symbolSize = this.getControllerVisual(val, 'symbolSize');\n      handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];\n      handleThumb.x = itemSize[0] - symbolSize / 2;\n      // Update handle label position.\n      var textPoint = graphic.applyTransform(shapes.handleLabelPoints[handleIndex], graphic.getTransform(handleThumb, this.group));\n      if (this._orient === 'horizontal') {\n        // If visualMap controls symbol size, an additional offset needs to be added to labels to avoid collision at minimum size.\n        // Offset reaches value of 0 at \"maximum\" position, so maximum position is not altered at all.\n        var minimumOffset = align === 'left' || align === 'top' ? (itemSize[0] - symbolSize) / 2 : (itemSize[0] - symbolSize) / -2;\n        textPoint[1] += minimumOffset;\n      }\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),\n        verticalAlign: 'middle',\n        align: this._orient === 'vertical' ? this._applyTransform('left', shapes.mainGroup) : 'center'\n      });\n    }, this);\n  };\n  ContinuousView.prototype._showIndicator = function (cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var itemSize = visualMapModel.itemSize;\n    var sizeExtent = [0, itemSize[1]];\n    var shapes = this._shapes;\n    var indicator = shapes.indicator;\n    if (!indicator) {\n      return;\n    }\n    indicator.attr('invisible', false);\n    var opts = {\n      convertOpacityToAlpha: true\n    };\n    var color = this.getControllerVisual(cursorValue, 'color', opts);\n    var symbolSize = this.getControllerVisual(cursorValue, 'symbolSize');\n    var y = linearMap(cursorValue, dataExtent, sizeExtent, true);\n    var x = itemSize[0] - symbolSize / 2;\n    var oldIndicatorPos = {\n      x: indicator.x,\n      y: indicator.y\n    };\n    // Update handle label position.\n    indicator.y = y;\n    indicator.x = x;\n    var textPoint = graphic.applyTransform(shapes.indicatorLabelPoint, graphic.getTransform(indicator, this.group));\n    var indicatorLabel = shapes.indicatorLabel;\n    indicatorLabel.attr('invisible', false);\n    var align = this._applyTransform('left', shapes.mainGroup);\n    var orient = this._orient;\n    var isHorizontal = orient === 'horizontal';\n    indicatorLabel.setStyle({\n      text: (rangeSymbol ? rangeSymbol : '') + visualMapModel.formatValueText(textValue),\n      verticalAlign: isHorizontal ? align : 'middle',\n      align: isHorizontal ? 'center' : align\n    });\n    var indicatorNewProps = {\n      x: x,\n      y: y,\n      style: {\n        fill: color\n      }\n    };\n    var labelNewProps = {\n      style: {\n        x: textPoint[0],\n        y: textPoint[1]\n      }\n    };\n    if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {\n      var animationCfg = {\n        duration: 100,\n        easing: 'cubicInOut',\n        additive: true\n      };\n      indicator.x = oldIndicatorPos.x;\n      indicator.y = oldIndicatorPos.y;\n      indicator.animateTo(indicatorNewProps, animationCfg);\n      indicatorLabel.animateTo(labelNewProps, animationCfg);\n    } else {\n      indicator.attr(indicatorNewProps);\n      indicatorLabel.attr(labelNewProps);\n    }\n    this._firstShowIndicator = false;\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.enterBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._enableHoverLinkToSeries = function () {\n    var self = this;\n    this._shapes.mainGroup.on('mousemove', function (e) {\n      self._hovering = true;\n      if (!self._dragging) {\n        var itemSize = self.visualMapModel.itemSize;\n        var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true);\n        // For hover link show when hover handle, which might be\n        // below or upper than sizeExtent.\n        pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);\n        self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);\n      }\n    }).on('mouseout', function () {\n      // When mouse is out of handle, hoverLink still need\n      // to be displayed when realtime is set as false.\n      self._hovering = false;\n      !self._dragging && self._clearHoverLinkToSeries();\n    });\n  };\n  ContinuousView.prototype._enableHoverLinkFromSeries = function () {\n    var zr = this.api.getZr();\n    if (this.visualMapModel.option.hoverLink) {\n      zr.on('mouseover', this._hoverLinkFromSeriesMouseOver, this);\n      zr.on('mouseout', this._hideIndicator, this);\n    } else {\n      this._clearHoverLinkFromSeries();\n    }\n  };\n  ContinuousView.prototype._doHoverLinkToSeries = function (cursorPos, hoverOnBar) {\n    var visualMapModel = this.visualMapModel;\n    var itemSize = visualMapModel.itemSize;\n    if (!visualMapModel.option.hoverLink) {\n      return;\n    }\n    var sizeExtent = [0, itemSize[1]];\n    var dataExtent = visualMapModel.getExtent();\n    // For hover link show when hover handle, which might be below or upper than sizeExtent.\n    cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);\n    var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);\n    var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];\n    var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);\n    var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)];\n    // Consider data range is out of visualMap range, see test/visualMap-continuous.html,\n    // where china and india has very large population.\n    hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);\n    hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity);\n    // Do not show indicator when mouse is over handle,\n    // otherwise labels overlap, especially when dragging.\n    if (hoverOnBar) {\n      if (valueRange[0] === -Infinity) {\n        this._showIndicator(cursorValue, valueRange[1], '< ', halfHoverLinkSize);\n      } else if (valueRange[1] === Infinity) {\n        this._showIndicator(cursorValue, valueRange[0], '> ', halfHoverLinkSize);\n      } else {\n        this._showIndicator(cursorValue, cursorValue, '≈ ', halfHoverLinkSize);\n      }\n    }\n    // When realtime is set as false, handles, which are in barGroup,\n    // also trigger hoverLink, which help user to realize where they\n    // focus on when dragging. (see test/heatmap-large.html)\n    // When realtime is set as true, highlight will not show when hover\n    // handle, because the label on handle, which displays a exact value\n    // but not range, might mislead users.\n    var oldBatch = this._hoverLinkDataIndices;\n    var newBatch = [];\n    if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {\n      newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);\n    }\n    var resultBatches = modelUtil.compressBatches(oldBatch, newBatch);\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(resultBatches[0], visualMapModel));\n    this._dispatchHighDown('highlight', helper.makeHighDownBatch(resultBatches[1], visualMapModel));\n  };\n  ContinuousView.prototype._hoverLinkFromSeriesMouseOver = function (e) {\n    var ecData;\n    findEventDispatcher(e.target, function (target) {\n      var currECData = getECData(target);\n      if (currECData.dataIndex != null) {\n        ecData = currECData;\n        return true;\n      }\n    }, true);\n    if (!ecData) {\n      return;\n    }\n    var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);\n    var visualMapModel = this.visualMapModel;\n    if (!visualMapModel.isTargetSeries(dataModel)) {\n      return;\n    }\n    var data = dataModel.getData(ecData.dataType);\n    var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);\n    if (!isNaN(value)) {\n      this._showIndicator(value, value);\n    }\n  };\n  ContinuousView.prototype._hideIndicator = function () {\n    var shapes = this._shapes;\n    shapes.indicator && shapes.indicator.attr('invisible', true);\n    shapes.indicatorLabel && shapes.indicatorLabel.attr('invisible', true);\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.leaveBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._clearHoverLinkToSeries = function () {\n    this._hideIndicator();\n    var indices = this._hoverLinkDataIndices;\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(indices, this.visualMapModel));\n    indices.length = 0;\n  };\n  ContinuousView.prototype._clearHoverLinkFromSeries = function () {\n    this._hideIndicator();\n    var zr = this.api.getZr();\n    zr.off('mouseover', this._hoverLinkFromSeriesMouseOver);\n    zr.off('mouseout', this._hideIndicator);\n  };\n  ContinuousView.prototype._applyTransform = function (vertex, element, inverse, global) {\n    var transform = graphic.getTransform(element, global ? null : this.group);\n    return zrUtil.isArray(vertex) ? graphic.applyTransform(vertex, transform, inverse) : graphic.transformDirection(vertex, transform, inverse);\n  };\n  // TODO: TYPE more specified payload types.\n  ContinuousView.prototype._dispatchHighDown = function (type, batch) {\n    batch && batch.length && this.api.dispatchAction({\n      type: type,\n      batch: batch\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousView.prototype.dispose = function () {\n    this._clearHoverLinkFromSeries();\n    this._clearHoverLinkToSeries();\n  };\n  ContinuousView.type = 'visualMap.continuous';\n  return ContinuousView;\n}(VisualMapView);\nfunction createPolygon(points, cursor, onDrift, onDragEnd) {\n  return new graphic.Polygon({\n    shape: {\n      points: points\n    },\n    draggable: !!onDrift,\n    cursor: cursor,\n    drift: onDrift,\n    onmousemove: function (e) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n    },\n    ondragend: onDragEnd\n  });\n}\nfunction getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {\n  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;\n  var hoverLinkDataSize = visualMapModel.get('hoverLinkDataSize');\n  if (hoverLinkDataSize) {\n    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;\n  }\n  return halfHoverLinkSize;\n}\nfunction useHoverLinkOnHandle(visualMapModel) {\n  var hoverLinkOnHandle = visualMapModel.get('hoverLinkOnHandle');\n  return !!(hoverLinkOnHandle == null ? visualMapModel.get('realtime') : hoverLinkOnHandle);\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default ContinuousView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,IAAIC,SAAS,GAAGX,UAAU,CAACW,SAAS;AACpC,IAAIC,IAAI,GAAGjB,MAAM,CAACiB,IAAI;AACtB,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB;AACA,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,cAAc,GAAG,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClD3B,SAAS,CAAC0B,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,OAAO,GAAG,CAAC,CAAC;IAClBJ,KAAK,CAACK,aAAa,GAAG,EAAE;IACxBL,KAAK,CAACM,WAAW,GAAG,EAAE;IACtBN,KAAK,CAACO,qBAAqB,GAAG,EAAE;IAChC,OAAOP,KAAK;EACd;EACAF,cAAc,CAACU,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACtDZ,MAAM,CAACS,SAAS,CAACC,IAAI,CAACG,IAAI,CAAC,IAAI,EAAEF,OAAO,EAAEC,GAAG,CAAC;IAC9C,IAAI,CAACE,6BAA6B,GAAGxC,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACD,6BAA6B,EAAE,IAAI,CAAC;IAC1F,IAAI,CAACE,cAAc,GAAG1C,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAAC;EAC9D,CAAC;EACDjB,cAAc,CAACU,SAAS,CAACQ,QAAQ,GAAG,UAAUC,cAAc,EAAEP,OAAO,EAAEC,GAAG,EAAEO,OAAO,EAAE;IACnF,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACf,IAAI,KAAK,iBAAiB,IAAIe,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC/E,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC;EACDvB,cAAc,CAACU,SAAS,CAACa,UAAU,GAAG,YAAY;IAChD,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIN,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIO,SAAS,GAAG,IAAI,CAACF,KAAK;IAC1B,IAAI,CAACG,OAAO,GAAGR,cAAc,CAACS,GAAG,CAAC,QAAQ,CAAC;IAC3C,IAAI,CAACC,UAAU,GAAGV,cAAc,CAACS,GAAG,CAAC,YAAY,CAAC;IAClD,IAAI,CAACE,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,UAAU,CAACL,SAAS,CAAC;IAC1B,IAAIM,aAAa,GAAGb,cAAc,CAACS,GAAG,CAAC,MAAM,CAAC;IAC9C,IAAI,CAACK,eAAe,CAACP,SAAS,EAAEM,aAAa,EAAE,CAAC,CAAC;IACjD,IAAI,CAACC,eAAe,CAACP,SAAS,EAAEM,aAAa,EAAE,CAAC,CAAC;IACjD;IACA,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC;IACtB;IACA;IACA,IAAI,CAACC,gBAAgB,CAACT,SAAS,CAAC;IAChC;IACA,IAAI,CAACQ,WAAW,CAAC,CAAC;IAClB,IAAI,CAACE,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,aAAa,CAACZ,SAAS,CAAC;EAC/B,CAAC;EACD1B,cAAc,CAACU,SAAS,CAACuB,eAAe,GAAG,UAAUT,KAAK,EAAEQ,aAAa,EAAEO,SAAS,EAAE;IACpF,IAAI,CAACP,aAAa,EAAE;MAClB;IACF;IACA;IACA,IAAIQ,IAAI,GAAGR,aAAa,CAAC,CAAC,GAAGO,SAAS,CAAC;IACvCC,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,EAAE,GAAG,EAAE;IACpC,IAAIrB,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIsB,OAAO,GAAGtB,cAAc,CAACS,GAAG,CAAC,SAAS,CAAC;IAC3C,IAAIc,QAAQ,GAAGvB,cAAc,CAACuB,QAAQ;IACtC,IAAIC,QAAQ,GAAG,IAAI,CAACrC,OAAO,CAACsC,SAAS;IACrC,IAAIC,QAAQ,GAAG,IAAI,CAACC,eAAe,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEH,SAAS,KAAK,CAAC,GAAG,CAACE,OAAO,GAAGC,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAAC,EAAEE,QAAQ,CAAC;IACpH,IAAII,KAAK,GAAG,IAAI,CAACD,eAAe,CAACP,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,EAAEI,QAAQ,CAAC;IAC9E,IAAIK,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAIsB,cAAc,GAAG,IAAI,CAAC9B,cAAc,CAAC8B,cAAc;IACvD,IAAI,CAACzB,KAAK,CAAC0B,GAAG,CAAC,IAAIvE,OAAO,CAACwE,IAAI,CAAC;MAC9BC,KAAK,EAAE/D,eAAe,CAAC4D,cAAc,EAAE;QACrCI,CAAC,EAAER,QAAQ,CAAC,CAAC,CAAC;QACdS,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC;QACdU,aAAa,EAAEP,MAAM,KAAK,YAAY,GAAG,QAAQ,GAAGD,KAAK;QACzDA,KAAK,EAAEC,MAAM,KAAK,YAAY,GAAGD,KAAK,GAAG,QAAQ;QACjDP,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EACDxC,cAAc,CAACU,SAAS,CAACqB,UAAU,GAAG,UAAUyB,WAAW,EAAE;IAC3D,IAAIrC,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIsC,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzB,IAAIoC,QAAQ,GAAGvB,cAAc,CAACuB,QAAQ;IACtC,IAAIM,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAI+B,SAAS,GAAG,IAAI,CAAC7B,UAAU;IAC/B,IAAI8B,SAAS,GAAG7E,MAAM,CAAC8E,YAAY,CAACzC,cAAc,EAAE,IAAI,CAACN,GAAG,EAAE6B,QAAQ,CAAC;IACvE,IAAIE,SAAS,GAAGa,MAAM,CAACb,SAAS,GAAG,IAAI,CAACiB,eAAe,CAACF,SAAS,CAAC;IAClE,IAAIG,gBAAgB,GAAG,IAAInF,OAAO,CAACoF,KAAK,CAAC,CAAC;IAC1CnB,SAAS,CAACM,GAAG,CAACY,gBAAgB,CAAC;IAC/B;IACAA,gBAAgB,CAACZ,GAAG,CAACO,MAAM,CAACO,UAAU,GAAGC,aAAa,CAAC,CAAC,CAAC;IACzDH,gBAAgB,CAACZ,GAAG,CAACO,MAAM,CAACS,OAAO,GAAGD,aAAa,CAAC,IAAI,EAAEP,SAAS,GAAGS,SAAS,CAAC,IAAI,CAACxC,OAAO,CAAC,GAAG,IAAI,EAAEpD,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACoD,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE7F,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACoD,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3M;IACAN,gBAAgB,CAACO,WAAW,CAAC,IAAI1F,OAAO,CAAC2F,IAAI,CAAC;MAC5CC,KAAK,EAAE;QACLlB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJkB,KAAK,EAAE9B,QAAQ,CAAC,CAAC,CAAC;QAClB+B,MAAM,EAAE/B,QAAQ,CAAC,CAAC,CAAC;QACnBgC,CAAC,EAAE;MACL;IACF,CAAC,CAAC,CAAC;IACH,IAAIC,QAAQ,GAAGxD,cAAc,CAAC8B,cAAc,CAAC2B,WAAW,CAAC,GAAG,CAAC;IAC7D,IAAIC,QAAQ,GAAGjF,OAAO,CAAC+E,QAAQ,CAACH,KAAK,EAAEG,QAAQ,CAACF,MAAM,CAAC;IACvD;IACA,IAAIf,SAAS,EAAE;MACbD,MAAM,CAACqB,YAAY,GAAG,EAAE;MACxBrB,MAAM,CAACsB,YAAY,GAAG,EAAE;MACxBtB,MAAM,CAACuB,iBAAiB,GAAG,EAAE;MAC7B,IAAI,CAACC,aAAa,CAAC9D,cAAc,EAAEyB,SAAS,EAAE,CAAC,EAAEF,QAAQ,EAAEmC,QAAQ,EAAE7B,MAAM,CAAC;MAC5E,IAAI,CAACiC,aAAa,CAAC9D,cAAc,EAAEyB,SAAS,EAAE,CAAC,EAAEF,QAAQ,EAAEmC,QAAQ,EAAE7B,MAAM,CAAC;IAC9E;IACA,IAAI,CAACkC,gBAAgB,CAAC/D,cAAc,EAAEyB,SAAS,EAAEF,QAAQ,EAAEmC,QAAQ,EAAE7B,MAAM,CAAC;IAC5EQ,WAAW,CAACN,GAAG,CAACN,SAAS,CAAC;EAC5B,CAAC;EACD5C,cAAc,CAACU,SAAS,CAACuE,aAAa,GAAG,UAAU9D,cAAc,EAAEyB,SAAS,EAAEuC,WAAW,EAAEzC,QAAQ,EAAEmC,QAAQ,EAAE7B,MAAM,EAAE;IACrH,IAAIoC,OAAO,GAAG7G,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACoD,WAAW,EAAE,IAAI,EAAEe,WAAW,EAAE,KAAK,CAAC;IACrE,IAAIE,SAAS,GAAG9G,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACoD,WAAW,EAAE,IAAI,EAAEe,WAAW,EAAE,IAAI,CAAC;IACtE,IAAIG,UAAU,GAAGtG,YAAY,CAACmC,cAAc,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI6C,WAAW,GAAGrG,YAAY,CAACiC,cAAc,CAACS,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC0D,UAAU,GAAG,CAAC,EAAE,CAACA,UAAU,GAAG,CAAC,EAAEA,UAAU,EAAEA,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;IACtI,IAAIE,MAAM,GAAGrB,SAAS,CAAC,IAAI,CAACxC,OAAO,CAAC;IACpC4D,WAAW,CAACE,IAAI,CAAC;MACfD,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAE,IAAI;MACfC,KAAK,EAAEP,OAAO;MACdQ,SAAS,EAAEP,SAAS;MACpBQ,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACxBrH,SAAS,CAACsH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;MACzB;IACF,CAAC,CAAC;IACFT,WAAW,CAAClC,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/B6C,WAAW,CAACU,QAAQ,CAAC9E,cAAc,CAAC+E,QAAQ,CAAC,aAAa,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC3EZ,WAAW,CAACa,QAAQ,CAAC;MACnBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFf,WAAW,CAACnC,KAAK,CAACmD,SAAS,IAAI,CAAC;IAChChB,WAAW,CAACiB,WAAW,CAAC,UAAU,CAAC,CAACpD,KAAK,GAAGjC,cAAc,CAAC+E,QAAQ,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IAC/GlH,uBAAuB,CAACsG,WAAW,EAAE,IAAI,CAAC;IAC1C3C,SAAS,CAACM,GAAG,CAACqC,WAAW,CAAC;IAC1B;IACA;IACA;IACA;IACA,IAAItC,cAAc,GAAG,IAAI,CAAC9B,cAAc,CAAC8B,cAAc;IACvD,IAAIwD,WAAW,GAAG,IAAI9H,OAAO,CAACwE,IAAI,CAAC;MACjCqC,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAE,IAAI;MACfC,KAAK,EAAEP,OAAO;MACdS,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACxB;QACArH,SAAS,CAACsH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;MACzB,CAAC;MACDJ,SAAS,EAAEP,SAAS;MACpBjC,KAAK,EAAE/D,eAAe,CAAC4D,cAAc,EAAE;QACrCI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJd,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC;IACFiE,WAAW,CAACD,WAAW,CAAC,MAAM,CAAC,CAACpD,KAAK,GAAG;MACtCsD,OAAO,EAAE;IACX,CAAC;IACDD,WAAW,CAACE,eAAe,GAAG;MAC5BC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAI,CAACpF,KAAK,CAAC0B,GAAG,CAACuD,WAAW,CAAC;IAC3B,IAAII,gBAAgB,GAAG,CAACvB,UAAU,EAAE,CAAC,CAAC;IACtC,IAAI7B,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzBmD,MAAM,CAACqB,YAAY,CAACK,WAAW,CAAC,GAAGI,WAAW;IAC9C9B,MAAM,CAACuB,iBAAiB,CAACG,WAAW,CAAC,GAAG0B,gBAAgB;IACxDpD,MAAM,CAACsB,YAAY,CAACI,WAAW,CAAC,GAAGsB,WAAW;EAChD,CAAC;EACDzG,cAAc,CAACU,SAAS,CAACwE,gBAAgB,GAAG,UAAU/D,cAAc,EAAEyB,SAAS,EAAEF,QAAQ,EAAEmC,QAAQ,EAAE7B,MAAM,EAAE;IAC3G,IAAI8D,KAAK,GAAG9H,YAAY,CAACmC,cAAc,CAACS,GAAG,CAAC,eAAe,CAAC,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAIqE,SAAS,GAAG7H,YAAY,CAACiC,cAAc,CAACS,GAAG,CAAC,eAAe,CAAC,EAAE,CAACkF,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAEA,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IACnHC,SAAS,CAACtB,IAAI,CAAC;MACbD,MAAM,EAAE,MAAM;MACdwB,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZ5D,CAAC,EAAEX,QAAQ,CAAC,CAAC,CAAC,GAAG;IACnB,CAAC,CAAC;IACF,IAAIwE,cAAc,GAAG/F,cAAc,CAAC+E,QAAQ,CAAC,gBAAgB,CAAC,CAACC,YAAY,CAAC,CAAC;IAC7E,IAAIY,SAAS,YAAY5H,OAAO,EAAE;MAChC,IAAIgI,SAAS,GAAGJ,SAAS,CAAC3D,KAAK;MAC/B2D,SAAS,CAACd,QAAQ,CAAC1H,MAAM,CAAC6I,MAAM,CAAC;QAC/B;QACAC,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBhE,CAAC,EAAE8D,SAAS,CAAC9D,CAAC;QACdC,CAAC,EAAE6D,SAAS,CAAC7D,CAAC;QACdkB,KAAK,EAAE2C,SAAS,CAAC3C,KAAK;QACtBC,MAAM,EAAE0C,SAAS,CAAC1C;MACpB,CAAC,EAAEyC,cAAc,CAAC,CAAC;IACrB,CAAC,MAAM;MACLH,SAAS,CAACd,QAAQ,CAACiB,cAAc,CAAC;IACpC;IACAtE,SAAS,CAACM,GAAG,CAAC6D,SAAS,CAAC;IACxB,IAAI9D,cAAc,GAAG,IAAI,CAAC9B,cAAc,CAAC8B,cAAc;IACvD,IAAIqE,cAAc,GAAG,IAAI3I,OAAO,CAACwE,IAAI,CAAC;MACpC8D,MAAM,EAAE,IAAI;MACZD,SAAS,EAAE,IAAI;MACf5D,KAAK,EAAE/D,eAAe,CAAC4D,cAAc,EAAE;QACrCI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJd,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAChB,KAAK,CAAC0B,GAAG,CAACoE,cAAc,CAAC;IAC9B,IAAIC,mBAAmB,GAAG,CAAC,CAACvE,MAAM,KAAK,YAAY,GAAG6B,QAAQ,GAAG,CAAC,GAAG9E,cAAc,IAAI2C,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1G,IAAIe,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzBmD,MAAM,CAACsD,SAAS,GAAGA,SAAS;IAC5BtD,MAAM,CAAC6D,cAAc,GAAGA,cAAc;IACtC7D,MAAM,CAAC8D,mBAAmB,GAAGA,mBAAmB;IAChD,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACjC,CAAC;EACDxH,cAAc,CAACU,SAAS,CAAC0D,WAAW,GAAG,UAAUe,WAAW,EAAEsC,KAAK;EACnE;EACAC,EAAE,EAAEC,EAAE,EAAE;IACN,IAAI,CAAC,IAAI,CAAC9F,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAAC+F,SAAS,GAAG,CAACH,KAAK;IACvB,IAAI,CAACA,KAAK,EAAE;MACV;MACA,IAAII,MAAM,GAAG,IAAI,CAAC/E,eAAe,CAAC,CAAC4E,EAAE,EAAEC,EAAE,CAAC,EAAE,IAAI,CAACrH,OAAO,CAACsC,SAAS,EAAE,IAAI,CAAC;MACzE,IAAI,CAACkF,eAAe,CAAC3C,WAAW,EAAE0C,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAI,CAAC5G,cAAc,CAAC,CAAC;MACrB;MACA;MACA,IAAI,CAACiB,WAAW,CAAC,CAAC;IACpB;IACA;IACA,IAAIuF,KAAK,KAAK,CAAC,IAAI,CAACtG,cAAc,CAACS,GAAG,CAAC,UAAU,CAAC,EAAE;MAClD;MACA,IAAI,CAACf,GAAG,CAACkH,cAAc,CAAC;QACtB1H,IAAI,EAAE,iBAAiB;QACvBgB,IAAI,EAAE,IAAI,CAACC,GAAG;QACd0G,WAAW,EAAE,IAAI,CAAC7G,cAAc,CAAC8G,EAAE;QACnCC,QAAQ,EAAE,IAAI,CAAC3H,aAAa,CAAC4H,KAAK,CAAC;MACrC,CAAC,CAAC;IACJ;IACA,IAAIV,KAAK,EAAE;MACT,CAAC,IAAI,CAACW,SAAS,IAAI,IAAI,CAACC,uBAAuB,CAAC,CAAC;IACnD,CAAC,MAAM,IAAIC,oBAAoB,CAAC,IAAI,CAACnH,cAAc,CAAC,EAAE;MACpD,IAAI,CAACoH,oBAAoB,CAAC,IAAI,CAAC/H,WAAW,CAAC2E,WAAW,CAAC,EAAE,KAAK,CAAC;IACjE;EACF,CAAC;EACDnF,cAAc,CAACU,SAAS,CAACoB,cAAc,GAAG,YAAY;IACpD,IAAIX,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIqH,YAAY,GAAG,IAAI,CAACjI,aAAa,GAAGY,cAAc,CAACsH,WAAW,CAAC,CAAC;IACpE,IAAIC,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAEzH,cAAc,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAClC,WAAW,GAAG,CAACjB,SAAS,CAACiJ,YAAY,CAAC,CAAC,CAAC,EAAEE,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,EAAErJ,SAAS,CAACiJ,YAAY,CAAC,CAAC,CAAC,EAAEE,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,CAAC;EACzI,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE5I,cAAc,CAACU,SAAS,CAACoH,eAAe,GAAG,UAAU3C,WAAW,EAAE0D,KAAK,EAAE;IACvEA,KAAK,GAAGA,KAAK,IAAI,CAAC;IAClB,IAAI1H,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI2H,UAAU,GAAG,IAAI,CAACtI,WAAW;IACjC,IAAIoI,UAAU,GAAG,CAAC,CAAC,EAAEzH,cAAc,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChD7D,UAAU,CAACgK,KAAK,EAAEC,UAAU,EAAEF,UAAU,EAAEzD,WAAW;IACrD;IACA,CAAC,CAAC;IACF,IAAIuD,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C;IACA,IAAI,CAACpI,aAAa,GAAG,CAAChB,SAAS,CAACuJ,UAAU,CAAC,CAAC,CAAC,EAAEF,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,EAAEnJ,SAAS,CAACuJ,UAAU,CAAC,CAAC,CAAC,EAAEF,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,CAAC;EACvI,CAAC;EACD1I,cAAc,CAACU,SAAS,CAACwB,WAAW,GAAG,UAAU6G,SAAS,EAAE;IAC1D,IAAI5H,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIuH,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C,IAAIlF,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzB,IAAI0I,oBAAoB,GAAG,CAAC,CAAC,EAAE7H,cAAc,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAIuG,iBAAiB,GAAGF,SAAS,GAAGC,oBAAoB,GAAG,IAAI,CAACxI,WAAW;IAC3E,IAAI0I,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC5I,aAAa,EAAEmI,UAAU,EAAEO,iBAAiB,EAAE,SAAS,CAAC;IACvG,IAAIG,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,CAACT,UAAU,EAAEA,UAAU,EAAEM,oBAAoB,EAAE,YAAY,CAAC;IACxGvF,MAAM,CAACS,OAAO,CAACkC,QAAQ,CAAC;MACtBiD,IAAI,EAAEH,aAAa,CAACI;MACpB;IACF,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,EAAEL,aAAa,CAACM,SAAS,CAAC;IAC9C/F,MAAM,CAACO,UAAU,CAACoC,QAAQ,CAAC;MACzBiD,IAAI,EAAED,gBAAgB,CAACE;MACvB;IACF,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,EAAEH,gBAAgB,CAACI,SAAS,CAAC;IACjD,IAAI,CAACC,aAAa,CAACR,iBAAiB,EAAEC,aAAa,CAAC;EACtD,CAAC;EACDlJ,cAAc,CAACU,SAAS,CAACyI,gBAAgB,GAAG,UAAUX,YAAY,EAAEE,UAAU,EAAEI,UAAU,EAAEY,UAAU,EAAE;IACtG,IAAIC,IAAI,GAAG;MACTD,UAAU,EAAEA,UAAU;MACtBE,qBAAqB,EAAE;IACzB,CAAC;IACD,IAAIC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAACtB,YAAY,EAAEmB,IAAI,CAAC;IAC5D,IAAII,WAAW,GAAG,CAAC,IAAI,CAACC,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAEmB,IAAI,CAAC,EAAE,IAAI,CAACK,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAEmB,IAAI,CAAC,CAAC;IAChJ,IAAIH,SAAS,GAAG,IAAI,CAACS,gBAAgB,CAACnB,UAAU,EAAEiB,WAAW,CAAC;IAC9D,OAAO;MACLT,QAAQ,EAAE,IAAI9K,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEqL,UAAU,CAAC;MACpDL,SAAS,EAAEA,SAAS;MACpBU,YAAY,EAAE,CAACL,UAAU,CAAC,CAAC,CAAC,CAACM,KAAK,EAAEN,UAAU,CAACA,UAAU,CAACO,MAAM,GAAG,CAAC,CAAC,CAACD,KAAK;IAC7E,CAAC;EACH,CAAC;EACDnK,cAAc,CAACU,SAAS,CAACoJ,kBAAkB,GAAG,UAAUtB,YAAY,EAAEmB,IAAI,EAAE;IAC1E;IACA;IACA;IACA,IAAIU,YAAY,GAAG,GAAG,CAAC,CAAC;IACxB,IAAIR,UAAU,GAAG,EAAE;IACnB,IAAIS,IAAI,GAAG,CAAC9B,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,IAAI6B,YAAY;IAC7DR,UAAU,CAACU,IAAI,CAAC;MACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmB,IAAI,CAAC;MAC/Da,MAAM,EAAE;IACV,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,IAAIC,SAAS,GAAGlC,YAAY,CAAC,CAAC,CAAC,GAAG8B,IAAI,GAAGG,CAAC;MAC1C,IAAIC,SAAS,GAAGlC,YAAY,CAAC,CAAC,CAAC,EAAE;QAC/B;MACF;MACAqB,UAAU,CAACU,IAAI,CAAC;QACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACU,SAAS,EAAE,OAAO,EAAEf,IAAI,CAAC;QACzDa,MAAM,EAAEC,CAAC,GAAGJ;MACd,CAAC,CAAC;IACJ;IACAR,UAAU,CAACU,IAAI,CAAC;MACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmB,IAAI,CAAC;MAC/Da,MAAM,EAAE;IACV,CAAC,CAAC;IACF,OAAOX,UAAU;EACnB,CAAC;EACD7J,cAAc,CAACU,SAAS,CAACuJ,gBAAgB,GAAG,UAAUnB,UAAU,EAAEiB,WAAW,EAAE;IAC7E,IAAIrH,QAAQ,GAAG,IAAI,CAACvB,cAAc,CAACuB,QAAQ;IAC3C,OAAO,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,GAAGqH,WAAW,CAAC,CAAC,CAAC,EAAEjB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACpG,QAAQ,CAAC,CAAC,CAAC,EAAEoG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACpG,QAAQ,CAAC,CAAC,CAAC,EAAEoG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACpG,QAAQ,CAAC,CAAC,CAAC,GAAGqH,WAAW,CAAC,CAAC,CAAC,EAAEjB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EACnK,CAAC;EACD9I,cAAc,CAACU,SAAS,CAACmD,eAAe,GAAG,UAAUF,SAAS,EAAE;IAC9D,IAAIX,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAIgJ,OAAO,GAAG,IAAI,CAACxJ,cAAc,CAACS,GAAG,CAAC,SAAS,CAAC;IAChD,OAAO,IAAIjD,OAAO,CAACoF,KAAK,CAACf,MAAM,KAAK,YAAY,IAAI,CAAC2H,OAAO,GAAG;MAC7DC,MAAM,EAAEjH,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;MACvCkH,QAAQ,EAAEnL,IAAI,CAACoL,EAAE,GAAG;IACtB,CAAC,GAAG9H,MAAM,KAAK,YAAY,IAAI2H,OAAO,GAAG;MACvCC,MAAM,EAAEjH,SAAS,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;MACvCkH,QAAQ,EAAE,CAACnL,IAAI,CAACoL,EAAE,GAAG;IACvB,CAAC,GAAG9H,MAAM,KAAK,UAAU,IAAI,CAAC2H,OAAO,GAAG;MACtCC,MAAM,EAAEjH,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACrCoH,MAAM,EAAE,CAAC;IACX,CAAC,GAAG;MACFH,MAAM,EAAEjH,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EACD3D,cAAc,CAACU,SAAS,CAAC+I,aAAa,GAAG,UAAUX,UAAU,EAAEI,aAAa,EAAE;IAC5E,IAAI,CAAC,IAAI,CAACrH,UAAU,EAAE;MACpB;IACF;IACA,IAAI4B,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzB,IAAIa,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI2D,YAAY,GAAGrB,MAAM,CAACqB,YAAY;IACtC,IAAIC,YAAY,GAAGtB,MAAM,CAACsB,YAAY;IACtC,IAAIrC,QAAQ,GAAGvB,cAAc,CAACuB,QAAQ;IACtC,IAAIgG,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C,IAAI5F,KAAK,GAAG,IAAI,CAACD,eAAe,CAAC,MAAM,EAAEW,MAAM,CAACb,SAAS,CAAC;IAC1DpD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU2F,WAAW,EAAE;MAClC,IAAII,WAAW,GAAGT,YAAY,CAACK,WAAW,CAAC;MAC3CI,WAAW,CAACa,QAAQ,CAAC,MAAM,EAAE8C,aAAa,CAACgB,YAAY,CAAC/E,WAAW,CAAC,CAAC;MACrEI,WAAW,CAACjC,CAAC,GAAGwF,UAAU,CAAC3D,WAAW,CAAC;MACvC,IAAI6F,GAAG,GAAGzL,SAAS,CAACuJ,UAAU,CAAC3D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAEzC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEgG,UAAU,EAAE,IAAI,CAAC;MAChF,IAAIuC,UAAU,GAAG,IAAI,CAACjB,mBAAmB,CAACgB,GAAG,EAAE,YAAY,CAAC;MAC5DzF,WAAW,CAACqF,MAAM,GAAGrF,WAAW,CAACwF,MAAM,GAAGE,UAAU,GAAGvI,QAAQ,CAAC,CAAC,CAAC;MAClE6C,WAAW,CAAClC,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAGuI,UAAU,GAAG,CAAC;MAC5C;MACA,IAAIC,SAAS,GAAGvM,OAAO,CAACwM,cAAc,CAAC1H,MAAM,CAACuB,iBAAiB,CAACG,WAAW,CAAC,EAAExG,OAAO,CAACyM,YAAY,CAAC7F,WAAW,EAAE,IAAI,CAAC/D,KAAK,CAAC,CAAC;MAC5H,IAAI,IAAI,CAACG,OAAO,KAAK,YAAY,EAAE;QACjC;QACA;QACA,IAAI0J,aAAa,GAAGtI,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,KAAK,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC,GAAGuI,UAAU,IAAI,CAAC,GAAG,CAACvI,QAAQ,CAAC,CAAC,CAAC,GAAGuI,UAAU,IAAI,CAAC,CAAC;QAC1HC,SAAS,CAAC,CAAC,CAAC,IAAIG,aAAa;MAC/B;MACAtG,YAAY,CAACI,WAAW,CAAC,CAACiB,QAAQ,CAAC;QACjC/C,CAAC,EAAE6H,SAAS,CAAC,CAAC,CAAC;QACf5H,CAAC,EAAE4H,SAAS,CAAC,CAAC,CAAC;QACf1I,IAAI,EAAErB,cAAc,CAACmK,eAAe,CAAC,IAAI,CAAC/K,aAAa,CAAC4E,WAAW,CAAC,CAAC;QACrE5B,aAAa,EAAE,QAAQ;QACvBR,KAAK,EAAE,IAAI,CAACpB,OAAO,KAAK,UAAU,GAAG,IAAI,CAACmB,eAAe,CAAC,MAAM,EAAEW,MAAM,CAACb,SAAS,CAAC,GAAG;MACxF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD5C,cAAc,CAACU,SAAS,CAAC6K,cAAc,GAAG,UAAUC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;IAC1G,IAAIxK,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIuH,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C,IAAIjG,QAAQ,GAAGvB,cAAc,CAACuB,QAAQ;IACtC,IAAIkG,UAAU,GAAG,CAAC,CAAC,EAAElG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,IAAIe,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzB,IAAIyG,SAAS,GAAGtD,MAAM,CAACsD,SAAS;IAChC,IAAI,CAACA,SAAS,EAAE;MACd;IACF;IACAA,SAAS,CAACtB,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;IAClC,IAAIkE,IAAI,GAAG;MACTC,qBAAqB,EAAE;IACzB,CAAC;IACD,IAAIO,KAAK,GAAG,IAAI,CAACH,mBAAmB,CAACwB,WAAW,EAAE,OAAO,EAAE7B,IAAI,CAAC;IAChE,IAAIsB,UAAU,GAAG,IAAI,CAACjB,mBAAmB,CAACwB,WAAW,EAAE,YAAY,CAAC;IACpE,IAAIlI,CAAC,GAAG/D,SAAS,CAACiM,WAAW,EAAE9C,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC;IAC5D,IAAIvF,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAGuI,UAAU,GAAG,CAAC;IACpC,IAAIW,eAAe,GAAG;MACpBvI,CAAC,EAAE0D,SAAS,CAAC1D,CAAC;MACdC,CAAC,EAAEyD,SAAS,CAACzD;IACf,CAAC;IACD;IACAyD,SAAS,CAACzD,CAAC,GAAGA,CAAC;IACfyD,SAAS,CAAC1D,CAAC,GAAGA,CAAC;IACf,IAAI6H,SAAS,GAAGvM,OAAO,CAACwM,cAAc,CAAC1H,MAAM,CAAC8D,mBAAmB,EAAE5I,OAAO,CAACyM,YAAY,CAACrE,SAAS,EAAE,IAAI,CAACvF,KAAK,CAAC,CAAC;IAC/G,IAAI8F,cAAc,GAAG7D,MAAM,CAAC6D,cAAc;IAC1CA,cAAc,CAAC7B,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;IACvC,IAAI1C,KAAK,GAAG,IAAI,CAACD,eAAe,CAAC,MAAM,EAAEW,MAAM,CAACb,SAAS,CAAC;IAC1D,IAAII,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAIkK,YAAY,GAAG7I,MAAM,KAAK,YAAY;IAC1CsE,cAAc,CAAClB,QAAQ,CAAC;MACtB5D,IAAI,EAAE,CAACkJ,WAAW,GAAGA,WAAW,GAAG,EAAE,IAAIvK,cAAc,CAACmK,eAAe,CAACG,SAAS,CAAC;MAClFlI,aAAa,EAAEsI,YAAY,GAAG9I,KAAK,GAAG,QAAQ;MAC9CA,KAAK,EAAE8I,YAAY,GAAG,QAAQ,GAAG9I;IACnC,CAAC,CAAC;IACF,IAAI+I,iBAAiB,GAAG;MACtBzI,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJF,KAAK,EAAE;QACLiG,IAAI,EAAEc;MACR;IACF,CAAC;IACD,IAAI4B,aAAa,GAAG;MAClB3I,KAAK,EAAE;QACLC,CAAC,EAAE6H,SAAS,CAAC,CAAC,CAAC;QACf5H,CAAC,EAAE4H,SAAS,CAAC,CAAC;MAChB;IACF,CAAC;IACD,IAAI/J,cAAc,CAACP,OAAO,CAACoL,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACxE,mBAAmB,EAAE;MAC5E,IAAIyE,YAAY,GAAG;QACjBrF,QAAQ,EAAE,GAAG;QACbsF,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDpF,SAAS,CAAC1D,CAAC,GAAGuI,eAAe,CAACvI,CAAC;MAC/B0D,SAAS,CAACzD,CAAC,GAAGsI,eAAe,CAACtI,CAAC;MAC/ByD,SAAS,CAACqF,SAAS,CAACN,iBAAiB,EAAEG,YAAY,CAAC;MACpD3E,cAAc,CAAC8E,SAAS,CAACL,aAAa,EAAEE,YAAY,CAAC;IACvD,CAAC,MAAM;MACLlF,SAAS,CAACtB,IAAI,CAACqG,iBAAiB,CAAC;MACjCxE,cAAc,CAAC7B,IAAI,CAACsG,aAAa,CAAC;IACpC;IACA,IAAI,CAACvE,mBAAmB,GAAG,KAAK;IAChC,IAAIzC,YAAY,GAAG,IAAI,CAACzE,OAAO,CAACyE,YAAY;IAC5C,IAAIA,YAAY,EAAE;MAChB,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,YAAY,CAACqF,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC5C;QACA;QACA,IAAI,CAAC5J,GAAG,CAACwL,SAAS,CAACtH,YAAY,CAAC0F,CAAC,CAAC,CAAC;MACrC;IACF;EACF,CAAC;EACDzK,cAAc,CAACU,SAAS,CAAC0B,wBAAwB,GAAG,YAAY;IAC9D,IAAIkK,IAAI,GAAG,IAAI;IACf,IAAI,CAAChM,OAAO,CAACsC,SAAS,CAAC2J,EAAE,CAAC,WAAW,EAAE,UAAUzG,CAAC,EAAE;MAClDwG,IAAI,CAAClE,SAAS,GAAG,IAAI;MACrB,IAAI,CAACkE,IAAI,CAAC1E,SAAS,EAAE;QACnB,IAAIlF,QAAQ,GAAG4J,IAAI,CAACnL,cAAc,CAACuB,QAAQ;QAC3C,IAAI8J,GAAG,GAAGF,IAAI,CAACxJ,eAAe,CAAC,CAACgD,CAAC,CAAC2G,OAAO,EAAE3G,CAAC,CAAC4G,OAAO,CAAC,EAAEJ,IAAI,CAAChM,OAAO,CAACsC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;QAC1F;QACA;QACA4J,GAAG,CAAC,CAAC,CAAC,GAAG/M,OAAO,CAACG,OAAO,CAAC,CAAC,EAAE4M,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE9J,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjD4J,IAAI,CAAC/D,oBAAoB,CAACiE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI9J,QAAQ,CAAC,CAAC,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CAAC6J,EAAE,CAAC,UAAU,EAAE,YAAY;MAC5B;MACA;MACAD,IAAI,CAAClE,SAAS,GAAG,KAAK;MACtB,CAACkE,IAAI,CAAC1E,SAAS,IAAI0E,IAAI,CAACjE,uBAAuB,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EACDrI,cAAc,CAACU,SAAS,CAAC2B,0BAA0B,GAAG,YAAY;IAChE,IAAIsK,EAAE,GAAG,IAAI,CAAC9L,GAAG,CAAC+L,KAAK,CAAC,CAAC;IACzB,IAAI,IAAI,CAACzL,cAAc,CAAC0L,MAAM,CAACC,SAAS,EAAE;MACxCH,EAAE,CAACJ,EAAE,CAAC,WAAW,EAAE,IAAI,CAACxL,6BAA6B,EAAE,IAAI,CAAC;MAC5D4L,EAAE,CAACJ,EAAE,CAAC,UAAU,EAAE,IAAI,CAACtL,cAAc,EAAE,IAAI,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAAC8L,yBAAyB,CAAC,CAAC;IAClC;EACF,CAAC;EACD/M,cAAc,CAACU,SAAS,CAAC6H,oBAAoB,GAAG,UAAUyE,SAAS,EAAEC,UAAU,EAAE;IAC/E,IAAI9L,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIuB,QAAQ,GAAGvB,cAAc,CAACuB,QAAQ;IACtC,IAAI,CAACvB,cAAc,CAAC0L,MAAM,CAACC,SAAS,EAAE;MACpC;IACF;IACA,IAAIlE,UAAU,GAAG,CAAC,CAAC,EAAElG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,IAAIgG,UAAU,GAAGvH,cAAc,CAACwH,SAAS,CAAC,CAAC;IAC3C;IACAqE,SAAS,GAAGvN,OAAO,CAACG,OAAO,CAACgJ,UAAU,CAAC,CAAC,CAAC,EAAEoE,SAAS,CAAC,EAAEpE,UAAU,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI+C,iBAAiB,GAAGuB,oBAAoB,CAAC/L,cAAc,EAAEuH,UAAU,EAAEE,UAAU,CAAC;IACpF,IAAIuE,UAAU,GAAG,CAACH,SAAS,GAAGrB,iBAAiB,EAAEqB,SAAS,GAAGrB,iBAAiB,CAAC;IAC/E,IAAIH,WAAW,GAAGjM,SAAS,CAACyN,SAAS,EAAEpE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC;IACpE,IAAI0E,UAAU,GAAG,CAAC7N,SAAS,CAAC4N,UAAU,CAAC,CAAC,CAAC,EAAEvE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,EAAEnJ,SAAS,CAAC4N,UAAU,CAAC,CAAC,CAAC,EAAEvE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,CAAC;IACjI;IACA;IACAyE,UAAU,CAAC,CAAC,CAAC,GAAGvE,UAAU,CAAC,CAAC,CAAC,KAAKwE,UAAU,CAAC,CAAC,CAAC,GAAG,CAACC,QAAQ,CAAC;IAC5DF,UAAU,CAAC,CAAC,CAAC,GAAGvE,UAAU,CAAC,CAAC,CAAC,KAAKwE,UAAU,CAAC,CAAC,CAAC,GAAGC,QAAQ,CAAC;IAC3D;IACA;IACA,IAAIJ,UAAU,EAAE;MACd,IAAIG,UAAU,CAAC,CAAC,CAAC,KAAK,CAACC,QAAQ,EAAE;QAC/B,IAAI,CAAC9B,cAAc,CAACC,WAAW,EAAE4B,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEzB,iBAAiB,CAAC;MAC1E,CAAC,MAAM,IAAIyB,UAAU,CAAC,CAAC,CAAC,KAAKC,QAAQ,EAAE;QACrC,IAAI,CAAC9B,cAAc,CAACC,WAAW,EAAE4B,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEzB,iBAAiB,CAAC;MAC1E,CAAC,MAAM;QACL,IAAI,CAACJ,cAAc,CAACC,WAAW,EAAEA,WAAW,EAAE,IAAI,EAAEG,iBAAiB,CAAC;MACxE;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI2B,QAAQ,GAAG,IAAI,CAAC7M,qBAAqB;IACzC,IAAI8M,QAAQ,GAAG,EAAE;IACjB,IAAIN,UAAU,IAAI3E,oBAAoB,CAACnH,cAAc,CAAC,EAAE;MACtDoM,QAAQ,GAAG,IAAI,CAAC9M,qBAAqB,GAAGU,cAAc,CAACqM,qBAAqB,CAACJ,UAAU,CAAC;IAC1F;IACA,IAAIK,aAAa,GAAG1O,SAAS,CAAC2O,eAAe,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;IACjE,IAAI,CAACI,iBAAiB,CAAC,UAAU,EAAE7O,MAAM,CAAC8O,iBAAiB,CAACH,aAAa,CAAC,CAAC,CAAC,EAAEtM,cAAc,CAAC,CAAC;IAC9F,IAAI,CAACwM,iBAAiB,CAAC,WAAW,EAAE7O,MAAM,CAAC8O,iBAAiB,CAACH,aAAa,CAAC,CAAC,CAAC,EAAEtM,cAAc,CAAC,CAAC;EACjG,CAAC;EACDnB,cAAc,CAACU,SAAS,CAACK,6BAA6B,GAAG,UAAU+E,CAAC,EAAE;IACpE,IAAI+H,MAAM;IACVvO,mBAAmB,CAACwG,CAAC,CAACgI,MAAM,EAAE,UAAUA,MAAM,EAAE;MAC9C,IAAIC,UAAU,GAAG3O,SAAS,CAAC0O,MAAM,CAAC;MAClC,IAAIC,UAAU,CAACC,SAAS,IAAI,IAAI,EAAE;QAChCH,MAAM,GAAGE,UAAU;QACnB,OAAO,IAAI;MACb;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACF,MAAM,EAAE;MACX;IACF;IACA,IAAII,SAAS,GAAG,IAAI,CAACrN,OAAO,CAACsN,gBAAgB,CAACL,MAAM,CAACM,WAAW,CAAC;IACjE,IAAIhN,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI,CAACA,cAAc,CAACiN,cAAc,CAACH,SAAS,CAAC,EAAE;MAC7C;IACF;IACA,IAAII,IAAI,GAAGJ,SAAS,CAACK,OAAO,CAACT,MAAM,CAACU,QAAQ,CAAC;IAC7C,IAAIC,KAAK,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC7M,GAAG,CAACT,cAAc,CAACuN,qBAAqB,CAACL,IAAI,CAAC,EAAER,MAAM,CAACG,SAAS,CAAC;IAC7F,IAAI,CAACW,KAAK,CAACH,KAAK,CAAC,EAAE;MACjB,IAAI,CAACjD,cAAc,CAACiD,KAAK,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EACDxO,cAAc,CAACU,SAAS,CAACO,cAAc,GAAG,YAAY;IACpD,IAAIwC,MAAM,GAAG,IAAI,CAACnD,OAAO;IACzBmD,MAAM,CAACsD,SAAS,IAAItD,MAAM,CAACsD,SAAS,CAACtB,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;IAC5DhC,MAAM,CAAC6D,cAAc,IAAI7D,MAAM,CAAC6D,cAAc,CAAC7B,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;IACtE,IAAIV,YAAY,GAAG,IAAI,CAACzE,OAAO,CAACyE,YAAY;IAC5C,IAAIA,YAAY,EAAE;MAChB,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,YAAY,CAACqF,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC5C;QACA;QACA,IAAI,CAAC5J,GAAG,CAAC+N,SAAS,CAAC7J,YAAY,CAAC0F,CAAC,CAAC,CAAC;MACrC;IACF;EACF,CAAC;EACDzK,cAAc,CAACU,SAAS,CAAC2H,uBAAuB,GAAG,YAAY;IAC7D,IAAI,CAACpH,cAAc,CAAC,CAAC;IACrB,IAAI4N,OAAO,GAAG,IAAI,CAACpO,qBAAqB;IACxC,IAAI,CAACkN,iBAAiB,CAAC,UAAU,EAAE7O,MAAM,CAAC8O,iBAAiB,CAACiB,OAAO,EAAE,IAAI,CAAC1N,cAAc,CAAC,CAAC;IAC1F0N,OAAO,CAACzE,MAAM,GAAG,CAAC;EACpB,CAAC;EACDpK,cAAc,CAACU,SAAS,CAACqM,yBAAyB,GAAG,YAAY;IAC/D,IAAI,CAAC9L,cAAc,CAAC,CAAC;IACrB,IAAI0L,EAAE,GAAG,IAAI,CAAC9L,GAAG,CAAC+L,KAAK,CAAC,CAAC;IACzBD,EAAE,CAACmC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC/N,6BAA6B,CAAC;IACvD4L,EAAE,CAACmC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC7N,cAAc,CAAC;EACzC,CAAC;EACDjB,cAAc,CAACU,SAAS,CAACoC,eAAe,GAAG,UAAU+E,MAAM,EAAEkH,OAAO,EAAEpE,OAAO,EAAEqE,MAAM,EAAE;IACrF,IAAIC,SAAS,GAAGtQ,OAAO,CAACyM,YAAY,CAAC2D,OAAO,EAAEC,MAAM,GAAG,IAAI,GAAG,IAAI,CAACxN,KAAK,CAAC;IACzE,OAAOjD,MAAM,CAAC2Q,OAAO,CAACrH,MAAM,CAAC,GAAGlJ,OAAO,CAACwM,cAAc,CAACtD,MAAM,EAAEoH,SAAS,EAAEtE,OAAO,CAAC,GAAGhM,OAAO,CAACwQ,kBAAkB,CAACtH,MAAM,EAAEoH,SAAS,EAAEtE,OAAO,CAAC;EAC7I,CAAC;EACD;EACA3K,cAAc,CAACU,SAAS,CAACiN,iBAAiB,GAAG,UAAUtN,IAAI,EAAE+O,KAAK,EAAE;IAClEA,KAAK,IAAIA,KAAK,CAAChF,MAAM,IAAI,IAAI,CAACvJ,GAAG,CAACkH,cAAc,CAAC;MAC/C1H,IAAI,EAAEA,IAAI;MACV+O,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEpP,cAAc,CAACU,SAAS,CAAC2O,OAAO,GAAG,YAAY;IAC7C,IAAI,CAACtC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC1E,uBAAuB,CAAC,CAAC;EAChC,CAAC;EACDrI,cAAc,CAACK,IAAI,GAAG,sBAAsB;EAC5C,OAAOL,cAAc;AACvB,CAAC,CAACtB,aAAa,CAAC;AAChB,SAASuF,aAAaA,CAACqL,MAAM,EAAE9J,MAAM,EAAEJ,OAAO,EAAEC,SAAS,EAAE;EACzD,OAAO,IAAI1G,OAAO,CAAC4Q,OAAO,CAAC;IACzBhL,KAAK,EAAE;MACL+K,MAAM,EAAEA;IACV,CAAC;IACD5J,SAAS,EAAE,CAAC,CAACN,OAAO;IACpBI,MAAM,EAAEA,MAAM;IACdG,KAAK,EAAEP,OAAO;IACdS,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;MACxB;MACArH,SAAS,CAACsH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;IACzB,CAAC;IACDJ,SAAS,EAAEP;EACb,CAAC,CAAC;AACJ;AACA,SAAS6H,oBAAoBA,CAAC/L,cAAc,EAAEuH,UAAU,EAAEE,UAAU,EAAE;EACpE,IAAI+C,iBAAiB,GAAG7L,eAAe,GAAG,CAAC;EAC3C,IAAI0P,iBAAiB,GAAGrO,cAAc,CAACS,GAAG,CAAC,mBAAmB,CAAC;EAC/D,IAAI4N,iBAAiB,EAAE;IACrB7D,iBAAiB,GAAGpM,SAAS,CAACiQ,iBAAiB,EAAE9G,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;EACpF;EACA,OAAO+C,iBAAiB;AAC1B;AACA,SAASrD,oBAAoBA,CAACnH,cAAc,EAAE;EAC5C,IAAIsO,iBAAiB,GAAGtO,cAAc,CAACS,GAAG,CAAC,mBAAmB,CAAC;EAC/D,OAAO,CAAC,EAAE6N,iBAAiB,IAAI,IAAI,GAAGtO,cAAc,CAACS,GAAG,CAAC,UAAU,CAAC,GAAG6N,iBAAiB,CAAC;AAC3F;AACA,SAAStL,SAASA,CAACnB,MAAM,EAAE;EACzB,OAAOA,MAAM,KAAK,UAAU,GAAG,WAAW,GAAG,WAAW;AAC1D;AACA,eAAehD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}