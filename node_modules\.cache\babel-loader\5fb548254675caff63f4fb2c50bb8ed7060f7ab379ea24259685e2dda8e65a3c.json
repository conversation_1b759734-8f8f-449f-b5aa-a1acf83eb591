{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport RoamController from './RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enableComponentHighDownFeatures, setDefaultStateProxy } from '../../util/states.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { getUID } from '../../util/component.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner } from '../../util/model.js';\n/**\r\n * Only these tags enable use `itemStyle` if they are named in SVG.\r\n * Other tags like <text> <tspan> <image> might not suitable for `itemStyle`.\r\n * They will not be considered to be styled until some requirements come.\r\n */\nvar OPTION_STYLE_ENABLED_TAGS = ['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path'];\nvar OPTION_STYLE_ENABLED_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS);\nvar STATE_TRIGGER_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar LABEL_HOST_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar mapLabelRaw = makeInner();\nfunction getFixedItemStyle(model) {\n  var itemStyle = model.getItemStyle();\n  var areaColor = model.get('areaColor');\n  // If user want the color not to be changed when hover,\n  // they should both set areaColor and color to be null.\n  if (areaColor != null) {\n    itemStyle.fill = areaColor;\n  }\n  return itemStyle;\n}\n// Only stroke can be used for line.\n// Using fill in style if stroke not exits.\n// TODO Not sure yet. Perhaps a separate `lineStyle`?\nfunction fixLineStyle(styleHost) {\n  var style = styleHost.style;\n  if (style) {\n    style.stroke = style.stroke || style.fill;\n    style.fill = null;\n  }\n}\nvar MapDraw = /** @class */function () {\n  function MapDraw(api) {\n    var group = new graphic.Group();\n    this.uid = getUID('ec_map_draw');\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    this.group = group;\n    group.add(this._regionsGroup = new graphic.Group());\n    group.add(this._svgGroup = new graphic.Group());\n  }\n  MapDraw.prototype.draw = function (mapOrGeoModel, ecModel, api, fromView, payload) {\n    var isGeo = mapOrGeoModel.mainType === 'geo';\n    // Map series has data. GEO model that controlled by map series\n    // will be assigned with map data. Other GEO model has no data.\n    var data = mapOrGeoModel.getData && mapOrGeoModel.getData();\n    isGeo && ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'map'\n    }, function (mapSeries) {\n      if (!data && mapSeries.getHostGeoModel() === mapOrGeoModel) {\n        data = mapSeries.getData();\n      }\n    });\n    var geo = mapOrGeoModel.coordinateSystem;\n    var regionsGroup = this._regionsGroup;\n    var group = this.group;\n    var transformInfo = geo.getTransformInfo();\n    var transformInfoRaw = transformInfo.raw;\n    var transformInfoRoam = transformInfo.roam;\n    // No animation when first draw or in action\n    var isFirstDraw = !regionsGroup.childAt(0) || payload;\n    if (isFirstDraw) {\n      group.x = transformInfoRoam.x;\n      group.y = transformInfoRoam.y;\n      group.scaleX = transformInfoRoam.scaleX;\n      group.scaleY = transformInfoRoam.scaleY;\n      group.dirty();\n    } else {\n      graphic.updateProps(group, transformInfoRoam, mapOrGeoModel);\n    }\n    var isVisualEncodedByVisualMap = data && data.getVisual('visualMeta') && data.getVisual('visualMeta').length > 0;\n    var viewBuildCtx = {\n      api: api,\n      geo: geo,\n      mapOrGeoModel: mapOrGeoModel,\n      data: data,\n      isVisualEncodedByVisualMap: isVisualEncodedByVisualMap,\n      isGeo: isGeo,\n      transformInfoRaw: transformInfoRaw\n    };\n    if (geo.resourceType === 'geoJSON') {\n      this._buildGeoJSON(viewBuildCtx);\n    } else if (geo.resourceType === 'geoSVG') {\n      this._buildSVG(viewBuildCtx);\n    }\n    this._updateController(mapOrGeoModel, ecModel, api);\n    this._updateMapSelectHandler(mapOrGeoModel, regionsGroup, api, fromView);\n  };\n  MapDraw.prototype._buildGeoJSON = function (viewBuildCtx) {\n    var regionsGroupByName = this._regionsGroupByName = zrUtil.createHashMap();\n    var regionsInfoByName = zrUtil.createHashMap();\n    var regionsGroup = this._regionsGroup;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n    var data = viewBuildCtx.data;\n    var projection = viewBuildCtx.geo.projection;\n    var projectionStream = projection && projection.stream;\n    function transformPoint(point, project) {\n      if (project) {\n        // projection may return null point.\n        point = project(point);\n      }\n      return point && [point[0] * transformInfoRaw.scaleX + transformInfoRaw.x, point[1] * transformInfoRaw.scaleY + transformInfoRaw.y];\n    }\n    ;\n    function transformPolygonPoints(inPoints) {\n      var outPoints = [];\n      // If projectionStream is provided. Use it instead of single point project.\n      var project = !projectionStream && projection && projection.project;\n      for (var i = 0; i < inPoints.length; ++i) {\n        var newPt = transformPoint(inPoints[i], project);\n        newPt && outPoints.push(newPt);\n      }\n      return outPoints;\n    }\n    function getPolyShape(points) {\n      return {\n        shape: {\n          points: transformPolygonPoints(points)\n        }\n      };\n    }\n    regionsGroup.removeAll();\n    // Only when the resource is GeoJSON, there is `geo.regions`.\n    zrUtil.each(viewBuildCtx.geo.regions, function (region) {\n      var regionName = region.name;\n      // Consider in GeoJson properties.name may be duplicated, for example,\n      // there is multiple region named \"United Kindom\" or \"France\" (so many\n      // colonies). And it is not appropriate to merge them in geo, which\n      // will make them share the same label and bring trouble in label\n      // location calculation.\n      var regionGroup = regionsGroupByName.get(regionName);\n      var _a = regionsInfoByName.get(regionName) || {},\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      if (!regionGroup) {\n        regionGroup = regionsGroupByName.set(regionName, new graphic.Group());\n        regionsGroup.add(regionGroup);\n        dataIdx = data ? data.indexOfName(regionName) : null;\n        regionModel = viewBuildCtx.isGeo ? mapOrGeoModel.getRegionModel(regionName) : data ? data.getItemModel(dataIdx) : null;\n        var silent = regionModel.get('silent', true);\n        silent != null && (regionGroup.silent = silent);\n        regionsInfoByName.set(regionName, {\n          dataIdx: dataIdx,\n          regionModel: regionModel\n        });\n      }\n      var polygonSubpaths = [];\n      var polylineSubpaths = [];\n      zrUtil.each(region.geometries, function (geometry) {\n        // Polygon and MultiPolygon\n        if (geometry.type === 'polygon') {\n          var polys = [geometry.exterior].concat(geometry.interiors || []);\n          if (projectionStream) {\n            polys = projectPolys(polys, projectionStream);\n          }\n          zrUtil.each(polys, function (poly) {\n            polygonSubpaths.push(new graphic.Polygon(getPolyShape(poly)));\n          });\n        }\n        // LineString and MultiLineString\n        else {\n          var points = geometry.points;\n          if (projectionStream) {\n            points = projectPolys(points, projectionStream, true);\n          }\n          zrUtil.each(points, function (points) {\n            polylineSubpaths.push(new graphic.Polyline(getPolyShape(points)));\n          });\n        }\n      });\n      var centerPt = transformPoint(region.getCenter(), projection && projection.project);\n      function createCompoundPath(subpaths, isLine) {\n        if (!subpaths.length) {\n          return;\n        }\n        var compoundPath = new graphic.CompoundPath({\n          culling: true,\n          segmentIgnoreThreshold: 1,\n          shape: {\n            paths: subpaths\n          }\n        });\n        regionGroup.add(compoundPath);\n        applyOptionStyleForRegion(viewBuildCtx, compoundPath, dataIdx, regionModel);\n        resetLabelForRegion(viewBuildCtx, compoundPath, regionName, regionModel, mapOrGeoModel, dataIdx, centerPt);\n        if (isLine) {\n          fixLineStyle(compoundPath);\n          zrUtil.each(compoundPath.states, fixLineStyle);\n        }\n      }\n      createCompoundPath(polygonSubpaths);\n      createCompoundPath(polylineSubpaths, true);\n    });\n    // Ensure children have been added to `regionGroup` before calling them.\n    regionsGroupByName.each(function (regionGroup, regionName) {\n      var _a = regionsInfoByName.get(regionName),\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      resetEventTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel, dataIdx);\n      resetTooltipForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n      resetStateTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n    }, this);\n  };\n  MapDraw.prototype._buildSVG = function (viewBuildCtx) {\n    var mapName = viewBuildCtx.geo.map;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    this._svgGroup.x = transformInfoRaw.x;\n    this._svgGroup.y = transformInfoRaw.y;\n    this._svgGroup.scaleX = transformInfoRaw.scaleX;\n    this._svgGroup.scaleY = transformInfoRaw.scaleY;\n    if (this._svgResourceChanged(mapName)) {\n      this._freeSVG();\n      this._useSVG(mapName);\n    }\n    var svgDispatcherMap = this._svgDispatcherMap = zrUtil.createHashMap();\n    var focusSelf = false;\n    zrUtil.each(this._svgGraphicRecord.named, function (namedItem) {\n      // Note that we also allow different elements have the same name.\n      // For example, a glyph of a city and the label of the city have\n      // the same name and their tooltip info can be defined in a single\n      // region option.\n      var regionName = namedItem.name;\n      var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n      var data = viewBuildCtx.data;\n      var svgNodeTagLower = namedItem.svgNodeTagLower;\n      var el = namedItem.el;\n      var dataIdx = data ? data.indexOfName(regionName) : null;\n      var regionModel = mapOrGeoModel.getRegionModel(regionName);\n      if (OPTION_STYLE_ENABLED_TAG_MAP.get(svgNodeTagLower) != null && el instanceof Displayable) {\n        applyOptionStyleForRegion(viewBuildCtx, el, dataIdx, regionModel);\n      }\n      if (el instanceof Displayable) {\n        el.culling = true;\n      }\n      var silent = regionModel.get('silent', true);\n      silent != null && (el.silent = silent);\n      // We do not know how the SVG like so we'd better not to change z2.\n      // Otherwise it might bring some unexpected result. For example,\n      // an area hovered that make some inner city can not be clicked.\n      el.z2EmphasisLift = 0;\n      // If self named:\n      if (!namedItem.namedFrom) {\n        // label should batter to be displayed based on the center of <g>\n        // if it is named rather than displayed on each child.\n        if (LABEL_HOST_MAP.get(svgNodeTagLower) != null) {\n          resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx, null);\n        }\n        resetEventTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx);\n        resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n        if (STATE_TRIGGER_TAG_MAP.get(svgNodeTagLower) != null) {\n          var focus_1 = resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n          if (focus_1 === 'self') {\n            focusSelf = true;\n          }\n          var els = svgDispatcherMap.get(regionName) || svgDispatcherMap.set(regionName, []);\n          els.push(el);\n        }\n      }\n    }, this);\n    this._enableBlurEntireSVG(focusSelf, viewBuildCtx);\n  };\n  MapDraw.prototype._enableBlurEntireSVG = function (focusSelf, viewBuildCtx) {\n    // It's a little complicated to support blurring the entire geoSVG in series-map.\n    // So do not support it until some requirements come.\n    // At present, in series-map, only regions can be blurred.\n    if (focusSelf && viewBuildCtx.isGeo) {\n      var blurStyle = viewBuildCtx.mapOrGeoModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      // Only support `opacity` here. Because not sure that other props are suitable for\n      // all of the elements generated by SVG (especially for Text/TSpan/Image/... ).\n      var opacity_1 = blurStyle.opacity;\n      this._svgGraphicRecord.root.traverse(function (el) {\n        if (!el.isGroup) {\n          // PENDING: clear those settings to SVG elements when `_freeSVG`.\n          // (Currently it happen not to be needed.)\n          setDefaultStateProxy(el);\n          var style = el.ensureState('blur').style || {};\n          // Do not overwrite the region style that already set from region option.\n          if (style.opacity == null && opacity_1 != null) {\n            style.opacity = opacity_1;\n          }\n          // If `ensureState('blur').style = {}`, there will be default opacity.\n          // Enable `stateTransition` (animation).\n          el.ensureState('emphasis');\n        }\n      });\n    }\n  };\n  MapDraw.prototype.remove = function () {\n    this._regionsGroup.removeAll();\n    this._regionsGroupByName = null;\n    this._svgGroup.removeAll();\n    this._freeSVG();\n    this._controller.dispose();\n    this._controllerHost = null;\n  };\n  MapDraw.prototype.findHighDownDispatchers = function (name, geoModel) {\n    if (name == null) {\n      return [];\n    }\n    var geo = geoModel.coordinateSystem;\n    if (geo.resourceType === 'geoJSON') {\n      var regionsGroupByName = this._regionsGroupByName;\n      if (regionsGroupByName) {\n        var regionGroup = regionsGroupByName.get(name);\n        return regionGroup ? [regionGroup] : [];\n      }\n    } else if (geo.resourceType === 'geoSVG') {\n      return this._svgDispatcherMap && this._svgDispatcherMap.get(name) || [];\n    }\n  };\n  MapDraw.prototype._svgResourceChanged = function (mapName) {\n    return this._svgMapName !== mapName;\n  };\n  MapDraw.prototype._useSVG = function (mapName) {\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      var svgGraphic = resource.useGraphic(this.uid);\n      this._svgGroup.add(svgGraphic.root);\n      this._svgGraphicRecord = svgGraphic;\n      this._svgMapName = mapName;\n    }\n  };\n  MapDraw.prototype._freeSVG = function () {\n    var mapName = this._svgMapName;\n    if (mapName == null) {\n      return;\n    }\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      resource.freeGraphic(this.uid);\n    }\n    this._svgGraphicRecord = null;\n    this._svgDispatcherMap = null;\n    this._svgGroup.removeAll();\n    this._svgMapName = null;\n  };\n  MapDraw.prototype._updateController = function (mapOrGeoModel, ecModel, api) {\n    var geo = mapOrGeoModel.coordinateSystem;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    // @ts-ignore FIXME:TS\n    controllerHost.zoomLimit = mapOrGeoModel.get('scaleLimit');\n    controllerHost.zoom = geo.getZoom();\n    // roamType is will be set default true if it is null\n    // @ts-ignore FIXME:TS\n    controller.enable(mapOrGeoModel.get('roam') || false);\n    var mainType = mapOrGeoModel.mainType;\n    function makeActionBase() {\n      var action = {\n        type: 'geoRoam',\n        componentType: mainType\n      };\n      action[mainType + 'Id'] = mapOrGeoModel.id;\n      return action;\n    }\n    controller.off('pan').on('pan', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        dx: e.dx,\n        dy: e.dy,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.off('zoom').on('zoom', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        totalZoom: controllerHost.zoom,\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.setPointerChecker(function (e, x, y) {\n      return geo.containPoint([x, y]) && !onIrrelevantElement(e, api, mapOrGeoModel);\n    });\n  };\n  /**\r\n   * FIXME: this is a temporarily workaround.\r\n   * When `geoRoam` the elements need to be reset in `MapView['render']`, because the props like\r\n   * `ignore` might have been modified by `LabelManager`, and `LabelManager#addLabelsOfSeries`\r\n   * will subsequently cache `defaultAttr` like `ignore`. If do not do this reset, the modified\r\n   * props will have no chance to be restored.\r\n   * Note: This reset should be after `clearStates` in `renderSeries` because `useStates` in\r\n   * `renderSeries` will cache the modified `ignore` to `el._normalState`.\r\n   * TODO:\r\n   * Use clone/immutable in `LabelManager`?\r\n   */\n  MapDraw.prototype.resetForLabelLayout = function () {\n    this.group.traverse(function (el) {\n      var label = el.getTextContent();\n      if (label) {\n        label.ignore = mapLabelRaw(label).ignore;\n      }\n    });\n  };\n  MapDraw.prototype._updateMapSelectHandler = function (mapOrGeoModel, regionsGroup, api, fromView) {\n    var mapDraw = this;\n    regionsGroup.off('mousedown');\n    regionsGroup.off('click');\n    // @ts-ignore FIXME:TS resolve type conflict\n    if (mapOrGeoModel.get('selectedMode')) {\n      regionsGroup.on('mousedown', function () {\n        mapDraw._mouseDownFlag = true;\n      });\n      regionsGroup.on('click', function (e) {\n        if (!mapDraw._mouseDownFlag) {\n          return;\n        }\n        mapDraw._mouseDownFlag = false;\n      });\n    }\n  };\n  return MapDraw;\n}();\n;\nfunction applyOptionStyleForRegion(viewBuildCtx, el, dataIndex, regionModel) {\n  // All of the path are using `itemStyle`, because\n  // (1) Some SVG also use fill on polyline (The different between\n  // polyline and polygon is \"open\" or \"close\" but not fill or not).\n  // (2) For the common props like opacity, if some use itemStyle\n  // and some use `lineStyle`, it might confuse users.\n  // (3) Most SVG use <path>, where can not detect whether to draw a \"line\"\n  // or a filled shape, so use `itemStyle` for <path>.\n  var normalStyleModel = regionModel.getModel('itemStyle');\n  var emphasisStyleModel = regionModel.getModel(['emphasis', 'itemStyle']);\n  var blurStyleModel = regionModel.getModel(['blur', 'itemStyle']);\n  var selectStyleModel = regionModel.getModel(['select', 'itemStyle']);\n  // NOTE: DON'T use 'style' in visual when drawing map.\n  // This component is used for drawing underlying map for both geo component and map series.\n  var normalStyle = getFixedItemStyle(normalStyleModel);\n  var emphasisStyle = getFixedItemStyle(emphasisStyleModel);\n  var selectStyle = getFixedItemStyle(selectStyleModel);\n  var blurStyle = getFixedItemStyle(blurStyleModel);\n  // Update the itemStyle if has data visual\n  var data = viewBuildCtx.data;\n  if (data) {\n    // Only visual color of each item will be used. It can be encoded by visualMap\n    // But visual color of series is used in symbol drawing\n    // Visual color for each series is for the symbol draw\n    var style = data.getItemVisual(dataIndex, 'style');\n    var decal = data.getItemVisual(dataIndex, 'decal');\n    if (viewBuildCtx.isVisualEncodedByVisualMap && style.fill) {\n      normalStyle.fill = style.fill;\n    }\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, viewBuildCtx.api);\n    }\n  }\n  // SVG text, tspan and image can be named but not supporeted\n  // to be styled by region option yet.\n  el.setStyle(normalStyle);\n  el.style.strokeNoScale = true;\n  el.ensureState('emphasis').style = emphasisStyle;\n  el.ensureState('select').style = selectStyle;\n  el.ensureState('blur').style = blurStyle;\n  // Enable blur\n  setDefaultStateProxy(el);\n}\nfunction resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx,\n// If labelXY not provided, use `textConfig.position: 'inside'`\nlabelXY) {\n  var data = viewBuildCtx.data;\n  var isGeo = viewBuildCtx.isGeo;\n  var isDataNaN = data && isNaN(data.get(data.mapDimension('value'), dataIdx));\n  var itemLayout = data && data.getItemLayout(dataIdx);\n  // In the following cases label will be drawn\n  // 1. In map series and data value is NaN\n  // 2. In geo component\n  // 3. Region has no series legendIcon, which will be add a showLabel flag in mapSymbolLayout\n  if (isGeo || isDataNaN || itemLayout && itemLayout.showLabel) {\n    var query = !isGeo ? dataIdx : regionName;\n    var labelFetcher = void 0;\n    // Consider dataIdx not found.\n    if (!data || dataIdx >= 0) {\n      labelFetcher = mapOrGeoModel;\n    }\n    var specifiedTextOpt = labelXY ? {\n      normal: {\n        align: 'center',\n        verticalAlign: 'middle'\n      }\n    } : null;\n    // Caveat: must be called after `setDefaultStateProxy(el);` called.\n    // because textContent will be assign with `el.stateProxy` inside.\n    setLabelStyle(el, getLabelStatesModels(regionModel), {\n      labelFetcher: labelFetcher,\n      labelDataIndex: query,\n      defaultText: regionName\n    }, specifiedTextOpt);\n    var textEl = el.getTextContent();\n    if (textEl) {\n      mapLabelRaw(textEl).ignore = textEl.ignore;\n      if (el.textConfig && labelXY) {\n        // Compute a relative offset based on the el bounding rect.\n        var rect = el.getBoundingRect().clone();\n        // Need to make sure the percent position base on the same rect in normal and\n        // emphasis state. Otherwise if using boundingRect of el, but the emphasis state\n        // has borderWidth (even 0.5px), the text position will be changed obviously\n        // if the position is very big like ['1234%', '1345%'].\n        el.textConfig.layoutRect = rect;\n        el.textConfig.position = [(labelXY[0] - rect.x) / rect.width * 100 + '%', (labelXY[1] - rect.y) / rect.height * 100 + '%'];\n      }\n    }\n    // PENDING:\n    // If labelLayout is enabled (test/label-layout.html), el.dataIndex should be specified.\n    // But el.dataIndex is also used to determine whether user event should be triggered,\n    // where el.seriesIndex or el.dataModel must be specified. At present for a single el\n    // there is not case that \"only label layout enabled but user event disabled\", so here\n    // we depends `resetEventTriggerForRegion` to do the job of setting `el.dataIndex`.\n    el.disableLabelAnimation = true;\n  } else {\n    el.removeTextContent();\n    el.removeTextConfig();\n    el.disableLabelAnimation = null;\n  }\n}\nfunction resetEventTriggerForRegion(viewBuildCtx, eventTrigger, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx) {\n  // setItemGraphicEl, setHoverStyle after all polygons and labels\n  // are added to the regionGroup\n  if (viewBuildCtx.data) {\n    // FIXME: when series-map use a SVG map, and there are duplicated name specified\n    // on different SVG elements, after `data.setItemGraphicEl(...)`:\n    // (1) all of them will be mounted with `dataIndex`, `seriesIndex`, so that tooltip\n    // can be triggered only mouse hover. That's correct.\n    // (2) only the last element will be kept in `data`, so that if trigger tooltip\n    // by `dispatchAction`, only the last one can be found and triggered. That might be\n    // not correct. We will fix it in future if anyone demanding that.\n    viewBuildCtx.data.setItemGraphicEl(dataIdx, eventTrigger);\n  }\n  // series-map will not trigger \"geoselectchange\" no matter it is\n  // based on a declared geo component. Because series-map will\n  // trigger \"selectchange\". If it trigger both the two events,\n  // If users call `chart.dispatchAction({type: 'toggleSelect'})`,\n  // it not easy to also fire event \"geoselectchanged\".\n  else {\n    // Package custom mouse event for geo component\n    getECData(eventTrigger).eventData = {\n      componentType: 'geo',\n      componentIndex: mapOrGeoModel.componentIndex,\n      geoIndex: mapOrGeoModel.componentIndex,\n      name: regionName,\n      region: regionModel && regionModel.option || {}\n    };\n  }\n}\nfunction resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  if (!viewBuildCtx.data) {\n    graphic.setTooltipConfig({\n      el: el,\n      componentModel: mapOrGeoModel,\n      itemName: regionName,\n      // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n      itemTooltipOption: regionModel.get('tooltip')\n    });\n  }\n}\nfunction resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  el.highDownSilentOnTouch = !!mapOrGeoModel.get('selectedMode');\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  var emphasisModel = regionModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  toggleHoverEmphasis(el, focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  if (viewBuildCtx.isGeo) {\n    enableComponentHighDownFeatures(el, mapOrGeoModel, regionName);\n  }\n  return focus;\n}\nfunction projectPolys(rings,\n// Polygons include exterior and interiors. Or polylines.\ncreateStream, isLine) {\n  var polygons = [];\n  var curPoly;\n  function startPolygon() {\n    curPoly = [];\n  }\n  function endPolygon() {\n    if (curPoly.length) {\n      polygons.push(curPoly);\n      curPoly = [];\n    }\n  }\n  var stream = createStream({\n    polygonStart: startPolygon,\n    polygonEnd: endPolygon,\n    lineStart: startPolygon,\n    lineEnd: endPolygon,\n    point: function (x, y) {\n      // May have NaN values from stream.\n      if (isFinite(x) && isFinite(y)) {\n        curPoly.push([x, y]);\n      }\n    },\n    sphere: function () {}\n  });\n  !isLine && stream.polygonStart();\n  zrUtil.each(rings, function (ring) {\n    stream.lineStart();\n    for (var i = 0; i < ring.length; i++) {\n      stream.point(ring[i][0], ring[i][1]);\n    }\n    stream.lineEnd();\n  });\n  !isLine && stream.polygonEnd();\n  return polygons;\n}\nexport default MapDraw;\n// @ts-ignore FIXME:TS fix the \"compatible with each other\"?", "map": {"version": 3, "names": ["zrUtil", "RoamController", "roamHelper", "onIrrelevantElement", "graphic", "toggleHoverEmphasis", "enableComponentHighDownFeatures", "setDefaultStateProxy", "geoSourceManager", "getUID", "setLabelStyle", "getLabelStatesModels", "getECData", "createOrUpdatePatternFromDecal", "Displayable", "makeInner", "OPTION_STYLE_ENABLED_TAGS", "OPTION_STYLE_ENABLED_TAG_MAP", "createHashMap", "STATE_TRIGGER_TAG_MAP", "concat", "LABEL_HOST_MAP", "mapLabelRaw", "getFixedItemStyle", "model", "itemStyle", "getItemStyle", "areaColor", "get", "fill", "fixLineStyle", "styleHost", "style", "stroke", "MapDraw", "api", "group", "Group", "uid", "_controller", "getZr", "_controllerHost", "target", "add", "_regionsGroup", "_svgGroup", "prototype", "draw", "mapOrGeoModel", "ecModel", "fromView", "payload", "isGeo", "mainType", "data", "getData", "eachComponent", "subType", "mapSeries", "getHostGeoModel", "geo", "coordinateSystem", "regionsGroup", "transformInfo", "getTransformInfo", "transformInfoRaw", "raw", "transformInfoRoam", "roam", "isFirstDraw", "childAt", "x", "y", "scaleX", "scaleY", "dirty", "updateProps", "isVisualEncodedByVisualMap", "getVisual", "length", "viewBuildCtx", "resourceType", "_buildGeoJSON", "_buildSVG", "_updateController", "_updateMapSelectHandler", "regionsGroupByName", "_regionsGroupByName", "regionsInfoByName", "projection", "projectionStream", "stream", "transformPoint", "point", "project", "transformPolygonPoints", "inPoints", "outPoints", "i", "newPt", "push", "getPolyShape", "points", "shape", "removeAll", "each", "regions", "region", "regionName", "name", "regionGroup", "_a", "dataIdx", "regionModel", "set", "indexOfName", "getRegionModel", "getItemModel", "silent", "polygonSubpaths", "polylineSubpaths", "geometries", "geometry", "type", "polys", "exterior", "interiors", "projectPolys", "poly", "Polygon", "Polyline", "centerPt", "getCenter", "createCompoundPath", "subpaths", "isLine", "compoundPath", "CompoundPath", "culling", "segmentIgnoreThreshold", "paths", "applyOptionStyleForRegion", "resetLabelForRegion", "states", "resetEventTriggerForRegion", "resetTooltipForRegion", "resetStateTriggerForRegion", "mapName", "map", "_svgResourceChanged", "_freeSVG", "_useSVG", "svgDispatcherMap", "_svgDispatcherMap", "focusSelf", "_svgGraphicRecord", "named", "namedItem", "svgNodeTagLower", "el", "z2EmphasisLift", "named<PERSON><PERSON>", "focus_1", "els", "_enableBlurEntireSVG", "blurStyle", "getModel", "opacity_1", "opacity", "root", "traverse", "isGroup", "ensureState", "remove", "dispose", "findHighDownDispatchers", "geoModel", "_svgMapName", "resource", "getGeoResource", "svgGraphic", "useGraphic", "freeGraphic", "controller", "controllerHost", "zoomLimit", "zoom", "getZoom", "enable", "makeActionBase", "action", "componentType", "id", "off", "on", "e", "_mouseDownFlag", "updateViewOnPan", "dx", "dy", "dispatchAction", "extend", "animation", "duration", "updateViewOnZoom", "scale", "originX", "originY", "totalZoom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containPoint", "resetForLabelLayout", "label", "getTextContent", "ignore", "mapDraw", "dataIndex", "normalStyleModel", "emphasisStyleModel", "blurStyleModel", "selectStyleModel", "normalStyle", "emphasisStyle", "selectStyle", "getItemVisual", "decal", "setStyle", "strokeNoScale", "labelXY", "isDataNaN", "isNaN", "mapDimension", "itemLayout", "getItemLayout", "showLabel", "query", "labelFetcher", "specifiedTextOpt", "normal", "align", "verticalAlign", "labelDataIndex", "defaultText", "textEl", "textConfig", "rect", "getBoundingRect", "clone", "layoutRect", "position", "width", "height", "disableLabelAnimation", "removeTextContent", "removeTextConfig", "eventTrigger", "setItemGraphicEl", "eventData", "componentIndex", "geoIndex", "option", "setTooltipConfig", "componentModel", "itemName", "itemTooltipOption", "highDownSilentOnTouch", "emphasisModel", "focus", "rings", "createStream", "polygons", "cur<PERSON><PERSON>", "startPolygon", "endPolygon", "polygonStart", "polygonEnd", "lineStart", "lineEnd", "isFinite", "sphere", "ring"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/helper/MapDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport RoamController from './RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enableComponentHighDownFeatures, setDefaultStateProxy } from '../../util/states.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { getUID } from '../../util/component.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner } from '../../util/model.js';\n/**\r\n * Only these tags enable use `itemStyle` if they are named in SVG.\r\n * Other tags like <text> <tspan> <image> might not suitable for `itemStyle`.\r\n * They will not be considered to be styled until some requirements come.\r\n */\nvar OPTION_STYLE_ENABLED_TAGS = ['rect', 'circle', 'line', 'ellipse', 'polygon', 'polyline', 'path'];\nvar OPTION_STYLE_ENABLED_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS);\nvar STATE_TRIGGER_TAG_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar LABEL_HOST_MAP = zrUtil.createHashMap(OPTION_STYLE_ENABLED_TAGS.concat(['g']));\nvar mapLabelRaw = makeInner();\nfunction getFixedItemStyle(model) {\n  var itemStyle = model.getItemStyle();\n  var areaColor = model.get('areaColor');\n  // If user want the color not to be changed when hover,\n  // they should both set areaColor and color to be null.\n  if (areaColor != null) {\n    itemStyle.fill = areaColor;\n  }\n  return itemStyle;\n}\n// Only stroke can be used for line.\n// Using fill in style if stroke not exits.\n// TODO Not sure yet. Perhaps a separate `lineStyle`?\nfunction fixLineStyle(styleHost) {\n  var style = styleHost.style;\n  if (style) {\n    style.stroke = style.stroke || style.fill;\n    style.fill = null;\n  }\n}\nvar MapDraw = /** @class */function () {\n  function MapDraw(api) {\n    var group = new graphic.Group();\n    this.uid = getUID('ec_map_draw');\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    this.group = group;\n    group.add(this._regionsGroup = new graphic.Group());\n    group.add(this._svgGroup = new graphic.Group());\n  }\n  MapDraw.prototype.draw = function (mapOrGeoModel, ecModel, api, fromView, payload) {\n    var isGeo = mapOrGeoModel.mainType === 'geo';\n    // Map series has data. GEO model that controlled by map series\n    // will be assigned with map data. Other GEO model has no data.\n    var data = mapOrGeoModel.getData && mapOrGeoModel.getData();\n    isGeo && ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'map'\n    }, function (mapSeries) {\n      if (!data && mapSeries.getHostGeoModel() === mapOrGeoModel) {\n        data = mapSeries.getData();\n      }\n    });\n    var geo = mapOrGeoModel.coordinateSystem;\n    var regionsGroup = this._regionsGroup;\n    var group = this.group;\n    var transformInfo = geo.getTransformInfo();\n    var transformInfoRaw = transformInfo.raw;\n    var transformInfoRoam = transformInfo.roam;\n    // No animation when first draw or in action\n    var isFirstDraw = !regionsGroup.childAt(0) || payload;\n    if (isFirstDraw) {\n      group.x = transformInfoRoam.x;\n      group.y = transformInfoRoam.y;\n      group.scaleX = transformInfoRoam.scaleX;\n      group.scaleY = transformInfoRoam.scaleY;\n      group.dirty();\n    } else {\n      graphic.updateProps(group, transformInfoRoam, mapOrGeoModel);\n    }\n    var isVisualEncodedByVisualMap = data && data.getVisual('visualMeta') && data.getVisual('visualMeta').length > 0;\n    var viewBuildCtx = {\n      api: api,\n      geo: geo,\n      mapOrGeoModel: mapOrGeoModel,\n      data: data,\n      isVisualEncodedByVisualMap: isVisualEncodedByVisualMap,\n      isGeo: isGeo,\n      transformInfoRaw: transformInfoRaw\n    };\n    if (geo.resourceType === 'geoJSON') {\n      this._buildGeoJSON(viewBuildCtx);\n    } else if (geo.resourceType === 'geoSVG') {\n      this._buildSVG(viewBuildCtx);\n    }\n    this._updateController(mapOrGeoModel, ecModel, api);\n    this._updateMapSelectHandler(mapOrGeoModel, regionsGroup, api, fromView);\n  };\n  MapDraw.prototype._buildGeoJSON = function (viewBuildCtx) {\n    var regionsGroupByName = this._regionsGroupByName = zrUtil.createHashMap();\n    var regionsInfoByName = zrUtil.createHashMap();\n    var regionsGroup = this._regionsGroup;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n    var data = viewBuildCtx.data;\n    var projection = viewBuildCtx.geo.projection;\n    var projectionStream = projection && projection.stream;\n    function transformPoint(point, project) {\n      if (project) {\n        // projection may return null point.\n        point = project(point);\n      }\n      return point && [point[0] * transformInfoRaw.scaleX + transformInfoRaw.x, point[1] * transformInfoRaw.scaleY + transformInfoRaw.y];\n    }\n    ;\n    function transformPolygonPoints(inPoints) {\n      var outPoints = [];\n      // If projectionStream is provided. Use it instead of single point project.\n      var project = !projectionStream && projection && projection.project;\n      for (var i = 0; i < inPoints.length; ++i) {\n        var newPt = transformPoint(inPoints[i], project);\n        newPt && outPoints.push(newPt);\n      }\n      return outPoints;\n    }\n    function getPolyShape(points) {\n      return {\n        shape: {\n          points: transformPolygonPoints(points)\n        }\n      };\n    }\n    regionsGroup.removeAll();\n    // Only when the resource is GeoJSON, there is `geo.regions`.\n    zrUtil.each(viewBuildCtx.geo.regions, function (region) {\n      var regionName = region.name;\n      // Consider in GeoJson properties.name may be duplicated, for example,\n      // there is multiple region named \"United Kindom\" or \"France\" (so many\n      // colonies). And it is not appropriate to merge them in geo, which\n      // will make them share the same label and bring trouble in label\n      // location calculation.\n      var regionGroup = regionsGroupByName.get(regionName);\n      var _a = regionsInfoByName.get(regionName) || {},\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      if (!regionGroup) {\n        regionGroup = regionsGroupByName.set(regionName, new graphic.Group());\n        regionsGroup.add(regionGroup);\n        dataIdx = data ? data.indexOfName(regionName) : null;\n        regionModel = viewBuildCtx.isGeo ? mapOrGeoModel.getRegionModel(regionName) : data ? data.getItemModel(dataIdx) : null;\n        var silent = regionModel.get('silent', true);\n        silent != null && (regionGroup.silent = silent);\n        regionsInfoByName.set(regionName, {\n          dataIdx: dataIdx,\n          regionModel: regionModel\n        });\n      }\n      var polygonSubpaths = [];\n      var polylineSubpaths = [];\n      zrUtil.each(region.geometries, function (geometry) {\n        // Polygon and MultiPolygon\n        if (geometry.type === 'polygon') {\n          var polys = [geometry.exterior].concat(geometry.interiors || []);\n          if (projectionStream) {\n            polys = projectPolys(polys, projectionStream);\n          }\n          zrUtil.each(polys, function (poly) {\n            polygonSubpaths.push(new graphic.Polygon(getPolyShape(poly)));\n          });\n        }\n        // LineString and MultiLineString\n        else {\n          var points = geometry.points;\n          if (projectionStream) {\n            points = projectPolys(points, projectionStream, true);\n          }\n          zrUtil.each(points, function (points) {\n            polylineSubpaths.push(new graphic.Polyline(getPolyShape(points)));\n          });\n        }\n      });\n      var centerPt = transformPoint(region.getCenter(), projection && projection.project);\n      function createCompoundPath(subpaths, isLine) {\n        if (!subpaths.length) {\n          return;\n        }\n        var compoundPath = new graphic.CompoundPath({\n          culling: true,\n          segmentIgnoreThreshold: 1,\n          shape: {\n            paths: subpaths\n          }\n        });\n        regionGroup.add(compoundPath);\n        applyOptionStyleForRegion(viewBuildCtx, compoundPath, dataIdx, regionModel);\n        resetLabelForRegion(viewBuildCtx, compoundPath, regionName, regionModel, mapOrGeoModel, dataIdx, centerPt);\n        if (isLine) {\n          fixLineStyle(compoundPath);\n          zrUtil.each(compoundPath.states, fixLineStyle);\n        }\n      }\n      createCompoundPath(polygonSubpaths);\n      createCompoundPath(polylineSubpaths, true);\n    });\n    // Ensure children have been added to `regionGroup` before calling them.\n    regionsGroupByName.each(function (regionGroup, regionName) {\n      var _a = regionsInfoByName.get(regionName),\n        dataIdx = _a.dataIdx,\n        regionModel = _a.regionModel;\n      resetEventTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel, dataIdx);\n      resetTooltipForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n      resetStateTriggerForRegion(viewBuildCtx, regionGroup, regionName, regionModel, mapOrGeoModel);\n    }, this);\n  };\n  MapDraw.prototype._buildSVG = function (viewBuildCtx) {\n    var mapName = viewBuildCtx.geo.map;\n    var transformInfoRaw = viewBuildCtx.transformInfoRaw;\n    this._svgGroup.x = transformInfoRaw.x;\n    this._svgGroup.y = transformInfoRaw.y;\n    this._svgGroup.scaleX = transformInfoRaw.scaleX;\n    this._svgGroup.scaleY = transformInfoRaw.scaleY;\n    if (this._svgResourceChanged(mapName)) {\n      this._freeSVG();\n      this._useSVG(mapName);\n    }\n    var svgDispatcherMap = this._svgDispatcherMap = zrUtil.createHashMap();\n    var focusSelf = false;\n    zrUtil.each(this._svgGraphicRecord.named, function (namedItem) {\n      // Note that we also allow different elements have the same name.\n      // For example, a glyph of a city and the label of the city have\n      // the same name and their tooltip info can be defined in a single\n      // region option.\n      var regionName = namedItem.name;\n      var mapOrGeoModel = viewBuildCtx.mapOrGeoModel;\n      var data = viewBuildCtx.data;\n      var svgNodeTagLower = namedItem.svgNodeTagLower;\n      var el = namedItem.el;\n      var dataIdx = data ? data.indexOfName(regionName) : null;\n      var regionModel = mapOrGeoModel.getRegionModel(regionName);\n      if (OPTION_STYLE_ENABLED_TAG_MAP.get(svgNodeTagLower) != null && el instanceof Displayable) {\n        applyOptionStyleForRegion(viewBuildCtx, el, dataIdx, regionModel);\n      }\n      if (el instanceof Displayable) {\n        el.culling = true;\n      }\n      var silent = regionModel.get('silent', true);\n      silent != null && (el.silent = silent);\n      // We do not know how the SVG like so we'd better not to change z2.\n      // Otherwise it might bring some unexpected result. For example,\n      // an area hovered that make some inner city can not be clicked.\n      el.z2EmphasisLift = 0;\n      // If self named:\n      if (!namedItem.namedFrom) {\n        // label should batter to be displayed based on the center of <g>\n        // if it is named rather than displayed on each child.\n        if (LABEL_HOST_MAP.get(svgNodeTagLower) != null) {\n          resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx, null);\n        }\n        resetEventTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel, dataIdx);\n        resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n        if (STATE_TRIGGER_TAG_MAP.get(svgNodeTagLower) != null) {\n          var focus_1 = resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel);\n          if (focus_1 === 'self') {\n            focusSelf = true;\n          }\n          var els = svgDispatcherMap.get(regionName) || svgDispatcherMap.set(regionName, []);\n          els.push(el);\n        }\n      }\n    }, this);\n    this._enableBlurEntireSVG(focusSelf, viewBuildCtx);\n  };\n  MapDraw.prototype._enableBlurEntireSVG = function (focusSelf, viewBuildCtx) {\n    // It's a little complicated to support blurring the entire geoSVG in series-map.\n    // So do not support it until some requirements come.\n    // At present, in series-map, only regions can be blurred.\n    if (focusSelf && viewBuildCtx.isGeo) {\n      var blurStyle = viewBuildCtx.mapOrGeoModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      // Only support `opacity` here. Because not sure that other props are suitable for\n      // all of the elements generated by SVG (especially for Text/TSpan/Image/... ).\n      var opacity_1 = blurStyle.opacity;\n      this._svgGraphicRecord.root.traverse(function (el) {\n        if (!el.isGroup) {\n          // PENDING: clear those settings to SVG elements when `_freeSVG`.\n          // (Currently it happen not to be needed.)\n          setDefaultStateProxy(el);\n          var style = el.ensureState('blur').style || {};\n          // Do not overwrite the region style that already set from region option.\n          if (style.opacity == null && opacity_1 != null) {\n            style.opacity = opacity_1;\n          }\n          // If `ensureState('blur').style = {}`, there will be default opacity.\n          // Enable `stateTransition` (animation).\n          el.ensureState('emphasis');\n        }\n      });\n    }\n  };\n  MapDraw.prototype.remove = function () {\n    this._regionsGroup.removeAll();\n    this._regionsGroupByName = null;\n    this._svgGroup.removeAll();\n    this._freeSVG();\n    this._controller.dispose();\n    this._controllerHost = null;\n  };\n  MapDraw.prototype.findHighDownDispatchers = function (name, geoModel) {\n    if (name == null) {\n      return [];\n    }\n    var geo = geoModel.coordinateSystem;\n    if (geo.resourceType === 'geoJSON') {\n      var regionsGroupByName = this._regionsGroupByName;\n      if (regionsGroupByName) {\n        var regionGroup = regionsGroupByName.get(name);\n        return regionGroup ? [regionGroup] : [];\n      }\n    } else if (geo.resourceType === 'geoSVG') {\n      return this._svgDispatcherMap && this._svgDispatcherMap.get(name) || [];\n    }\n  };\n  MapDraw.prototype._svgResourceChanged = function (mapName) {\n    return this._svgMapName !== mapName;\n  };\n  MapDraw.prototype._useSVG = function (mapName) {\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      var svgGraphic = resource.useGraphic(this.uid);\n      this._svgGroup.add(svgGraphic.root);\n      this._svgGraphicRecord = svgGraphic;\n      this._svgMapName = mapName;\n    }\n  };\n  MapDraw.prototype._freeSVG = function () {\n    var mapName = this._svgMapName;\n    if (mapName == null) {\n      return;\n    }\n    var resource = geoSourceManager.getGeoResource(mapName);\n    if (resource && resource.type === 'geoSVG') {\n      resource.freeGraphic(this.uid);\n    }\n    this._svgGraphicRecord = null;\n    this._svgDispatcherMap = null;\n    this._svgGroup.removeAll();\n    this._svgMapName = null;\n  };\n  MapDraw.prototype._updateController = function (mapOrGeoModel, ecModel, api) {\n    var geo = mapOrGeoModel.coordinateSystem;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    // @ts-ignore FIXME:TS\n    controllerHost.zoomLimit = mapOrGeoModel.get('scaleLimit');\n    controllerHost.zoom = geo.getZoom();\n    // roamType is will be set default true if it is null\n    // @ts-ignore FIXME:TS\n    controller.enable(mapOrGeoModel.get('roam') || false);\n    var mainType = mapOrGeoModel.mainType;\n    function makeActionBase() {\n      var action = {\n        type: 'geoRoam',\n        componentType: mainType\n      };\n      action[mainType + 'Id'] = mapOrGeoModel.id;\n      return action;\n    }\n    controller.off('pan').on('pan', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        dx: e.dx,\n        dy: e.dy,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.off('zoom').on('zoom', function (e) {\n      this._mouseDownFlag = false;\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction(zrUtil.extend(makeActionBase(), {\n        totalZoom: controllerHost.zoom,\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY,\n        animation: {\n          duration: 0\n        }\n      }));\n    }, this);\n    controller.setPointerChecker(function (e, x, y) {\n      return geo.containPoint([x, y]) && !onIrrelevantElement(e, api, mapOrGeoModel);\n    });\n  };\n  /**\r\n   * FIXME: this is a temporarily workaround.\r\n   * When `geoRoam` the elements need to be reset in `MapView['render']`, because the props like\r\n   * `ignore` might have been modified by `LabelManager`, and `LabelManager#addLabelsOfSeries`\r\n   * will subsequently cache `defaultAttr` like `ignore`. If do not do this reset, the modified\r\n   * props will have no chance to be restored.\r\n   * Note: This reset should be after `clearStates` in `renderSeries` because `useStates` in\r\n   * `renderSeries` will cache the modified `ignore` to `el._normalState`.\r\n   * TODO:\r\n   * Use clone/immutable in `LabelManager`?\r\n   */\n  MapDraw.prototype.resetForLabelLayout = function () {\n    this.group.traverse(function (el) {\n      var label = el.getTextContent();\n      if (label) {\n        label.ignore = mapLabelRaw(label).ignore;\n      }\n    });\n  };\n  MapDraw.prototype._updateMapSelectHandler = function (mapOrGeoModel, regionsGroup, api, fromView) {\n    var mapDraw = this;\n    regionsGroup.off('mousedown');\n    regionsGroup.off('click');\n    // @ts-ignore FIXME:TS resolve type conflict\n    if (mapOrGeoModel.get('selectedMode')) {\n      regionsGroup.on('mousedown', function () {\n        mapDraw._mouseDownFlag = true;\n      });\n      regionsGroup.on('click', function (e) {\n        if (!mapDraw._mouseDownFlag) {\n          return;\n        }\n        mapDraw._mouseDownFlag = false;\n      });\n    }\n  };\n  return MapDraw;\n}();\n;\nfunction applyOptionStyleForRegion(viewBuildCtx, el, dataIndex, regionModel) {\n  // All of the path are using `itemStyle`, because\n  // (1) Some SVG also use fill on polyline (The different between\n  // polyline and polygon is \"open\" or \"close\" but not fill or not).\n  // (2) For the common props like opacity, if some use itemStyle\n  // and some use `lineStyle`, it might confuse users.\n  // (3) Most SVG use <path>, where can not detect whether to draw a \"line\"\n  // or a filled shape, so use `itemStyle` for <path>.\n  var normalStyleModel = regionModel.getModel('itemStyle');\n  var emphasisStyleModel = regionModel.getModel(['emphasis', 'itemStyle']);\n  var blurStyleModel = regionModel.getModel(['blur', 'itemStyle']);\n  var selectStyleModel = regionModel.getModel(['select', 'itemStyle']);\n  // NOTE: DON'T use 'style' in visual when drawing map.\n  // This component is used for drawing underlying map for both geo component and map series.\n  var normalStyle = getFixedItemStyle(normalStyleModel);\n  var emphasisStyle = getFixedItemStyle(emphasisStyleModel);\n  var selectStyle = getFixedItemStyle(selectStyleModel);\n  var blurStyle = getFixedItemStyle(blurStyleModel);\n  // Update the itemStyle if has data visual\n  var data = viewBuildCtx.data;\n  if (data) {\n    // Only visual color of each item will be used. It can be encoded by visualMap\n    // But visual color of series is used in symbol drawing\n    // Visual color for each series is for the symbol draw\n    var style = data.getItemVisual(dataIndex, 'style');\n    var decal = data.getItemVisual(dataIndex, 'decal');\n    if (viewBuildCtx.isVisualEncodedByVisualMap && style.fill) {\n      normalStyle.fill = style.fill;\n    }\n    if (decal) {\n      normalStyle.decal = createOrUpdatePatternFromDecal(decal, viewBuildCtx.api);\n    }\n  }\n  // SVG text, tspan and image can be named but not supporeted\n  // to be styled by region option yet.\n  el.setStyle(normalStyle);\n  el.style.strokeNoScale = true;\n  el.ensureState('emphasis').style = emphasisStyle;\n  el.ensureState('select').style = selectStyle;\n  el.ensureState('blur').style = blurStyle;\n  // Enable blur\n  setDefaultStateProxy(el);\n}\nfunction resetLabelForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx,\n// If labelXY not provided, use `textConfig.position: 'inside'`\nlabelXY) {\n  var data = viewBuildCtx.data;\n  var isGeo = viewBuildCtx.isGeo;\n  var isDataNaN = data && isNaN(data.get(data.mapDimension('value'), dataIdx));\n  var itemLayout = data && data.getItemLayout(dataIdx);\n  // In the following cases label will be drawn\n  // 1. In map series and data value is NaN\n  // 2. In geo component\n  // 3. Region has no series legendIcon, which will be add a showLabel flag in mapSymbolLayout\n  if (isGeo || isDataNaN || itemLayout && itemLayout.showLabel) {\n    var query = !isGeo ? dataIdx : regionName;\n    var labelFetcher = void 0;\n    // Consider dataIdx not found.\n    if (!data || dataIdx >= 0) {\n      labelFetcher = mapOrGeoModel;\n    }\n    var specifiedTextOpt = labelXY ? {\n      normal: {\n        align: 'center',\n        verticalAlign: 'middle'\n      }\n    } : null;\n    // Caveat: must be called after `setDefaultStateProxy(el);` called.\n    // because textContent will be assign with `el.stateProxy` inside.\n    setLabelStyle(el, getLabelStatesModels(regionModel), {\n      labelFetcher: labelFetcher,\n      labelDataIndex: query,\n      defaultText: regionName\n    }, specifiedTextOpt);\n    var textEl = el.getTextContent();\n    if (textEl) {\n      mapLabelRaw(textEl).ignore = textEl.ignore;\n      if (el.textConfig && labelXY) {\n        // Compute a relative offset based on the el bounding rect.\n        var rect = el.getBoundingRect().clone();\n        // Need to make sure the percent position base on the same rect in normal and\n        // emphasis state. Otherwise if using boundingRect of el, but the emphasis state\n        // has borderWidth (even 0.5px), the text position will be changed obviously\n        // if the position is very big like ['1234%', '1345%'].\n        el.textConfig.layoutRect = rect;\n        el.textConfig.position = [(labelXY[0] - rect.x) / rect.width * 100 + '%', (labelXY[1] - rect.y) / rect.height * 100 + '%'];\n      }\n    }\n    // PENDING:\n    // If labelLayout is enabled (test/label-layout.html), el.dataIndex should be specified.\n    // But el.dataIndex is also used to determine whether user event should be triggered,\n    // where el.seriesIndex or el.dataModel must be specified. At present for a single el\n    // there is not case that \"only label layout enabled but user event disabled\", so here\n    // we depends `resetEventTriggerForRegion` to do the job of setting `el.dataIndex`.\n    el.disableLabelAnimation = true;\n  } else {\n    el.removeTextContent();\n    el.removeTextConfig();\n    el.disableLabelAnimation = null;\n  }\n}\nfunction resetEventTriggerForRegion(viewBuildCtx, eventTrigger, regionName, regionModel, mapOrGeoModel,\n// Exist only if `viewBuildCtx.data` exists.\ndataIdx) {\n  // setItemGraphicEl, setHoverStyle after all polygons and labels\n  // are added to the regionGroup\n  if (viewBuildCtx.data) {\n    // FIXME: when series-map use a SVG map, and there are duplicated name specified\n    // on different SVG elements, after `data.setItemGraphicEl(...)`:\n    // (1) all of them will be mounted with `dataIndex`, `seriesIndex`, so that tooltip\n    // can be triggered only mouse hover. That's correct.\n    // (2) only the last element will be kept in `data`, so that if trigger tooltip\n    // by `dispatchAction`, only the last one can be found and triggered. That might be\n    // not correct. We will fix it in future if anyone demanding that.\n    viewBuildCtx.data.setItemGraphicEl(dataIdx, eventTrigger);\n  }\n  // series-map will not trigger \"geoselectchange\" no matter it is\n  // based on a declared geo component. Because series-map will\n  // trigger \"selectchange\". If it trigger both the two events,\n  // If users call `chart.dispatchAction({type: 'toggleSelect'})`,\n  // it not easy to also fire event \"geoselectchanged\".\n  else {\n    // Package custom mouse event for geo component\n    getECData(eventTrigger).eventData = {\n      componentType: 'geo',\n      componentIndex: mapOrGeoModel.componentIndex,\n      geoIndex: mapOrGeoModel.componentIndex,\n      name: regionName,\n      region: regionModel && regionModel.option || {}\n    };\n  }\n}\nfunction resetTooltipForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  if (!viewBuildCtx.data) {\n    graphic.setTooltipConfig({\n      el: el,\n      componentModel: mapOrGeoModel,\n      itemName: regionName,\n      // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n      itemTooltipOption: regionModel.get('tooltip')\n    });\n  }\n}\nfunction resetStateTriggerForRegion(viewBuildCtx, el, regionName, regionModel, mapOrGeoModel) {\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  el.highDownSilentOnTouch = !!mapOrGeoModel.get('selectedMode');\n  // @ts-ignore FIXME:TS fix the \"compatible with each other\"?\n  var emphasisModel = regionModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  toggleHoverEmphasis(el, focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  if (viewBuildCtx.isGeo) {\n    enableComponentHighDownFeatures(el, mapOrGeoModel, regionName);\n  }\n  return focus;\n}\nfunction projectPolys(rings,\n// Polygons include exterior and interiors. Or polylines.\ncreateStream, isLine) {\n  var polygons = [];\n  var curPoly;\n  function startPolygon() {\n    curPoly = [];\n  }\n  function endPolygon() {\n    if (curPoly.length) {\n      polygons.push(curPoly);\n      curPoly = [];\n    }\n  }\n  var stream = createStream({\n    polygonStart: startPolygon,\n    polygonEnd: endPolygon,\n    lineStart: startPolygon,\n    lineEnd: endPolygon,\n    point: function (x, y) {\n      // May have NaN values from stream.\n      if (isFinite(x) && isFinite(y)) {\n        curPoly.push([x, y]);\n      }\n    },\n    sphere: function () {}\n  });\n  !isLine && stream.polygonStart();\n  zrUtil.each(rings, function (ring) {\n    stream.lineStart();\n    for (var i = 0; i < ring.length; i++) {\n      stream.point(ring[i][0], ring[i][1]);\n    }\n    stream.lineEnd();\n  });\n  !isLine && stream.polygonEnd();\n  return polygons;\n}\nexport default MapDraw;\n// @ts-ignore FIXME:TS fix the \"compatible with each other\"?"], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAO,KAAKC,UAAU,MAAM,sCAAsC;AAClE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,+BAA+B,EAAEC,oBAAoB,QAAQ,sBAAsB;AACjH,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,8BAA8B,QAAQ,qBAAqB;AACpE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C;AACA;AACA;AACA;AACA;AACA,IAAIC,yBAAyB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC;AACpG,IAAIC,4BAA4B,GAAGjB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAAC;AAClF,IAAIG,qBAAqB,GAAGnB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,IAAIC,cAAc,GAAGrB,MAAM,CAACkB,aAAa,CAACF,yBAAyB,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAClF,IAAIE,WAAW,GAAGP,SAAS,CAAC,CAAC;AAC7B,SAASQ,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAIC,SAAS,GAAGD,KAAK,CAACE,YAAY,CAAC,CAAC;EACpC,IAAIC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,WAAW,CAAC;EACtC;EACA;EACA,IAAID,SAAS,IAAI,IAAI,EAAE;IACrBF,SAAS,CAACI,IAAI,GAAGF,SAAS;EAC5B;EACA,OAAOF,SAAS;AAClB;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACC,SAAS,EAAE;EAC/B,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAK;EAC3B,IAAIA,KAAK,EAAE;IACTA,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACH,IAAI;IACzCG,KAAK,CAACH,IAAI,GAAG,IAAI;EACnB;AACF;AACA,IAAIK,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAOA,CAACC,GAAG,EAAE;IACpB,IAAIC,KAAK,GAAG,IAAIhC,OAAO,CAACiC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACC,GAAG,GAAG7B,MAAM,CAAC,aAAa,CAAC;IAChC,IAAI,CAAC8B,WAAW,GAAG,IAAItC,cAAc,CAACkC,GAAG,CAACK,KAAK,CAAC,CAAC,CAAC;IAClD,IAAI,CAACC,eAAe,GAAG;MACrBC,MAAM,EAAEN;IACV,CAAC;IACD,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClBA,KAAK,CAACO,GAAG,CAAC,IAAI,CAACC,aAAa,GAAG,IAAIxC,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;IACnDD,KAAK,CAACO,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,IAAIzC,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;EACjD;EACAH,OAAO,CAACY,SAAS,CAACC,IAAI,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEd,GAAG,EAAEe,QAAQ,EAAEC,OAAO,EAAE;IACjF,IAAIC,KAAK,GAAGJ,aAAa,CAACK,QAAQ,KAAK,KAAK;IAC5C;IACA;IACA,IAAIC,IAAI,GAAGN,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAAC,CAAC;IAC3DH,KAAK,IAAIH,OAAO,CAACO,aAAa,CAAC;MAC7BH,QAAQ,EAAE,QAAQ;MAClBI,OAAO,EAAE;IACX,CAAC,EAAE,UAAUC,SAAS,EAAE;MACtB,IAAI,CAACJ,IAAI,IAAII,SAAS,CAACC,eAAe,CAAC,CAAC,KAAKX,aAAa,EAAE;QAC1DM,IAAI,GAAGI,SAAS,CAACH,OAAO,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAIK,GAAG,GAAGZ,aAAa,CAACa,gBAAgB;IACxC,IAAIC,YAAY,GAAG,IAAI,CAAClB,aAAa;IACrC,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI2B,aAAa,GAAGH,GAAG,CAACI,gBAAgB,CAAC,CAAC;IAC1C,IAAIC,gBAAgB,GAAGF,aAAa,CAACG,GAAG;IACxC,IAAIC,iBAAiB,GAAGJ,aAAa,CAACK,IAAI;IAC1C;IACA,IAAIC,WAAW,GAAG,CAACP,YAAY,CAACQ,OAAO,CAAC,CAAC,CAAC,IAAInB,OAAO;IACrD,IAAIkB,WAAW,EAAE;MACfjC,KAAK,CAACmC,CAAC,GAAGJ,iBAAiB,CAACI,CAAC;MAC7BnC,KAAK,CAACoC,CAAC,GAAGL,iBAAiB,CAACK,CAAC;MAC7BpC,KAAK,CAACqC,MAAM,GAAGN,iBAAiB,CAACM,MAAM;MACvCrC,KAAK,CAACsC,MAAM,GAAGP,iBAAiB,CAACO,MAAM;MACvCtC,KAAK,CAACuC,KAAK,CAAC,CAAC;IACf,CAAC,MAAM;MACLvE,OAAO,CAACwE,WAAW,CAACxC,KAAK,EAAE+B,iBAAiB,EAAEnB,aAAa,CAAC;IAC9D;IACA,IAAI6B,0BAA0B,GAAGvB,IAAI,IAAIA,IAAI,CAACwB,SAAS,CAAC,YAAY,CAAC,IAAIxB,IAAI,CAACwB,SAAS,CAAC,YAAY,CAAC,CAACC,MAAM,GAAG,CAAC;IAChH,IAAIC,YAAY,GAAG;MACjB7C,GAAG,EAAEA,GAAG;MACRyB,GAAG,EAAEA,GAAG;MACRZ,aAAa,EAAEA,aAAa;MAC5BM,IAAI,EAAEA,IAAI;MACVuB,0BAA0B,EAAEA,0BAA0B;MACtDzB,KAAK,EAAEA,KAAK;MACZa,gBAAgB,EAAEA;IACpB,CAAC;IACD,IAAIL,GAAG,CAACqB,YAAY,KAAK,SAAS,EAAE;MAClC,IAAI,CAACC,aAAa,CAACF,YAAY,CAAC;IAClC,CAAC,MAAM,IAAIpB,GAAG,CAACqB,YAAY,KAAK,QAAQ,EAAE;MACxC,IAAI,CAACE,SAAS,CAACH,YAAY,CAAC;IAC9B;IACA,IAAI,CAACI,iBAAiB,CAACpC,aAAa,EAAEC,OAAO,EAAEd,GAAG,CAAC;IACnD,IAAI,CAACkD,uBAAuB,CAACrC,aAAa,EAAEc,YAAY,EAAE3B,GAAG,EAAEe,QAAQ,CAAC;EAC1E,CAAC;EACDhB,OAAO,CAACY,SAAS,CAACoC,aAAa,GAAG,UAAUF,YAAY,EAAE;IACxD,IAAIM,kBAAkB,GAAG,IAAI,CAACC,mBAAmB,GAAGvF,MAAM,CAACkB,aAAa,CAAC,CAAC;IAC1E,IAAIsE,iBAAiB,GAAGxF,MAAM,CAACkB,aAAa,CAAC,CAAC;IAC9C,IAAI4C,YAAY,GAAG,IAAI,CAAClB,aAAa;IACrC,IAAIqB,gBAAgB,GAAGe,YAAY,CAACf,gBAAgB;IACpD,IAAIjB,aAAa,GAAGgC,YAAY,CAAChC,aAAa;IAC9C,IAAIM,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;IAC5B,IAAImC,UAAU,GAAGT,YAAY,CAACpB,GAAG,CAAC6B,UAAU;IAC5C,IAAIC,gBAAgB,GAAGD,UAAU,IAAIA,UAAU,CAACE,MAAM;IACtD,SAASC,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;MACtC,IAAIA,OAAO,EAAE;QACX;QACAD,KAAK,GAAGC,OAAO,CAACD,KAAK,CAAC;MACxB;MACA,OAAOA,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG5B,gBAAgB,CAACQ,MAAM,GAAGR,gBAAgB,CAACM,CAAC,EAAEsB,KAAK,CAAC,CAAC,CAAC,GAAG5B,gBAAgB,CAACS,MAAM,GAAGT,gBAAgB,CAACO,CAAC,CAAC;IACpI;IACA;IACA,SAASuB,sBAAsBA,CAACC,QAAQ,EAAE;MACxC,IAAIC,SAAS,GAAG,EAAE;MAClB;MACA,IAAIH,OAAO,GAAG,CAACJ,gBAAgB,IAAID,UAAU,IAAIA,UAAU,CAACK,OAAO;MACnE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACjB,MAAM,EAAE,EAAEmB,CAAC,EAAE;QACxC,IAAIC,KAAK,GAAGP,cAAc,CAACI,QAAQ,CAACE,CAAC,CAAC,EAAEJ,OAAO,CAAC;QAChDK,KAAK,IAAIF,SAAS,CAACG,IAAI,CAACD,KAAK,CAAC;MAChC;MACA,OAAOF,SAAS;IAClB;IACA,SAASI,YAAYA,CAACC,MAAM,EAAE;MAC5B,OAAO;QACLC,KAAK,EAAE;UACLD,MAAM,EAAEP,sBAAsB,CAACO,MAAM;QACvC;MACF,CAAC;IACH;IACAxC,YAAY,CAAC0C,SAAS,CAAC,CAAC;IACxB;IACAxG,MAAM,CAACyG,IAAI,CAACzB,YAAY,CAACpB,GAAG,CAAC8C,OAAO,EAAE,UAAUC,MAAM,EAAE;MACtD,IAAIC,UAAU,GAAGD,MAAM,CAACE,IAAI;MAC5B;MACA;MACA;MACA;MACA;MACA,IAAIC,WAAW,GAAGxB,kBAAkB,CAAC1D,GAAG,CAACgF,UAAU,CAAC;MACpD,IAAIG,EAAE,GAAGvB,iBAAiB,CAAC5D,GAAG,CAACgF,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9CI,OAAO,GAAGD,EAAE,CAACC,OAAO;QACpBC,WAAW,GAAGF,EAAE,CAACE,WAAW;MAC9B,IAAI,CAACH,WAAW,EAAE;QAChBA,WAAW,GAAGxB,kBAAkB,CAAC4B,GAAG,CAACN,UAAU,EAAE,IAAIxG,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;QACrEyB,YAAY,CAACnB,GAAG,CAACmE,WAAW,CAAC;QAC7BE,OAAO,GAAG1D,IAAI,GAAGA,IAAI,CAAC6D,WAAW,CAACP,UAAU,CAAC,GAAG,IAAI;QACpDK,WAAW,GAAGjC,YAAY,CAAC5B,KAAK,GAAGJ,aAAa,CAACoE,cAAc,CAACR,UAAU,CAAC,GAAGtD,IAAI,GAAGA,IAAI,CAAC+D,YAAY,CAACL,OAAO,CAAC,GAAG,IAAI;QACtH,IAAIM,MAAM,GAAGL,WAAW,CAACrF,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;QAC5C0F,MAAM,IAAI,IAAI,KAAKR,WAAW,CAACQ,MAAM,GAAGA,MAAM,CAAC;QAC/C9B,iBAAiB,CAAC0B,GAAG,CAACN,UAAU,EAAE;UAChCI,OAAO,EAAEA,OAAO;UAChBC,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ;MACA,IAAIM,eAAe,GAAG,EAAE;MACxB,IAAIC,gBAAgB,GAAG,EAAE;MACzBxH,MAAM,CAACyG,IAAI,CAACE,MAAM,CAACc,UAAU,EAAE,UAAUC,QAAQ,EAAE;QACjD;QACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,SAAS,EAAE;UAC/B,IAAIC,KAAK,GAAG,CAACF,QAAQ,CAACG,QAAQ,CAAC,CAACzG,MAAM,CAACsG,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAChE,IAAIpC,gBAAgB,EAAE;YACpBkC,KAAK,GAAGG,YAAY,CAACH,KAAK,EAAElC,gBAAgB,CAAC;UAC/C;UACA1F,MAAM,CAACyG,IAAI,CAACmB,KAAK,EAAE,UAAUI,IAAI,EAAE;YACjCT,eAAe,CAACnB,IAAI,CAAC,IAAIhG,OAAO,CAAC6H,OAAO,CAAC5B,YAAY,CAAC2B,IAAI,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC;QACJ;QACA;QAAA,KACK;UACH,IAAI1B,MAAM,GAAGoB,QAAQ,CAACpB,MAAM;UAC5B,IAAIZ,gBAAgB,EAAE;YACpBY,MAAM,GAAGyB,YAAY,CAACzB,MAAM,EAAEZ,gBAAgB,EAAE,IAAI,CAAC;UACvD;UACA1F,MAAM,CAACyG,IAAI,CAACH,MAAM,EAAE,UAAUA,MAAM,EAAE;YACpCkB,gBAAgB,CAACpB,IAAI,CAAC,IAAIhG,OAAO,CAAC8H,QAAQ,CAAC7B,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI6B,QAAQ,GAAGvC,cAAc,CAACe,MAAM,CAACyB,SAAS,CAAC,CAAC,EAAE3C,UAAU,IAAIA,UAAU,CAACK,OAAO,CAAC;MACnF,SAASuC,kBAAkBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;QAC5C,IAAI,CAACD,QAAQ,CAACvD,MAAM,EAAE;UACpB;QACF;QACA,IAAIyD,YAAY,GAAG,IAAIpI,OAAO,CAACqI,YAAY,CAAC;UAC1CC,OAAO,EAAE,IAAI;UACbC,sBAAsB,EAAE,CAAC;UACzBpC,KAAK,EAAE;YACLqC,KAAK,EAAEN;UACT;QACF,CAAC,CAAC;QACFxB,WAAW,CAACnE,GAAG,CAAC6F,YAAY,CAAC;QAC7BK,yBAAyB,CAAC7D,YAAY,EAAEwD,YAAY,EAAExB,OAAO,EAAEC,WAAW,CAAC;QAC3E6B,mBAAmB,CAAC9D,YAAY,EAAEwD,YAAY,EAAE5B,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,EAAEmB,QAAQ,CAAC;QAC1G,IAAII,MAAM,EAAE;UACVzG,YAAY,CAAC0G,YAAY,CAAC;UAC1BxI,MAAM,CAACyG,IAAI,CAAC+B,YAAY,CAACO,MAAM,EAAEjH,YAAY,CAAC;QAChD;MACF;MACAuG,kBAAkB,CAACd,eAAe,CAAC;MACnCc,kBAAkB,CAACb,gBAAgB,EAAE,IAAI,CAAC;IAC5C,CAAC,CAAC;IACF;IACAlC,kBAAkB,CAACmB,IAAI,CAAC,UAAUK,WAAW,EAAEF,UAAU,EAAE;MACzD,IAAIG,EAAE,GAAGvB,iBAAiB,CAAC5D,GAAG,CAACgF,UAAU,CAAC;QACxCI,OAAO,GAAGD,EAAE,CAACC,OAAO;QACpBC,WAAW,GAAGF,EAAE,CAACE,WAAW;MAC9B+B,0BAA0B,CAAChE,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,CAAC;MACtGiC,qBAAqB,CAACjE,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;MACxFkG,0BAA0B,CAAClE,YAAY,EAAE8B,WAAW,EAAEF,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;IAC/F,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDd,OAAO,CAACY,SAAS,CAACqC,SAAS,GAAG,UAAUH,YAAY,EAAE;IACpD,IAAImE,OAAO,GAAGnE,YAAY,CAACpB,GAAG,CAACwF,GAAG;IAClC,IAAInF,gBAAgB,GAAGe,YAAY,CAACf,gBAAgB;IACpD,IAAI,CAACpB,SAAS,CAAC0B,CAAC,GAAGN,gBAAgB,CAACM,CAAC;IACrC,IAAI,CAAC1B,SAAS,CAAC2B,CAAC,GAAGP,gBAAgB,CAACO,CAAC;IACrC,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,GAAGR,gBAAgB,CAACQ,MAAM;IAC/C,IAAI,CAAC5B,SAAS,CAAC6B,MAAM,GAAGT,gBAAgB,CAACS,MAAM;IAC/C,IAAI,IAAI,CAAC2E,mBAAmB,CAACF,OAAO,CAAC,EAAE;MACrC,IAAI,CAACG,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,OAAO,CAACJ,OAAO,CAAC;IACvB;IACA,IAAIK,gBAAgB,GAAG,IAAI,CAACC,iBAAiB,GAAGzJ,MAAM,CAACkB,aAAa,CAAC,CAAC;IACtE,IAAIwI,SAAS,GAAG,KAAK;IACrB1J,MAAM,CAACyG,IAAI,CAAC,IAAI,CAACkD,iBAAiB,CAACC,KAAK,EAAE,UAAUC,SAAS,EAAE;MAC7D;MACA;MACA;MACA;MACA,IAAIjD,UAAU,GAAGiD,SAAS,CAAChD,IAAI;MAC/B,IAAI7D,aAAa,GAAGgC,YAAY,CAAChC,aAAa;MAC9C,IAAIM,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;MAC5B,IAAIwG,eAAe,GAAGD,SAAS,CAACC,eAAe;MAC/C,IAAIC,EAAE,GAAGF,SAAS,CAACE,EAAE;MACrB,IAAI/C,OAAO,GAAG1D,IAAI,GAAGA,IAAI,CAAC6D,WAAW,CAACP,UAAU,CAAC,GAAG,IAAI;MACxD,IAAIK,WAAW,GAAGjE,aAAa,CAACoE,cAAc,CAACR,UAAU,CAAC;MAC1D,IAAI3F,4BAA4B,CAACW,GAAG,CAACkI,eAAe,CAAC,IAAI,IAAI,IAAIC,EAAE,YAAYjJ,WAAW,EAAE;QAC1F+H,yBAAyB,CAAC7D,YAAY,EAAE+E,EAAE,EAAE/C,OAAO,EAAEC,WAAW,CAAC;MACnE;MACA,IAAI8C,EAAE,YAAYjJ,WAAW,EAAE;QAC7BiJ,EAAE,CAACrB,OAAO,GAAG,IAAI;MACnB;MACA,IAAIpB,MAAM,GAAGL,WAAW,CAACrF,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;MAC5C0F,MAAM,IAAI,IAAI,KAAKyC,EAAE,CAACzC,MAAM,GAAGA,MAAM,CAAC;MACtC;MACA;MACA;MACAyC,EAAE,CAACC,cAAc,GAAG,CAAC;MACrB;MACA,IAAI,CAACH,SAAS,CAACI,SAAS,EAAE;QACxB;QACA;QACA,IAAI5I,cAAc,CAACO,GAAG,CAACkI,eAAe,CAAC,IAAI,IAAI,EAAE;UAC/ChB,mBAAmB,CAAC9D,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,EAAE,IAAI,CAAC;QAC9F;QACAgC,0BAA0B,CAAChE,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAEgE,OAAO,CAAC;QAC7FiC,qBAAqB,CAACjE,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;QAC/E,IAAI7B,qBAAqB,CAACS,GAAG,CAACkI,eAAe,CAAC,IAAI,IAAI,EAAE;UACtD,IAAII,OAAO,GAAGhB,0BAA0B,CAAClE,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,CAAC;UAClG,IAAIkH,OAAO,KAAK,MAAM,EAAE;YACtBR,SAAS,GAAG,IAAI;UAClB;UACA,IAAIS,GAAG,GAAGX,gBAAgB,CAAC5H,GAAG,CAACgF,UAAU,CAAC,IAAI4C,gBAAgB,CAACtC,GAAG,CAACN,UAAU,EAAE,EAAE,CAAC;UAClFuD,GAAG,CAAC/D,IAAI,CAAC2D,EAAE,CAAC;QACd;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACK,oBAAoB,CAACV,SAAS,EAAE1E,YAAY,CAAC;EACpD,CAAC;EACD9C,OAAO,CAACY,SAAS,CAACsH,oBAAoB,GAAG,UAAUV,SAAS,EAAE1E,YAAY,EAAE;IAC1E;IACA;IACA;IACA,IAAI0E,SAAS,IAAI1E,YAAY,CAAC5B,KAAK,EAAE;MACnC,IAAIiH,SAAS,GAAGrF,YAAY,CAAChC,aAAa,CAACsH,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC5I,YAAY,CAAC,CAAC;MACzF;MACA;MACA,IAAI6I,SAAS,GAAGF,SAAS,CAACG,OAAO;MACjC,IAAI,CAACb,iBAAiB,CAACc,IAAI,CAACC,QAAQ,CAAC,UAAUX,EAAE,EAAE;QACjD,IAAI,CAACA,EAAE,CAACY,OAAO,EAAE;UACf;UACA;UACApK,oBAAoB,CAACwJ,EAAE,CAAC;UACxB,IAAI/H,KAAK,GAAG+H,EAAE,CAACa,WAAW,CAAC,MAAM,CAAC,CAAC5I,KAAK,IAAI,CAAC,CAAC;UAC9C;UACA,IAAIA,KAAK,CAACwI,OAAO,IAAI,IAAI,IAAID,SAAS,IAAI,IAAI,EAAE;YAC9CvI,KAAK,CAACwI,OAAO,GAAGD,SAAS;UAC3B;UACA;UACA;UACAR,EAAE,CAACa,WAAW,CAAC,UAAU,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD1I,OAAO,CAACY,SAAS,CAAC+H,MAAM,GAAG,YAAY;IACrC,IAAI,CAACjI,aAAa,CAAC4D,SAAS,CAAC,CAAC;IAC9B,IAAI,CAACjB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC1C,SAAS,CAAC2D,SAAS,CAAC,CAAC;IAC1B,IAAI,CAAC8C,QAAQ,CAAC,CAAC;IACf,IAAI,CAAC/G,WAAW,CAACuI,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACrI,eAAe,GAAG,IAAI;EAC7B,CAAC;EACDP,OAAO,CAACY,SAAS,CAACiI,uBAAuB,GAAG,UAAUlE,IAAI,EAAEmE,QAAQ,EAAE;IACpE,IAAInE,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,EAAE;IACX;IACA,IAAIjD,GAAG,GAAGoH,QAAQ,CAACnH,gBAAgB;IACnC,IAAID,GAAG,CAACqB,YAAY,KAAK,SAAS,EAAE;MAClC,IAAIK,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;MACjD,IAAID,kBAAkB,EAAE;QACtB,IAAIwB,WAAW,GAAGxB,kBAAkB,CAAC1D,GAAG,CAACiF,IAAI,CAAC;QAC9C,OAAOC,WAAW,GAAG,CAACA,WAAW,CAAC,GAAG,EAAE;MACzC;IACF,CAAC,MAAM,IAAIlD,GAAG,CAACqB,YAAY,KAAK,QAAQ,EAAE;MACxC,OAAO,IAAI,CAACwE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC7H,GAAG,CAACiF,IAAI,CAAC,IAAI,EAAE;IACzE;EACF,CAAC;EACD3E,OAAO,CAACY,SAAS,CAACuG,mBAAmB,GAAG,UAAUF,OAAO,EAAE;IACzD,OAAO,IAAI,CAAC8B,WAAW,KAAK9B,OAAO;EACrC,CAAC;EACDjH,OAAO,CAACY,SAAS,CAACyG,OAAO,GAAG,UAAUJ,OAAO,EAAE;IAC7C,IAAI+B,QAAQ,GAAG1K,gBAAgB,CAAC2K,cAAc,CAAChC,OAAO,CAAC;IACvD,IAAI+B,QAAQ,IAAIA,QAAQ,CAACvD,IAAI,KAAK,QAAQ,EAAE;MAC1C,IAAIyD,UAAU,GAAGF,QAAQ,CAACG,UAAU,CAAC,IAAI,CAAC/I,GAAG,CAAC;MAC9C,IAAI,CAACO,SAAS,CAACF,GAAG,CAACyI,UAAU,CAACX,IAAI,CAAC;MACnC,IAAI,CAACd,iBAAiB,GAAGyB,UAAU;MACnC,IAAI,CAACH,WAAW,GAAG9B,OAAO;IAC5B;EACF,CAAC;EACDjH,OAAO,CAACY,SAAS,CAACwG,QAAQ,GAAG,YAAY;IACvC,IAAIH,OAAO,GAAG,IAAI,CAAC8B,WAAW;IAC9B,IAAI9B,OAAO,IAAI,IAAI,EAAE;MACnB;IACF;IACA,IAAI+B,QAAQ,GAAG1K,gBAAgB,CAAC2K,cAAc,CAAChC,OAAO,CAAC;IACvD,IAAI+B,QAAQ,IAAIA,QAAQ,CAACvD,IAAI,KAAK,QAAQ,EAAE;MAC1CuD,QAAQ,CAACI,WAAW,CAAC,IAAI,CAAChJ,GAAG,CAAC;IAChC;IACA,IAAI,CAACqH,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACF,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC5G,SAAS,CAAC2D,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACyE,WAAW,GAAG,IAAI;EACzB,CAAC;EACD/I,OAAO,CAACY,SAAS,CAACsC,iBAAiB,GAAG,UAAUpC,aAAa,EAAEC,OAAO,EAAEd,GAAG,EAAE;IAC3E,IAAIyB,GAAG,GAAGZ,aAAa,CAACa,gBAAgB;IACxC,IAAI0H,UAAU,GAAG,IAAI,CAAChJ,WAAW;IACjC,IAAIiJ,cAAc,GAAG,IAAI,CAAC/I,eAAe;IACzC;IACA+I,cAAc,CAACC,SAAS,GAAGzI,aAAa,CAACpB,GAAG,CAAC,YAAY,CAAC;IAC1D4J,cAAc,CAACE,IAAI,GAAG9H,GAAG,CAAC+H,OAAO,CAAC,CAAC;IACnC;IACA;IACAJ,UAAU,CAACK,MAAM,CAAC5I,aAAa,CAACpB,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;IACrD,IAAIyB,QAAQ,GAAGL,aAAa,CAACK,QAAQ;IACrC,SAASwI,cAAcA,CAAA,EAAG;MACxB,IAAIC,MAAM,GAAG;QACXnE,IAAI,EAAE,SAAS;QACfoE,aAAa,EAAE1I;MACjB,CAAC;MACDyI,MAAM,CAACzI,QAAQ,GAAG,IAAI,CAAC,GAAGL,aAAa,CAACgJ,EAAE;MAC1C,OAAOF,MAAM;IACf;IACAP,UAAU,CAACU,GAAG,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,KAAK,EAAE,UAAUC,CAAC,EAAE;MAC3C,IAAI,CAACC,cAAc,GAAG,KAAK;MAC3BlM,UAAU,CAACmM,eAAe,CAACb,cAAc,EAAEW,CAAC,CAACG,EAAE,EAAEH,CAAC,CAACI,EAAE,CAAC;MACtDpK,GAAG,CAACqK,cAAc,CAACxM,MAAM,CAACyM,MAAM,CAACZ,cAAc,CAAC,CAAC,EAAE;QACjDS,EAAE,EAAEH,CAAC,CAACG,EAAE;QACRC,EAAE,EAAEJ,CAAC,CAACI,EAAE;QACRG,SAAS,EAAE;UACTC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IACRpB,UAAU,CAACU,GAAG,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;MAC7C,IAAI,CAACC,cAAc,GAAG,KAAK;MAC3BlM,UAAU,CAAC0M,gBAAgB,CAACpB,cAAc,EAAEW,CAAC,CAACU,KAAK,EAAEV,CAAC,CAACW,OAAO,EAAEX,CAAC,CAACY,OAAO,CAAC;MAC1E5K,GAAG,CAACqK,cAAc,CAACxM,MAAM,CAACyM,MAAM,CAACZ,cAAc,CAAC,CAAC,EAAE;QACjDmB,SAAS,EAAExB,cAAc,CAACE,IAAI;QAC9BA,IAAI,EAAES,CAAC,CAACU,KAAK;QACbC,OAAO,EAAEX,CAAC,CAACW,OAAO;QAClBC,OAAO,EAAEZ,CAAC,CAACY,OAAO;QAClBL,SAAS,EAAE;UACTC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IACRpB,UAAU,CAAC0B,iBAAiB,CAAC,UAAUd,CAAC,EAAE5H,CAAC,EAAEC,CAAC,EAAE;MAC9C,OAAOZ,GAAG,CAACsJ,YAAY,CAAC,CAAC3I,CAAC,EAAEC,CAAC,CAAC,CAAC,IAAI,CAACrE,mBAAmB,CAACgM,CAAC,EAAEhK,GAAG,EAAEa,aAAa,CAAC;IAChF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEd,OAAO,CAACY,SAAS,CAACqK,mBAAmB,GAAG,YAAY;IAClD,IAAI,CAAC/K,KAAK,CAACsI,QAAQ,CAAC,UAAUX,EAAE,EAAE;MAChC,IAAIqD,KAAK,GAAGrD,EAAE,CAACsD,cAAc,CAAC,CAAC;MAC/B,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,MAAM,GAAGhM,WAAW,CAAC8L,KAAK,CAAC,CAACE,MAAM;MAC1C;IACF,CAAC,CAAC;EACJ,CAAC;EACDpL,OAAO,CAACY,SAAS,CAACuC,uBAAuB,GAAG,UAAUrC,aAAa,EAAEc,YAAY,EAAE3B,GAAG,EAAEe,QAAQ,EAAE;IAChG,IAAIqK,OAAO,GAAG,IAAI;IAClBzJ,YAAY,CAACmI,GAAG,CAAC,WAAW,CAAC;IAC7BnI,YAAY,CAACmI,GAAG,CAAC,OAAO,CAAC;IACzB;IACA,IAAIjJ,aAAa,CAACpB,GAAG,CAAC,cAAc,CAAC,EAAE;MACrCkC,YAAY,CAACoI,EAAE,CAAC,WAAW,EAAE,YAAY;QACvCqB,OAAO,CAACnB,cAAc,GAAG,IAAI;MAC/B,CAAC,CAAC;MACFtI,YAAY,CAACoI,EAAE,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;QACpC,IAAI,CAACoB,OAAO,CAACnB,cAAc,EAAE;UAC3B;QACF;QACAmB,OAAO,CAACnB,cAAc,GAAG,KAAK;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EACD,OAAOlK,OAAO;AAChB,CAAC,CAAC,CAAC;AACH;AACA,SAAS2G,yBAAyBA,CAAC7D,YAAY,EAAE+E,EAAE,EAAEyD,SAAS,EAAEvG,WAAW,EAAE;EAC3E;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIwG,gBAAgB,GAAGxG,WAAW,CAACqD,QAAQ,CAAC,WAAW,CAAC;EACxD,IAAIoD,kBAAkB,GAAGzG,WAAW,CAACqD,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;EACxE,IAAIqD,cAAc,GAAG1G,WAAW,CAACqD,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;EAChE,IAAIsD,gBAAgB,GAAG3G,WAAW,CAACqD,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;EACpE;EACA;EACA,IAAIuD,WAAW,GAAGtM,iBAAiB,CAACkM,gBAAgB,CAAC;EACrD,IAAIK,aAAa,GAAGvM,iBAAiB,CAACmM,kBAAkB,CAAC;EACzD,IAAIK,WAAW,GAAGxM,iBAAiB,CAACqM,gBAAgB,CAAC;EACrD,IAAIvD,SAAS,GAAG9I,iBAAiB,CAACoM,cAAc,CAAC;EACjD;EACA,IAAIrK,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;EAC5B,IAAIA,IAAI,EAAE;IACR;IACA;IACA;IACA,IAAItB,KAAK,GAAGsB,IAAI,CAAC0K,aAAa,CAACR,SAAS,EAAE,OAAO,CAAC;IAClD,IAAIS,KAAK,GAAG3K,IAAI,CAAC0K,aAAa,CAACR,SAAS,EAAE,OAAO,CAAC;IAClD,IAAIxI,YAAY,CAACH,0BAA0B,IAAI7C,KAAK,CAACH,IAAI,EAAE;MACzDgM,WAAW,CAAChM,IAAI,GAAGG,KAAK,CAACH,IAAI;IAC/B;IACA,IAAIoM,KAAK,EAAE;MACTJ,WAAW,CAACI,KAAK,GAAGpN,8BAA8B,CAACoN,KAAK,EAAEjJ,YAAY,CAAC7C,GAAG,CAAC;IAC7E;EACF;EACA;EACA;EACA4H,EAAE,CAACmE,QAAQ,CAACL,WAAW,CAAC;EACxB9D,EAAE,CAAC/H,KAAK,CAACmM,aAAa,GAAG,IAAI;EAC7BpE,EAAE,CAACa,WAAW,CAAC,UAAU,CAAC,CAAC5I,KAAK,GAAG8L,aAAa;EAChD/D,EAAE,CAACa,WAAW,CAAC,QAAQ,CAAC,CAAC5I,KAAK,GAAG+L,WAAW;EAC5ChE,EAAE,CAACa,WAAW,CAAC,MAAM,CAAC,CAAC5I,KAAK,GAAGqI,SAAS;EACxC;EACA9J,oBAAoB,CAACwJ,EAAE,CAAC;AAC1B;AACA,SAASjB,mBAAmBA,CAAC9D,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa;AACrF;AACAgE,OAAO;AACP;AACAoH,OAAO,EAAE;EACP,IAAI9K,IAAI,GAAG0B,YAAY,CAAC1B,IAAI;EAC5B,IAAIF,KAAK,GAAG4B,YAAY,CAAC5B,KAAK;EAC9B,IAAIiL,SAAS,GAAG/K,IAAI,IAAIgL,KAAK,CAAChL,IAAI,CAAC1B,GAAG,CAAC0B,IAAI,CAACiL,YAAY,CAAC,OAAO,CAAC,EAAEvH,OAAO,CAAC,CAAC;EAC5E,IAAIwH,UAAU,GAAGlL,IAAI,IAAIA,IAAI,CAACmL,aAAa,CAACzH,OAAO,CAAC;EACpD;EACA;EACA;EACA;EACA,IAAI5D,KAAK,IAAIiL,SAAS,IAAIG,UAAU,IAAIA,UAAU,CAACE,SAAS,EAAE;IAC5D,IAAIC,KAAK,GAAG,CAACvL,KAAK,GAAG4D,OAAO,GAAGJ,UAAU;IACzC,IAAIgI,YAAY,GAAG,KAAK,CAAC;IACzB;IACA,IAAI,CAACtL,IAAI,IAAI0D,OAAO,IAAI,CAAC,EAAE;MACzB4H,YAAY,GAAG5L,aAAa;IAC9B;IACA,IAAI6L,gBAAgB,GAAGT,OAAO,GAAG;MAC/BU,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,aAAa,EAAE;MACjB;IACF,CAAC,GAAG,IAAI;IACR;IACA;IACAtO,aAAa,CAACqJ,EAAE,EAAEpJ,oBAAoB,CAACsG,WAAW,CAAC,EAAE;MACnD2H,YAAY,EAAEA,YAAY;MAC1BK,cAAc,EAAEN,KAAK;MACrBO,WAAW,EAAEtI;IACf,CAAC,EAAEiI,gBAAgB,CAAC;IACpB,IAAIM,MAAM,GAAGpF,EAAE,CAACsD,cAAc,CAAC,CAAC;IAChC,IAAI8B,MAAM,EAAE;MACV7N,WAAW,CAAC6N,MAAM,CAAC,CAAC7B,MAAM,GAAG6B,MAAM,CAAC7B,MAAM;MAC1C,IAAIvD,EAAE,CAACqF,UAAU,IAAIhB,OAAO,EAAE;QAC5B;QACA,IAAIiB,IAAI,GAAGtF,EAAE,CAACuF,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACvC;QACA;QACA;QACA;QACAxF,EAAE,CAACqF,UAAU,CAACI,UAAU,GAAGH,IAAI;QAC/BtF,EAAE,CAACqF,UAAU,CAACK,QAAQ,GAAG,CAAC,CAACrB,OAAO,CAAC,CAAC,CAAC,GAAGiB,IAAI,CAAC9K,CAAC,IAAI8K,IAAI,CAACK,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACtB,OAAO,CAAC,CAAC,CAAC,GAAGiB,IAAI,CAAC7K,CAAC,IAAI6K,IAAI,CAACM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;MAC5H;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA5F,EAAE,CAAC6F,qBAAqB,GAAG,IAAI;EACjC,CAAC,MAAM;IACL7F,EAAE,CAAC8F,iBAAiB,CAAC,CAAC;IACtB9F,EAAE,CAAC+F,gBAAgB,CAAC,CAAC;IACrB/F,EAAE,CAAC6F,qBAAqB,GAAG,IAAI;EACjC;AACF;AACA,SAAS5G,0BAA0BA,CAAChE,YAAY,EAAE+K,YAAY,EAAEnJ,UAAU,EAAEK,WAAW,EAAEjE,aAAa;AACtG;AACAgE,OAAO,EAAE;EACP;EACA;EACA,IAAIhC,YAAY,CAAC1B,IAAI,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA0B,YAAY,CAAC1B,IAAI,CAAC0M,gBAAgB,CAAChJ,OAAO,EAAE+I,YAAY,CAAC;EAC3D;EACA;EACA;EACA;EACA;EACA;EAAA,KACK;IACH;IACAnP,SAAS,CAACmP,YAAY,CAAC,CAACE,SAAS,GAAG;MAClClE,aAAa,EAAE,KAAK;MACpBmE,cAAc,EAAElN,aAAa,CAACkN,cAAc;MAC5CC,QAAQ,EAAEnN,aAAa,CAACkN,cAAc;MACtCrJ,IAAI,EAAED,UAAU;MAChBD,MAAM,EAAEM,WAAW,IAAIA,WAAW,CAACmJ,MAAM,IAAI,CAAC;IAChD,CAAC;EACH;AACF;AACA,SAASnH,qBAAqBA,CAACjE,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAE;EACvF,IAAI,CAACgC,YAAY,CAAC1B,IAAI,EAAE;IACtBlD,OAAO,CAACiQ,gBAAgB,CAAC;MACvBtG,EAAE,EAAEA,EAAE;MACNuG,cAAc,EAAEtN,aAAa;MAC7BuN,QAAQ,EAAE3J,UAAU;MACpB;MACA4J,iBAAiB,EAAEvJ,WAAW,CAACrF,GAAG,CAAC,SAAS;IAC9C,CAAC,CAAC;EACJ;AACF;AACA,SAASsH,0BAA0BA,CAAClE,YAAY,EAAE+E,EAAE,EAAEnD,UAAU,EAAEK,WAAW,EAAEjE,aAAa,EAAE;EAC5F;EACA+G,EAAE,CAAC0G,qBAAqB,GAAG,CAAC,CAACzN,aAAa,CAACpB,GAAG,CAAC,cAAc,CAAC;EAC9D;EACA,IAAI8O,aAAa,GAAGzJ,WAAW,CAACqD,QAAQ,CAAC,UAAU,CAAC;EACpD,IAAIqG,KAAK,GAAGD,aAAa,CAAC9O,GAAG,CAAC,OAAO,CAAC;EACtCvB,mBAAmB,CAAC0J,EAAE,EAAE4G,KAAK,EAAED,aAAa,CAAC9O,GAAG,CAAC,WAAW,CAAC,EAAE8O,aAAa,CAAC9O,GAAG,CAAC,UAAU,CAAC,CAAC;EAC7F,IAAIoD,YAAY,CAAC5B,KAAK,EAAE;IACtB9C,+BAA+B,CAACyJ,EAAE,EAAE/G,aAAa,EAAE4D,UAAU,CAAC;EAChE;EACA,OAAO+J,KAAK;AACd;AACA,SAAS5I,YAAYA,CAAC6I,KAAK;AAC3B;AACAC,YAAY,EAAEtI,MAAM,EAAE;EACpB,IAAIuI,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO;EACX,SAASC,YAAYA,CAAA,EAAG;IACtBD,OAAO,GAAG,EAAE;EACd;EACA,SAASE,UAAUA,CAAA,EAAG;IACpB,IAAIF,OAAO,CAAChM,MAAM,EAAE;MAClB+L,QAAQ,CAAC1K,IAAI,CAAC2K,OAAO,CAAC;MACtBA,OAAO,GAAG,EAAE;IACd;EACF;EACA,IAAIpL,MAAM,GAAGkL,YAAY,CAAC;IACxBK,YAAY,EAAEF,YAAY;IAC1BG,UAAU,EAAEF,UAAU;IACtBG,SAAS,EAAEJ,YAAY;IACvBK,OAAO,EAAEJ,UAAU;IACnBpL,KAAK,EAAE,SAAAA,CAAUtB,CAAC,EAAEC,CAAC,EAAE;MACrB;MACA,IAAI8M,QAAQ,CAAC/M,CAAC,CAAC,IAAI+M,QAAQ,CAAC9M,CAAC,CAAC,EAAE;QAC9BuM,OAAO,CAAC3K,IAAI,CAAC,CAAC7B,CAAC,EAAEC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;IACD+M,MAAM,EAAE,SAAAA,CAAA,EAAY,CAAC;EACvB,CAAC,CAAC;EACF,CAAChJ,MAAM,IAAI5C,MAAM,CAACuL,YAAY,CAAC,CAAC;EAChClR,MAAM,CAACyG,IAAI,CAACmK,KAAK,EAAE,UAAUY,IAAI,EAAE;IACjC7L,MAAM,CAACyL,SAAS,CAAC,CAAC;IAClB,KAAK,IAAIlL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsL,IAAI,CAACzM,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACpCP,MAAM,CAACE,KAAK,CAAC2L,IAAI,CAACtL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEsL,IAAI,CAACtL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;IACAP,MAAM,CAAC0L,OAAO,CAAC,CAAC;EAClB,CAAC,CAAC;EACF,CAAC9I,MAAM,IAAI5C,MAAM,CAACwL,UAAU,CAAC,CAAC;EAC9B,OAAOL,QAAQ;AACjB;AACA,eAAe5O,OAAO;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}