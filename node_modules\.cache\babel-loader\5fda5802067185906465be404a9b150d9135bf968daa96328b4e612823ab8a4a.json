{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport OrdinalScale from '../scale/Ordinal.js';\nimport IntervalScale from '../scale/Interval.js';\nimport Scale from '../scale/Scale.js';\nimport { prepareLayoutBarSeries, makeColumnLayout, retrieveColumnLayout } from '../layout/barGrid.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport TimeScale from '../scale/Time.js';\nimport LogScale from '../scale/Log.js';\nimport { getStackedDimension } from '../data/helper/dataStackHelper.js';\nimport { ensureScaleRawExtentInfo } from './scaleRawExtentInfo.js';\n/**\r\n * Get axis scale extent before niced.\r\n * Item of returned array can only be number (including Infinity and NaN).\r\n *\r\n * Caution:\r\n * Precondition of calling this method:\r\n * The scale extent has been initialized using series data extent via\r\n * `scale.setExtent` or `scale.unionExtentFromData`;\r\n */\nexport function getScaleExtent(scale, model) {\n  var scaleType = scale.type;\n  var rawExtentResult = ensureScaleRawExtentInfo(scale, model, scale.getExtent()).calculate();\n  scale.setBlank(rawExtentResult.isBlank);\n  var min = rawExtentResult.min;\n  var max = rawExtentResult.max;\n  // If bars are placed on a base axis of type time or interval account for axis boundary overflow and current axis\n  // is base axis\n  // FIXME\n  // (1) Consider support value axis, where below zero and axis `onZero` should be handled properly.\n  // (2) Refactor the logic with `barGrid`. Is it not need to `makeBarWidthAndOffsetInfo` twice with different extent?\n  //     Should not depend on series type `bar`?\n  // (3) Fix that might overlap when using dataZoom.\n  // (4) Consider other chart types using `barGrid`?\n  // See #6728, #4862, `test/bar-overflow-time-plot.html`\n  var ecModel = model.ecModel;\n  if (ecModel && scaleType === 'time' /* || scaleType === 'interval' */) {\n    var barSeriesModels = prepareLayoutBarSeries('bar', ecModel);\n    var isBaseAxisAndHasBarSeries_1 = false;\n    zrUtil.each(barSeriesModels, function (seriesModel) {\n      isBaseAxisAndHasBarSeries_1 = isBaseAxisAndHasBarSeries_1 || seriesModel.getBaseAxis() === model.axis;\n    });\n    if (isBaseAxisAndHasBarSeries_1) {\n      // Calculate placement of bars on axis. TODO should be decoupled\n      // with barLayout\n      var barWidthAndOffset = makeColumnLayout(barSeriesModels);\n      // Adjust axis min and max to account for overflow\n      var adjustedScale = adjustScaleForOverflow(min, max, model, barWidthAndOffset);\n      min = adjustedScale.min;\n      max = adjustedScale.max;\n    }\n  }\n  return {\n    extent: [min, max],\n    // \"fix\" means \"fixed\", the value should not be\n    // changed in the subsequent steps.\n    fixMin: rawExtentResult.minFixed,\n    fixMax: rawExtentResult.maxFixed\n  };\n}\nfunction adjustScaleForOverflow(min, max, model,\n// Only support cartesian coord yet.\nbarWidthAndOffset) {\n  // Get Axis Length\n  var axisExtent = model.axis.getExtent();\n  var axisLength = Math.abs(axisExtent[1] - axisExtent[0]);\n  // Get bars on current base axis and calculate min and max overflow\n  var barsOnCurrentAxis = retrieveColumnLayout(barWidthAndOffset, model.axis);\n  if (barsOnCurrentAxis === undefined) {\n    return {\n      min: min,\n      max: max\n    };\n  }\n  var minOverflow = Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    minOverflow = Math.min(item.offset, minOverflow);\n  });\n  var maxOverflow = -Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    maxOverflow = Math.max(item.offset + item.width, maxOverflow);\n  });\n  minOverflow = Math.abs(minOverflow);\n  maxOverflow = Math.abs(maxOverflow);\n  var totalOverFlow = minOverflow + maxOverflow;\n  // Calculate required buffer based on old range and overflow\n  var oldRange = max - min;\n  var oldRangePercentOfNew = 1 - (minOverflow + maxOverflow) / axisLength;\n  var overflowBuffer = oldRange / oldRangePercentOfNew - oldRange;\n  max += overflowBuffer * (maxOverflow / totalOverFlow);\n  min -= overflowBuffer * (minOverflow / totalOverFlow);\n  return {\n    min: min,\n    max: max\n  };\n}\n// Precondition of calling this method:\n// The scale extent has been initialized using series data extent via\n// `scale.setExtent` or `scale.unionExtentFromData`;\nexport function niceScaleExtent(scale, inModel) {\n  var model = inModel;\n  var extentInfo = getScaleExtent(scale, model);\n  var extent = extentInfo.extent;\n  var splitNumber = model.get('splitNumber');\n  if (scale instanceof LogScale) {\n    scale.base = model.get('logBase');\n  }\n  var scaleType = scale.type;\n  var interval = model.get('interval');\n  var isIntervalOrTime = scaleType === 'interval' || scaleType === 'time';\n  scale.setExtent(extent[0], extent[1]);\n  scale.calcNiceExtent({\n    splitNumber: splitNumber,\n    fixMin: extentInfo.fixMin,\n    fixMax: extentInfo.fixMax,\n    minInterval: isIntervalOrTime ? model.get('minInterval') : null,\n    maxInterval: isIntervalOrTime ? model.get('maxInterval') : null\n  });\n  // If some one specified the min, max. And the default calculated interval\n  // is not good enough. He can specify the interval. It is often appeared\n  // in angle axis with angle 0 - 360. Interval calculated in interval scale is hard\n  // to be 60.\n  // FIXME\n  if (interval != null) {\n    scale.setInterval && scale.setInterval(interval);\n  }\n}\n/**\r\n * @param axisType Default retrieve from model.type\r\n */\nexport function createScaleByModel(model, axisType) {\n  axisType = axisType || model.get('type');\n  if (axisType) {\n    switch (axisType) {\n      // Buildin scale\n      case 'category':\n        return new OrdinalScale({\n          ordinalMeta: model.getOrdinalMeta ? model.getOrdinalMeta() : model.getCategories(),\n          extent: [Infinity, -Infinity]\n        });\n      case 'time':\n        return new TimeScale({\n          locale: model.ecModel.getLocaleModel(),\n          useUTC: model.ecModel.get('useUTC')\n        });\n      default:\n        // case 'value'/'interval', 'log', or others.\n        return new (Scale.getClass(axisType) || IntervalScale)();\n    }\n  }\n}\n/**\r\n * Check if the axis cross 0\r\n */\nexport function ifAxisCrossZero(axis) {\n  var dataExtent = axis.scale.getExtent();\n  var min = dataExtent[0];\n  var max = dataExtent[1];\n  return !(min > 0 && max > 0 || min < 0 && max < 0);\n}\n/**\r\n * @param axis\r\n * @return Label formatter function.\r\n *         param: {number} tickValue,\r\n *         param: {number} idx, the index in all ticks.\r\n *                         If category axis, this param is not required.\r\n *         return: {string} label string.\r\n */\nexport function makeLabelFormatter(axis) {\n  var labelFormatter = axis.getLabelModel().get('formatter');\n  var categoryTickStart = axis.type === 'category' ? axis.scale.getExtent()[0] : null;\n  if (axis.scale.type === 'time') {\n    return function (tpl) {\n      return function (tick, idx) {\n        return axis.scale.getFormattedLabel(tick, idx, tpl);\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isString(labelFormatter)) {\n    return function (tpl) {\n      return function (tick) {\n        // For category axis, get raw value; for numeric axis,\n        // get formatted label like '1,333,444'.\n        var label = axis.scale.getLabel(tick);\n        var text = tpl.replace('{value}', label != null ? label : '');\n        return text;\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isFunction(labelFormatter)) {\n    return function (cb) {\n      return function (tick, idx) {\n        // The original intention of `idx` is \"the index of the tick in all ticks\".\n        // But the previous implementation of category axis do not consider the\n        // `axisLabel.interval`, which cause that, for example, the `interval` is\n        // `1`, then the ticks \"name5\", \"name7\", \"name9\" are displayed, where the\n        // corresponding `idx` are `0`, `2`, `4`, but not `0`, `1`, `2`. So we keep\n        // the definition here for back compatibility.\n        if (categoryTickStart != null) {\n          idx = tick.value - categoryTickStart;\n        }\n        return cb(getAxisRawValue(axis, tick), idx, tick.level != null ? {\n          level: tick.level\n        } : null);\n      };\n    }(labelFormatter);\n  } else {\n    return function (tick) {\n      return axis.scale.getLabel(tick);\n    };\n  }\n}\nexport function getAxisRawValue(axis, tick) {\n  // In category axis with data zoom, tick is not the original\n  // index of axis.data. So tick should not be exposed to user\n  // in category axis.\n  return axis.type === 'category' ? axis.scale.getLabel(tick) : tick.value;\n}\n/**\r\n * @param axis\r\n * @return Be null/undefined if no labels.\r\n */\nexport function estimateLabelUnionRect(axis) {\n  var axisModel = axis.model;\n  var scale = axis.scale;\n  if (!axisModel.get(['axisLabel', 'show']) || scale.isBlank()) {\n    return;\n  }\n  var realNumberScaleTicks;\n  var tickCount;\n  var categoryScaleExtent = scale.getExtent();\n  // Optimize for large category data, avoid call `getTicks()`.\n  if (scale instanceof OrdinalScale) {\n    tickCount = scale.count();\n  } else {\n    realNumberScaleTicks = scale.getTicks();\n    tickCount = realNumberScaleTicks.length;\n  }\n  var axisLabelModel = axis.getLabelModel();\n  var labelFormatter = makeLabelFormatter(axis);\n  var rect;\n  var step = 1;\n  // Simple optimization for large amount of labels\n  if (tickCount > 40) {\n    step = Math.ceil(tickCount / 40);\n  }\n  for (var i = 0; i < tickCount; i += step) {\n    var tick = realNumberScaleTicks ? realNumberScaleTicks[i] : {\n      value: categoryScaleExtent[0] + i\n    };\n    var label = labelFormatter(tick, i);\n    var unrotatedSingleRect = axisLabelModel.getTextRect(label);\n    var singleRect = rotateTextRect(unrotatedSingleRect, axisLabelModel.get('rotate') || 0);\n    rect ? rect.union(singleRect) : rect = singleRect;\n  }\n  return rect;\n}\nfunction rotateTextRect(textRect, rotate) {\n  var rotateRadians = rotate * Math.PI / 180;\n  var beforeWidth = textRect.width;\n  var beforeHeight = textRect.height;\n  var afterWidth = beforeWidth * Math.abs(Math.cos(rotateRadians)) + Math.abs(beforeHeight * Math.sin(rotateRadians));\n  var afterHeight = beforeWidth * Math.abs(Math.sin(rotateRadians)) + Math.abs(beforeHeight * Math.cos(rotateRadians));\n  var rotatedRect = new BoundingRect(textRect.x, textRect.y, afterWidth, afterHeight);\n  return rotatedRect;\n}\n/**\r\n * @param model axisLabelModel or axisTickModel\r\n * @return {number|String} Can be null|'auto'|number|function\r\n */\nexport function getOptionCategoryInterval(model) {\n  var interval = model.get('interval');\n  return interval == null ? 'auto' : interval;\n}\n/**\r\n * Set `categoryInterval` as 0 implicitly indicates that\r\n * show all labels regardless of overlap.\r\n * @param {Object} axis axisModel.axis\r\n */\nexport function shouldShowAllLabels(axis) {\n  return axis.type === 'category' && getOptionCategoryInterval(axis.getLabelModel()) === 0;\n}\nexport function getDataDimensionsOnAxis(data, axisDim) {\n  // Remove duplicated dat dimensions caused by `getStackedDimension`.\n  var dataDimMap = {};\n  // Currently `mapDimensionsAll` will contain stack result dimension ('__\\0ecstackresult').\n  // PENDING: is it reasonable? Do we need to remove the original dim from \"coord dim\" since\n  // there has been stacked result dim?\n  zrUtil.each(data.mapDimensionsAll(axisDim), function (dataDim) {\n    // For example, the extent of the original dimension\n    // is [0.1, 0.5], the extent of the `stackResultDimension`\n    // is [7, 9], the final extent should NOT include [0.1, 0.5],\n    // because there is no graphic corresponding to [0.1, 0.5].\n    // See the case in `test/area-stack.html` `main1`, where area line\n    // stack needs `yAxis` not start from 0.\n    dataDimMap[getStackedDimension(data, dataDim)] = true;\n  });\n  return zrUtil.keys(dataDimMap);\n}\nexport function unionAxisExtentFromData(dataExtent, data, axisDim) {\n  if (data) {\n    zrUtil.each(getDataDimensionsOnAxis(data, axisDim), function (dim) {\n      var seriesExtent = data.getApproximateExtent(dim);\n      seriesExtent[0] < dataExtent[0] && (dataExtent[0] = seriesExtent[0]);\n      seriesExtent[1] > dataExtent[1] && (dataExtent[1] = seriesExtent[1]);\n    });\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "OrdinalScale", "IntervalScale", "Scale", "prepareLayoutBarSeries", "makeColumnLayout", "retrieveColumnLayout", "BoundingRect", "TimeScale", "LogScale", "getStackedDimension", "ensureScaleRawExtentInfo", "getScaleExtent", "scale", "model", "scaleType", "type", "rawExtentResult", "getExtent", "calculate", "setBlank", "isBlank", "min", "max", "ecModel", "barSeriesModels", "isBaseAxisAndHasBarSeries_1", "each", "seriesModel", "getBaseAxis", "axis", "barWidthAndOffset", "adjustedScale", "adjustScaleForOverflow", "extent", "fixMin", "minFixed", "fixMax", "maxFixed", "axisExtent", "axisLength", "Math", "abs", "barsOnCurrentAxis", "undefined", "minOverflow", "Infinity", "item", "offset", "maxOverflow", "width", "totalOverFlow", "oldRange", "oldRangePercentOfNew", "overflowBuffer", "niceScaleExtent", "inModel", "extentInfo", "splitNumber", "get", "base", "interval", "isIntervalOrTime", "setExtent", "calcNiceExtent", "minInterval", "maxInterval", "setInterval", "createScaleByModel", "axisType", "ordinalMeta", "getOrdinalMeta", "getCategories", "locale", "getLocaleModel", "useUTC", "getClass", "ifAxisCrossZero", "dataExtent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelFormatter", "getLabelModel", "categoryTickStart", "tpl", "tick", "idx", "getFormattedLabel", "isString", "label", "get<PERSON><PERSON><PERSON>", "text", "replace", "isFunction", "cb", "value", "getAxisRawValue", "level", "estimateLabelUnionRect", "axisModel", "realNumberScaleTicks", "tickCount", "categoryScaleExtent", "count", "getTicks", "length", "axisLabelModel", "rect", "step", "ceil", "i", "unrotatedSingleRect", "getTextRect", "singleRect", "rotateTextRect", "union", "textRect", "rotate", "rotateRadians", "PI", "beforeWidth", "beforeHeight", "height", "afterWidth", "cos", "sin", "afterHeight", "rotatedRect", "x", "y", "getOptionCategoryInterval", "shouldShowAllLabels", "getDataDimensionsOnAxis", "data", "axisDim", "dataDimMap", "mapDimensionsAll", "dataDim", "keys", "unionAxisExtentFromData", "dim", "seriesExtent", "getApproximateExtent"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/axisHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport OrdinalScale from '../scale/Ordinal.js';\nimport IntervalScale from '../scale/Interval.js';\nimport Scale from '../scale/Scale.js';\nimport { prepareLayoutBarSeries, makeColumnLayout, retrieveColumnLayout } from '../layout/barGrid.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport TimeScale from '../scale/Time.js';\nimport LogScale from '../scale/Log.js';\nimport { getStackedDimension } from '../data/helper/dataStackHelper.js';\nimport { ensureScaleRawExtentInfo } from './scaleRawExtentInfo.js';\n/**\r\n * Get axis scale extent before niced.\r\n * Item of returned array can only be number (including Infinity and NaN).\r\n *\r\n * Caution:\r\n * Precondition of calling this method:\r\n * The scale extent has been initialized using series data extent via\r\n * `scale.setExtent` or `scale.unionExtentFromData`;\r\n */\nexport function getScaleExtent(scale, model) {\n  var scaleType = scale.type;\n  var rawExtentResult = ensureScaleRawExtentInfo(scale, model, scale.getExtent()).calculate();\n  scale.setBlank(rawExtentResult.isBlank);\n  var min = rawExtentResult.min;\n  var max = rawExtentResult.max;\n  // If bars are placed on a base axis of type time or interval account for axis boundary overflow and current axis\n  // is base axis\n  // FIXME\n  // (1) Consider support value axis, where below zero and axis `onZero` should be handled properly.\n  // (2) Refactor the logic with `barGrid`. Is it not need to `makeBarWidthAndOffsetInfo` twice with different extent?\n  //     Should not depend on series type `bar`?\n  // (3) Fix that might overlap when using dataZoom.\n  // (4) Consider other chart types using `barGrid`?\n  // See #6728, #4862, `test/bar-overflow-time-plot.html`\n  var ecModel = model.ecModel;\n  if (ecModel && scaleType === 'time' /* || scaleType === 'interval' */) {\n    var barSeriesModels = prepareLayoutBarSeries('bar', ecModel);\n    var isBaseAxisAndHasBarSeries_1 = false;\n    zrUtil.each(barSeriesModels, function (seriesModel) {\n      isBaseAxisAndHasBarSeries_1 = isBaseAxisAndHasBarSeries_1 || seriesModel.getBaseAxis() === model.axis;\n    });\n    if (isBaseAxisAndHasBarSeries_1) {\n      // Calculate placement of bars on axis. TODO should be decoupled\n      // with barLayout\n      var barWidthAndOffset = makeColumnLayout(barSeriesModels);\n      // Adjust axis min and max to account for overflow\n      var adjustedScale = adjustScaleForOverflow(min, max, model, barWidthAndOffset);\n      min = adjustedScale.min;\n      max = adjustedScale.max;\n    }\n  }\n  return {\n    extent: [min, max],\n    // \"fix\" means \"fixed\", the value should not be\n    // changed in the subsequent steps.\n    fixMin: rawExtentResult.minFixed,\n    fixMax: rawExtentResult.maxFixed\n  };\n}\nfunction adjustScaleForOverflow(min, max, model,\n// Only support cartesian coord yet.\nbarWidthAndOffset) {\n  // Get Axis Length\n  var axisExtent = model.axis.getExtent();\n  var axisLength = Math.abs(axisExtent[1] - axisExtent[0]);\n  // Get bars on current base axis and calculate min and max overflow\n  var barsOnCurrentAxis = retrieveColumnLayout(barWidthAndOffset, model.axis);\n  if (barsOnCurrentAxis === undefined) {\n    return {\n      min: min,\n      max: max\n    };\n  }\n  var minOverflow = Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    minOverflow = Math.min(item.offset, minOverflow);\n  });\n  var maxOverflow = -Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    maxOverflow = Math.max(item.offset + item.width, maxOverflow);\n  });\n  minOverflow = Math.abs(minOverflow);\n  maxOverflow = Math.abs(maxOverflow);\n  var totalOverFlow = minOverflow + maxOverflow;\n  // Calculate required buffer based on old range and overflow\n  var oldRange = max - min;\n  var oldRangePercentOfNew = 1 - (minOverflow + maxOverflow) / axisLength;\n  var overflowBuffer = oldRange / oldRangePercentOfNew - oldRange;\n  max += overflowBuffer * (maxOverflow / totalOverFlow);\n  min -= overflowBuffer * (minOverflow / totalOverFlow);\n  return {\n    min: min,\n    max: max\n  };\n}\n// Precondition of calling this method:\n// The scale extent has been initialized using series data extent via\n// `scale.setExtent` or `scale.unionExtentFromData`;\nexport function niceScaleExtent(scale, inModel) {\n  var model = inModel;\n  var extentInfo = getScaleExtent(scale, model);\n  var extent = extentInfo.extent;\n  var splitNumber = model.get('splitNumber');\n  if (scale instanceof LogScale) {\n    scale.base = model.get('logBase');\n  }\n  var scaleType = scale.type;\n  var interval = model.get('interval');\n  var isIntervalOrTime = scaleType === 'interval' || scaleType === 'time';\n  scale.setExtent(extent[0], extent[1]);\n  scale.calcNiceExtent({\n    splitNumber: splitNumber,\n    fixMin: extentInfo.fixMin,\n    fixMax: extentInfo.fixMax,\n    minInterval: isIntervalOrTime ? model.get('minInterval') : null,\n    maxInterval: isIntervalOrTime ? model.get('maxInterval') : null\n  });\n  // If some one specified the min, max. And the default calculated interval\n  // is not good enough. He can specify the interval. It is often appeared\n  // in angle axis with angle 0 - 360. Interval calculated in interval scale is hard\n  // to be 60.\n  // FIXME\n  if (interval != null) {\n    scale.setInterval && scale.setInterval(interval);\n  }\n}\n/**\r\n * @param axisType Default retrieve from model.type\r\n */\nexport function createScaleByModel(model, axisType) {\n  axisType = axisType || model.get('type');\n  if (axisType) {\n    switch (axisType) {\n      // Buildin scale\n      case 'category':\n        return new OrdinalScale({\n          ordinalMeta: model.getOrdinalMeta ? model.getOrdinalMeta() : model.getCategories(),\n          extent: [Infinity, -Infinity]\n        });\n      case 'time':\n        return new TimeScale({\n          locale: model.ecModel.getLocaleModel(),\n          useUTC: model.ecModel.get('useUTC')\n        });\n      default:\n        // case 'value'/'interval', 'log', or others.\n        return new (Scale.getClass(axisType) || IntervalScale)();\n    }\n  }\n}\n/**\r\n * Check if the axis cross 0\r\n */\nexport function ifAxisCrossZero(axis) {\n  var dataExtent = axis.scale.getExtent();\n  var min = dataExtent[0];\n  var max = dataExtent[1];\n  return !(min > 0 && max > 0 || min < 0 && max < 0);\n}\n/**\r\n * @param axis\r\n * @return Label formatter function.\r\n *         param: {number} tickValue,\r\n *         param: {number} idx, the index in all ticks.\r\n *                         If category axis, this param is not required.\r\n *         return: {string} label string.\r\n */\nexport function makeLabelFormatter(axis) {\n  var labelFormatter = axis.getLabelModel().get('formatter');\n  var categoryTickStart = axis.type === 'category' ? axis.scale.getExtent()[0] : null;\n  if (axis.scale.type === 'time') {\n    return function (tpl) {\n      return function (tick, idx) {\n        return axis.scale.getFormattedLabel(tick, idx, tpl);\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isString(labelFormatter)) {\n    return function (tpl) {\n      return function (tick) {\n        // For category axis, get raw value; for numeric axis,\n        // get formatted label like '1,333,444'.\n        var label = axis.scale.getLabel(tick);\n        var text = tpl.replace('{value}', label != null ? label : '');\n        return text;\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isFunction(labelFormatter)) {\n    return function (cb) {\n      return function (tick, idx) {\n        // The original intention of `idx` is \"the index of the tick in all ticks\".\n        // But the previous implementation of category axis do not consider the\n        // `axisLabel.interval`, which cause that, for example, the `interval` is\n        // `1`, then the ticks \"name5\", \"name7\", \"name9\" are displayed, where the\n        // corresponding `idx` are `0`, `2`, `4`, but not `0`, `1`, `2`. So we keep\n        // the definition here for back compatibility.\n        if (categoryTickStart != null) {\n          idx = tick.value - categoryTickStart;\n        }\n        return cb(getAxisRawValue(axis, tick), idx, tick.level != null ? {\n          level: tick.level\n        } : null);\n      };\n    }(labelFormatter);\n  } else {\n    return function (tick) {\n      return axis.scale.getLabel(tick);\n    };\n  }\n}\nexport function getAxisRawValue(axis, tick) {\n  // In category axis with data zoom, tick is not the original\n  // index of axis.data. So tick should not be exposed to user\n  // in category axis.\n  return axis.type === 'category' ? axis.scale.getLabel(tick) : tick.value;\n}\n/**\r\n * @param axis\r\n * @return Be null/undefined if no labels.\r\n */\nexport function estimateLabelUnionRect(axis) {\n  var axisModel = axis.model;\n  var scale = axis.scale;\n  if (!axisModel.get(['axisLabel', 'show']) || scale.isBlank()) {\n    return;\n  }\n  var realNumberScaleTicks;\n  var tickCount;\n  var categoryScaleExtent = scale.getExtent();\n  // Optimize for large category data, avoid call `getTicks()`.\n  if (scale instanceof OrdinalScale) {\n    tickCount = scale.count();\n  } else {\n    realNumberScaleTicks = scale.getTicks();\n    tickCount = realNumberScaleTicks.length;\n  }\n  var axisLabelModel = axis.getLabelModel();\n  var labelFormatter = makeLabelFormatter(axis);\n  var rect;\n  var step = 1;\n  // Simple optimization for large amount of labels\n  if (tickCount > 40) {\n    step = Math.ceil(tickCount / 40);\n  }\n  for (var i = 0; i < tickCount; i += step) {\n    var tick = realNumberScaleTicks ? realNumberScaleTicks[i] : {\n      value: categoryScaleExtent[0] + i\n    };\n    var label = labelFormatter(tick, i);\n    var unrotatedSingleRect = axisLabelModel.getTextRect(label);\n    var singleRect = rotateTextRect(unrotatedSingleRect, axisLabelModel.get('rotate') || 0);\n    rect ? rect.union(singleRect) : rect = singleRect;\n  }\n  return rect;\n}\nfunction rotateTextRect(textRect, rotate) {\n  var rotateRadians = rotate * Math.PI / 180;\n  var beforeWidth = textRect.width;\n  var beforeHeight = textRect.height;\n  var afterWidth = beforeWidth * Math.abs(Math.cos(rotateRadians)) + Math.abs(beforeHeight * Math.sin(rotateRadians));\n  var afterHeight = beforeWidth * Math.abs(Math.sin(rotateRadians)) + Math.abs(beforeHeight * Math.cos(rotateRadians));\n  var rotatedRect = new BoundingRect(textRect.x, textRect.y, afterWidth, afterHeight);\n  return rotatedRect;\n}\n/**\r\n * @param model axisLabelModel or axisTickModel\r\n * @return {number|String} Can be null|'auto'|number|function\r\n */\nexport function getOptionCategoryInterval(model) {\n  var interval = model.get('interval');\n  return interval == null ? 'auto' : interval;\n}\n/**\r\n * Set `categoryInterval` as 0 implicitly indicates that\r\n * show all labels regardless of overlap.\r\n * @param {Object} axis axisModel.axis\r\n */\nexport function shouldShowAllLabels(axis) {\n  return axis.type === 'category' && getOptionCategoryInterval(axis.getLabelModel()) === 0;\n}\nexport function getDataDimensionsOnAxis(data, axisDim) {\n  // Remove duplicated dat dimensions caused by `getStackedDimension`.\n  var dataDimMap = {};\n  // Currently `mapDimensionsAll` will contain stack result dimension ('__\\0ecstackresult').\n  // PENDING: is it reasonable? Do we need to remove the original dim from \"coord dim\" since\n  // there has been stacked result dim?\n  zrUtil.each(data.mapDimensionsAll(axisDim), function (dataDim) {\n    // For example, the extent of the original dimension\n    // is [0.1, 0.5], the extent of the `stackResultDimension`\n    // is [7, 9], the final extent should NOT include [0.1, 0.5],\n    // because there is no graphic corresponding to [0.1, 0.5].\n    // See the case in `test/area-stack.html` `main1`, where area line\n    // stack needs `yAxis` not start from 0.\n    dataDimMap[getStackedDimension(data, dataDim)] = true;\n  });\n  return zrUtil.keys(dataDimMap);\n}\nexport function unionAxisExtentFromData(dataExtent, data, axisDim) {\n  if (data) {\n    zrUtil.each(getDataDimensionsOnAxis(data, axisDim), function (dim) {\n      var seriesExtent = data.getApproximateExtent(dim);\n      seriesExtent[0] < dataExtent[0] && (dataExtent[0] = seriesExtent[0]);\n      seriesExtent[1] > dataExtent[1] && (dataExtent[1] = seriesExtent[1]);\n    });\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,sBAAsB,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,sBAAsB;AACrG,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGF,KAAK,CAACG,IAAI;EAC1B,IAAIC,eAAe,GAAGN,wBAAwB,CAACE,KAAK,EAAEC,KAAK,EAAED,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;EAC3FN,KAAK,CAACO,QAAQ,CAACH,eAAe,CAACI,OAAO,CAAC;EACvC,IAAIC,GAAG,GAAGL,eAAe,CAACK,GAAG;EAC7B,IAAIC,GAAG,GAAGN,eAAe,CAACM,GAAG;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,OAAO,GAAGV,KAAK,CAACU,OAAO;EAC3B,IAAIA,OAAO,IAAIT,SAAS,KAAK,MAAM,CAAC,mCAAmC;IACrE,IAAIU,eAAe,GAAGrB,sBAAsB,CAAC,KAAK,EAAEoB,OAAO,CAAC;IAC5D,IAAIE,2BAA2B,GAAG,KAAK;IACvC1B,MAAM,CAAC2B,IAAI,CAACF,eAAe,EAAE,UAAUG,WAAW,EAAE;MAClDF,2BAA2B,GAAGA,2BAA2B,IAAIE,WAAW,CAACC,WAAW,CAAC,CAAC,KAAKf,KAAK,CAACgB,IAAI;IACvG,CAAC,CAAC;IACF,IAAIJ,2BAA2B,EAAE;MAC/B;MACA;MACA,IAAIK,iBAAiB,GAAG1B,gBAAgB,CAACoB,eAAe,CAAC;MACzD;MACA,IAAIO,aAAa,GAAGC,sBAAsB,CAACX,GAAG,EAAEC,GAAG,EAAET,KAAK,EAAEiB,iBAAiB,CAAC;MAC9ET,GAAG,GAAGU,aAAa,CAACV,GAAG;MACvBC,GAAG,GAAGS,aAAa,CAACT,GAAG;IACzB;EACF;EACA,OAAO;IACLW,MAAM,EAAE,CAACZ,GAAG,EAAEC,GAAG,CAAC;IAClB;IACA;IACAY,MAAM,EAAElB,eAAe,CAACmB,QAAQ;IAChCC,MAAM,EAAEpB,eAAe,CAACqB;EAC1B,CAAC;AACH;AACA,SAASL,sBAAsBA,CAACX,GAAG,EAAEC,GAAG,EAAET,KAAK;AAC/C;AACAiB,iBAAiB,EAAE;EACjB;EACA,IAAIQ,UAAU,GAAGzB,KAAK,CAACgB,IAAI,CAACZ,SAAS,CAAC,CAAC;EACvC,IAAIsB,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;EACxD;EACA,IAAII,iBAAiB,GAAGrC,oBAAoB,CAACyB,iBAAiB,EAAEjB,KAAK,CAACgB,IAAI,CAAC;EAC3E,IAAIa,iBAAiB,KAAKC,SAAS,EAAE;IACnC,OAAO;MACLtB,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA;IACP,CAAC;EACH;EACA,IAAIsB,WAAW,GAAGC,QAAQ;EAC1B9C,MAAM,CAAC2B,IAAI,CAACgB,iBAAiB,EAAE,UAAUI,IAAI,EAAE;IAC7CF,WAAW,GAAGJ,IAAI,CAACnB,GAAG,CAACyB,IAAI,CAACC,MAAM,EAAEH,WAAW,CAAC;EAClD,CAAC,CAAC;EACF,IAAII,WAAW,GAAG,CAACH,QAAQ;EAC3B9C,MAAM,CAAC2B,IAAI,CAACgB,iBAAiB,EAAE,UAAUI,IAAI,EAAE;IAC7CE,WAAW,GAAGR,IAAI,CAAClB,GAAG,CAACwB,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACG,KAAK,EAAED,WAAW,CAAC;EAC/D,CAAC,CAAC;EACFJ,WAAW,GAAGJ,IAAI,CAACC,GAAG,CAACG,WAAW,CAAC;EACnCI,WAAW,GAAGR,IAAI,CAACC,GAAG,CAACO,WAAW,CAAC;EACnC,IAAIE,aAAa,GAAGN,WAAW,GAAGI,WAAW;EAC7C;EACA,IAAIG,QAAQ,GAAG7B,GAAG,GAAGD,GAAG;EACxB,IAAI+B,oBAAoB,GAAG,CAAC,GAAG,CAACR,WAAW,GAAGI,WAAW,IAAIT,UAAU;EACvE,IAAIc,cAAc,GAAGF,QAAQ,GAAGC,oBAAoB,GAAGD,QAAQ;EAC/D7B,GAAG,IAAI+B,cAAc,IAAIL,WAAW,GAAGE,aAAa,CAAC;EACrD7B,GAAG,IAAIgC,cAAc,IAAIT,WAAW,GAAGM,aAAa,CAAC;EACrD,OAAO;IACL7B,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA;EACP,CAAC;AACH;AACA;AACA;AACA;AACA,OAAO,SAASgC,eAAeA,CAAC1C,KAAK,EAAE2C,OAAO,EAAE;EAC9C,IAAI1C,KAAK,GAAG0C,OAAO;EACnB,IAAIC,UAAU,GAAG7C,cAAc,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC7C,IAAIoB,MAAM,GAAGuB,UAAU,CAACvB,MAAM;EAC9B,IAAIwB,WAAW,GAAG5C,KAAK,CAAC6C,GAAG,CAAC,aAAa,CAAC;EAC1C,IAAI9C,KAAK,YAAYJ,QAAQ,EAAE;IAC7BI,KAAK,CAAC+C,IAAI,GAAG9C,KAAK,CAAC6C,GAAG,CAAC,SAAS,CAAC;EACnC;EACA,IAAI5C,SAAS,GAAGF,KAAK,CAACG,IAAI;EAC1B,IAAI6C,QAAQ,GAAG/C,KAAK,CAAC6C,GAAG,CAAC,UAAU,CAAC;EACpC,IAAIG,gBAAgB,GAAG/C,SAAS,KAAK,UAAU,IAAIA,SAAS,KAAK,MAAM;EACvEF,KAAK,CAACkD,SAAS,CAAC7B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EACrCrB,KAAK,CAACmD,cAAc,CAAC;IACnBN,WAAW,EAAEA,WAAW;IACxBvB,MAAM,EAAEsB,UAAU,CAACtB,MAAM;IACzBE,MAAM,EAAEoB,UAAU,CAACpB,MAAM;IACzB4B,WAAW,EAAEH,gBAAgB,GAAGhD,KAAK,CAAC6C,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI;IAC/DO,WAAW,EAAEJ,gBAAgB,GAAGhD,KAAK,CAAC6C,GAAG,CAAC,aAAa,CAAC,GAAG;EAC7D,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA,IAAIE,QAAQ,IAAI,IAAI,EAAE;IACpBhD,KAAK,CAACsD,WAAW,IAAItD,KAAK,CAACsD,WAAW,CAACN,QAAQ,CAAC;EAClD;AACF;AACA;AACA;AACA;AACA,OAAO,SAASO,kBAAkBA,CAACtD,KAAK,EAAEuD,QAAQ,EAAE;EAClDA,QAAQ,GAAGA,QAAQ,IAAIvD,KAAK,CAAC6C,GAAG,CAAC,MAAM,CAAC;EACxC,IAAIU,QAAQ,EAAE;IACZ,QAAQA,QAAQ;MACd;MACA,KAAK,UAAU;QACb,OAAO,IAAIpE,YAAY,CAAC;UACtBqE,WAAW,EAAExD,KAAK,CAACyD,cAAc,GAAGzD,KAAK,CAACyD,cAAc,CAAC,CAAC,GAAGzD,KAAK,CAAC0D,aAAa,CAAC,CAAC;UAClFtC,MAAM,EAAE,CAACY,QAAQ,EAAE,CAACA,QAAQ;QAC9B,CAAC,CAAC;MACJ,KAAK,MAAM;QACT,OAAO,IAAItC,SAAS,CAAC;UACnBiE,MAAM,EAAE3D,KAAK,CAACU,OAAO,CAACkD,cAAc,CAAC,CAAC;UACtCC,MAAM,EAAE7D,KAAK,CAACU,OAAO,CAACmC,GAAG,CAAC,QAAQ;QACpC,CAAC,CAAC;MACJ;QACE;QACA,OAAO,KAAKxD,KAAK,CAACyE,QAAQ,CAACP,QAAQ,CAAC,IAAInE,aAAa,EAAE,CAAC;IAC5D;EACF;AACF;AACA;AACA;AACA;AACA,OAAO,SAAS2E,eAAeA,CAAC/C,IAAI,EAAE;EACpC,IAAIgD,UAAU,GAAGhD,IAAI,CAACjB,KAAK,CAACK,SAAS,CAAC,CAAC;EACvC,IAAII,GAAG,GAAGwD,UAAU,CAAC,CAAC,CAAC;EACvB,IAAIvD,GAAG,GAAGuD,UAAU,CAAC,CAAC,CAAC;EACvB,OAAO,EAAExD,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,IAAID,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwD,kBAAkBA,CAACjD,IAAI,EAAE;EACvC,IAAIkD,cAAc,GAAGlD,IAAI,CAACmD,aAAa,CAAC,CAAC,CAACtB,GAAG,CAAC,WAAW,CAAC;EAC1D,IAAIuB,iBAAiB,GAAGpD,IAAI,CAACd,IAAI,KAAK,UAAU,GAAGc,IAAI,CAACjB,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACnF,IAAIY,IAAI,CAACjB,KAAK,CAACG,IAAI,KAAK,MAAM,EAAE;IAC9B,OAAO,UAAUmE,GAAG,EAAE;MACpB,OAAO,UAAUC,IAAI,EAAEC,GAAG,EAAE;QAC1B,OAAOvD,IAAI,CAACjB,KAAK,CAACyE,iBAAiB,CAACF,IAAI,EAAEC,GAAG,EAAEF,GAAG,CAAC;MACrD,CAAC;IACH,CAAC,CAACH,cAAc,CAAC;EACnB,CAAC,MAAM,IAAIhF,MAAM,CAACuF,QAAQ,CAACP,cAAc,CAAC,EAAE;IAC1C,OAAO,UAAUG,GAAG,EAAE;MACpB,OAAO,UAAUC,IAAI,EAAE;QACrB;QACA;QACA,IAAII,KAAK,GAAG1D,IAAI,CAACjB,KAAK,CAAC4E,QAAQ,CAACL,IAAI,CAAC;QACrC,IAAIM,IAAI,GAAGP,GAAG,CAACQ,OAAO,CAAC,SAAS,EAAEH,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,EAAE,CAAC;QAC7D,OAAOE,IAAI;MACb,CAAC;IACH,CAAC,CAACV,cAAc,CAAC;EACnB,CAAC,MAAM,IAAIhF,MAAM,CAAC4F,UAAU,CAACZ,cAAc,CAAC,EAAE;IAC5C,OAAO,UAAUa,EAAE,EAAE;MACnB,OAAO,UAAUT,IAAI,EAAEC,GAAG,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA,IAAIH,iBAAiB,IAAI,IAAI,EAAE;UAC7BG,GAAG,GAAGD,IAAI,CAACU,KAAK,GAAGZ,iBAAiB;QACtC;QACA,OAAOW,EAAE,CAACE,eAAe,CAACjE,IAAI,EAAEsD,IAAI,CAAC,EAAEC,GAAG,EAAED,IAAI,CAACY,KAAK,IAAI,IAAI,GAAG;UAC/DA,KAAK,EAAEZ,IAAI,CAACY;QACd,CAAC,GAAG,IAAI,CAAC;MACX,CAAC;IACH,CAAC,CAAChB,cAAc,CAAC;EACnB,CAAC,MAAM;IACL,OAAO,UAAUI,IAAI,EAAE;MACrB,OAAOtD,IAAI,CAACjB,KAAK,CAAC4E,QAAQ,CAACL,IAAI,CAAC;IAClC,CAAC;EACH;AACF;AACA,OAAO,SAASW,eAAeA,CAACjE,IAAI,EAAEsD,IAAI,EAAE;EAC1C;EACA;EACA;EACA,OAAOtD,IAAI,CAACd,IAAI,KAAK,UAAU,GAAGc,IAAI,CAACjB,KAAK,CAAC4E,QAAQ,CAACL,IAAI,CAAC,GAAGA,IAAI,CAACU,KAAK;AAC1E;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,sBAAsBA,CAACnE,IAAI,EAAE;EAC3C,IAAIoE,SAAS,GAAGpE,IAAI,CAAChB,KAAK;EAC1B,IAAID,KAAK,GAAGiB,IAAI,CAACjB,KAAK;EACtB,IAAI,CAACqF,SAAS,CAACvC,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI9C,KAAK,CAACQ,OAAO,CAAC,CAAC,EAAE;IAC5D;EACF;EACA,IAAI8E,oBAAoB;EACxB,IAAIC,SAAS;EACb,IAAIC,mBAAmB,GAAGxF,KAAK,CAACK,SAAS,CAAC,CAAC;EAC3C;EACA,IAAIL,KAAK,YAAYZ,YAAY,EAAE;IACjCmG,SAAS,GAAGvF,KAAK,CAACyF,KAAK,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLH,oBAAoB,GAAGtF,KAAK,CAAC0F,QAAQ,CAAC,CAAC;IACvCH,SAAS,GAAGD,oBAAoB,CAACK,MAAM;EACzC;EACA,IAAIC,cAAc,GAAG3E,IAAI,CAACmD,aAAa,CAAC,CAAC;EACzC,IAAID,cAAc,GAAGD,kBAAkB,CAACjD,IAAI,CAAC;EAC7C,IAAI4E,IAAI;EACR,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIP,SAAS,GAAG,EAAE,EAAE;IAClBO,IAAI,GAAGlE,IAAI,CAACmE,IAAI,CAACR,SAAS,GAAG,EAAE,CAAC;EAClC;EACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,IAAIF,IAAI,EAAE;IACxC,IAAIvB,IAAI,GAAGe,oBAAoB,GAAGA,oBAAoB,CAACU,CAAC,CAAC,GAAG;MAC1Df,KAAK,EAAEO,mBAAmB,CAAC,CAAC,CAAC,GAAGQ;IAClC,CAAC;IACD,IAAIrB,KAAK,GAAGR,cAAc,CAACI,IAAI,EAAEyB,CAAC,CAAC;IACnC,IAAIC,mBAAmB,GAAGL,cAAc,CAACM,WAAW,CAACvB,KAAK,CAAC;IAC3D,IAAIwB,UAAU,GAAGC,cAAc,CAACH,mBAAmB,EAAEL,cAAc,CAAC9C,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACvF+C,IAAI,GAAGA,IAAI,CAACQ,KAAK,CAACF,UAAU,CAAC,GAAGN,IAAI,GAAGM,UAAU;EACnD;EACA,OAAON,IAAI;AACb;AACA,SAASO,cAAcA,CAACE,QAAQ,EAAEC,MAAM,EAAE;EACxC,IAAIC,aAAa,GAAGD,MAAM,GAAG3E,IAAI,CAAC6E,EAAE,GAAG,GAAG;EAC1C,IAAIC,WAAW,GAAGJ,QAAQ,CAACjE,KAAK;EAChC,IAAIsE,YAAY,GAAGL,QAAQ,CAACM,MAAM;EAClC,IAAIC,UAAU,GAAGH,WAAW,GAAG9E,IAAI,CAACC,GAAG,CAACD,IAAI,CAACkF,GAAG,CAACN,aAAa,CAAC,CAAC,GAAG5E,IAAI,CAACC,GAAG,CAAC8E,YAAY,GAAG/E,IAAI,CAACmF,GAAG,CAACP,aAAa,CAAC,CAAC;EACnH,IAAIQ,WAAW,GAAGN,WAAW,GAAG9E,IAAI,CAACC,GAAG,CAACD,IAAI,CAACmF,GAAG,CAACP,aAAa,CAAC,CAAC,GAAG5E,IAAI,CAACC,GAAG,CAAC8E,YAAY,GAAG/E,IAAI,CAACkF,GAAG,CAACN,aAAa,CAAC,CAAC;EACpH,IAAIS,WAAW,GAAG,IAAIvH,YAAY,CAAC4G,QAAQ,CAACY,CAAC,EAAEZ,QAAQ,CAACa,CAAC,EAAEN,UAAU,EAAEG,WAAW,CAAC;EACnF,OAAOC,WAAW;AACpB;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,yBAAyBA,CAACnH,KAAK,EAAE;EAC/C,IAAI+C,QAAQ,GAAG/C,KAAK,CAAC6C,GAAG,CAAC,UAAU,CAAC;EACpC,OAAOE,QAAQ,IAAI,IAAI,GAAG,MAAM,GAAGA,QAAQ;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqE,mBAAmBA,CAACpG,IAAI,EAAE;EACxC,OAAOA,IAAI,CAACd,IAAI,KAAK,UAAU,IAAIiH,yBAAyB,CAACnG,IAAI,CAACmD,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;AAC1F;AACA,OAAO,SAASkD,uBAAuBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACrD;EACA,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB;EACA;EACA;EACAtI,MAAM,CAAC2B,IAAI,CAACyG,IAAI,CAACG,gBAAgB,CAACF,OAAO,CAAC,EAAE,UAAUG,OAAO,EAAE;IAC7D;IACA;IACA;IACA;IACA;IACA;IACAF,UAAU,CAAC5H,mBAAmB,CAAC0H,IAAI,EAAEI,OAAO,CAAC,CAAC,GAAG,IAAI;EACvD,CAAC,CAAC;EACF,OAAOxI,MAAM,CAACyI,IAAI,CAACH,UAAU,CAAC;AAChC;AACA,OAAO,SAASI,uBAAuBA,CAAC5D,UAAU,EAAEsD,IAAI,EAAEC,OAAO,EAAE;EACjE,IAAID,IAAI,EAAE;IACRpI,MAAM,CAAC2B,IAAI,CAACwG,uBAAuB,CAACC,IAAI,EAAEC,OAAO,CAAC,EAAE,UAAUM,GAAG,EAAE;MACjE,IAAIC,YAAY,GAAGR,IAAI,CAACS,oBAAoB,CAACF,GAAG,CAAC;MACjDC,YAAY,CAAC,CAAC,CAAC,GAAG9D,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAG8D,YAAY,CAAC,CAAC,CAAC,CAAC;MACpEA,YAAY,CAAC,CAAC,CAAC,GAAG9D,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAG8D,YAAY,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}