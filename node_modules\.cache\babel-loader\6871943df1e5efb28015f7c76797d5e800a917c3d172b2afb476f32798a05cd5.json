{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bar-card\"\n};\nconst _hoisted_2 = {\n  ref: \"volumn\",\n  class: \"volume\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, null, 512 /* NEED_PATCH */)]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\n        \"个人\",\n        \"单位\",\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#1e4a72\",\n            },\n            {\n              offset: 0.5,\n              color: \"#2d6ba3\",\n            },\n            {\n              offset: 1,\n              color: \"#5fb3f0\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#0f3a5f\",\n            },\n            {\n              offset: 0.5,\n              color: \"#1e5a8a\",\n            },\n            {\n              offset: 1,\n              color: \"#4da6d9\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var yValue = api.value(1);\n        var start = api.coord([api.value(0), 0]);\n        var end = api.coord([api.value(0), yValue]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseWidth = height * 0.8; // 底面宽度\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的多个面\n        var topPoint = [x, y]; // 顶点\n        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点\n        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点\n        var bottomBack = [x, start[1] + baseWidth * 0.3]; // 底面后点（营造3D效果）\n\n        var group = {\n          type: \"group\",\n          children: [\n            // 主面（正面）\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomLeft, bottomRight],\n              },\n              style: {\n                fill: style.fill,\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 1,\n              },\n            },\n            // 左侧面（营造3D效果）\n            {\n              type: \"polygon\",\n              z2: 2,\n              shape: {\n                points: [topPoint, bottomLeft, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.7,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 1,\n              },\n            },\n            // 右侧面（营造3D效果）\n            {\n              type: \"polygon\",\n              z2: 1,\n              shape: {\n                points: [topPoint, bottomRight, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.5,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 1,\n              },\n            },\n          ],\n        };\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n          },\n          formatter: function(params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false,\n        },\n        xAxis: {\n          data: xData,\n          show: false, // 隐藏X轴\n        },\n        yAxis: {\n          show: false, // 隐藏Y轴\n          max: 100,\n        },\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n\n            label: {\n              show: true,\n              position: \"top\",\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.35),\n              formatter: function(params) {\n                return \"{a\" + params.dataIndex + \"|\" + params.data + \"人}\";\n              },\n              rich: {\n                a0: {\n                  color: \"#77A9FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a1: {\n                  color: \"#FEBBBD\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a2: {\n                  color: \"#A1EEB3\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a3: {\n                  color: \"#F3BD5B\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a4: {\n                  color: \"#57D1FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a5: {\n                  color: \"#F0ED97\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n                a6: {\n                  color: \"#9EA0FF\",\n                  fontSize: this.fontSize(0.35),\n                  align: \"center\",\n                  fontFamily: \"PangMenZhengDao\",\n                },\n              },\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EACdC,GAAG,EAAC,QAAQ;EAACD,KAAK,EAAC;;;uBAD1BE,mBAAA,CAEM,OAFNC,UAEM,GADJC,mBAAA,CAAmC,OAAnCC,UAAmC,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}