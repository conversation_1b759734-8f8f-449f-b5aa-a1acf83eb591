{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>',\n  data() {\n    return {\n      myChart: null\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\"个人\", \"单位\"];\n      var colors = [{\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#1e4a72\"\n        }, {\n          offset: 0.5,\n          color: \"#2d6ba3\"\n        }, {\n          offset: 1,\n          color: \"#5fb3f0\"\n        }]\n      }, {\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#0f3a5f\"\n        }, {\n          offset: 0.5,\n          color: \"#1e5a8a\"\n        }, {\n          offset: 1,\n          color: \"#4da6d9\"\n        }]\n      }];\n      var renderItem = function (params, api) {\n        var dataIndex = params.dataIndex;\n        var value = api.value(1);\n\n        // 调整高度比例，避免差距过大\n        var normalizedHeight;\n        if (value >= 50) {\n          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例\n        } else {\n          normalizedHeight = 30 + value * 0.6; // 小值适当放大\n        }\n        var start = api.coord([dataIndex, 0]);\n        var end = api.coord([dataIndex, normalizedHeight]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseRadius = height * 0.4; // 底面半径\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的关键点\n        var topPoint = [x, y]; // 顶点\n\n        // 底面的四个关键点（形成正方形底面）\n        var bottomFront = [x, start[1]]; // 前面中心\n        var bottomBack = [x, start[1] + baseRadius * 0.6]; // 后面中心（营造3D深度）\n        var bottomLeft = [x - baseRadius, start[1] + baseRadius * 0.3]; // 左侧\n        var bottomRight = [x + baseRadius, start[1] + baseRadius * 0.3]; // 右侧\n\n        var group = {\n          type: \"group\",\n          children: [\n          // 前面（主面）\n          {\n            type: \"polygon\",\n            z2: 4,\n            shape: {\n              points: [topPoint, bottomLeft, bottomFront, bottomRight]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: style.fill.colorStops\n              },\n              stroke: 'rgba(255,255,255,0.1)',\n              lineWidth: 0.5\n            }\n          },\n          // 左侧面\n          {\n            type: \"polygon\",\n            z2: 3,\n            shape: {\n              points: [topPoint, bottomBack, bottomLeft]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: style.fill.colorStops[0].color\n                }, {\n                  offset: 0.5,\n                  color: style.fill.colorStops[1].color\n                }, {\n                  offset: 1,\n                  color: style.fill.colorStops[2].color\n                }]\n              },\n              opacity: 0.7,\n              stroke: 'rgba(255,255,255,0.05)',\n              lineWidth: 0.5\n            }\n          },\n          // 右侧面\n          {\n            type: \"polygon\",\n            z2: 2,\n            shape: {\n              points: [topPoint, bottomRight, bottomBack]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: style.fill.colorStops[0].color\n                }, {\n                  offset: 0.5,\n                  color: style.fill.colorStops[1].color\n                }, {\n                  offset: 1,\n                  color: style.fill.colorStops[2].color\n                }]\n              },\n              opacity: 0.5,\n              stroke: 'rgba(255,255,255,0.05)',\n              lineWidth: 0.5\n            }\n          },\n          // 底面（可选，增强立体感）\n          {\n            type: \"polygon\",\n            z2: 1,\n            shape: {\n              points: [bottomLeft, bottomFront, bottomRight, bottomBack]\n            },\n            style: {\n              fill: style.fill.colorStops[2].color,\n              opacity: 0.3,\n              stroke: 'rgba(255,255,255,0.1)',\n              lineWidth: 0.5\n            }\n          }]\n        };\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4)\n          },\n          formatter: function (params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false\n        },\n        xAxis: {\n          data: xData,\n          show: false // 隐藏X轴\n        },\n        yAxis: {\n          show: false,\n          // 隐藏Y轴\n          max: 120 // 调整最大值以适应新的高度计算\n        },\n        series: [{\n          type: \"custom\",\n          itemStyle: {\n            color: function (params) {\n              return colors[params.dataIndex];\n            }\n          },\n          label: {\n            show: true,\n            position: [0, -40],\n            color: \"#ffffff\",\n            fontSize: this.fontSize(0.8),\n            fontWeight: 'bold',\n            formatter: function (params) {\n              return params.data + '%';\n            },\n            align: 'center'\n          },\n          data: data,\n          renderItem: renderItem\n        },\n        // 添加底部标签\n        {\n          type: \"custom\",\n          renderItem: function (params, api) {\n            var x = api.coord([params.dataIndex, 0])[0];\n            var y = api.coord([params.dataIndex, 0])[1] + 40;\n            return {\n              type: 'group',\n              children: [\n              // 白色虚线\n              {\n                type: 'line',\n                shape: {\n                  x1: x,\n                  y1: y - 30,\n                  x2: x,\n                  y2: y - 10\n                },\n                style: {\n                  stroke: '#ffffff',\n                  lineWidth: 2,\n                  lineDash: [5, 5],\n                  opacity: 0.8\n                }\n              },\n              // 标签文字\n              {\n                type: 'text',\n                style: {\n                  text: xData[params.dataIndex],\n                  x: x,\n                  y: y,\n                  textAlign: 'center',\n                  textVerticalAlign: 'top',\n                  fill: '#ffffff',\n                  fontSize: 16,\n                  fontWeight: 'normal'\n                }\n              }]\n            };\n          },\n          data: data\n        }]\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "myChart", "mounted", "$refs", "volumn", "reload<PERSON><PERSON>", "window", "addEventListener", "beforeUnmount", "dispose<PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON>", "$echarts", "init", "xData", "colors", "type", "x", "y", "x2", "y2", "global", "colorStops", "offset", "color", "renderItem", "params", "api", "dataIndex", "value", "normalizedHeight", "start", "coord", "end", "style", "height", "baseRadius", "topPoint", "bottomFront", "bottomBack", "bottomLeft", "bottomRight", "group", "children", "z2", "shape", "points", "fill", "stroke", "lineWidth", "opacity", "option", "title", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "formatter", "grid", "bottom", "right", "containLabel", "xAxis", "show", "yAxis", "max", "series", "itemStyle", "label", "position", "align", "x1", "y1", "lineDash", "textAlign", "textVerticalAlign", "setOption", "res", "clientWidth", "innerWidth", "document", "documentElement", "body", "dispose"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\n        \"个人\",\n        \"单位\",\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#1e4a72\",\n            },\n            {\n              offset: 0.5,\n              color: \"#2d6ba3\",\n            },\n            {\n              offset: 1,\n              color: \"#5fb3f0\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#0f3a5f\",\n            },\n            {\n              offset: 0.5,\n              color: \"#1e5a8a\",\n            },\n            {\n              offset: 1,\n              color: \"#4da6d9\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var dataIndex = params.dataIndex;\n        var value = api.value(1);\n\n        // 调整高度比例，避免差距过大\n        var normalizedHeight;\n        if (value >= 50) {\n          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例\n        } else {\n          normalizedHeight = 30 + value * 0.6; // 小值适当放大\n        }\n\n        var start = api.coord([dataIndex, 0]);\n        var end = api.coord([dataIndex, normalizedHeight]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseRadius = height * 0.4; // 底面半径\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的关键点\n        var topPoint = [x, y]; // 顶点\n\n        // 底面的四个关键点（形成正方形底面）\n        var bottomFront = [x, start[1]]; // 前面中心\n        var bottomBack = [x, start[1] + baseRadius * 0.6]; // 后面中心（营造3D深度）\n        var bottomLeft = [x - baseRadius, start[1] + baseRadius * 0.3]; // 左侧\n        var bottomRight = [x + baseRadius, start[1] + baseRadius * 0.3]; // 右侧\n\n        var group = {\n          type: \"group\",\n          children: [\n            // 前面（主面）\n            {\n              type: \"polygon\",\n              z2: 4,\n              shape: {\n                points: [topPoint, bottomLeft, bottomFront, bottomRight],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: style.fill.colorStops\n                },\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 0.5,\n              },\n            },\n            // 左侧面\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomBack, bottomLeft],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 0.5, color: style.fill.colorStops[1].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.7,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 0.5,\n              },\n            },\n            // 右侧面\n            {\n              type: \"polygon\",\n              z2: 2,\n              shape: {\n                points: [topPoint, bottomRight, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 0.5, color: style.fill.colorStops[1].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.5,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 0.5,\n              },\n            },\n            // 底面（可选，增强立体感）\n            {\n              type: \"polygon\",\n              z2: 1,\n              shape: {\n                points: [bottomLeft, bottomFront, bottomRight, bottomBack],\n              },\n              style: {\n                fill: style.fill.colorStops[2].color,\n                opacity: 0.3,\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 0.5,\n              },\n            },\n          ],\n        };\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n          },\n          formatter: function(params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false,\n        },\n        xAxis: {\n          data: xData,\n          show: false, // 隐藏X轴\n        },\n        yAxis: {\n          show: false, // 隐藏Y轴\n          max: 120, // 调整最大值以适应新的高度计算\n        },\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n            label: {\n              show: true,\n              position: [0, -40],\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.8),\n              fontWeight: 'bold',\n              formatter: function(params) {\n                return params.data + '%';\n              },\n              align: 'center'\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n          // 添加底部标签\n          {\n            type: \"custom\",\n            renderItem: function(params, api) {\n              var x = api.coord([params.dataIndex, 0])[0];\n              var y = api.coord([params.dataIndex, 0])[1] + 40;\n\n              return {\n                type: 'group',\n                children: [\n                  // 白色虚线\n                  {\n                    type: 'line',\n                    shape: {\n                      x1: x,\n                      y1: y - 30,\n                      x2: x,\n                      y2: y - 10\n                    },\n                    style: {\n                      stroke: '#ffffff',\n                      lineWidth: 2,\n                      lineDash: [5, 5],\n                      opacity: 0.8\n                    }\n                  },\n                  // 标签文字\n                  {\n                    type: 'text',\n                    style: {\n                      text: xData[params.dataIndex],\n                      x: x,\n                      y: y,\n                      textAlign: 'center',\n                      textVerticalAlign: 'top',\n                      fill: '#ffffff',\n                      fontSize: 16,\n                      fontWeight: 'normal'\n                    }\n                  }\n                ]\n              };\n            },\n            data: data,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,EAAE;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;MAClB;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC,IAAI,CAACF,WAAW,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACAG,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,OAAO,EAAE;IACPC,SAASA,CAAA,EAAG;MACV,IAAI,CAACV,OAAM,GAAI,IAAI,CAACW,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACV,KAAK,CAACC,MAAM,CAAC;MACpD,IAAIJ,IAAG,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC;MACnB,IAAIc,KAAI,GAAI,CACV,IAAI,EACJ,IAAI,CACL;MACD,IAAIC,MAAK,GAAI,CACX;QACEC,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,EACD;QACER,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,CACF;MAED,IAAIC,UAAS,GAAI,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;QACrC,IAAIC,SAAQ,GAAIF,MAAM,CAACE,SAAS;QAChC,IAAIC,KAAI,GAAIF,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;;QAExB;QACA,IAAIC,gBAAgB;QACpB,IAAID,KAAI,IAAK,EAAE,EAAE;UACfC,gBAAe,GAAI,EAAC,GAAI,CAACD,KAAI,GAAI,EAAE,IAAI,GAAG,EAAE;QAC9C,OAAO;UACLC,gBAAe,GAAI,EAAC,GAAID,KAAI,GAAI,GAAG,EAAE;QACvC;QAEA,IAAIE,KAAI,GAAIJ,GAAG,CAACK,KAAK,CAAC,CAACJ,SAAS,EAAE,CAAC,CAAC,CAAC;QACrC,IAAIK,GAAE,GAAIN,GAAG,CAACK,KAAK,CAAC,CAACJ,SAAS,EAAEE,gBAAgB,CAAC,CAAC;QAClD,IAAII,KAAI,GAAIP,GAAG,CAACO,KAAK,CAAC,CAAC;;QAEvB;QACA,IAAIC,MAAK,GAAIJ,KAAK,CAAC,CAAC,IAAIE,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAIG,UAAS,GAAID,MAAK,GAAI,GAAG,EAAE;QAC/B,IAAIlB,CAAA,GAAIc,KAAK,CAAC,CAAC,CAAC;QAChB,IAAIb,CAAA,GAAIe,GAAG,CAAC,CAAC,CAAC;;QAEd;QACA,IAAII,QAAO,GAAI,CAACpB,CAAC,EAAEC,CAAC,CAAC,EAAE;;QAEvB;QACA,IAAIoB,WAAU,GAAI,CAACrB,CAAC,EAAEc,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,IAAIQ,UAAS,GAAI,CAACtB,CAAC,EAAEc,KAAK,CAAC,CAAC,IAAIK,UAAS,GAAI,GAAG,CAAC,EAAE;QACnD,IAAII,UAAS,GAAI,CAACvB,CAAA,GAAImB,UAAU,EAAEL,KAAK,CAAC,CAAC,IAAIK,UAAS,GAAI,GAAG,CAAC,EAAE;QAChE,IAAIK,WAAU,GAAI,CAACxB,CAAA,GAAImB,UAAU,EAAEL,KAAK,CAAC,CAAC,IAAIK,UAAS,GAAI,GAAG,CAAC,EAAE;;QAEjE,IAAIM,KAAI,GAAI;UACV1B,IAAI,EAAE,OAAO;UACb2B,QAAQ,EAAE;UACR;UACA;YACE3B,IAAI,EAAE,SAAS;YACf4B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACT,QAAQ,EAAEG,UAAU,EAAEF,WAAW,EAAEG,WAAW;YACzD,CAAC;YACDP,KAAK,EAAE;cACLa,IAAI,EAAE;gBACJ/B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAEY,KAAK,CAACa,IAAI,CAACzB;cACzB,CAAC;cACD0B,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACEjC,IAAI,EAAE,SAAS;YACf4B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACT,QAAQ,EAAEE,UAAU,EAAEC,UAAU;YAC3C,CAAC;YACDN,KAAK,EAAE;cACLa,IAAI,EAAE;gBACJ/B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE,CACV;kBAAEC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACpD;kBAAED,MAAM,EAAE,GAAG;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACtD;kBAAED,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM;cAEvD,CAAC;cACD0B,OAAO,EAAE,GAAG;cACZF,MAAM,EAAE,wBAAwB;cAChCC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACEjC,IAAI,EAAE,SAAS;YACf4B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACT,QAAQ,EAAEI,WAAW,EAAEF,UAAU;YAC5C,CAAC;YACDL,KAAK,EAAE;cACLa,IAAI,EAAE;gBACJ/B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE,CACV;kBAAEC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACpD;kBAAED,MAAM,EAAE,GAAG;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACtD;kBAAED,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEU,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM;cAEvD,CAAC;cACD0B,OAAO,EAAE,GAAG;cACZF,MAAM,EAAE,wBAAwB;cAChCC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACEjC,IAAI,EAAE,SAAS;YACf4B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACN,UAAU,EAAEF,WAAW,EAAEG,WAAW,EAAEF,UAAU;YAC3D,CAAC;YACDL,KAAK,EAAE;cACLa,IAAI,EAAEb,KAAK,CAACa,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK;cACpC0B,OAAO,EAAE,GAAG;cACZF,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE;YACb;UACF,CAAC;QAEL,CAAC;QAED,OAAOP,KAAK;MACd,CAAC;;MAED;MACA,IAAIS,MAAK,GAAI;QACXC,KAAK,EAAE;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,IAAI;UACTC,SAAS,EAAE;YACThC,KAAK,EAAE,SAAS;YAChBiC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,mBAAmB;UACpCC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfR,SAAS,EAAE;YACThC,KAAK,EAAE,SAAS;YAChBiC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG;UAC7B,CAAC;UACDQ,SAAS,EAAE,SAAAA,CAASvC,MAAM,EAAE;YAC1B,OAAOA,MAAM,CAAC3B,IAAG,GAAI,IAAG,GAAI2B,MAAM,CAACG,KAAI,GAAI,GAAG;UAChD;QACF,CAAC;QACDqC,IAAI,EAAE;UACJX,GAAG,EAAE,KAAK;UACVD,IAAI,EAAE,KAAK;UACXa,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLtE,IAAI,EAAEc,KAAK;UACXyD,IAAI,EAAE,KAAK,CAAE;QACf,CAAC;QACDC,KAAK,EAAE;UACLD,IAAI,EAAE,KAAK;UAAE;UACbE,GAAG,EAAE,GAAG,CAAE;QACZ,CAAC;QAEDC,MAAM,EAAE,CACN;UACE1D,IAAI,EAAE,QAAQ;UACd2D,SAAS,EAAE;YACTnD,KAAK,EAAE,SAAAA,CAASE,MAAM,EAAE;cACtB,OAAOX,MAAM,CAACW,MAAM,CAACE,SAAS,CAAC;YACjC;UACF,CAAC;UACDgD,KAAK,EAAE;YACLL,IAAI,EAAE,IAAI;YACVM,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAClBrD,KAAK,EAAE,SAAS;YAChBiC,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE,MAAM;YAClBO,SAAS,EAAE,SAAAA,CAASvC,MAAM,EAAE;cAC1B,OAAOA,MAAM,CAAC1B,IAAG,GAAI,GAAG;YAC1B,CAAC;YACD8E,KAAK,EAAE;UACT,CAAC;UACD9E,IAAI,EAAEA,IAAI;UACVyB,UAAU,EAAEA;QACd,CAAC;QACD;QACA;UACET,IAAI,EAAE,QAAQ;UACdS,UAAU,EAAE,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;YAChC,IAAIV,CAAA,GAAIU,GAAG,CAACK,KAAK,CAAC,CAACN,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAIV,CAAA,GAAIS,GAAG,CAACK,KAAK,CAAC,CAACN,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YAEhD,OAAO;cACLZ,IAAI,EAAE,OAAO;cACb2B,QAAQ,EAAE;cACR;cACA;gBACE3B,IAAI,EAAE,MAAM;gBACZ6B,KAAK,EAAE;kBACLkC,EAAE,EAAE9D,CAAC;kBACL+D,EAAE,EAAE9D,CAAA,GAAI,EAAE;kBACVC,EAAE,EAAEF,CAAC;kBACLG,EAAE,EAAEF,CAAA,GAAI;gBACV,CAAC;gBACDgB,KAAK,EAAE;kBACLc,MAAM,EAAE,SAAS;kBACjBC,SAAS,EAAE,CAAC;kBACZgC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBAChB/B,OAAO,EAAE;gBACX;cACF,CAAC;cACD;cACA;gBACElC,IAAI,EAAE,MAAM;gBACZkB,KAAK,EAAE;kBACLmB,IAAI,EAAEvC,KAAK,CAACY,MAAM,CAACE,SAAS,CAAC;kBAC7BX,CAAC,EAAEA,CAAC;kBACJC,CAAC,EAAEA,CAAC;kBACJgE,SAAS,EAAE,QAAQ;kBACnBC,iBAAiB,EAAE,KAAK;kBACxBpC,IAAI,EAAE,SAAS;kBACfU,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd;cACF;YAEJ,CAAC;UACH,CAAC;UACD1D,IAAI,EAAEA;QACR,CAAC;MAEL,CAAC;MACD,IAAI,CAACC,OAAO,CAACmF,SAAS,CAACjC,MAAM,CAAC;IAChC,CAAC;IACD;IACAM,QAAQA,CAAC4B,GAAG,EAAE;MACZ,MAAMC,WAAU,GACdhF,MAAM,CAACiF,UAAS,IAChBC,QAAQ,CAACC,eAAe,CAACH,WAAU,IACnCE,QAAQ,CAACE,IAAI,CAACJ,WAAW;MAC3B,IAAI,CAACA,WAAW,EAAE;MAClB,MAAM7B,QAAO,GAAI,EAAC,IAAK6B,WAAU,GAAI,IAAI,CAAC;MAC1C,OAAOD,GAAE,GAAI5B,QAAQ;IACvB,CAAC;IACD;IACAhD,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACR,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAAC0F,OAAO,CAAC,CAAC;MACxB;IACF,CAAC;IACD;IACAtF,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACI,YAAY,CAAC,CAAC;MACnB,IAAI,CAACE,SAAS,CAAC,CAAC;IAClB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}