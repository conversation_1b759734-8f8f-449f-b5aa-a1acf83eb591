{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, each, isArray, isString } from 'zrender/lib/core/util.js';\nimport { deprecateReplaceLog, deprecateLog } from '../util/log.js';\nimport { queryDataIndex } from '../util/model.js';\n// Legacy data selection action.\n// Includes: pieSelect, pieUnSelect, pieToggleSelect, mapSelect, mapUnSelect, mapToggleSelect\nexport function createLegacyDataSelectAction(seriesType, ecRegisterAction) {\n  function getSeriesIndices(ecModel, payload) {\n    var seriesIndices = [];\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: seriesType,\n      query: payload\n    }, function (seriesModel) {\n      seriesIndices.push(seriesModel.seriesIndex);\n    });\n    return seriesIndices;\n  }\n  each([[seriesType + 'ToggleSelect', 'toggleSelect'], [seriesType + 'Select', 'select'], [seriesType + 'UnSelect', 'unselect']], function (eventsMap) {\n    ecRegisterAction(eventsMap[0], function (payload, ecModel, api) {\n      payload = extend({}, payload);\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(payload.type, eventsMap[1]);\n      }\n      api.dispatchAction(extend(payload, {\n        type: eventsMap[1],\n        seriesIndex: getSeriesIndices(ecModel, payload)\n      }));\n    });\n  });\n}\nfunction handleSeriesLegacySelectEvents(type, eventPostfix, ecIns, ecModel, payload) {\n  var legacyEventName = type + eventPostfix;\n  if (!ecIns.isSilent(legacyEventName)) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateLog(\"event \" + legacyEventName + \" is deprecated.\");\n    }\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'pie'\n    }, function (seriesModel) {\n      var seriesIndex = seriesModel.seriesIndex;\n      var selectedMap = seriesModel.option.selectedMap;\n      var selected = payload.selected;\n      for (var i = 0; i < selected.length; i++) {\n        if (selected[i].seriesIndex === seriesIndex) {\n          var data = seriesModel.getData();\n          var dataIndex = queryDataIndex(data, payload.fromActionPayload);\n          ecIns.trigger(legacyEventName, {\n            type: legacyEventName,\n            seriesId: seriesModel.id,\n            name: isArray(dataIndex) ? data.getName(dataIndex[0]) : data.getName(dataIndex),\n            selected: isString(selectedMap) ? selectedMap : extend({}, selectedMap)\n          });\n        }\n      }\n    });\n  }\n}\nexport function handleLegacySelectEvents(messageCenter, ecIns, api) {\n  messageCenter.on('selectchanged', function (params) {\n    var ecModel = api.getModel();\n    if (params.isFromClick) {\n      handleSeriesLegacySelectEvents('map', 'selectchanged', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selectchanged', ecIns, ecModel, params);\n    } else if (params.fromAction === 'select') {\n      handleSeriesLegacySelectEvents('map', 'selected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selected', ecIns, ecModel, params);\n    } else if (params.fromAction === 'unselect') {\n      handleSeriesLegacySelectEvents('map', 'unselected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'unselected', ecIns, ecModel, params);\n    }\n  });\n}", "map": {"version": 3, "names": ["extend", "each", "isArray", "isString", "deprecateReplaceLog", "deprecateLog", "queryDataIndex", "createLegacyDataSelectAction", "seriesType", "ecRegisterAction", "getSeriesIndices", "ecModel", "payload", "seriesIndices", "eachComponent", "mainType", "subType", "query", "seriesModel", "push", "seriesIndex", "eventsMap", "api", "process", "env", "NODE_ENV", "type", "dispatchAction", "handleSeriesLegacySelectEvents", "eventPostfix", "ecIns", "legacyEventName", "isSilent", "selectedMap", "option", "selected", "i", "length", "data", "getData", "dataIndex", "fromActionPayload", "trigger", "seriesId", "id", "name", "getName", "handleLegacySelectEvents", "messageCenter", "on", "params", "getModel", "isFromClick", "fromAction"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/legacy/dataSelectAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, each, isArray, isString } from 'zrender/lib/core/util.js';\nimport { deprecateReplaceLog, deprecateLog } from '../util/log.js';\nimport { queryDataIndex } from '../util/model.js';\n// Legacy data selection action.\n// Includes: pieSelect, pieUnSelect, pieToggleSelect, mapSelect, mapUnSelect, mapToggleSelect\nexport function createLegacyDataSelectAction(seriesType, ecRegisterAction) {\n  function getSeriesIndices(ecModel, payload) {\n    var seriesIndices = [];\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: seriesType,\n      query: payload\n    }, function (seriesModel) {\n      seriesIndices.push(seriesModel.seriesIndex);\n    });\n    return seriesIndices;\n  }\n  each([[seriesType + 'ToggleSelect', 'toggleSelect'], [seriesType + 'Select', 'select'], [seriesType + 'UnSelect', 'unselect']], function (eventsMap) {\n    ecRegisterAction(eventsMap[0], function (payload, ecModel, api) {\n      payload = extend({}, payload);\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(payload.type, eventsMap[1]);\n      }\n      api.dispatchAction(extend(payload, {\n        type: eventsMap[1],\n        seriesIndex: getSeriesIndices(ecModel, payload)\n      }));\n    });\n  });\n}\nfunction handleSeriesLegacySelectEvents(type, eventPostfix, ecIns, ecModel, payload) {\n  var legacyEventName = type + eventPostfix;\n  if (!ecIns.isSilent(legacyEventName)) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateLog(\"event \" + legacyEventName + \" is deprecated.\");\n    }\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'pie'\n    }, function (seriesModel) {\n      var seriesIndex = seriesModel.seriesIndex;\n      var selectedMap = seriesModel.option.selectedMap;\n      var selected = payload.selected;\n      for (var i = 0; i < selected.length; i++) {\n        if (selected[i].seriesIndex === seriesIndex) {\n          var data = seriesModel.getData();\n          var dataIndex = queryDataIndex(data, payload.fromActionPayload);\n          ecIns.trigger(legacyEventName, {\n            type: legacyEventName,\n            seriesId: seriesModel.id,\n            name: isArray(dataIndex) ? data.getName(dataIndex[0]) : data.getName(dataIndex),\n            selected: isString(selectedMap) ? selectedMap : extend({}, selectedMap)\n          });\n        }\n      }\n    });\n  }\n}\nexport function handleLegacySelectEvents(messageCenter, ecIns, api) {\n  messageCenter.on('selectchanged', function (params) {\n    var ecModel = api.getModel();\n    if (params.isFromClick) {\n      handleSeriesLegacySelectEvents('map', 'selectchanged', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selectchanged', ecIns, ecModel, params);\n    } else if (params.fromAction === 'select') {\n      handleSeriesLegacySelectEvents('map', 'selected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'selected', ecIns, ecModel, params);\n    } else if (params.fromAction === 'unselect') {\n      handleSeriesLegacySelectEvents('map', 'unselected', ecIns, ecModel, params);\n      handleSeriesLegacySelectEvents('pie', 'unselected', ecIns, ecModel, params);\n    }\n  });\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AAC1E,SAASC,mBAAmB,EAAEC,YAAY,QAAQ,gBAAgB;AAClE,SAASC,cAAc,QAAQ,kBAAkB;AACjD;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,UAAU,EAAEC,gBAAgB,EAAE;EACzE,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1C,IAAIC,aAAa,GAAG,EAAE;IACtBF,OAAO,CAACG,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAER,UAAU;MACnBS,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,WAAW,EAAE;MACxBL,aAAa,CAACM,IAAI,CAACD,WAAW,CAACE,WAAW,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOP,aAAa;EACtB;EACAZ,IAAI,CAAC,CAAC,CAACO,UAAU,GAAG,cAAc,EAAE,cAAc,CAAC,EAAE,CAACA,UAAU,GAAG,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAACA,UAAU,GAAG,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,UAAUa,SAAS,EAAE;IACnJZ,gBAAgB,CAACY,SAAS,CAAC,CAAC,CAAC,EAAE,UAAUT,OAAO,EAAED,OAAO,EAAEW,GAAG,EAAE;MAC9DV,OAAO,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEY,OAAO,CAAC;MAC7B,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCrB,mBAAmB,CAACQ,OAAO,CAACc,IAAI,EAAEL,SAAS,CAAC,CAAC,CAAC,CAAC;MACjD;MACAC,GAAG,CAACK,cAAc,CAAC3B,MAAM,CAACY,OAAO,EAAE;QACjCc,IAAI,EAAEL,SAAS,CAAC,CAAC,CAAC;QAClBD,WAAW,EAAEV,gBAAgB,CAACC,OAAO,EAAEC,OAAO;MAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASgB,8BAA8BA,CAACF,IAAI,EAAEG,YAAY,EAAEC,KAAK,EAAEnB,OAAO,EAAEC,OAAO,EAAE;EACnF,IAAImB,eAAe,GAAGL,IAAI,GAAGG,YAAY;EACzC,IAAI,CAACC,KAAK,CAACE,QAAQ,CAACD,eAAe,CAAC,EAAE;IACpC,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCpB,YAAY,CAAC,QAAQ,GAAG0B,eAAe,GAAG,iBAAiB,CAAC;IAC9D;IACApB,OAAO,CAACG,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;IACX,CAAC,EAAE,UAAUE,WAAW,EAAE;MACxB,IAAIE,WAAW,GAAGF,WAAW,CAACE,WAAW;MACzC,IAAIa,WAAW,GAAGf,WAAW,CAACgB,MAAM,CAACD,WAAW;MAChD,IAAIE,QAAQ,GAAGvB,OAAO,CAACuB,QAAQ;MAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QACxC,IAAID,QAAQ,CAACC,CAAC,CAAC,CAAChB,WAAW,KAAKA,WAAW,EAAE;UAC3C,IAAIkB,IAAI,GAAGpB,WAAW,CAACqB,OAAO,CAAC,CAAC;UAChC,IAAIC,SAAS,GAAGlC,cAAc,CAACgC,IAAI,EAAE1B,OAAO,CAAC6B,iBAAiB,CAAC;UAC/DX,KAAK,CAACY,OAAO,CAACX,eAAe,EAAE;YAC7BL,IAAI,EAAEK,eAAe;YACrBY,QAAQ,EAAEzB,WAAW,CAAC0B,EAAE;YACxBC,IAAI,EAAE3C,OAAO,CAACsC,SAAS,CAAC,GAAGF,IAAI,CAACQ,OAAO,CAACN,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAACQ,OAAO,CAACN,SAAS,CAAC;YAC/EL,QAAQ,EAAEhC,QAAQ,CAAC8B,WAAW,CAAC,GAAGA,WAAW,GAAGjC,MAAM,CAAC,CAAC,CAAC,EAAEiC,WAAW;UACxE,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ;AACF;AACA,OAAO,SAASc,wBAAwBA,CAACC,aAAa,EAAElB,KAAK,EAAER,GAAG,EAAE;EAClE0B,aAAa,CAACC,EAAE,CAAC,eAAe,EAAE,UAAUC,MAAM,EAAE;IAClD,IAAIvC,OAAO,GAAGW,GAAG,CAAC6B,QAAQ,CAAC,CAAC;IAC5B,IAAID,MAAM,CAACE,WAAW,EAAE;MACtBxB,8BAA8B,CAAC,KAAK,EAAE,eAAe,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;MAC9EtB,8BAA8B,CAAC,KAAK,EAAE,eAAe,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;IAChF,CAAC,MAAM,IAAIA,MAAM,CAACG,UAAU,KAAK,QAAQ,EAAE;MACzCzB,8BAA8B,CAAC,KAAK,EAAE,UAAU,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;MACzEtB,8BAA8B,CAAC,KAAK,EAAE,UAAU,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;IAC3E,CAAC,MAAM,IAAIA,MAAM,CAACG,UAAU,KAAK,UAAU,EAAE;MAC3CzB,8BAA8B,CAAC,KAAK,EAAE,YAAY,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;MAC3EtB,8BAA8B,CAAC,KAAK,EAAE,YAAY,EAAEE,KAAK,EAAEnB,OAAO,EAAEuC,MAAM,CAAC;IAC7E;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}