{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport Model from '../../model/Model.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar SunburstSeriesModel = /** @class */function (_super) {\n  __extends(SunburstSeriesModel, _super);\n  function SunburstSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SunburstSeriesModel.type;\n    _this.ignoreStyleOnData = true;\n    return _this;\n  }\n  SunburstSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levelModels = this._levelModels = zrUtil.map(option.levels || [], function (levelDefine) {\n      return new Model(levelDefine, this, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = levelModels[node.depth];\n        levelModel && (model.parentModel = levelModel);\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  SunburstSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /*\r\n   * @override\r\n   */\n  SunburstSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treePathInfo = wrapTreePathInfo(node, this);\n    return params;\n  };\n  SunburstSeriesModel.prototype.getLevelModel = function (node) {\n    return this._levelModels && this._levelModels[node.depth];\n  };\n  SunburstSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  SunburstSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  SunburstSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  SunburstSeriesModel.type = 'series.sunburst';\n  SunburstSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    // 最小角度改为0\n    minAngle: 0,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // 'rootToNode', 'link', or false\n    nodeClick: 'rootToNode',\n    renderLabelForZeroData: false,\n    label: {\n      // could be: 'radial', 'tangential', or 'none'\n      rotate: 'radial',\n      show: true,\n      opacity: 1,\n      // 'left' is for inner side of inside, and 'right' is for outer\n      // side for inside\n      align: 'center',\n      position: 'inside',\n      distance: 5,\n      silent: true\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderColor: 'white',\n      borderType: 'solid',\n      shadowBlur: 0,\n      shadowColor: 'rgba(0, 0, 0, 0.2)',\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    emphasis: {\n      focus: 'descendant'\n    },\n    blur: {\n      itemStyle: {\n        opacity: 0.2\n      },\n      label: {\n        opacity: 0.1\n      }\n    },\n    // Animation type can be expansion, scale.\n    animationType: 'expansion',\n    animationDuration: 1000,\n    animationDurationUpdate: 500,\n    data: [],\n    /**\r\n     * Sort order.\r\n     *\r\n     * Valid values: 'desc', 'asc', null, or callback function.\r\n     * 'desc' and 'asc' for descend and ascendant order;\r\n     * null for not sorting;\r\n     * example of callback function:\r\n     * function(nodeA, nodeB) {\r\n     *     return nodeA.getValue() - nodeB.getValue();\r\n     * }\r\n     */\n    sort: 'desc'\n  };\n  return SunburstSeriesModel;\n}(SeriesModel);\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    // TODO First value of array must be a number\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\nexport default SunburstSeriesModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "SeriesModel", "Tree", "wrapTreePathInfo", "Model", "enableAriaDecalForTree", "SunburstSeriesModel", "_super", "_this", "apply", "arguments", "type", "ignoreStyleOnData", "prototype", "getInitialData", "option", "ecModel", "root", "name", "children", "data", "completeTreeValue", "levelModels", "_levelModels", "map", "levels", "levelDefine", "tree", "createTree", "beforeLink", "nodeData", "wrapMethod", "model", "idx", "node", "getNodeByDataIndex", "levelModel", "depth", "parentModel", "optionUpdated", "resetViewRoot", "getDataParams", "dataIndex", "params", "getData", "treePathInfo", "getLevelModel", "getViewRoot", "_viewRoot", "viewRoot", "getRawData", "contains", "enableAriaDecal", "defaultOption", "z", "center", "radius", "clockwise", "startAngle", "minAngle", "stillShowZeroSum", "nodeClick", "renderLabelForZeroData", "label", "rotate", "show", "opacity", "align", "position", "distance", "silent", "itemStyle", "borderWidth", "borderColor", "borderType", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "emphasis", "focus", "blur", "animationType", "animationDuration", "animationDurationUpdate", "sort", "dataNode", "sum", "each", "child", "childValue", "value", "isArray", "thisValue", "isNaN"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/sunburst/SunburstSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport Model from '../../model/Model.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar SunburstSeriesModel = /** @class */function (_super) {\n  __extends(SunburstSeriesModel, _super);\n  function SunburstSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SunburstSeriesModel.type;\n    _this.ignoreStyleOnData = true;\n    return _this;\n  }\n  SunburstSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levelModels = this._levelModels = zrUtil.map(option.levels || [], function (levelDefine) {\n      return new Model(levelDefine, this, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = levelModels[node.depth];\n        levelModel && (model.parentModel = levelModel);\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  SunburstSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /*\r\n   * @override\r\n   */\n  SunburstSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treePathInfo = wrapTreePathInfo(node, this);\n    return params;\n  };\n  SunburstSeriesModel.prototype.getLevelModel = function (node) {\n    return this._levelModels && this._levelModels[node.depth];\n  };\n  SunburstSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  SunburstSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  SunburstSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  SunburstSeriesModel.type = 'series.sunburst';\n  SunburstSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    // 默认全局居中\n    center: ['50%', '50%'],\n    radius: [0, '75%'],\n    // 默认顺时针\n    clockwise: true,\n    startAngle: 90,\n    // 最小角度改为0\n    minAngle: 0,\n    // If still show when all data zero.\n    stillShowZeroSum: true,\n    // 'rootToNode', 'link', or false\n    nodeClick: 'rootToNode',\n    renderLabelForZeroData: false,\n    label: {\n      // could be: 'radial', 'tangential', or 'none'\n      rotate: 'radial',\n      show: true,\n      opacity: 1,\n      // 'left' is for inner side of inside, and 'right' is for outer\n      // side for inside\n      align: 'center',\n      position: 'inside',\n      distance: 5,\n      silent: true\n    },\n    itemStyle: {\n      borderWidth: 1,\n      borderColor: 'white',\n      borderType: 'solid',\n      shadowBlur: 0,\n      shadowColor: 'rgba(0, 0, 0, 0.2)',\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    emphasis: {\n      focus: 'descendant'\n    },\n    blur: {\n      itemStyle: {\n        opacity: 0.2\n      },\n      label: {\n        opacity: 0.1\n      }\n    },\n    // Animation type can be expansion, scale.\n    animationType: 'expansion',\n    animationDuration: 1000,\n    animationDurationUpdate: 500,\n    data: [],\n    /**\r\n     * Sort order.\r\n     *\r\n     * Valid values: 'desc', 'asc', null, or callback function.\r\n     * 'desc' and 'asc' for descend and ascendant order;\r\n     * null for not sorting;\r\n     * example of callback function:\r\n     * function(nodeA, nodeB) {\r\n     *     return nodeA.getValue() - nodeB.getValue();\r\n     * }\r\n     */\n    sort: 'desc'\n  };\n  return SunburstSeriesModel;\n}(SeriesModel);\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    // TODO First value of array must be a number\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\nexport default SunburstSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,IAAIC,mBAAmB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACvDR,SAAS,CAACO,mBAAmB,EAAEC,MAAM,CAAC;EACtC,SAASD,mBAAmBA,CAAA,EAAG;IAC7B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,mBAAmB,CAACK,IAAI;IACrCH,KAAK,CAACI,iBAAiB,GAAG,IAAI;IAC9B,OAAOJ,KAAK;EACd;EACAF,mBAAmB,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACxE;IACA,IAAIC,IAAI,GAAG;MACTC,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBC,QAAQ,EAAEJ,MAAM,CAACK;IACnB,CAAC;IACDC,iBAAiB,CAACJ,IAAI,CAAC;IACvB,IAAIK,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGvB,MAAM,CAACwB,GAAG,CAACT,MAAM,CAACU,MAAM,IAAI,EAAE,EAAE,UAAUC,WAAW,EAAE;MAC3F,OAAO,IAAItB,KAAK,CAACsB,WAAW,EAAE,IAAI,EAAEV,OAAO,CAAC;IAC9C,CAAC,EAAE,IAAI,CAAC;IACR;IACA;IACA;IACA,IAAIW,IAAI,GAAGzB,IAAI,CAAC0B,UAAU,CAACX,IAAI,EAAE,IAAI,EAAEY,UAAU,CAAC;IAClD,SAASA,UAAUA,CAACC,QAAQ,EAAE;MAC5BA,QAAQ,CAACC,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;QACxD,IAAIC,IAAI,GAAGP,IAAI,CAACQ,kBAAkB,CAACF,GAAG,CAAC;QACvC,IAAIG,UAAU,GAAGd,WAAW,CAACY,IAAI,CAACG,KAAK,CAAC;QACxCD,UAAU,KAAKJ,KAAK,CAACM,WAAW,GAAGF,UAAU,CAAC;QAC9C,OAAOJ,KAAK;MACd,CAAC,CAAC;IACJ;IACA,OAAOL,IAAI,CAACP,IAAI;EAClB,CAAC;EACDd,mBAAmB,CAACO,SAAS,CAAC0B,aAAa,GAAG,YAAY;IACxD,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC;EACD;AACF;AACA;EACElC,mBAAmB,CAACO,SAAS,CAAC4B,aAAa,GAAG,UAAUC,SAAS,EAAE;IACjE,IAAIC,MAAM,GAAGpC,MAAM,CAACM,SAAS,CAAC4B,aAAa,CAAChC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClE,IAAIwB,IAAI,GAAG,IAAI,CAACU,OAAO,CAAC,CAAC,CAACjB,IAAI,CAACQ,kBAAkB,CAACO,SAAS,CAAC;IAC5DC,MAAM,CAACE,YAAY,GAAG1C,gBAAgB,CAAC+B,IAAI,EAAE,IAAI,CAAC;IAClD,OAAOS,MAAM;EACf,CAAC;EACDrC,mBAAmB,CAACO,SAAS,CAACiC,aAAa,GAAG,UAAUZ,IAAI,EAAE;IAC5D,OAAO,IAAI,CAACX,YAAY,IAAI,IAAI,CAACA,YAAY,CAACW,IAAI,CAACG,KAAK,CAAC;EAC3D,CAAC;EACD/B,mBAAmB,CAACO,SAAS,CAACkC,WAAW,GAAG,YAAY;IACtD,OAAO,IAAI,CAACC,SAAS;EACvB,CAAC;EACD1C,mBAAmB,CAACO,SAAS,CAAC2B,aAAa,GAAG,UAAUS,QAAQ,EAAE;IAChEA,QAAQ,GAAG,IAAI,CAACD,SAAS,GAAGC,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAACD,SAAS;IAChE,IAAI/B,IAAI,GAAG,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACvB,IAAI,CAACV,IAAI;IACtC,IAAI,CAACgC,QAAQ,IAAIA,QAAQ,KAAKhC,IAAI,IAAI,CAACA,IAAI,CAACkC,QAAQ,CAACF,QAAQ,CAAC,EAAE;MAC9D,IAAI,CAACD,SAAS,GAAG/B,IAAI;IACvB;EACF,CAAC;EACDX,mBAAmB,CAACO,SAAS,CAACuC,eAAe,GAAG,YAAY;IAC1D/C,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACDC,mBAAmB,CAACK,IAAI,GAAG,iBAAiB;EAC5CL,mBAAmB,CAAC+C,aAAa,GAAG;IAClC;IACAC,CAAC,EAAE,CAAC;IACJ;IACAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACtBC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAClB;IACAC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,EAAE;IACd;IACAC,QAAQ,EAAE,CAAC;IACX;IACAC,gBAAgB,EAAE,IAAI;IACtB;IACAC,SAAS,EAAE,YAAY;IACvBC,sBAAsB,EAAE,KAAK;IAC7BC,KAAK,EAAE;MACL;MACAC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,CAAC;MACV;MACA;MACAC,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC;IACDC,SAAS,EAAE;MACTC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,OAAO;MACpBC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,oBAAoB;MACjCC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBZ,OAAO,EAAE;IACX,CAAC;IACDa,QAAQ,EAAE;MACRC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJV,SAAS,EAAE;QACTL,OAAO,EAAE;MACX,CAAC;MACDH,KAAK,EAAE;QACLG,OAAO,EAAE;MACX;IACF,CAAC;IACD;IACAgB,aAAa,EAAE,WAAW;IAC1BC,iBAAiB,EAAE,IAAI;IACvBC,uBAAuB,EAAE,GAAG;IAC5BhE,IAAI,EAAE,EAAE;IACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIiE,IAAI,EAAE;EACR,CAAC;EACD,OAAO/E,mBAAmB;AAC5B,CAAC,CAACL,WAAW,CAAC;AACd,SAASoB,iBAAiBA,CAACiE,QAAQ,EAAE;EACnC;EACA;EACA;EACA,IAAIC,GAAG,GAAG,CAAC;EACXvF,MAAM,CAACwF,IAAI,CAACF,QAAQ,CAACnE,QAAQ,EAAE,UAAUsE,KAAK,EAAE;IAC9CpE,iBAAiB,CAACoE,KAAK,CAAC;IACxB,IAAIC,UAAU,GAAGD,KAAK,CAACE,KAAK;IAC5B;IACA3F,MAAM,CAAC4F,OAAO,CAACF,UAAU,CAAC,KAAKA,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1DH,GAAG,IAAIG,UAAU;EACnB,CAAC,CAAC;EACF,IAAIG,SAAS,GAAGP,QAAQ,CAACK,KAAK;EAC9B,IAAI3F,MAAM,CAAC4F,OAAO,CAACC,SAAS,CAAC,EAAE;IAC7BA,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIC,KAAK,CAACD,SAAS,CAAC,EAAE;IACzCA,SAAS,GAAGN,GAAG;EACjB;EACA;EACA,IAAIM,SAAS,GAAG,CAAC,EAAE;IACjBA,SAAS,GAAG,CAAC;EACf;EACA7F,MAAM,CAAC4F,OAAO,CAACN,QAAQ,CAACK,KAAK,CAAC,GAAGL,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,GAAGE,SAAS,GAAGP,QAAQ,CAACK,KAAK,GAAGE,SAAS;AAC7F;AACA,eAAevF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}