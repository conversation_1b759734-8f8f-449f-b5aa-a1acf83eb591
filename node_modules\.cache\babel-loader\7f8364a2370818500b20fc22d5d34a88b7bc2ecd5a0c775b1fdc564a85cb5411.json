{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport function retrieveTargetInfo(payload, validPayloadTypes, seriesModel) {\n  if (payload && zrUtil.indexOf(validPayloadTypes, payload.type) >= 0) {\n    var root = seriesModel.getData().tree.root;\n    var targetNode = payload.targetNode;\n    if (zrUtil.isString(targetNode)) {\n      targetNode = root.getNodeById(targetNode);\n    }\n    if (targetNode && root.contains(targetNode)) {\n      return {\n        node: targetNode\n      };\n    }\n    var targetNodeId = payload.targetNodeId;\n    if (targetNodeId != null && (targetNode = root.getNodeById(targetNodeId))) {\n      return {\n        node: targetNode\n      };\n    }\n  }\n}\n// Not includes the given node at the last item.\nexport function getPathToRoot(node) {\n  var path = [];\n  while (node) {\n    node = node.parentNode;\n    node && path.push(node);\n  }\n  return path.reverse();\n}\nexport function aboveViewRoot(viewRoot, node) {\n  var viewPath = getPathToRoot(viewRoot);\n  return zrUtil.indexOf(viewPath, node) >= 0;\n}\n// From root to the input node (the input node will be included).\nexport function wrapTreePathInfo(node, seriesModel) {\n  var treePathInfo = [];\n  while (node) {\n    var nodeDataIndex = node.dataIndex;\n    treePathInfo.push({\n      name: node.name,\n      dataIndex: nodeDataIndex,\n      value: seriesModel.getRawValue(nodeDataIndex)\n    });\n    node = node.parentNode;\n  }\n  treePathInfo.reverse();\n  return treePathInfo;\n}", "map": {"version": 3, "names": ["zrUtil", "retrieveTargetInfo", "payload", "validPayloadTypes", "seriesModel", "indexOf", "type", "root", "getData", "tree", "targetNode", "isString", "getNodeById", "contains", "node", "targetNodeId", "getPathToRoot", "path", "parentNode", "push", "reverse", "aboveViewRoot", "viewRoot", "viewPath", "wrapTreePathInfo", "treePathInfo", "nodeDataIndex", "dataIndex", "name", "value", "getRawValue"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/helper/treeHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport function retrieveTargetInfo(payload, validPayloadTypes, seriesModel) {\n  if (payload && zrUtil.indexOf(validPayloadTypes, payload.type) >= 0) {\n    var root = seriesModel.getData().tree.root;\n    var targetNode = payload.targetNode;\n    if (zrUtil.isString(targetNode)) {\n      targetNode = root.getNodeById(targetNode);\n    }\n    if (targetNode && root.contains(targetNode)) {\n      return {\n        node: targetNode\n      };\n    }\n    var targetNodeId = payload.targetNodeId;\n    if (targetNodeId != null && (targetNode = root.getNodeById(targetNodeId))) {\n      return {\n        node: targetNode\n      };\n    }\n  }\n}\n// Not includes the given node at the last item.\nexport function getPathToRoot(node) {\n  var path = [];\n  while (node) {\n    node = node.parentNode;\n    node && path.push(node);\n  }\n  return path.reverse();\n}\nexport function aboveViewRoot(viewRoot, node) {\n  var viewPath = getPathToRoot(viewRoot);\n  return zrUtil.indexOf(viewPath, node) >= 0;\n}\n// From root to the input node (the input node will be included).\nexport function wrapTreePathInfo(node, seriesModel) {\n  var treePathInfo = [];\n  while (node) {\n    var nodeDataIndex = node.dataIndex;\n    treePathInfo.push({\n      name: node.name,\n      dataIndex: nodeDataIndex,\n      value: seriesModel.getRawValue(nodeDataIndex)\n    });\n    node = node.parentNode;\n  }\n  treePathInfo.reverse();\n  return treePathInfo;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,iBAAiB,EAAEC,WAAW,EAAE;EAC1E,IAAIF,OAAO,IAAIF,MAAM,CAACK,OAAO,CAACF,iBAAiB,EAAED,OAAO,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE;IACnE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC,CAACC,IAAI,CAACF,IAAI;IAC1C,IAAIG,UAAU,GAAGR,OAAO,CAACQ,UAAU;IACnC,IAAIV,MAAM,CAACW,QAAQ,CAACD,UAAU,CAAC,EAAE;MAC/BA,UAAU,GAAGH,IAAI,CAACK,WAAW,CAACF,UAAU,CAAC;IAC3C;IACA,IAAIA,UAAU,IAAIH,IAAI,CAACM,QAAQ,CAACH,UAAU,CAAC,EAAE;MAC3C,OAAO;QACLI,IAAI,EAAEJ;MACR,CAAC;IACH;IACA,IAAIK,YAAY,GAAGb,OAAO,CAACa,YAAY;IACvC,IAAIA,YAAY,IAAI,IAAI,KAAKL,UAAU,GAAGH,IAAI,CAACK,WAAW,CAACG,YAAY,CAAC,CAAC,EAAE;MACzE,OAAO;QACLD,IAAI,EAAEJ;MACR,CAAC;IACH;EACF;AACF;AACA;AACA,OAAO,SAASM,aAAaA,CAACF,IAAI,EAAE;EAClC,IAAIG,IAAI,GAAG,EAAE;EACb,OAAOH,IAAI,EAAE;IACXA,IAAI,GAAGA,IAAI,CAACI,UAAU;IACtBJ,IAAI,IAAIG,IAAI,CAACE,IAAI,CAACL,IAAI,CAAC;EACzB;EACA,OAAOG,IAAI,CAACG,OAAO,CAAC,CAAC;AACvB;AACA,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAER,IAAI,EAAE;EAC5C,IAAIS,QAAQ,GAAGP,aAAa,CAACM,QAAQ,CAAC;EACtC,OAAOtB,MAAM,CAACK,OAAO,CAACkB,QAAQ,EAAET,IAAI,CAAC,IAAI,CAAC;AAC5C;AACA;AACA,OAAO,SAASU,gBAAgBA,CAACV,IAAI,EAAEV,WAAW,EAAE;EAClD,IAAIqB,YAAY,GAAG,EAAE;EACrB,OAAOX,IAAI,EAAE;IACX,IAAIY,aAAa,GAAGZ,IAAI,CAACa,SAAS;IAClCF,YAAY,CAACN,IAAI,CAAC;MAChBS,IAAI,EAAEd,IAAI,CAACc,IAAI;MACfD,SAAS,EAAED,aAAa;MACxBG,KAAK,EAAEzB,WAAW,CAAC0B,WAAW,CAACJ,aAAa;IAC9C,CAAC,CAAC;IACFZ,IAAI,GAAGA,IAAI,CAACI,UAAU;EACxB;EACAO,YAAY,CAACL,OAAO,CAAC,CAAC;EACtB,OAAOK,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}