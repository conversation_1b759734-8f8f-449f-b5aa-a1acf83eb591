{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nexport function buildElStyle(axisPointerModel) {\n  var axisPointerType = axisPointerModel.get('type');\n  var styleModel = axisPointerModel.getModel(axisPointerType + 'Style');\n  var style;\n  if (axisPointerType === 'line') {\n    style = styleModel.getLineStyle();\n    style.fill = null;\n  } else if (axisPointerType === 'shadow') {\n    style = styleModel.getAreaStyle();\n    style.stroke = null;\n  }\n  return style;\n}\n/**\r\n * @param {Function} labelPos {align, verticalAlign, position}\r\n */\nexport function buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos) {\n  var value = axisPointerModel.get('value');\n  var text = getValueLabel(value, axisModel.axis, axisModel.ecModel, axisPointerModel.get('seriesDataIndices'), {\n    precision: axisPointerModel.get(['label', 'precision']),\n    formatter: axisPointerModel.get(['label', 'formatter'])\n  });\n  var labelModel = axisPointerModel.getModel('label');\n  var paddings = formatUtil.normalizeCssArray(labelModel.get('padding') || 0);\n  var font = labelModel.getFont();\n  var textRect = textContain.getBoundingRect(text, font);\n  var position = labelPos.position;\n  var width = textRect.width + paddings[1] + paddings[3];\n  var height = textRect.height + paddings[0] + paddings[2];\n  // Adjust by align.\n  var align = labelPos.align;\n  align === 'right' && (position[0] -= width);\n  align === 'center' && (position[0] -= width / 2);\n  var verticalAlign = labelPos.verticalAlign;\n  verticalAlign === 'bottom' && (position[1] -= height);\n  verticalAlign === 'middle' && (position[1] -= height / 2);\n  // Not overflow ec container\n  confineInContainer(position, width, height, api);\n  var bgColor = labelModel.get('backgroundColor');\n  if (!bgColor || bgColor === 'auto') {\n    bgColor = axisModel.get(['axisLine', 'lineStyle', 'color']);\n  }\n  elOption.label = {\n    // shape: {x: 0, y: 0, width: width, height: height, r: labelModel.get('borderRadius')},\n    x: position[0],\n    y: position[1],\n    style: createTextStyle(labelModel, {\n      text: text,\n      font: font,\n      fill: labelModel.getTextColor(),\n      padding: paddings,\n      backgroundColor: bgColor\n    }),\n    // Label should be over axisPointer.\n    z2: 10\n  };\n}\n// Do not overflow ec container\nfunction confineInContainer(position, width, height, api) {\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  position[0] = Math.min(position[0] + width, viewWidth) - width;\n  position[1] = Math.min(position[1] + height, viewHeight) - height;\n  position[0] = Math.max(position[0], 0);\n  position[1] = Math.max(position[1], 0);\n}\nexport function getValueLabel(value, axis, ecModel, seriesDataIndices, opt) {\n  value = axis.scale.parse(value);\n  var text = axis.scale.getLabel({\n    value: value\n  }, {\n    // If `precision` is set, width can be fixed (like '12.00500'), which\n    // helps to debounce when when moving label.\n    precision: opt.precision\n  });\n  var formatter = opt.formatter;\n  if (formatter) {\n    var params_1 = {\n      value: axisHelper.getAxisRawValue(axis, {\n        value: value\n      }),\n      axisDimension: axis.dim,\n      axisIndex: axis.index,\n      seriesData: []\n    };\n    zrUtil.each(seriesDataIndices, function (idxItem) {\n      var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n      var dataIndex = idxItem.dataIndexInside;\n      var dataParams = series && series.getDataParams(dataIndex);\n      dataParams && params_1.seriesData.push(dataParams);\n    });\n    if (zrUtil.isString(formatter)) {\n      text = formatter.replace('{value}', text);\n    } else if (zrUtil.isFunction(formatter)) {\n      text = formatter(params_1);\n    }\n  }\n  return text;\n}\nexport function getTransformedPosition(axis, value, layoutInfo) {\n  var transform = matrix.create();\n  matrix.rotate(transform, transform, layoutInfo.rotation);\n  matrix.translate(transform, transform, layoutInfo.position);\n  return graphic.applyTransform([axis.dataToCoord(value), (layoutInfo.labelOffset || 0) + (layoutInfo.labelDirection || 1) * (layoutInfo.labelMargin || 0)], transform);\n}\nexport function buildCartesianSingleLabelElOption(value, elOption, layoutInfo, axisModel, axisPointerModel, api) {\n  // @ts-ignore\n  var textLayout = AxisBuilder.innerTextLayout(layoutInfo.rotation, 0, layoutInfo.labelDirection);\n  layoutInfo.labelMargin = axisPointerModel.get(['label', 'margin']);\n  buildLabelElOption(elOption, axisModel, axisPointerModel, api, {\n    position: getTransformedPosition(axisModel.axis, value, layoutInfo),\n    align: textLayout.textAlign,\n    verticalAlign: textLayout.textVerticalAlign\n  });\n}\nexport function makeLineShape(p1, p2, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x1: p1[xDimIndex],\n    y1: p1[1 - xDimIndex],\n    x2: p2[xDimIndex],\n    y2: p2[1 - xDimIndex]\n  };\n}\nexport function makeRectShape(xy, wh, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x: xy[xDimIndex],\n    y: xy[1 - xDimIndex],\n    width: wh[xDimIndex],\n    height: wh[1 - xDimIndex]\n  };\n}\nexport function makeSectorShape(cx, cy, r0, r, startAngle, endAngle) {\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r,\n    startAngle: startAngle,\n    endAngle: endAngle,\n    clockwise: true\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "graphic", "textContain", "formatUtil", "matrix", "axisHelper", "AxisBuilder", "createTextStyle", "buildElStyle", "axisPointerModel", "axisPointerType", "get", "styleModel", "getModel", "style", "getLineStyle", "fill", "getAreaStyle", "stroke", "buildLabelElOption", "elOption", "axisModel", "api", "labelPos", "value", "text", "getValueLabel", "axis", "ecModel", "precision", "formatter", "labelModel", "paddings", "normalizeCssArray", "font", "getFont", "textRect", "getBoundingRect", "position", "width", "height", "align", "verticalAlign", "confineInContainer", "bgColor", "label", "x", "y", "getTextColor", "padding", "backgroundColor", "z2", "viewWidth", "getWidth", "viewHeight", "getHeight", "Math", "min", "max", "seriesDataIndices", "opt", "scale", "parse", "get<PERSON><PERSON><PERSON>", "params_1", "getAxisRawValue", "axisDimension", "dim", "axisIndex", "index", "seriesData", "each", "idxItem", "series", "getSeriesByIndex", "seriesIndex", "dataIndex", "dataIndexInside", "dataParams", "getDataParams", "push", "isString", "replace", "isFunction", "getTransformedPosition", "layoutInfo", "transform", "create", "rotate", "rotation", "translate", "applyTransform", "dataToCoord", "labelOffset", "labelDirection", "labelMargin", "buildCartesianSingleLabelElOption", "textLayout", "innerTextLayout", "textAlign", "textVerticalAlign", "makeLineShape", "p1", "p2", "xDimIndex", "x1", "y1", "x2", "y2", "makeRectShape", "xy", "wh", "makeSectorShape", "cx", "cy", "r0", "r", "startAngle", "endAngle", "clockwise"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/axisPointer/viewHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nexport function buildElStyle(axisPointerModel) {\n  var axisPointerType = axisPointerModel.get('type');\n  var styleModel = axisPointerModel.getModel(axisPointerType + 'Style');\n  var style;\n  if (axisPointerType === 'line') {\n    style = styleModel.getLineStyle();\n    style.fill = null;\n  } else if (axisPointerType === 'shadow') {\n    style = styleModel.getAreaStyle();\n    style.stroke = null;\n  }\n  return style;\n}\n/**\r\n * @param {Function} labelPos {align, verticalAlign, position}\r\n */\nexport function buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos) {\n  var value = axisPointerModel.get('value');\n  var text = getValueLabel(value, axisModel.axis, axisModel.ecModel, axisPointerModel.get('seriesDataIndices'), {\n    precision: axisPointerModel.get(['label', 'precision']),\n    formatter: axisPointerModel.get(['label', 'formatter'])\n  });\n  var labelModel = axisPointerModel.getModel('label');\n  var paddings = formatUtil.normalizeCssArray(labelModel.get('padding') || 0);\n  var font = labelModel.getFont();\n  var textRect = textContain.getBoundingRect(text, font);\n  var position = labelPos.position;\n  var width = textRect.width + paddings[1] + paddings[3];\n  var height = textRect.height + paddings[0] + paddings[2];\n  // Adjust by align.\n  var align = labelPos.align;\n  align === 'right' && (position[0] -= width);\n  align === 'center' && (position[0] -= width / 2);\n  var verticalAlign = labelPos.verticalAlign;\n  verticalAlign === 'bottom' && (position[1] -= height);\n  verticalAlign === 'middle' && (position[1] -= height / 2);\n  // Not overflow ec container\n  confineInContainer(position, width, height, api);\n  var bgColor = labelModel.get('backgroundColor');\n  if (!bgColor || bgColor === 'auto') {\n    bgColor = axisModel.get(['axisLine', 'lineStyle', 'color']);\n  }\n  elOption.label = {\n    // shape: {x: 0, y: 0, width: width, height: height, r: labelModel.get('borderRadius')},\n    x: position[0],\n    y: position[1],\n    style: createTextStyle(labelModel, {\n      text: text,\n      font: font,\n      fill: labelModel.getTextColor(),\n      padding: paddings,\n      backgroundColor: bgColor\n    }),\n    // Label should be over axisPointer.\n    z2: 10\n  };\n}\n// Do not overflow ec container\nfunction confineInContainer(position, width, height, api) {\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  position[0] = Math.min(position[0] + width, viewWidth) - width;\n  position[1] = Math.min(position[1] + height, viewHeight) - height;\n  position[0] = Math.max(position[0], 0);\n  position[1] = Math.max(position[1], 0);\n}\nexport function getValueLabel(value, axis, ecModel, seriesDataIndices, opt) {\n  value = axis.scale.parse(value);\n  var text = axis.scale.getLabel({\n    value: value\n  }, {\n    // If `precision` is set, width can be fixed (like '12.00500'), which\n    // helps to debounce when when moving label.\n    precision: opt.precision\n  });\n  var formatter = opt.formatter;\n  if (formatter) {\n    var params_1 = {\n      value: axisHelper.getAxisRawValue(axis, {\n        value: value\n      }),\n      axisDimension: axis.dim,\n      axisIndex: axis.index,\n      seriesData: []\n    };\n    zrUtil.each(seriesDataIndices, function (idxItem) {\n      var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n      var dataIndex = idxItem.dataIndexInside;\n      var dataParams = series && series.getDataParams(dataIndex);\n      dataParams && params_1.seriesData.push(dataParams);\n    });\n    if (zrUtil.isString(formatter)) {\n      text = formatter.replace('{value}', text);\n    } else if (zrUtil.isFunction(formatter)) {\n      text = formatter(params_1);\n    }\n  }\n  return text;\n}\nexport function getTransformedPosition(axis, value, layoutInfo) {\n  var transform = matrix.create();\n  matrix.rotate(transform, transform, layoutInfo.rotation);\n  matrix.translate(transform, transform, layoutInfo.position);\n  return graphic.applyTransform([axis.dataToCoord(value), (layoutInfo.labelOffset || 0) + (layoutInfo.labelDirection || 1) * (layoutInfo.labelMargin || 0)], transform);\n}\nexport function buildCartesianSingleLabelElOption(value, elOption, layoutInfo, axisModel, axisPointerModel, api) {\n  // @ts-ignore\n  var textLayout = AxisBuilder.innerTextLayout(layoutInfo.rotation, 0, layoutInfo.labelDirection);\n  layoutInfo.labelMargin = axisPointerModel.get(['label', 'margin']);\n  buildLabelElOption(elOption, axisModel, axisPointerModel, api, {\n    position: getTransformedPosition(axisModel.axis, value, layoutInfo),\n    align: textLayout.textAlign,\n    verticalAlign: textLayout.textVerticalAlign\n  });\n}\nexport function makeLineShape(p1, p2, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x1: p1[xDimIndex],\n    y1: p1[1 - xDimIndex],\n    x2: p2[xDimIndex],\n    y2: p2[1 - xDimIndex]\n  };\n}\nexport function makeRectShape(xy, wh, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x: xy[xDimIndex],\n    y: xy[1 - xDimIndex],\n    width: wh[xDimIndex],\n    height: wh[1 - xDimIndex]\n  };\n}\nexport function makeSectorShape(cx, cy, r0, r, startAngle, endAngle) {\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r,\n    startAngle: startAngle,\n    endAngle: endAngle,\n    clockwise: true\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,SAASC,YAAYA,CAACC,gBAAgB,EAAE;EAC7C,IAAIC,eAAe,GAAGD,gBAAgB,CAACE,GAAG,CAAC,MAAM,CAAC;EAClD,IAAIC,UAAU,GAAGH,gBAAgB,CAACI,QAAQ,CAACH,eAAe,GAAG,OAAO,CAAC;EACrE,IAAII,KAAK;EACT,IAAIJ,eAAe,KAAK,MAAM,EAAE;IAC9BI,KAAK,GAAGF,UAAU,CAACG,YAAY,CAAC,CAAC;IACjCD,KAAK,CAACE,IAAI,GAAG,IAAI;EACnB,CAAC,MAAM,IAAIN,eAAe,KAAK,QAAQ,EAAE;IACvCI,KAAK,GAAGF,UAAU,CAACK,YAAY,CAAC,CAAC;IACjCH,KAAK,CAACI,MAAM,GAAG,IAAI;EACrB;EACA,OAAOJ,KAAK;AACd;AACA;AACA;AACA;AACA,OAAO,SAASK,kBAAkBA,CAACC,QAAQ,EAAEC,SAAS,EAAEZ,gBAAgB,EAAEa,GAAG,EAAEC,QAAQ,EAAE;EACvF,IAAIC,KAAK,GAAGf,gBAAgB,CAACE,GAAG,CAAC,OAAO,CAAC;EACzC,IAAIc,IAAI,GAAGC,aAAa,CAACF,KAAK,EAAEH,SAAS,CAACM,IAAI,EAAEN,SAAS,CAACO,OAAO,EAAEnB,gBAAgB,CAACE,GAAG,CAAC,mBAAmB,CAAC,EAAE;IAC5GkB,SAAS,EAAEpB,gBAAgB,CAACE,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACvDmB,SAAS,EAAErB,gBAAgB,CAACE,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC;EACxD,CAAC,CAAC;EACF,IAAIoB,UAAU,GAAGtB,gBAAgB,CAACI,QAAQ,CAAC,OAAO,CAAC;EACnD,IAAImB,QAAQ,GAAG7B,UAAU,CAAC8B,iBAAiB,CAACF,UAAU,CAACpB,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EAC3E,IAAIuB,IAAI,GAAGH,UAAU,CAACI,OAAO,CAAC,CAAC;EAC/B,IAAIC,QAAQ,GAAGlC,WAAW,CAACmC,eAAe,CAACZ,IAAI,EAAES,IAAI,CAAC;EACtD,IAAII,QAAQ,GAAGf,QAAQ,CAACe,QAAQ;EAChC,IAAIC,KAAK,GAAGH,QAAQ,CAACG,KAAK,GAAGP,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;EACtD,IAAIQ,MAAM,GAAGJ,QAAQ,CAACI,MAAM,GAAGR,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;EACxD;EACA,IAAIS,KAAK,GAAGlB,QAAQ,CAACkB,KAAK;EAC1BA,KAAK,KAAK,OAAO,KAAKH,QAAQ,CAAC,CAAC,CAAC,IAAIC,KAAK,CAAC;EAC3CE,KAAK,KAAK,QAAQ,KAAKH,QAAQ,CAAC,CAAC,CAAC,IAAIC,KAAK,GAAG,CAAC,CAAC;EAChD,IAAIG,aAAa,GAAGnB,QAAQ,CAACmB,aAAa;EAC1CA,aAAa,KAAK,QAAQ,KAAKJ,QAAQ,CAAC,CAAC,CAAC,IAAIE,MAAM,CAAC;EACrDE,aAAa,KAAK,QAAQ,KAAKJ,QAAQ,CAAC,CAAC,CAAC,IAAIE,MAAM,GAAG,CAAC,CAAC;EACzD;EACAG,kBAAkB,CAACL,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAElB,GAAG,CAAC;EAChD,IAAIsB,OAAO,GAAGb,UAAU,CAACpB,GAAG,CAAC,iBAAiB,CAAC;EAC/C,IAAI,CAACiC,OAAO,IAAIA,OAAO,KAAK,MAAM,EAAE;IAClCA,OAAO,GAAGvB,SAAS,CAACV,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;EAC7D;EACAS,QAAQ,CAACyB,KAAK,GAAG;IACf;IACAC,CAAC,EAAER,QAAQ,CAAC,CAAC,CAAC;IACdS,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC;IACdxB,KAAK,EAAEP,eAAe,CAACwB,UAAU,EAAE;MACjCN,IAAI,EAAEA,IAAI;MACVS,IAAI,EAAEA,IAAI;MACVlB,IAAI,EAAEe,UAAU,CAACiB,YAAY,CAAC,CAAC;MAC/BC,OAAO,EAAEjB,QAAQ;MACjBkB,eAAe,EAAEN;IACnB,CAAC,CAAC;IACF;IACAO,EAAE,EAAE;EACN,CAAC;AACH;AACA;AACA,SAASR,kBAAkBA,CAACL,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAElB,GAAG,EAAE;EACxD,IAAI8B,SAAS,GAAG9B,GAAG,CAAC+B,QAAQ,CAAC,CAAC;EAC9B,IAAIC,UAAU,GAAGhC,GAAG,CAACiC,SAAS,CAAC,CAAC;EAChCjB,QAAQ,CAAC,CAAC,CAAC,GAAGkB,IAAI,CAACC,GAAG,CAACnB,QAAQ,CAAC,CAAC,CAAC,GAAGC,KAAK,EAAEa,SAAS,CAAC,GAAGb,KAAK;EAC9DD,QAAQ,CAAC,CAAC,CAAC,GAAGkB,IAAI,CAACC,GAAG,CAACnB,QAAQ,CAAC,CAAC,CAAC,GAAGE,MAAM,EAAEc,UAAU,CAAC,GAAGd,MAAM;EACjEF,QAAQ,CAAC,CAAC,CAAC,GAAGkB,IAAI,CAACE,GAAG,CAACpB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACtCA,QAAQ,CAAC,CAAC,CAAC,GAAGkB,IAAI,CAACE,GAAG,CAACpB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACxC;AACA,OAAO,SAASZ,aAAaA,CAACF,KAAK,EAAEG,IAAI,EAAEC,OAAO,EAAE+B,iBAAiB,EAAEC,GAAG,EAAE;EAC1EpC,KAAK,GAAGG,IAAI,CAACkC,KAAK,CAACC,KAAK,CAACtC,KAAK,CAAC;EAC/B,IAAIC,IAAI,GAAGE,IAAI,CAACkC,KAAK,CAACE,QAAQ,CAAC;IAC7BvC,KAAK,EAAEA;EACT,CAAC,EAAE;IACD;IACA;IACAK,SAAS,EAAE+B,GAAG,CAAC/B;EACjB,CAAC,CAAC;EACF,IAAIC,SAAS,GAAG8B,GAAG,CAAC9B,SAAS;EAC7B,IAAIA,SAAS,EAAE;IACb,IAAIkC,QAAQ,GAAG;MACbxC,KAAK,EAAEnB,UAAU,CAAC4D,eAAe,CAACtC,IAAI,EAAE;QACtCH,KAAK,EAAEA;MACT,CAAC,CAAC;MACF0C,aAAa,EAAEvC,IAAI,CAACwC,GAAG;MACvBC,SAAS,EAAEzC,IAAI,CAAC0C,KAAK;MACrBC,UAAU,EAAE;IACd,CAAC;IACDtE,MAAM,CAACuE,IAAI,CAACZ,iBAAiB,EAAE,UAAUa,OAAO,EAAE;MAChD,IAAIC,MAAM,GAAG7C,OAAO,CAAC8C,gBAAgB,CAACF,OAAO,CAACG,WAAW,CAAC;MAC1D,IAAIC,SAAS,GAAGJ,OAAO,CAACK,eAAe;MACvC,IAAIC,UAAU,GAAGL,MAAM,IAAIA,MAAM,CAACM,aAAa,CAACH,SAAS,CAAC;MAC1DE,UAAU,IAAId,QAAQ,CAACM,UAAU,CAACU,IAAI,CAACF,UAAU,CAAC;IACpD,CAAC,CAAC;IACF,IAAI9E,MAAM,CAACiF,QAAQ,CAACnD,SAAS,CAAC,EAAE;MAC9BL,IAAI,GAAGK,SAAS,CAACoD,OAAO,CAAC,SAAS,EAAEzD,IAAI,CAAC;IAC3C,CAAC,MAAM,IAAIzB,MAAM,CAACmF,UAAU,CAACrD,SAAS,CAAC,EAAE;MACvCL,IAAI,GAAGK,SAAS,CAACkC,QAAQ,CAAC;IAC5B;EACF;EACA,OAAOvC,IAAI;AACb;AACA,OAAO,SAAS2D,sBAAsBA,CAACzD,IAAI,EAAEH,KAAK,EAAE6D,UAAU,EAAE;EAC9D,IAAIC,SAAS,GAAGlF,MAAM,CAACmF,MAAM,CAAC,CAAC;EAC/BnF,MAAM,CAACoF,MAAM,CAACF,SAAS,EAAEA,SAAS,EAAED,UAAU,CAACI,QAAQ,CAAC;EACxDrF,MAAM,CAACsF,SAAS,CAACJ,SAAS,EAAEA,SAAS,EAAED,UAAU,CAAC/C,QAAQ,CAAC;EAC3D,OAAOrC,OAAO,CAAC0F,cAAc,CAAC,CAAChE,IAAI,CAACiE,WAAW,CAACpE,KAAK,CAAC,EAAE,CAAC6D,UAAU,CAACQ,WAAW,IAAI,CAAC,IAAI,CAACR,UAAU,CAACS,cAAc,IAAI,CAAC,KAAKT,UAAU,CAACU,WAAW,IAAI,CAAC,CAAC,CAAC,EAAET,SAAS,CAAC;AACvK;AACA,OAAO,SAASU,iCAAiCA,CAACxE,KAAK,EAAEJ,QAAQ,EAAEiE,UAAU,EAAEhE,SAAS,EAAEZ,gBAAgB,EAAEa,GAAG,EAAE;EAC/G;EACA,IAAI2E,UAAU,GAAG3F,WAAW,CAAC4F,eAAe,CAACb,UAAU,CAACI,QAAQ,EAAE,CAAC,EAAEJ,UAAU,CAACS,cAAc,CAAC;EAC/FT,UAAU,CAACU,WAAW,GAAGtF,gBAAgB,CAACE,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;EAClEQ,kBAAkB,CAACC,QAAQ,EAAEC,SAAS,EAAEZ,gBAAgB,EAAEa,GAAG,EAAE;IAC7DgB,QAAQ,EAAE8C,sBAAsB,CAAC/D,SAAS,CAACM,IAAI,EAAEH,KAAK,EAAE6D,UAAU,CAAC;IACnE5C,KAAK,EAAEwD,UAAU,CAACE,SAAS;IAC3BzD,aAAa,EAAEuD,UAAU,CAACG;EAC5B,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;EAC/CA,SAAS,GAAGA,SAAS,IAAI,CAAC;EAC1B,OAAO;IACLC,EAAE,EAAEH,EAAE,CAACE,SAAS,CAAC;IACjBE,EAAE,EAAEJ,EAAE,CAAC,CAAC,GAAGE,SAAS,CAAC;IACrBG,EAAE,EAAEJ,EAAE,CAACC,SAAS,CAAC;IACjBI,EAAE,EAAEL,EAAE,CAAC,CAAC,GAAGC,SAAS;EACtB,CAAC;AACH;AACA,OAAO,SAASK,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEP,SAAS,EAAE;EAC/CA,SAAS,GAAGA,SAAS,IAAI,CAAC;EAC1B,OAAO;IACL1D,CAAC,EAAEgE,EAAE,CAACN,SAAS,CAAC;IAChBzD,CAAC,EAAE+D,EAAE,CAAC,CAAC,GAAGN,SAAS,CAAC;IACpBjE,KAAK,EAAEwE,EAAE,CAACP,SAAS,CAAC;IACpBhE,MAAM,EAAEuE,EAAE,CAAC,CAAC,GAAGP,SAAS;EAC1B,CAAC;AACH;AACA,OAAO,SAASQ,eAAeA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EACnE,OAAO;IACLL,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNC,CAAC,EAAEA,CAAC;IACJC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAE;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}