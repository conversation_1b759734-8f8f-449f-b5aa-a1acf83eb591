{"ast": null, "code": "import PyramidChart from './components/PyramidChart.vue';\nexport default {\n  name: 'App',\n  components: {\n    PyramidChart\n  }\n};", "map": {"version": 3, "names": ["Pyramid<PERSON>hart", "name", "components"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\App.vue"], "sourcesContent": ["<template>\n  <div class=\"page-con\">\n    <div class=\"main-con\">\n      <PyramidChart />\n    </div>\n  </div>\n</template>\n\n<script>\nimport PyramidChart from './components/PyramidChart.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    PyramidChart\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  background: #0a1a2e;\n}\n\n#app {\n  height: 100vh;\n  width: 100vw;\n}\n\n.page-con {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #0a1a2e 0%, #16213e 50%, #1a2332 100%);\n  \n  .main-con {\n    width: 80%;\n    height: 60%;\n    max-width: 800px;\n    max-height: 600px;\n  }\n}\n</style>\n"], "mappings": "AASA,OAAOA,YAAW,MAAO,+BAA8B;AAEvD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}