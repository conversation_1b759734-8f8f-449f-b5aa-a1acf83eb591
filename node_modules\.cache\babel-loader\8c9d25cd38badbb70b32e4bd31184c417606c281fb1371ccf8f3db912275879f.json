{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\n/**\r\n * @param finder contains {seriesIndex, dataIndex, dataIndexInside}\r\n * @param ecModel\r\n * @return  {point: [x, y], el: ...} point Will not be null.\r\n */\nexport default function findPointFromSeries(finder, ecModel) {\n  var point = [];\n  var seriesIndex = finder.seriesIndex;\n  var seriesModel;\n  if (seriesIndex == null || !(seriesModel = ecModel.getSeriesByIndex(seriesIndex))) {\n    return {\n      point: []\n    };\n  }\n  var data = seriesModel.getData();\n  var dataIndex = modelUtil.queryDataIndex(data, finder);\n  if (dataIndex == null || dataIndex < 0 || zrUtil.isArray(dataIndex)) {\n    return {\n      point: []\n    };\n  }\n  var el = data.getItemGraphicEl(dataIndex);\n  var coordSys = seriesModel.coordinateSystem;\n  if (seriesModel.getTooltipPosition) {\n    point = seriesModel.getTooltipPosition(dataIndex) || [];\n  } else if (coordSys && coordSys.dataToPoint) {\n    if (finder.isStacked) {\n      var baseAxis = coordSys.getBaseAxis();\n      var valueAxis = coordSys.getOtherAxis(baseAxis);\n      var valueAxisDim = valueAxis.dim;\n      var baseAxisDim = baseAxis.dim;\n      var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n      var baseDim = data.mapDimension(baseAxisDim);\n      var stackedData = [];\n      stackedData[baseDataOffset] = data.get(baseDim, dataIndex);\n      stackedData[1 - baseDataOffset] = data.get(data.getCalculationInfo('stackResultDimension'), dataIndex);\n      point = coordSys.dataToPoint(stackedData) || [];\n    } else {\n      point = coordSys.dataToPoint(data.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }), dataIndex)) || [];\n    }\n  } else if (el) {\n    // Use graphic bounding rect\n    var rect = el.getBoundingRect().clone();\n    rect.applyTransform(el.transform);\n    point = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  }\n  return {\n    point: point,\n    el: el\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "modelUtil", "findPointFromSeries", "finder", "ecModel", "point", "seriesIndex", "seriesModel", "getSeriesByIndex", "data", "getData", "dataIndex", "queryDataIndex", "isArray", "el", "getItemGraphicEl", "coordSys", "coordinateSystem", "getTooltipPosition", "dataToPoint", "isStacked", "baseAxis", "getBaseAxis", "valueAxis", "getOtherAxis", "valueAxisDim", "dim", "baseAxisDim", "baseDataOffset", "baseDim", "mapDimension", "stackedData", "get", "getCalculationInfo", "getV<PERSON>ues", "map", "dimensions", "rect", "getBoundingRect", "clone", "applyTransform", "transform", "x", "width", "y", "height"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/axisPointer/findPointFromSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\n/**\r\n * @param finder contains {seriesIndex, dataIndex, dataIndexInside}\r\n * @param ecModel\r\n * @return  {point: [x, y], el: ...} point Will not be null.\r\n */\nexport default function findPointFromSeries(finder, ecModel) {\n  var point = [];\n  var seriesIndex = finder.seriesIndex;\n  var seriesModel;\n  if (seriesIndex == null || !(seriesModel = ecModel.getSeriesByIndex(seriesIndex))) {\n    return {\n      point: []\n    };\n  }\n  var data = seriesModel.getData();\n  var dataIndex = modelUtil.queryDataIndex(data, finder);\n  if (dataIndex == null || dataIndex < 0 || zrUtil.isArray(dataIndex)) {\n    return {\n      point: []\n    };\n  }\n  var el = data.getItemGraphicEl(dataIndex);\n  var coordSys = seriesModel.coordinateSystem;\n  if (seriesModel.getTooltipPosition) {\n    point = seriesModel.getTooltipPosition(dataIndex) || [];\n  } else if (coordSys && coordSys.dataToPoint) {\n    if (finder.isStacked) {\n      var baseAxis = coordSys.getBaseAxis();\n      var valueAxis = coordSys.getOtherAxis(baseAxis);\n      var valueAxisDim = valueAxis.dim;\n      var baseAxisDim = baseAxis.dim;\n      var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n      var baseDim = data.mapDimension(baseAxisDim);\n      var stackedData = [];\n      stackedData[baseDataOffset] = data.get(baseDim, dataIndex);\n      stackedData[1 - baseDataOffset] = data.get(data.getCalculationInfo('stackResultDimension'), dataIndex);\n      point = coordSys.dataToPoint(stackedData) || [];\n    } else {\n      point = coordSys.dataToPoint(data.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }), dataIndex)) || [];\n    }\n  } else if (el) {\n    // Use graphic bounding rect\n    var rect = el.getBoundingRect().clone();\n    rect.applyTransform(el.transform);\n    point = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  }\n  return {\n    point: point,\n    el: el\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC3D,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGH,MAAM,CAACG,WAAW;EACpC,IAAIC,WAAW;EACf,IAAID,WAAW,IAAI,IAAI,IAAI,EAAEC,WAAW,GAAGH,OAAO,CAACI,gBAAgB,CAACF,WAAW,CAAC,CAAC,EAAE;IACjF,OAAO;MACLD,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAII,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;EAChC,IAAIC,SAAS,GAAGV,SAAS,CAACW,cAAc,CAACH,IAAI,EAAEN,MAAM,CAAC;EACtD,IAAIQ,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,CAAC,IAAIX,MAAM,CAACa,OAAO,CAACF,SAAS,CAAC,EAAE;IACnE,OAAO;MACLN,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAIS,EAAE,GAAGL,IAAI,CAACM,gBAAgB,CAACJ,SAAS,CAAC;EACzC,IAAIK,QAAQ,GAAGT,WAAW,CAACU,gBAAgB;EAC3C,IAAIV,WAAW,CAACW,kBAAkB,EAAE;IAClCb,KAAK,GAAGE,WAAW,CAACW,kBAAkB,CAACP,SAAS,CAAC,IAAI,EAAE;EACzD,CAAC,MAAM,IAAIK,QAAQ,IAAIA,QAAQ,CAACG,WAAW,EAAE;IAC3C,IAAIhB,MAAM,CAACiB,SAAS,EAAE;MACpB,IAAIC,QAAQ,GAAGL,QAAQ,CAACM,WAAW,CAAC,CAAC;MACrC,IAAIC,SAAS,GAAGP,QAAQ,CAACQ,YAAY,CAACH,QAAQ,CAAC;MAC/C,IAAII,YAAY,GAAGF,SAAS,CAACG,GAAG;MAChC,IAAIC,WAAW,GAAGN,QAAQ,CAACK,GAAG;MAC9B,IAAIE,cAAc,GAAGH,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;MAC9E,IAAII,OAAO,GAAGpB,IAAI,CAACqB,YAAY,CAACH,WAAW,CAAC;MAC5C,IAAII,WAAW,GAAG,EAAE;MACpBA,WAAW,CAACH,cAAc,CAAC,GAAGnB,IAAI,CAACuB,GAAG,CAACH,OAAO,EAAElB,SAAS,CAAC;MAC1DoB,WAAW,CAAC,CAAC,GAAGH,cAAc,CAAC,GAAGnB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,kBAAkB,CAAC,sBAAsB,CAAC,EAAEtB,SAAS,CAAC;MACtGN,KAAK,GAAGW,QAAQ,CAACG,WAAW,CAACY,WAAW,CAAC,IAAI,EAAE;IACjD,CAAC,MAAM;MACL1B,KAAK,GAAGW,QAAQ,CAACG,WAAW,CAACV,IAAI,CAACyB,SAAS,CAAClC,MAAM,CAACmC,GAAG,CAACnB,QAAQ,CAACoB,UAAU,EAAE,UAAUV,GAAG,EAAE;QACzF,OAAOjB,IAAI,CAACqB,YAAY,CAACJ,GAAG,CAAC;MAC/B,CAAC,CAAC,EAAEf,SAAS,CAAC,CAAC,IAAI,EAAE;IACvB;EACF,CAAC,MAAM,IAAIG,EAAE,EAAE;IACb;IACA,IAAIuB,IAAI,GAAGvB,EAAE,CAACwB,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACvCF,IAAI,CAACG,cAAc,CAAC1B,EAAE,CAAC2B,SAAS,CAAC;IACjCpC,KAAK,GAAG,CAACgC,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC,EAAEN,IAAI,CAACO,CAAC,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC;EAC7D;EACA,OAAO;IACLxC,KAAK,EAAEA,KAAK;IACZS,EAAE,EAAEA;EACN,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}