{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport * as echarts from 'echarts';\nconst app = createApp(App);\n\n// 全局注册 echarts\napp.config.globalProperties.$echarts = echarts;\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "echarts", "app", "config", "globalProperties", "$echarts", "mount"], "sources": ["E:/1-test/test-echarts-bar3d/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport * as echarts from 'echarts'\n\nconst app = createApp(App)\n\n// 全局注册 echarts\napp.config.globalProperties.$echarts = echarts\n\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,KAAKC,OAAO,MAAM,SAAS;AAElC,MAAMC,GAAG,GAAGH,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAE,GAAG,CAACC,MAAM,CAACC,gBAAgB,CAACC,QAAQ,GAAGJ,OAAO;AAE9CC,GAAG,CAACI,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}