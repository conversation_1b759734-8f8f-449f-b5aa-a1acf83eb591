{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\nexport default {\n  time: {\n    month: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],\n    monthAbbr: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n    dayOfWeek: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],\n    dayOfWeekAbbr: ['日', '一', '二', '三', '四', '五', '六']\n  },\n  legend: {\n    selector: {\n      all: '全选',\n      inverse: '反选'\n    }\n  },\n  toolbox: {\n    brush: {\n      title: {\n        rect: '矩形选择',\n        polygon: '圈选',\n        lineX: '横向选择',\n        lineY: '纵向选择',\n        keep: '保持选择',\n        clear: '清除选择'\n      }\n    },\n    dataView: {\n      title: '数据视图',\n      lang: ['数据视图', '关闭', '刷新']\n    },\n    dataZoom: {\n      title: {\n        zoom: '区域缩放',\n        back: '区域缩放还原'\n      }\n    },\n    magicType: {\n      title: {\n        line: '切换为折线图',\n        bar: '切换为柱状图',\n        stack: '切换为堆叠',\n        tiled: '切换为平铺'\n      }\n    },\n    restore: {\n      title: '还原'\n    },\n    saveAsImage: {\n      title: '保存为图片',\n      lang: ['右键另存为图片']\n    }\n  },\n  series: {\n    typeNames: {\n      pie: '饼图',\n      bar: '柱状图',\n      line: '折线图',\n      scatter: '散点图',\n      effectScatter: '涟漪散点图',\n      radar: '雷达图',\n      tree: '树图',\n      treemap: '矩形树图',\n      boxplot: '箱型图',\n      candlestick: 'K线图',\n      k: 'K线图',\n      heatmap: '热力图',\n      map: '地图',\n      parallel: '平行坐标图',\n      lines: '线图',\n      graph: '关系图',\n      sankey: '桑基图',\n      funnel: '漏斗图',\n      gauge: '仪表盘图',\n      pictorialBar: '象形柱图',\n      themeRiver: '主题河流图',\n      sunburst: '旭日图',\n      custom: '自定义图表',\n      chart: '图表'\n    }\n  },\n  aria: {\n    general: {\n      withTitle: '这是一个关于“{title}”的图表。',\n      withoutTitle: '这是一个图表，'\n    },\n    series: {\n      single: {\n        prefix: '',\n        withName: '图表类型是{seriesType}，表示{seriesName}。',\n        withoutName: '图表类型是{seriesType}。'\n      },\n      multiple: {\n        prefix: '它由{seriesCount}个图表系列组成。',\n        withName: '第{seriesId}个系列是一个表示{seriesName}的{seriesType}，',\n        withoutName: '第{seriesId}个系列是一个{seriesType}，',\n        separator: {\n          middle: '；',\n          end: '。'\n        }\n      }\n    },\n    data: {\n      allData: '其数据是——',\n      partialData: '其中，前{displayCnt}项是——',\n      withName: '{name}的数据是{value}',\n      withoutName: '{value}',\n      separator: {\n        middle: '，',\n        end: ''\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["time", "month", "monthAbbr", "dayOfWeek", "dayOfWeekAbbr", "legend", "selector", "all", "inverse", "toolbox", "brush", "title", "rect", "polygon", "lineX", "lineY", "keep", "clear", "dataView", "lang", "dataZoom", "zoom", "back", "magicType", "line", "bar", "stack", "tiled", "restore", "saveAsImage", "series", "typeNames", "pie", "scatter", "effectScatter", "radar", "tree", "treemap", "boxplot", "candlestick", "k", "heatmap", "map", "parallel", "lines", "graph", "sankey", "funnel", "gauge", "pictorialBar", "themeRiver", "sunburst", "custom", "chart", "aria", "general", "with<PERSON><PERSON><PERSON>", "without<PERSON>itle", "single", "prefix", "with<PERSON><PERSON>", "without<PERSON><PERSON>", "multiple", "separator", "middle", "end", "data", "allData", "partialData"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/i18n/langZH.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\nexport default {\n  time: {\n    month: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],\n    monthAbbr: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n    dayOfWeek: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],\n    dayOfWeekAbbr: ['日', '一', '二', '三', '四', '五', '六']\n  },\n  legend: {\n    selector: {\n      all: '全选',\n      inverse: '反选'\n    }\n  },\n  toolbox: {\n    brush: {\n      title: {\n        rect: '矩形选择',\n        polygon: '圈选',\n        lineX: '横向选择',\n        lineY: '纵向选择',\n        keep: '保持选择',\n        clear: '清除选择'\n      }\n    },\n    dataView: {\n      title: '数据视图',\n      lang: ['数据视图', '关闭', '刷新']\n    },\n    dataZoom: {\n      title: {\n        zoom: '区域缩放',\n        back: '区域缩放还原'\n      }\n    },\n    magicType: {\n      title: {\n        line: '切换为折线图',\n        bar: '切换为柱状图',\n        stack: '切换为堆叠',\n        tiled: '切换为平铺'\n      }\n    },\n    restore: {\n      title: '还原'\n    },\n    saveAsImage: {\n      title: '保存为图片',\n      lang: ['右键另存为图片']\n    }\n  },\n  series: {\n    typeNames: {\n      pie: '饼图',\n      bar: '柱状图',\n      line: '折线图',\n      scatter: '散点图',\n      effectScatter: '涟漪散点图',\n      radar: '雷达图',\n      tree: '树图',\n      treemap: '矩形树图',\n      boxplot: '箱型图',\n      candlestick: 'K线图',\n      k: 'K线图',\n      heatmap: '热力图',\n      map: '地图',\n      parallel: '平行坐标图',\n      lines: '线图',\n      graph: '关系图',\n      sankey: '桑基图',\n      funnel: '漏斗图',\n      gauge: '仪表盘图',\n      pictorialBar: '象形柱图',\n      themeRiver: '主题河流图',\n      sunburst: '旭日图',\n      custom: '自定义图表',\n      chart: '图表'\n    }\n  },\n  aria: {\n    general: {\n      withTitle: '这是一个关于“{title}”的图表。',\n      withoutTitle: '这是一个图表，'\n    },\n    series: {\n      single: {\n        prefix: '',\n        withName: '图表类型是{seriesType}，表示{seriesName}。',\n        withoutName: '图表类型是{seriesType}。'\n      },\n      multiple: {\n        prefix: '它由{seriesCount}个图表系列组成。',\n        withName: '第{seriesId}个系列是一个表示{seriesName}的{seriesType}，',\n        withoutName: '第{seriesId}个系列是一个{seriesType}，',\n        separator: {\n          middle: '；',\n          end: '。'\n        }\n      }\n    },\n    data: {\n      allData: '其数据是——',\n      partialData: '其中，前{displayCnt}项是——',\n      withName: '{name}的数据是{value}',\n      withoutName: '{value}',\n      separator: {\n        middle: '，',\n        end: ''\n      }\n    }\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EACbA,IAAI,EAAE;IACJC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IACjFC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtFC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5DC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EACnD,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE;MACRC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,QAAQ,EAAE;MACRP,KAAK,EAAE,MAAM;MACbQ,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI;IAC3B,CAAC;IACDC,QAAQ,EAAE;MACRT,KAAK,EAAE;QACLU,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR;IACF,CAAC;IACDC,SAAS,EAAE;MACTZ,KAAK,EAAE;QACLa,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,QAAQ;QACbC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,OAAO,EAAE;MACPjB,KAAK,EAAE;IACT,CAAC;IACDkB,WAAW,EAAE;MACXlB,KAAK,EAAE,OAAO;MACdQ,IAAI,EAAE,CAAC,SAAS;IAClB;EACF,CAAC;EACDW,MAAM,EAAE;IACNC,SAAS,EAAE;MACTC,GAAG,EAAE,IAAI;MACTP,GAAG,EAAE,KAAK;MACVD,IAAI,EAAE,KAAK;MACXS,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,OAAO;MACtBC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,CAAC,EAAE,KAAK;MACRC,OAAO,EAAE,KAAK;MACdC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,MAAM;MACbC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,OAAO;MACnBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,IAAI,EAAE;IACJC,OAAO,EAAE;MACPC,SAAS,EAAE,qBAAqB;MAChCC,YAAY,EAAE;IAChB,CAAC;IACD3B,MAAM,EAAE;MACN4B,MAAM,EAAE;QACNC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,mCAAmC;QAC7CC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRH,MAAM,EAAE,yBAAyB;QACjCC,QAAQ,EAAE,+CAA+C;QACzDC,WAAW,EAAE,gCAAgC;QAC7CE,SAAS,EAAE;UACTC,MAAM,EAAE,GAAG;UACXC,GAAG,EAAE;QACP;MACF;IACF,CAAC;IACDC,IAAI,EAAE;MACJC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,sBAAsB;MACnCR,QAAQ,EAAE,mBAAmB;MAC7BC,WAAW,EAAE,SAAS;MACtBE,SAAS,EAAE;QACTC,MAAM,EAAE,GAAG;QACXC,GAAG,EAAE;MACP;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}