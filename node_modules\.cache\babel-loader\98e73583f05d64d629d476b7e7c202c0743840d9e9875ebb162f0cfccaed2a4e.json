{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function calendarPrepareCustom(coordSys) {\n  var rect = coordSys.getRect();\n  var rangeInfo = coordSys.getRangeInfo();\n  return {\n    coordSys: {\n      type: 'calendar',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height,\n      cellWidth: coordSys.getCellWidth(),\n      cellHeight: coordSys.getCellHeight(),\n      rangeInfo: {\n        start: rangeInfo.start,\n        end: rangeInfo.end,\n        weeks: rangeInfo.weeks,\n        dayCount: rangeInfo.allDay\n      }\n    },\n    api: {\n      coord: function (data, clamp) {\n        return coordSys.dataToPoint(data, clamp);\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["calendarPrepareCustom", "coordSys", "rect", "getRect", "rangeInfo", "getRangeInfo", "type", "x", "y", "width", "height", "cellWidth", "get<PERSON>ell<PERSON><PERSON>th", "cellHeight", "getCellHeight", "start", "end", "weeks", "dayCount", "allDay", "api", "coord", "data", "clamp", "dataToPoint"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/calendar/prepareCustom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function calendarPrepareCustom(coordSys) {\n  var rect = coordSys.getRect();\n  var rangeInfo = coordSys.getRangeInfo();\n  return {\n    coordSys: {\n      type: 'calendar',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height,\n      cellWidth: coordSys.getCellWidth(),\n      cellHeight: coordSys.getCellHeight(),\n      rangeInfo: {\n        start: rangeInfo.start,\n        end: rangeInfo.end,\n        weeks: rangeInfo.weeks,\n        dayCount: rangeInfo.allDay\n      }\n    },\n    api: {\n      coord: function (data, clamp) {\n        return coordSys.dataToPoint(data, clamp);\n      }\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,QAAQ,EAAE;EACtD,IAAIC,IAAI,GAAGD,QAAQ,CAACE,OAAO,CAAC,CAAC;EAC7B,IAAIC,SAAS,GAAGH,QAAQ,CAACI,YAAY,CAAC,CAAC;EACvC,OAAO;IACLJ,QAAQ,EAAE;MACRK,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAEL,IAAI,CAACK,CAAC;MACTC,CAAC,EAAEN,IAAI,CAACM,CAAC;MACTC,KAAK,EAAEP,IAAI,CAACO,KAAK;MACjBC,MAAM,EAAER,IAAI,CAACQ,MAAM;MACnBC,SAAS,EAAEV,QAAQ,CAACW,YAAY,CAAC,CAAC;MAClCC,UAAU,EAAEZ,QAAQ,CAACa,aAAa,CAAC,CAAC;MACpCV,SAAS,EAAE;QACTW,KAAK,EAAEX,SAAS,CAACW,KAAK;QACtBC,GAAG,EAAEZ,SAAS,CAACY,GAAG;QAClBC,KAAK,EAAEb,SAAS,CAACa,KAAK;QACtBC,QAAQ,EAAEd,SAAS,CAACe;MACtB;IACF,CAAC;IACDC,GAAG,EAAE;MACHC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAE;QAC5B,OAAOtB,QAAQ,CAACuB,WAAW,CAACF,IAAI,EAAEC,KAAK,CAAC;MAC1C;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}