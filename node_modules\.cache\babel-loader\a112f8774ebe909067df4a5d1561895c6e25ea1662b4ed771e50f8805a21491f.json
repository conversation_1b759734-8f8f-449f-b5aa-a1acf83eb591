{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Eventful from '../core/Eventful.js';\nimport requestAnimationFrame from './requestAnimationFrame.js';\nimport Animator from './Animator.js';\nexport function getTime() {\n  return new Date().getTime();\n}\nvar Animation = function (_super) {\n  __extends(Animation, _super);\n  function Animation(opts) {\n    var _this = _super.call(this) || this;\n    _this._running = false;\n    _this._time = 0;\n    _this._pausedTime = 0;\n    _this._pauseStart = 0;\n    _this._paused = false;\n    opts = opts || {};\n    _this.stage = opts.stage || {};\n    return _this;\n  }\n  Animation.prototype.addClip = function (clip) {\n    if (clip.animation) {\n      this.removeClip(clip);\n    }\n    if (!this._head) {\n      this._head = this._tail = clip;\n    } else {\n      this._tail.next = clip;\n      clip.prev = this._tail;\n      clip.next = null;\n      this._tail = clip;\n    }\n    clip.animation = this;\n  };\n  Animation.prototype.addAnimator = function (animator) {\n    animator.animation = this;\n    var clip = animator.getClip();\n    if (clip) {\n      this.addClip(clip);\n    }\n  };\n  Animation.prototype.removeClip = function (clip) {\n    if (!clip.animation) {\n      return;\n    }\n    var prev = clip.prev;\n    var next = clip.next;\n    if (prev) {\n      prev.next = next;\n    } else {\n      this._head = next;\n    }\n    if (next) {\n      next.prev = prev;\n    } else {\n      this._tail = prev;\n    }\n    clip.next = clip.prev = clip.animation = null;\n  };\n  Animation.prototype.removeAnimator = function (animator) {\n    var clip = animator.getClip();\n    if (clip) {\n      this.removeClip(clip);\n    }\n    animator.animation = null;\n  };\n  Animation.prototype.update = function (notTriggerFrameAndStageUpdate) {\n    var time = getTime() - this._pausedTime;\n    var delta = time - this._time;\n    var clip = this._head;\n    while (clip) {\n      var nextClip = clip.next;\n      var finished = clip.step(time, delta);\n      if (finished) {\n        clip.ondestroy();\n        this.removeClip(clip);\n        clip = nextClip;\n      } else {\n        clip = nextClip;\n      }\n    }\n    this._time = time;\n    if (!notTriggerFrameAndStageUpdate) {\n      this.trigger('frame', delta);\n      this.stage.update && this.stage.update();\n    }\n  };\n  Animation.prototype._startLoop = function () {\n    var self = this;\n    this._running = true;\n    function step() {\n      if (self._running) {\n        requestAnimationFrame(step);\n        !self._paused && self.update();\n      }\n    }\n    requestAnimationFrame(step);\n  };\n  Animation.prototype.start = function () {\n    if (this._running) {\n      return;\n    }\n    this._time = getTime();\n    this._pausedTime = 0;\n    this._startLoop();\n  };\n  Animation.prototype.stop = function () {\n    this._running = false;\n  };\n  Animation.prototype.pause = function () {\n    if (!this._paused) {\n      this._pauseStart = getTime();\n      this._paused = true;\n    }\n  };\n  Animation.prototype.resume = function () {\n    if (this._paused) {\n      this._pausedTime += getTime() - this._pauseStart;\n      this._paused = false;\n    }\n  };\n  Animation.prototype.clear = function () {\n    var clip = this._head;\n    while (clip) {\n      var nextClip = clip.next;\n      clip.prev = clip.next = clip.animation = null;\n      clip = nextClip;\n    }\n    this._head = this._tail = null;\n  };\n  Animation.prototype.isFinished = function () {\n    return this._head == null;\n  };\n  Animation.prototype.animate = function (target, options) {\n    options = options || {};\n    this.start();\n    var animator = new Animator(target, options.loop);\n    this.addAnimator(animator);\n    return animator;\n  };\n  return Animation;\n}(Eventful);\nexport default Animation;", "map": {"version": 3, "names": ["__extends", "Eventful", "requestAnimationFrame", "Animator", "getTime", "Date", "Animation", "_super", "opts", "_this", "call", "_running", "_time", "_pausedTime", "_pauseStart", "_paused", "stage", "prototype", "addClip", "clip", "animation", "removeClip", "_head", "_tail", "next", "prev", "addAnimator", "animator", "getClip", "removeAnimator", "update", "notTriggerFrameAndStageUpdate", "time", "delta", "nextClip", "finished", "step", "ondestroy", "trigger", "_startLoop", "self", "start", "stop", "pause", "resume", "clear", "isFinished", "animate", "target", "options", "loop"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/animation/Animation.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Eventful from '../core/Eventful.js';\nimport requestAnimationFrame from './requestAnimationFrame.js';\nimport Animator from './Animator.js';\nexport function getTime() {\n    return new Date().getTime();\n}\nvar Animation = (function (_super) {\n    __extends(Animation, _super);\n    function Animation(opts) {\n        var _this = _super.call(this) || this;\n        _this._running = false;\n        _this._time = 0;\n        _this._pausedTime = 0;\n        _this._pauseStart = 0;\n        _this._paused = false;\n        opts = opts || {};\n        _this.stage = opts.stage || {};\n        return _this;\n    }\n    Animation.prototype.addClip = function (clip) {\n        if (clip.animation) {\n            this.removeClip(clip);\n        }\n        if (!this._head) {\n            this._head = this._tail = clip;\n        }\n        else {\n            this._tail.next = clip;\n            clip.prev = this._tail;\n            clip.next = null;\n            this._tail = clip;\n        }\n        clip.animation = this;\n    };\n    Animation.prototype.addAnimator = function (animator) {\n        animator.animation = this;\n        var clip = animator.getClip();\n        if (clip) {\n            this.addClip(clip);\n        }\n    };\n    Animation.prototype.removeClip = function (clip) {\n        if (!clip.animation) {\n            return;\n        }\n        var prev = clip.prev;\n        var next = clip.next;\n        if (prev) {\n            prev.next = next;\n        }\n        else {\n            this._head = next;\n        }\n        if (next) {\n            next.prev = prev;\n        }\n        else {\n            this._tail = prev;\n        }\n        clip.next = clip.prev = clip.animation = null;\n    };\n    Animation.prototype.removeAnimator = function (animator) {\n        var clip = animator.getClip();\n        if (clip) {\n            this.removeClip(clip);\n        }\n        animator.animation = null;\n    };\n    Animation.prototype.update = function (notTriggerFrameAndStageUpdate) {\n        var time = getTime() - this._pausedTime;\n        var delta = time - this._time;\n        var clip = this._head;\n        while (clip) {\n            var nextClip = clip.next;\n            var finished = clip.step(time, delta);\n            if (finished) {\n                clip.ondestroy();\n                this.removeClip(clip);\n                clip = nextClip;\n            }\n            else {\n                clip = nextClip;\n            }\n        }\n        this._time = time;\n        if (!notTriggerFrameAndStageUpdate) {\n            this.trigger('frame', delta);\n            this.stage.update && this.stage.update();\n        }\n    };\n    Animation.prototype._startLoop = function () {\n        var self = this;\n        this._running = true;\n        function step() {\n            if (self._running) {\n                requestAnimationFrame(step);\n                !self._paused && self.update();\n            }\n        }\n        requestAnimationFrame(step);\n    };\n    Animation.prototype.start = function () {\n        if (this._running) {\n            return;\n        }\n        this._time = getTime();\n        this._pausedTime = 0;\n        this._startLoop();\n    };\n    Animation.prototype.stop = function () {\n        this._running = false;\n    };\n    Animation.prototype.pause = function () {\n        if (!this._paused) {\n            this._pauseStart = getTime();\n            this._paused = true;\n        }\n    };\n    Animation.prototype.resume = function () {\n        if (this._paused) {\n            this._pausedTime += getTime() - this._pauseStart;\n            this._paused = false;\n        }\n    };\n    Animation.prototype.clear = function () {\n        var clip = this._head;\n        while (clip) {\n            var nextClip = clip.next;\n            clip.prev = clip.next = clip.animation = null;\n            clip = nextClip;\n        }\n        this._head = this._tail = null;\n    };\n    Animation.prototype.isFinished = function () {\n        return this._head == null;\n    };\n    Animation.prototype.animate = function (target, options) {\n        options = options || {};\n        this.start();\n        var animator = new Animator(target, options.loop);\n        this.addAnimator(animator);\n        return animator;\n    };\n    return Animation;\n}(Eventful));\nexport default Animation;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAO,SAASC,OAAOA,CAAA,EAAG;EACtB,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC;AAC/B;AACA,IAAIE,SAAS,GAAI,UAAUC,MAAM,EAAE;EAC/BP,SAAS,CAACM,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,IAAI,EAAE;IACrB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,QAAQ,GAAG,KAAK;IACtBF,KAAK,CAACG,KAAK,GAAG,CAAC;IACfH,KAAK,CAACI,WAAW,GAAG,CAAC;IACrBJ,KAAK,CAACK,WAAW,GAAG,CAAC;IACrBL,KAAK,CAACM,OAAO,GAAG,KAAK;IACrBP,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjBC,KAAK,CAACO,KAAK,GAAGR,IAAI,CAACQ,KAAK,IAAI,CAAC,CAAC;IAC9B,OAAOP,KAAK;EAChB;EACAH,SAAS,CAACW,SAAS,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;IAC1C,IAAIA,IAAI,CAACC,SAAS,EAAE;MAChB,IAAI,CAACC,UAAU,CAACF,IAAI,CAAC;IACzB;IACA,IAAI,CAAC,IAAI,CAACG,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,KAAK,GAAGJ,IAAI;IAClC,CAAC,MACI;MACD,IAAI,CAACI,KAAK,CAACC,IAAI,GAAGL,IAAI;MACtBA,IAAI,CAACM,IAAI,GAAG,IAAI,CAACF,KAAK;MACtBJ,IAAI,CAACK,IAAI,GAAG,IAAI;MAChB,IAAI,CAACD,KAAK,GAAGJ,IAAI;IACrB;IACAA,IAAI,CAACC,SAAS,GAAG,IAAI;EACzB,CAAC;EACDd,SAAS,CAACW,SAAS,CAACS,WAAW,GAAG,UAAUC,QAAQ,EAAE;IAClDA,QAAQ,CAACP,SAAS,GAAG,IAAI;IACzB,IAAID,IAAI,GAAGQ,QAAQ,CAACC,OAAO,CAAC,CAAC;IAC7B,IAAIT,IAAI,EAAE;MACN,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC;IACtB;EACJ,CAAC;EACDb,SAAS,CAACW,SAAS,CAACI,UAAU,GAAG,UAAUF,IAAI,EAAE;IAC7C,IAAI,CAACA,IAAI,CAACC,SAAS,EAAE;MACjB;IACJ;IACA,IAAIK,IAAI,GAAGN,IAAI,CAACM,IAAI;IACpB,IAAID,IAAI,GAAGL,IAAI,CAACK,IAAI;IACpB,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACD,IAAI,GAAGA,IAAI;IACpB,CAAC,MACI;MACD,IAAI,CAACF,KAAK,GAAGE,IAAI;IACrB;IACA,IAAIA,IAAI,EAAE;MACNA,IAAI,CAACC,IAAI,GAAGA,IAAI;IACpB,CAAC,MACI;MACD,IAAI,CAACF,KAAK,GAAGE,IAAI;IACrB;IACAN,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACM,IAAI,GAAGN,IAAI,CAACC,SAAS,GAAG,IAAI;EACjD,CAAC;EACDd,SAAS,CAACW,SAAS,CAACY,cAAc,GAAG,UAAUF,QAAQ,EAAE;IACrD,IAAIR,IAAI,GAAGQ,QAAQ,CAACC,OAAO,CAAC,CAAC;IAC7B,IAAIT,IAAI,EAAE;MACN,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC;IACzB;IACAQ,QAAQ,CAACP,SAAS,GAAG,IAAI;EAC7B,CAAC;EACDd,SAAS,CAACW,SAAS,CAACa,MAAM,GAAG,UAAUC,6BAA6B,EAAE;IAClE,IAAIC,IAAI,GAAG5B,OAAO,CAAC,CAAC,GAAG,IAAI,CAACS,WAAW;IACvC,IAAIoB,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACpB,KAAK;IAC7B,IAAIO,IAAI,GAAG,IAAI,CAACG,KAAK;IACrB,OAAOH,IAAI,EAAE;MACT,IAAIe,QAAQ,GAAGf,IAAI,CAACK,IAAI;MACxB,IAAIW,QAAQ,GAAGhB,IAAI,CAACiB,IAAI,CAACJ,IAAI,EAAEC,KAAK,CAAC;MACrC,IAAIE,QAAQ,EAAE;QACVhB,IAAI,CAACkB,SAAS,CAAC,CAAC;QAChB,IAAI,CAAChB,UAAU,CAACF,IAAI,CAAC;QACrBA,IAAI,GAAGe,QAAQ;MACnB,CAAC,MACI;QACDf,IAAI,GAAGe,QAAQ;MACnB;IACJ;IACA,IAAI,CAACtB,KAAK,GAAGoB,IAAI;IACjB,IAAI,CAACD,6BAA6B,EAAE;MAChC,IAAI,CAACO,OAAO,CAAC,OAAO,EAAEL,KAAK,CAAC;MAC5B,IAAI,CAACjB,KAAK,CAACc,MAAM,IAAI,IAAI,CAACd,KAAK,CAACc,MAAM,CAAC,CAAC;IAC5C;EACJ,CAAC;EACDxB,SAAS,CAACW,SAAS,CAACsB,UAAU,GAAG,YAAY;IACzC,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI,CAAC7B,QAAQ,GAAG,IAAI;IACpB,SAASyB,IAAIA,CAAA,EAAG;MACZ,IAAII,IAAI,CAAC7B,QAAQ,EAAE;QACfT,qBAAqB,CAACkC,IAAI,CAAC;QAC3B,CAACI,IAAI,CAACzB,OAAO,IAAIyB,IAAI,CAACV,MAAM,CAAC,CAAC;MAClC;IACJ;IACA5B,qBAAqB,CAACkC,IAAI,CAAC;EAC/B,CAAC;EACD9B,SAAS,CAACW,SAAS,CAACwB,KAAK,GAAG,YAAY;IACpC,IAAI,IAAI,CAAC9B,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACC,KAAK,GAAGR,OAAO,CAAC,CAAC;IACtB,IAAI,CAACS,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC0B,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjC,SAAS,CAACW,SAAS,CAACyB,IAAI,GAAG,YAAY;IACnC,IAAI,CAAC/B,QAAQ,GAAG,KAAK;EACzB,CAAC;EACDL,SAAS,CAACW,SAAS,CAAC0B,KAAK,GAAG,YAAY;IACpC,IAAI,CAAC,IAAI,CAAC5B,OAAO,EAAE;MACf,IAAI,CAACD,WAAW,GAAGV,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACW,OAAO,GAAG,IAAI;IACvB;EACJ,CAAC;EACDT,SAAS,CAACW,SAAS,CAAC2B,MAAM,GAAG,YAAY;IACrC,IAAI,IAAI,CAAC7B,OAAO,EAAE;MACd,IAAI,CAACF,WAAW,IAAIT,OAAO,CAAC,CAAC,GAAG,IAAI,CAACU,WAAW;MAChD,IAAI,CAACC,OAAO,GAAG,KAAK;IACxB;EACJ,CAAC;EACDT,SAAS,CAACW,SAAS,CAAC4B,KAAK,GAAG,YAAY;IACpC,IAAI1B,IAAI,GAAG,IAAI,CAACG,KAAK;IACrB,OAAOH,IAAI,EAAE;MACT,IAAIe,QAAQ,GAAGf,IAAI,CAACK,IAAI;MACxBL,IAAI,CAACM,IAAI,GAAGN,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACC,SAAS,GAAG,IAAI;MAC7CD,IAAI,GAAGe,QAAQ;IACnB;IACA,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI;EAClC,CAAC;EACDjB,SAAS,CAACW,SAAS,CAAC6B,UAAU,GAAG,YAAY;IACzC,OAAO,IAAI,CAACxB,KAAK,IAAI,IAAI;EAC7B,CAAC;EACDhB,SAAS,CAACW,SAAS,CAAC8B,OAAO,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACrDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAACR,KAAK,CAAC,CAAC;IACZ,IAAId,QAAQ,GAAG,IAAIxB,QAAQ,CAAC6C,MAAM,EAAEC,OAAO,CAACC,IAAI,CAAC;IACjD,IAAI,CAACxB,WAAW,CAACC,QAAQ,CAAC;IAC1B,OAAOA,QAAQ;EACnB,CAAC;EACD,OAAOrB,SAAS;AACpB,CAAC,CAACL,QAAQ,CAAE;AACZ,eAAeK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}