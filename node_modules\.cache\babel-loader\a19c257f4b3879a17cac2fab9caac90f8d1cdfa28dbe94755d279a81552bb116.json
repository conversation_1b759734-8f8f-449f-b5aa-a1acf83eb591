{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, indexOf, curry, extend, normalizeCssArray, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isHighDownDispatcher, setAsHighDownDispatcher, setDefaultStateProxy, enableHoverFocus, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as helper from '../helper/treeHelper.js';\nimport Breadcrumb from './Breadcrumb.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as animationUtil from '../../util/animation.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport ChartView from '../../view/Chart.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner, convertOptionIdName } from '../../util/model.js';\nimport { windowOpen } from '../../util/format.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nvar Group = graphic.Group;\nvar Rect = graphic.Rect;\nvar DRAG_THRESHOLD = 3;\nvar PATH_LABEL_NOAMAL = 'label';\nvar PATH_UPPERLABEL_NORMAL = 'upperLabel';\n// Should larger than emphasis states lift z\nvar Z2_BASE = Z2_EMPHASIS_LIFT * 10; // Should bigger than every z2.\nvar Z2_BG = Z2_EMPHASIS_LIFT * 2;\nvar Z2_CONTENT = Z2_EMPHASIS_LIFT * 3;\nvar getStateItemStyle = makeStyleMapper([['fill', 'color'],\n// `borderColor` and `borderWidth` has been occupied,\n// so use `stroke` to indicate the stroke of the rect.\n['stroke', 'strokeColor'], ['lineWidth', 'strokeWidth'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n]);\nvar getItemStyleNormal = function (model) {\n  // Normal style props should include emphasis style props.\n  var itemStyle = getStateItemStyle(model);\n  // Clear styles set by emphasis.\n  itemStyle.stroke = itemStyle.fill = itemStyle.lineWidth = null;\n  return itemStyle;\n};\nvar inner = makeInner();\nvar TreemapView = /** @class */function (_super) {\n  __extends(TreemapView, _super);\n  function TreemapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapView.type;\n    _this._state = 'ready';\n    _this._storage = createStorage();\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var models = ecModel.findComponents({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    });\n    if (indexOf(models, seriesModel) < 0) {\n      return;\n    }\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var payloadType = payload && payload.type;\n    var layoutInfo = seriesModel.layoutInfo;\n    var isInit = !this._oldTree;\n    var thisStorage = this._storage;\n    // Mark new root when action is treemapRootToNode.\n    var reRoot = payloadType === 'treemapRootToNode' && targetInfo && thisStorage ? {\n      rootNodeGroup: thisStorage.nodeGroup[targetInfo.node.getRawIndex()],\n      direction: payload.direction\n    } : null;\n    var containerGroup = this._giveContainerGroup(layoutInfo);\n    var hasAnimation = seriesModel.get('animation');\n    var renderResult = this._doRender(containerGroup, seriesModel, reRoot);\n    hasAnimation && !isInit && (!payloadType || payloadType === 'treemapZoomToNode' || payloadType === 'treemapRootToNode') ? this._doAnimation(containerGroup, renderResult, seriesModel, reRoot) : renderResult.renderFinally();\n    this._resetController(api);\n    this._renderBreadcrumb(seriesModel, api, targetInfo);\n  };\n  TreemapView.prototype._giveContainerGroup = function (layoutInfo) {\n    var containerGroup = this._containerGroup;\n    if (!containerGroup) {\n      // FIXME\n      // 加一层containerGroup是为了clip，但是现在clip功能并没有实现。\n      containerGroup = this._containerGroup = new Group();\n      this._initEvents(containerGroup);\n      this.group.add(containerGroup);\n    }\n    containerGroup.x = layoutInfo.x;\n    containerGroup.y = layoutInfo.y;\n    return containerGroup;\n  };\n  TreemapView.prototype._doRender = function (containerGroup, seriesModel, reRoot) {\n    var thisTree = seriesModel.getData().tree;\n    var oldTree = this._oldTree;\n    // Clear last shape records.\n    var lastsForAnimation = createStorage();\n    var thisStorage = createStorage();\n    var oldStorage = this._storage;\n    var willInvisibleEls = [];\n    function doRenderNode(thisNode, oldNode, parentGroup, depth) {\n      return renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth);\n    }\n    // Notice: When thisTree and oldTree are the same tree (see list.cloneShallow),\n    // the oldTree is actually losted, so we cannot find all of the old graphic\n    // elements from tree. So we use this strategy: make element storage, move\n    // from old storage to new storage, clear old storage.\n    dualTravel(thisTree.root ? [thisTree.root] : [], oldTree && oldTree.root ? [oldTree.root] : [], containerGroup, thisTree === oldTree || !oldTree, 0);\n    // Process all removing.\n    var willDeleteEls = clearStorage(oldStorage);\n    this._oldTree = thisTree;\n    this._storage = thisStorage;\n    if (this._controllerHost) {\n      var _oldRootLayout = this.seriesModel.layoutInfo;\n      var rootLayout = thisTree.root.getLayout();\n      if (rootLayout.width === _oldRootLayout.width && rootLayout.height === _oldRootLayout.height) {\n        this._controllerHost.zoom = 1;\n      }\n    }\n    return {\n      lastsForAnimation: lastsForAnimation,\n      willDeleteEls: willDeleteEls,\n      renderFinally: renderFinally\n    };\n    function dualTravel(thisViewChildren, oldViewChildren, parentGroup, sameTree, depth) {\n      // When 'render' is triggered by action,\n      // 'this' and 'old' may be the same tree,\n      // we use rawIndex in that case.\n      if (sameTree) {\n        oldViewChildren = thisViewChildren;\n        each(thisViewChildren, function (child, index) {\n          !child.isRemoved() && processNode(index, index);\n        });\n      }\n      // Diff hierarchically (diff only in each subtree, but not whole).\n      // because, consistency of view is important.\n      else {\n        new DataDiffer(oldViewChildren, thisViewChildren, getKey, getKey).add(processNode).update(processNode).remove(curry(processNode, null)).execute();\n      }\n      function getKey(node) {\n        // Identify by name or raw index.\n        return node.getId();\n      }\n      function processNode(newIndex, oldIndex) {\n        var thisNode = newIndex != null ? thisViewChildren[newIndex] : null;\n        var oldNode = oldIndex != null ? oldViewChildren[oldIndex] : null;\n        var group = doRenderNode(thisNode, oldNode, parentGroup, depth);\n        group && dualTravel(thisNode && thisNode.viewChildren || [], oldNode && oldNode.viewChildren || [], group, sameTree, depth + 1);\n      }\n    }\n    function clearStorage(storage) {\n      var willDeleteEls = createStorage();\n      storage && each(storage, function (store, storageName) {\n        var delEls = willDeleteEls[storageName];\n        each(store, function (el) {\n          el && (delEls.push(el), inner(el).willDelete = true);\n        });\n      });\n      return willDeleteEls;\n    }\n    function renderFinally() {\n      each(willDeleteEls, function (els) {\n        each(els, function (el) {\n          el.parent && el.parent.remove(el);\n        });\n      });\n      each(willInvisibleEls, function (el) {\n        el.invisible = true;\n        // Setting invisible is for optimizing, so no need to set dirty,\n        // just mark as invisible.\n        el.dirty();\n      });\n    }\n  };\n  TreemapView.prototype._doAnimation = function (containerGroup, renderResult, seriesModel, reRoot) {\n    var durationOption = seriesModel.get('animationDurationUpdate');\n    var easingOption = seriesModel.get('animationEasing');\n    // TODO: do not support function until necessary.\n    var duration = (isFunction(durationOption) ? 0 : durationOption) || 0;\n    var easing = (isFunction(easingOption) ? null : easingOption) || 'cubicOut';\n    var animationWrap = animationUtil.createWrap();\n    // Make delete animations.\n    each(renderResult.willDeleteEls, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        if (el.invisible) {\n          return;\n        }\n        var parent = el.parent; // Always has parent, and parent is nodeGroup.\n        var target;\n        var innerStore = inner(parent);\n        if (reRoot && reRoot.direction === 'drillDown') {\n          target = parent === reRoot.rootNodeGroup\n          // This is the content element of view root.\n          // Only `content` will enter this branch, because\n          // `background` and `nodeGroup` will not be deleted.\n          ? {\n            shape: {\n              x: 0,\n              y: 0,\n              width: innerStore.nodeWidth,\n              height: innerStore.nodeHeight\n            },\n            style: {\n              opacity: 0\n            }\n          }\n          // Others.\n          : {\n            style: {\n              opacity: 0\n            }\n          };\n        } else {\n          var targetX = 0;\n          var targetY = 0;\n          if (!innerStore.willDelete) {\n            // Let node animate to right-bottom corner, cooperating with fadeout,\n            // which is appropriate for user understanding.\n            // Divided by 2 for reRoot rolling up effect.\n            targetX = innerStore.nodeWidth / 2;\n            targetY = innerStore.nodeHeight / 2;\n          }\n          target = storageName === 'nodeGroup' ? {\n            x: targetX,\n            y: targetY,\n            style: {\n              opacity: 0\n            }\n          } : {\n            shape: {\n              x: targetX,\n              y: targetY,\n              width: 0,\n              height: 0\n            },\n            style: {\n              opacity: 0\n            }\n          };\n        }\n        // TODO: do not support delay until necessary.\n        target && animationWrap.add(el, target, duration, 0, easing);\n      });\n    });\n    // Make other animations\n    each(this._storage, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        var last = renderResult.lastsForAnimation[storageName][rawIndex];\n        var target = {};\n        if (!last) {\n          return;\n        }\n        if (el instanceof graphic.Group) {\n          if (last.oldX != null) {\n            target.x = el.x;\n            target.y = el.y;\n            el.x = last.oldX;\n            el.y = last.oldY;\n          }\n        } else {\n          if (last.oldShape) {\n            target.shape = extend({}, el.shape);\n            el.setShape(last.oldShape);\n          }\n          if (last.fadein) {\n            el.setStyle('opacity', 0);\n            target.style = {\n              opacity: 1\n            };\n          }\n          // When animation is stopped for succedent animation starting,\n          // el.style.opacity might not be 1\n          else if (el.style.opacity !== 1) {\n            target.style = {\n              opacity: 1\n            };\n          }\n        }\n        animationWrap.add(el, target, duration, 0, easing);\n      });\n    }, this);\n    this._state = 'animating';\n    animationWrap.finished(bind(function () {\n      this._state = 'ready';\n      renderResult.renderFinally();\n    }, this)).start();\n  };\n  TreemapView.prototype._resetController = function (api) {\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    if (!controllerHost) {\n      this._controllerHost = {\n        target: this.group\n      };\n      controllerHost = this._controllerHost;\n    }\n    // Init controller.\n    if (!controller) {\n      controller = this._controller = new RoamController(api.getZr());\n      controller.enable(this.seriesModel.get('roam'));\n      controllerHost.zoomLimit = this.seriesModel.get('scaleLimit');\n      controllerHost.zoom = this.seriesModel.get('zoom');\n      controller.on('pan', bind(this._onPan, this));\n      controller.on('zoom', bind(this._onZoom, this));\n    }\n    var rect = new BoundingRect(0, 0, api.getWidth(), api.getHeight());\n    controller.setPointerChecker(function (e, x, y) {\n      return rect.contain(x, y);\n    });\n  };\n  TreemapView.prototype._clearController = function () {\n    var controller = this._controller;\n    this._controllerHost = null;\n    if (controller) {\n      controller.dispose();\n      controller = null;\n    }\n  };\n  TreemapView.prototype._onPan = function (e) {\n    if (this._state !== 'animating' && (Math.abs(e.dx) > DRAG_THRESHOLD || Math.abs(e.dy) > DRAG_THRESHOLD)) {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      this.api.dispatchAction({\n        type: 'treemapMove',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rootLayout.x + e.dx,\n          y: rootLayout.y + e.dy,\n          width: rootLayout.width,\n          height: rootLayout.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._onZoom = function (e) {\n    var mouseX = e.originX;\n    var mouseY = e.originY;\n    var zoomDelta = e.scale;\n    if (this._state !== 'animating') {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      var rect = new BoundingRect(rootLayout.x, rootLayout.y, rootLayout.width, rootLayout.height);\n      // scaleLimit\n      var zoomLimit = null;\n      var _controllerHost = this._controllerHost;\n      zoomLimit = _controllerHost.zoomLimit;\n      var newZoom = _controllerHost.zoom = _controllerHost.zoom || 1;\n      newZoom *= zoomDelta;\n      if (zoomLimit) {\n        var zoomMin = zoomLimit.min || 0;\n        var zoomMax = zoomLimit.max || Infinity;\n        newZoom = Math.max(Math.min(zoomMax, newZoom), zoomMin);\n      }\n      var zoomScale = newZoom / _controllerHost.zoom;\n      _controllerHost.zoom = newZoom;\n      var layoutInfo = this.seriesModel.layoutInfo;\n      // Transform mouse coord from global to containerGroup.\n      mouseX -= layoutInfo.x;\n      mouseY -= layoutInfo.y;\n      // Scale root bounding rect.\n      var m = matrix.create();\n      matrix.translate(m, m, [-mouseX, -mouseY]);\n      matrix.scale(m, m, [zoomScale, zoomScale]);\n      matrix.translate(m, m, [mouseX, mouseY]);\n      rect.applyTransform(m);\n      this.api.dispatchAction({\n        type: 'treemapRender',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._initEvents = function (containerGroup) {\n    var _this = this;\n    containerGroup.on('click', function (e) {\n      if (_this._state !== 'ready') {\n        return;\n      }\n      var nodeClick = _this.seriesModel.get('nodeClick', true);\n      if (!nodeClick) {\n        return;\n      }\n      var targetInfo = _this.findTarget(e.offsetX, e.offsetY);\n      if (!targetInfo) {\n        return;\n      }\n      var node = targetInfo.node;\n      if (node.getLayout().isLeafRoot) {\n        _this._rootToNode(targetInfo);\n      } else {\n        if (nodeClick === 'zoomToNode') {\n          _this._zoomToNode(targetInfo);\n        } else if (nodeClick === 'link') {\n          var itemModel = node.hostTree.data.getItemModel(node.dataIndex);\n          var link = itemModel.get('link', true);\n          var linkTarget = itemModel.get('target', true) || 'blank';\n          link && windowOpen(link, linkTarget);\n        }\n      }\n    }, this);\n  };\n  TreemapView.prototype._renderBreadcrumb = function (seriesModel, api, targetInfo) {\n    var _this = this;\n    if (!targetInfo) {\n      targetInfo = seriesModel.get('leafDepth', true) != null ? {\n        node: seriesModel.getViewRoot()\n      }\n      // FIXME\n      // better way?\n      // Find breadcrumb tail on center of containerGroup.\n      : this.findTarget(api.getWidth() / 2, api.getHeight() / 2);\n      if (!targetInfo) {\n        targetInfo = {\n          node: seriesModel.getData().tree.root\n        };\n      }\n    }\n    (this._breadcrumb || (this._breadcrumb = new Breadcrumb(this.group))).render(seriesModel, api, targetInfo.node, function (node) {\n      if (_this._state !== 'animating') {\n        helper.aboveViewRoot(seriesModel.getViewRoot(), node) ? _this._rootToNode({\n          node: node\n        }) : _this._zoomToNode({\n          node: node\n        });\n      }\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.remove = function () {\n    this._clearController();\n    this._containerGroup && this._containerGroup.removeAll();\n    this._storage = createStorage();\n    this._state = 'ready';\n    this._breadcrumb && this._breadcrumb.remove();\n  };\n  TreemapView.prototype.dispose = function () {\n    this._clearController();\n  };\n  TreemapView.prototype._zoomToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapZoomToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  TreemapView.prototype._rootToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapRootToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  /**\r\n   * @public\r\n   * @param {number} x Global coord x.\r\n   * @param {number} y Global coord y.\r\n   * @return {Object} info If not found, return undefined;\r\n   * @return {number} info.node Target node.\r\n   * @return {number} info.offsetX x refer to target node.\r\n   * @return {number} info.offsetY y refer to target node.\r\n   */\n  TreemapView.prototype.findTarget = function (x, y) {\n    var targetInfo;\n    var viewRoot = this.seriesModel.getViewRoot();\n    viewRoot.eachNode({\n      attr: 'viewChildren',\n      order: 'preorder'\n    }, function (node) {\n      var bgEl = this._storage.background[node.getRawIndex()];\n      // If invisible, there might be no element.\n      if (bgEl) {\n        var point = bgEl.transformCoordToLocal(x, y);\n        var shape = bgEl.shape;\n        // For performance consideration, don't use 'getBoundingRect'.\n        if (shape.x <= point[0] && point[0] <= shape.x + shape.width && shape.y <= point[1] && point[1] <= shape.y + shape.height) {\n          targetInfo = {\n            node: node,\n            offsetX: point[0],\n            offsetY: point[1]\n          };\n        } else {\n          return false; // Suppress visit subtree.\n        }\n      }\n    }, this);\n    return targetInfo;\n  };\n  TreemapView.type = 'treemap';\n  return TreemapView;\n}(ChartView);\n/**\r\n * @inner\r\n */\nfunction createStorage() {\n  return {\n    nodeGroup: [],\n    background: [],\n    content: []\n  };\n}\n/**\r\n * @inner\r\n * @return Return undefined means do not travel further.\r\n */\nfunction renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth) {\n  // Whether under viewRoot.\n  if (!thisNode) {\n    // Deleting nodes will be performed finally. This method just find\n    // element from old storage, or create new element, set them to new\n    // storage, and set styles.\n    return;\n  }\n  // -------------------------------------------------------------------\n  // Start of closure variables available in \"Procedures in renderNode\".\n  var thisLayout = thisNode.getLayout();\n  var data = seriesModel.getData();\n  var nodeModel = thisNode.getModel();\n  // Only for enabling highlight/downplay. Clear firstly.\n  // Because some node will not be rendered.\n  data.setItemGraphicEl(thisNode.dataIndex, null);\n  if (!thisLayout || !thisLayout.isInView) {\n    return;\n  }\n  var thisWidth = thisLayout.width;\n  var thisHeight = thisLayout.height;\n  var borderWidth = thisLayout.borderWidth;\n  var thisInvisible = thisLayout.invisible;\n  var thisRawIndex = thisNode.getRawIndex();\n  var oldRawIndex = oldNode && oldNode.getRawIndex();\n  var thisViewChildren = thisNode.viewChildren;\n  var upperHeight = thisLayout.upperHeight;\n  var isParent = thisViewChildren && thisViewChildren.length;\n  var itemStyleNormalModel = nodeModel.getModel('itemStyle');\n  var itemStyleEmphasisModel = nodeModel.getModel(['emphasis', 'itemStyle']);\n  var itemStyleBlurModel = nodeModel.getModel(['blur', 'itemStyle']);\n  var itemStyleSelectModel = nodeModel.getModel(['select', 'itemStyle']);\n  var borderRadius = itemStyleNormalModel.get('borderRadius') || 0;\n  // End of closure ariables available in \"Procedures in renderNode\".\n  // -----------------------------------------------------------------\n  // Node group\n  var group = giveGraphic('nodeGroup', Group);\n  if (!group) {\n    return;\n  }\n  parentGroup.add(group);\n  // x,y are not set when el is above view root.\n  group.x = thisLayout.x || 0;\n  group.y = thisLayout.y || 0;\n  group.markRedraw();\n  inner(group).nodeWidth = thisWidth;\n  inner(group).nodeHeight = thisHeight;\n  if (thisLayout.isAboveViewRoot) {\n    return group;\n  }\n  // Background\n  var bg = giveGraphic('background', Rect, depth, Z2_BG);\n  bg && renderBackground(group, bg, isParent && thisLayout.upperLabelHeight);\n  var emphasisModel = nodeModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var isDisabled = emphasisModel.get('disabled');\n  var focusOrIndices = focus === 'ancestor' ? thisNode.getAncestorsIndices() : focus === 'descendant' ? thisNode.getDescendantIndices() : focus;\n  // No children, render content.\n  if (isParent) {\n    // Because of the implementation about \"traverse\" in graphic hover style, we\n    // can not set hover listener on the \"group\" of non-leaf node. Otherwise the\n    // hover event from the descendents will be listenered.\n    if (isHighDownDispatcher(group)) {\n      setAsHighDownDispatcher(group, false);\n    }\n    if (bg) {\n      setAsHighDownDispatcher(bg, !isDisabled);\n      // Only for enabling highlight/downplay.\n      data.setItemGraphicEl(thisNode.dataIndex, bg);\n      enableHoverFocus(bg, focusOrIndices, blurScope);\n    }\n  } else {\n    var content = giveGraphic('content', Rect, depth, Z2_CONTENT);\n    content && renderContent(group, content);\n    bg.disableMorphing = true;\n    if (bg && isHighDownDispatcher(bg)) {\n      setAsHighDownDispatcher(bg, false);\n    }\n    setAsHighDownDispatcher(group, !isDisabled);\n    // Only for enabling highlight/downplay.\n    data.setItemGraphicEl(thisNode.dataIndex, group);\n    var cursorStyle = nodeModel.getShallow('cursor');\n    cursorStyle && content.attr('cursor', cursorStyle);\n    enableHoverFocus(group, focusOrIndices, blurScope);\n  }\n  return group;\n  // ----------------------------\n  // | Procedures in renderNode |\n  // ----------------------------\n  function renderBackground(group, bg, useUpperLabel) {\n    var ecData = getECData(bg);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    bg.setShape({\n      x: 0,\n      y: 0,\n      width: thisWidth,\n      height: thisHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(bg);\n    } else {\n      bg.invisible = false;\n      var style = thisNode.getVisual('style');\n      var visualBorderColor = style.stroke;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualBorderColor;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      emphasisStyle.fill = itemStyleEmphasisModel.get('borderColor');\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      blurStyle.fill = itemStyleBlurModel.get('borderColor');\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      selectStyle.fill = itemStyleSelectModel.get('borderColor');\n      if (useUpperLabel) {\n        var upperLabelWidth = thisWidth - 2 * borderWidth;\n        prepareText(\n        // PENDING: convert ZRColor to ColorString for text.\n        bg, visualBorderColor, style.opacity, {\n          x: borderWidth,\n          y: 0,\n          width: upperLabelWidth,\n          height: upperHeight\n        });\n      }\n      // For old bg.\n      else {\n        bg.removeTextContent();\n      }\n      bg.setStyle(normalStyle);\n      bg.ensureState('emphasis').style = emphasisStyle;\n      bg.ensureState('blur').style = blurStyle;\n      bg.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(bg);\n    }\n    group.add(bg);\n  }\n  function renderContent(group, content) {\n    var ecData = getECData(content);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    var contentWidth = Math.max(thisWidth - 2 * borderWidth, 0);\n    var contentHeight = Math.max(thisHeight - 2 * borderWidth, 0);\n    content.culling = true;\n    content.setShape({\n      x: borderWidth,\n      y: borderWidth,\n      width: contentWidth,\n      height: contentHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(content);\n    } else {\n      content.invisible = false;\n      var nodeStyle = thisNode.getVisual('style');\n      var visualColor = nodeStyle.fill;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualColor;\n      normalStyle.decal = nodeStyle.decal;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      // PENDING: convert ZRColor to ColorString for text.\n      prepareText(content, visualColor, nodeStyle.opacity, null);\n      content.setStyle(normalStyle);\n      content.ensureState('emphasis').style = emphasisStyle;\n      content.ensureState('blur').style = blurStyle;\n      content.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(content);\n    }\n    group.add(content);\n  }\n  function processInvisible(element) {\n    // Delay invisible setting utill animation finished,\n    // avoid element vanish suddenly before animation.\n    !element.invisible && willInvisibleEls.push(element);\n  }\n  function prepareText(rectEl, visualColor, visualOpacity,\n  // Can be null/undefined\n  upperLabelRect) {\n    var normalLabelModel = nodeModel.getModel(upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL);\n    var defaultText = convertOptionIdName(nodeModel.get('name'), null);\n    var isShow = normalLabelModel.getShallow('show');\n    setLabelStyle(rectEl, getLabelStatesModels(nodeModel, upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL), {\n      defaultText: isShow ? defaultText : null,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      labelFetcher: seriesModel,\n      labelDataIndex: thisNode.dataIndex\n    });\n    var textEl = rectEl.getTextContent();\n    if (!textEl) {\n      return;\n    }\n    var textStyle = textEl.style;\n    var textPadding = normalizeCssArray(textStyle.padding || 0);\n    if (upperLabelRect) {\n      rectEl.setTextConfig({\n        layoutRect: upperLabelRect\n      });\n      textEl.disableLabelLayout = true;\n    }\n    textEl.beforeUpdate = function () {\n      var width = Math.max((upperLabelRect ? upperLabelRect.width : rectEl.shape.width) - textPadding[1] - textPadding[3], 0);\n      var height = Math.max((upperLabelRect ? upperLabelRect.height : rectEl.shape.height) - textPadding[0] - textPadding[2], 0);\n      if (textStyle.width !== width || textStyle.height !== height) {\n        textEl.setStyle({\n          width: width,\n          height: height\n        });\n      }\n    };\n    textStyle.truncateMinChar = 2;\n    textStyle.lineOverflow = 'truncate';\n    addDrillDownIcon(textStyle, upperLabelRect, thisLayout);\n    var textEmphasisState = textEl.getState('emphasis');\n    addDrillDownIcon(textEmphasisState ? textEmphasisState.style : null, upperLabelRect, thisLayout);\n  }\n  function addDrillDownIcon(style, upperLabelRect, thisLayout) {\n    var text = style ? style.text : null;\n    if (!upperLabelRect && thisLayout.isLeafRoot && text != null) {\n      var iconChar = seriesModel.get('drillDownIcon', true);\n      style.text = iconChar ? iconChar + ' ' + text : text;\n    }\n  }\n  function giveGraphic(storageName, Ctor, depth, z) {\n    var element = oldRawIndex != null && oldStorage[storageName][oldRawIndex];\n    var lasts = lastsForAnimation[storageName];\n    if (element) {\n      // Remove from oldStorage\n      oldStorage[storageName][oldRawIndex] = null;\n      prepareAnimationWhenHasOld(lasts, element);\n    }\n    // If invisible and no old element, do not create new element (for optimizing).\n    else if (!thisInvisible) {\n      element = new Ctor();\n      if (element instanceof Displayable) {\n        element.z2 = calculateZ2(depth, z);\n      }\n      prepareAnimationWhenNoOld(lasts, element);\n    }\n    // Set to thisStorage\n    return thisStorage[storageName][thisRawIndex] = element;\n  }\n  function prepareAnimationWhenHasOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    if (element instanceof Group) {\n      lastCfg.oldX = element.x;\n      lastCfg.oldY = element.y;\n    } else {\n      lastCfg.oldShape = extend({}, element.shape);\n    }\n  }\n  // If a element is new, we need to find the animation start point carefully,\n  // otherwise it will looks strange when 'zoomToNode'.\n  function prepareAnimationWhenNoOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    var parentNode = thisNode.parentNode;\n    var isGroup = element instanceof graphic.Group;\n    if (parentNode && (!reRoot || reRoot.direction === 'drillDown')) {\n      var parentOldX = 0;\n      var parentOldY = 0;\n      // New nodes appear from right-bottom corner in 'zoomToNode' animation.\n      // For convenience, get old bounding rect from background.\n      var parentOldBg = lastsForAnimation.background[parentNode.getRawIndex()];\n      if (!reRoot && parentOldBg && parentOldBg.oldShape) {\n        parentOldX = parentOldBg.oldShape.width;\n        parentOldY = parentOldBg.oldShape.height;\n      }\n      // When no parent old shape found, its parent is new too,\n      // so we can just use {x:0, y:0}.\n      if (isGroup) {\n        lastCfg.oldX = 0;\n        lastCfg.oldY = parentOldY;\n      } else {\n        lastCfg.oldShape = {\n          x: parentOldX,\n          y: parentOldY,\n          width: 0,\n          height: 0\n        };\n      }\n    }\n    // Fade in, user can be aware that these nodes are new.\n    lastCfg.fadein = !isGroup;\n  }\n}\n// We cannot set all background with the same z, because the behaviour of\n// drill down and roll up differ background creation sequence from tree\n// hierarchy sequence, which cause lower background elements to overlap\n// upper ones. So we calculate z based on depth.\n// Moreover, we try to shrink down z interval to [0, 1] to avoid that\n// treemap with large z overlaps other components.\nfunction calculateZ2(depth, z2InLevel) {\n  return depth * Z2_BASE + z2InLevel;\n}\nexport default TreemapView;", "map": {"version": 3, "names": ["__extends", "bind", "each", "indexOf", "curry", "extend", "normalizeCssArray", "isFunction", "graphic", "getECData", "is<PERSON>ighD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "setDefaultStateProxy", "enableHoverFocus", "Z2_EMPHASIS_LIFT", "<PERSON><PERSON><PERSON><PERSON>", "helper", "Breadcrumb", "RoamController", "BoundingRect", "matrix", "animationUtil", "makeStyleMapper", "ChartView", "Displayable", "makeInner", "convertOptionIdName", "windowOpen", "setLabelStyle", "getLabelStatesModels", "Group", "Rect", "DRAG_THRESHOLD", "PATH_LABEL_NOAMAL", "PATH_UPPERLABEL_NORMAL", "Z2_BASE", "Z2_BG", "Z2_CONTENT", "getStateItemStyle", "getItemStyleNormal", "model", "itemStyle", "stroke", "fill", "lineWidth", "inner", "TreemapView", "_super", "_this", "apply", "arguments", "type", "_state", "_storage", "createStorage", "prototype", "render", "seriesModel", "ecModel", "api", "payload", "models", "findComponents", "mainType", "subType", "query", "types", "targetInfo", "retrieveTargetInfo", "payloadType", "layoutInfo", "isInit", "_old<PERSON><PERSON>", "thisStorage", "reRoot", "rootNodeGroup", "nodeGroup", "node", "getRawIndex", "direction", "containerGroup", "_giveContainerGroup", "hasAnimation", "get", "renderResult", "_doRender", "_doAnimation", "renderFinally", "_resetController", "_renderBreadcrumb", "_containerGroup", "_initEvents", "group", "add", "x", "y", "thisTree", "getData", "tree", "oldTree", "lastsForAnimation", "oldStorage", "willInvisibleEls", "doRenderNode", "thisNode", "oldNode", "parentGroup", "depth", "renderNode", "dualTravel", "root", "willDeleteEls", "clearStorage", "_controllerHost", "_oldRootLayout", "rootLayout", "getLayout", "width", "height", "zoom", "this<PERSON>iew<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sameTree", "child", "index", "isRemoved", "processNode", "<PERSON><PERSON><PERSON>", "update", "remove", "execute", "getId", "newIndex", "oldIndex", "viewChil<PERSON>n", "storage", "store", "storageName", "delEls", "el", "push", "will<PERSON>elete", "els", "parent", "invisible", "dirty", "durationOption", "easingOption", "duration", "easing", "animationWrap", "createWrap", "rawIndex", "target", "innerStore", "shape", "nodeWidth", "nodeHeight", "style", "opacity", "targetX", "targetY", "last", "oldX", "oldY", "oldShape", "setShape", "fadein", "setStyle", "finished", "start", "controller", "_controller", "controllerHost", "getZr", "enable", "zoomLimit", "on", "_on<PERSON>an", "_onZoom", "rect", "getWidth", "getHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "contain", "_clearController", "dispose", "Math", "abs", "dx", "dy", "dispatchAction", "from", "uid", "seriesId", "id", "rootRect", "mouseX", "originX", "mouseY", "originY", "zoomDel<PERSON>", "scale", "newZoom", "zoomMin", "min", "zoomMax", "max", "Infinity", "zoomScale", "m", "create", "translate", "applyTransform", "nodeClick", "<PERSON><PERSON><PERSON><PERSON>", "offsetX", "offsetY", "isLeafRoot", "_rootToNode", "_zoomToNode", "itemModel", "hostTree", "data", "getItemModel", "dataIndex", "link", "linkTarget", "getViewRoot", "_breadcrumb", "aboveViewRoot", "removeAll", "targetNode", "viewRoot", "eachNode", "attr", "order", "bgEl", "background", "point", "transformCoordToLocal", "content", "thisLayout", "nodeModel", "getModel", "setItemGraphicEl", "isInView", "thisWidth", "thisHeight", "borderWidth", "thisInvisible", "thisRawIndex", "oldRawIndex", "upperHeight", "isParent", "length", "itemStyleNormalModel", "itemStyleEmphasisModel", "itemStyleBlurModel", "itemStyleSelectModel", "borderRadius", "giveGraphic", "mark<PERSON><PERSON><PERSON>", "isAboveViewRoot", "bg", "renderBackground", "upperLabelHeight", "emphasisModel", "focus", "blurScope", "isDisabled", "focusOrIndices", "getAncestorsIndices", "getDescendantIndices", "renderContent", "disableMorphing", "cursorStyle", "getShallow", "useUpperLabel", "ecData", "seriesIndex", "r", "processInvisible", "getVisual", "visualBorderColor", "normalStyle", "emphasisStyle", "blurStyle", "selectStyle", "upperLabelWidth", "prepareText", "removeTextContent", "ensureState", "contentWidth", "contentHeight", "culling", "nodeStyle", "visualColor", "decal", "element", "rectEl", "visualOpacity", "upperLabelRect", "normalLabelModel", "defaultText", "isShow", "inheritColor", "defaultOpacity", "labelFetcher", "labelDataIndex", "textEl", "getTextContent", "textStyle", "textPadding", "padding", "setTextConfig", "layoutRect", "disableLabelLayout", "beforeUpdate", "truncateMinChar", "lineOverflow", "addDrillDownIcon", "textEmphasisState", "getState", "text", "iconChar", "Ctor", "z", "lasts", "prepareAnimationWhenHasOld", "z2", "calculateZ2", "prepareAnimationWhenNoOld", "lastCfg", "parentNode", "isGroup", "parentOldX", "parentOldY", "parentOldBg", "z2InLevel"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/treemap/TreemapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, indexOf, curry, extend, normalizeCssArray, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isHighDownDispatcher, setAsHighDownDispatcher, setDefaultStateProxy, enableHoverFocus, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as helper from '../helper/treeHelper.js';\nimport Breadcrumb from './Breadcrumb.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as animationUtil from '../../util/animation.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport ChartView from '../../view/Chart.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner, convertOptionIdName } from '../../util/model.js';\nimport { windowOpen } from '../../util/format.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nvar Group = graphic.Group;\nvar Rect = graphic.Rect;\nvar DRAG_THRESHOLD = 3;\nvar PATH_LABEL_NOAMAL = 'label';\nvar PATH_UPPERLABEL_NORMAL = 'upperLabel';\n// Should larger than emphasis states lift z\nvar Z2_BASE = Z2_EMPHASIS_LIFT * 10; // Should bigger than every z2.\nvar Z2_BG = Z2_EMPHASIS_LIFT * 2;\nvar Z2_CONTENT = Z2_EMPHASIS_LIFT * 3;\nvar getStateItemStyle = makeStyleMapper([['fill', 'color'],\n// `borderColor` and `borderWidth` has been occupied,\n// so use `stroke` to indicate the stroke of the rect.\n['stroke', 'strokeColor'], ['lineWidth', 'strokeWidth'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n]);\nvar getItemStyleNormal = function (model) {\n  // Normal style props should include emphasis style props.\n  var itemStyle = getStateItemStyle(model);\n  // Clear styles set by emphasis.\n  itemStyle.stroke = itemStyle.fill = itemStyle.lineWidth = null;\n  return itemStyle;\n};\nvar inner = makeInner();\nvar TreemapView = /** @class */function (_super) {\n  __extends(TreemapView, _super);\n  function TreemapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapView.type;\n    _this._state = 'ready';\n    _this._storage = createStorage();\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var models = ecModel.findComponents({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    });\n    if (indexOf(models, seriesModel) < 0) {\n      return;\n    }\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var payloadType = payload && payload.type;\n    var layoutInfo = seriesModel.layoutInfo;\n    var isInit = !this._oldTree;\n    var thisStorage = this._storage;\n    // Mark new root when action is treemapRootToNode.\n    var reRoot = payloadType === 'treemapRootToNode' && targetInfo && thisStorage ? {\n      rootNodeGroup: thisStorage.nodeGroup[targetInfo.node.getRawIndex()],\n      direction: payload.direction\n    } : null;\n    var containerGroup = this._giveContainerGroup(layoutInfo);\n    var hasAnimation = seriesModel.get('animation');\n    var renderResult = this._doRender(containerGroup, seriesModel, reRoot);\n    hasAnimation && !isInit && (!payloadType || payloadType === 'treemapZoomToNode' || payloadType === 'treemapRootToNode') ? this._doAnimation(containerGroup, renderResult, seriesModel, reRoot) : renderResult.renderFinally();\n    this._resetController(api);\n    this._renderBreadcrumb(seriesModel, api, targetInfo);\n  };\n  TreemapView.prototype._giveContainerGroup = function (layoutInfo) {\n    var containerGroup = this._containerGroup;\n    if (!containerGroup) {\n      // FIXME\n      // 加一层containerGroup是为了clip，但是现在clip功能并没有实现。\n      containerGroup = this._containerGroup = new Group();\n      this._initEvents(containerGroup);\n      this.group.add(containerGroup);\n    }\n    containerGroup.x = layoutInfo.x;\n    containerGroup.y = layoutInfo.y;\n    return containerGroup;\n  };\n  TreemapView.prototype._doRender = function (containerGroup, seriesModel, reRoot) {\n    var thisTree = seriesModel.getData().tree;\n    var oldTree = this._oldTree;\n    // Clear last shape records.\n    var lastsForAnimation = createStorage();\n    var thisStorage = createStorage();\n    var oldStorage = this._storage;\n    var willInvisibleEls = [];\n    function doRenderNode(thisNode, oldNode, parentGroup, depth) {\n      return renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth);\n    }\n    // Notice: When thisTree and oldTree are the same tree (see list.cloneShallow),\n    // the oldTree is actually losted, so we cannot find all of the old graphic\n    // elements from tree. So we use this strategy: make element storage, move\n    // from old storage to new storage, clear old storage.\n    dualTravel(thisTree.root ? [thisTree.root] : [], oldTree && oldTree.root ? [oldTree.root] : [], containerGroup, thisTree === oldTree || !oldTree, 0);\n    // Process all removing.\n    var willDeleteEls = clearStorage(oldStorage);\n    this._oldTree = thisTree;\n    this._storage = thisStorage;\n    if (this._controllerHost) {\n      var _oldRootLayout = this.seriesModel.layoutInfo;\n      var rootLayout = thisTree.root.getLayout();\n      if (rootLayout.width === _oldRootLayout.width && rootLayout.height === _oldRootLayout.height) {\n        this._controllerHost.zoom = 1;\n      }\n    }\n    return {\n      lastsForAnimation: lastsForAnimation,\n      willDeleteEls: willDeleteEls,\n      renderFinally: renderFinally\n    };\n    function dualTravel(thisViewChildren, oldViewChildren, parentGroup, sameTree, depth) {\n      // When 'render' is triggered by action,\n      // 'this' and 'old' may be the same tree,\n      // we use rawIndex in that case.\n      if (sameTree) {\n        oldViewChildren = thisViewChildren;\n        each(thisViewChildren, function (child, index) {\n          !child.isRemoved() && processNode(index, index);\n        });\n      }\n      // Diff hierarchically (diff only in each subtree, but not whole).\n      // because, consistency of view is important.\n      else {\n        new DataDiffer(oldViewChildren, thisViewChildren, getKey, getKey).add(processNode).update(processNode).remove(curry(processNode, null)).execute();\n      }\n      function getKey(node) {\n        // Identify by name or raw index.\n        return node.getId();\n      }\n      function processNode(newIndex, oldIndex) {\n        var thisNode = newIndex != null ? thisViewChildren[newIndex] : null;\n        var oldNode = oldIndex != null ? oldViewChildren[oldIndex] : null;\n        var group = doRenderNode(thisNode, oldNode, parentGroup, depth);\n        group && dualTravel(thisNode && thisNode.viewChildren || [], oldNode && oldNode.viewChildren || [], group, sameTree, depth + 1);\n      }\n    }\n    function clearStorage(storage) {\n      var willDeleteEls = createStorage();\n      storage && each(storage, function (store, storageName) {\n        var delEls = willDeleteEls[storageName];\n        each(store, function (el) {\n          el && (delEls.push(el), inner(el).willDelete = true);\n        });\n      });\n      return willDeleteEls;\n    }\n    function renderFinally() {\n      each(willDeleteEls, function (els) {\n        each(els, function (el) {\n          el.parent && el.parent.remove(el);\n        });\n      });\n      each(willInvisibleEls, function (el) {\n        el.invisible = true;\n        // Setting invisible is for optimizing, so no need to set dirty,\n        // just mark as invisible.\n        el.dirty();\n      });\n    }\n  };\n  TreemapView.prototype._doAnimation = function (containerGroup, renderResult, seriesModel, reRoot) {\n    var durationOption = seriesModel.get('animationDurationUpdate');\n    var easingOption = seriesModel.get('animationEasing');\n    // TODO: do not support function until necessary.\n    var duration = (isFunction(durationOption) ? 0 : durationOption) || 0;\n    var easing = (isFunction(easingOption) ? null : easingOption) || 'cubicOut';\n    var animationWrap = animationUtil.createWrap();\n    // Make delete animations.\n    each(renderResult.willDeleteEls, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        if (el.invisible) {\n          return;\n        }\n        var parent = el.parent; // Always has parent, and parent is nodeGroup.\n        var target;\n        var innerStore = inner(parent);\n        if (reRoot && reRoot.direction === 'drillDown') {\n          target = parent === reRoot.rootNodeGroup\n          // This is the content element of view root.\n          // Only `content` will enter this branch, because\n          // `background` and `nodeGroup` will not be deleted.\n          ? {\n            shape: {\n              x: 0,\n              y: 0,\n              width: innerStore.nodeWidth,\n              height: innerStore.nodeHeight\n            },\n            style: {\n              opacity: 0\n            }\n          }\n          // Others.\n          : {\n            style: {\n              opacity: 0\n            }\n          };\n        } else {\n          var targetX = 0;\n          var targetY = 0;\n          if (!innerStore.willDelete) {\n            // Let node animate to right-bottom corner, cooperating with fadeout,\n            // which is appropriate for user understanding.\n            // Divided by 2 for reRoot rolling up effect.\n            targetX = innerStore.nodeWidth / 2;\n            targetY = innerStore.nodeHeight / 2;\n          }\n          target = storageName === 'nodeGroup' ? {\n            x: targetX,\n            y: targetY,\n            style: {\n              opacity: 0\n            }\n          } : {\n            shape: {\n              x: targetX,\n              y: targetY,\n              width: 0,\n              height: 0\n            },\n            style: {\n              opacity: 0\n            }\n          };\n        }\n        // TODO: do not support delay until necessary.\n        target && animationWrap.add(el, target, duration, 0, easing);\n      });\n    });\n    // Make other animations\n    each(this._storage, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        var last = renderResult.lastsForAnimation[storageName][rawIndex];\n        var target = {};\n        if (!last) {\n          return;\n        }\n        if (el instanceof graphic.Group) {\n          if (last.oldX != null) {\n            target.x = el.x;\n            target.y = el.y;\n            el.x = last.oldX;\n            el.y = last.oldY;\n          }\n        } else {\n          if (last.oldShape) {\n            target.shape = extend({}, el.shape);\n            el.setShape(last.oldShape);\n          }\n          if (last.fadein) {\n            el.setStyle('opacity', 0);\n            target.style = {\n              opacity: 1\n            };\n          }\n          // When animation is stopped for succedent animation starting,\n          // el.style.opacity might not be 1\n          else if (el.style.opacity !== 1) {\n            target.style = {\n              opacity: 1\n            };\n          }\n        }\n        animationWrap.add(el, target, duration, 0, easing);\n      });\n    }, this);\n    this._state = 'animating';\n    animationWrap.finished(bind(function () {\n      this._state = 'ready';\n      renderResult.renderFinally();\n    }, this)).start();\n  };\n  TreemapView.prototype._resetController = function (api) {\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    if (!controllerHost) {\n      this._controllerHost = {\n        target: this.group\n      };\n      controllerHost = this._controllerHost;\n    }\n    // Init controller.\n    if (!controller) {\n      controller = this._controller = new RoamController(api.getZr());\n      controller.enable(this.seriesModel.get('roam'));\n      controllerHost.zoomLimit = this.seriesModel.get('scaleLimit');\n      controllerHost.zoom = this.seriesModel.get('zoom');\n      controller.on('pan', bind(this._onPan, this));\n      controller.on('zoom', bind(this._onZoom, this));\n    }\n    var rect = new BoundingRect(0, 0, api.getWidth(), api.getHeight());\n    controller.setPointerChecker(function (e, x, y) {\n      return rect.contain(x, y);\n    });\n  };\n  TreemapView.prototype._clearController = function () {\n    var controller = this._controller;\n    this._controllerHost = null;\n    if (controller) {\n      controller.dispose();\n      controller = null;\n    }\n  };\n  TreemapView.prototype._onPan = function (e) {\n    if (this._state !== 'animating' && (Math.abs(e.dx) > DRAG_THRESHOLD || Math.abs(e.dy) > DRAG_THRESHOLD)) {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      this.api.dispatchAction({\n        type: 'treemapMove',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rootLayout.x + e.dx,\n          y: rootLayout.y + e.dy,\n          width: rootLayout.width,\n          height: rootLayout.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._onZoom = function (e) {\n    var mouseX = e.originX;\n    var mouseY = e.originY;\n    var zoomDelta = e.scale;\n    if (this._state !== 'animating') {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      var rect = new BoundingRect(rootLayout.x, rootLayout.y, rootLayout.width, rootLayout.height);\n      // scaleLimit\n      var zoomLimit = null;\n      var _controllerHost = this._controllerHost;\n      zoomLimit = _controllerHost.zoomLimit;\n      var newZoom = _controllerHost.zoom = _controllerHost.zoom || 1;\n      newZoom *= zoomDelta;\n      if (zoomLimit) {\n        var zoomMin = zoomLimit.min || 0;\n        var zoomMax = zoomLimit.max || Infinity;\n        newZoom = Math.max(Math.min(zoomMax, newZoom), zoomMin);\n      }\n      var zoomScale = newZoom / _controllerHost.zoom;\n      _controllerHost.zoom = newZoom;\n      var layoutInfo = this.seriesModel.layoutInfo;\n      // Transform mouse coord from global to containerGroup.\n      mouseX -= layoutInfo.x;\n      mouseY -= layoutInfo.y;\n      // Scale root bounding rect.\n      var m = matrix.create();\n      matrix.translate(m, m, [-mouseX, -mouseY]);\n      matrix.scale(m, m, [zoomScale, zoomScale]);\n      matrix.translate(m, m, [mouseX, mouseY]);\n      rect.applyTransform(m);\n      this.api.dispatchAction({\n        type: 'treemapRender',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._initEvents = function (containerGroup) {\n    var _this = this;\n    containerGroup.on('click', function (e) {\n      if (_this._state !== 'ready') {\n        return;\n      }\n      var nodeClick = _this.seriesModel.get('nodeClick', true);\n      if (!nodeClick) {\n        return;\n      }\n      var targetInfo = _this.findTarget(e.offsetX, e.offsetY);\n      if (!targetInfo) {\n        return;\n      }\n      var node = targetInfo.node;\n      if (node.getLayout().isLeafRoot) {\n        _this._rootToNode(targetInfo);\n      } else {\n        if (nodeClick === 'zoomToNode') {\n          _this._zoomToNode(targetInfo);\n        } else if (nodeClick === 'link') {\n          var itemModel = node.hostTree.data.getItemModel(node.dataIndex);\n          var link = itemModel.get('link', true);\n          var linkTarget = itemModel.get('target', true) || 'blank';\n          link && windowOpen(link, linkTarget);\n        }\n      }\n    }, this);\n  };\n  TreemapView.prototype._renderBreadcrumb = function (seriesModel, api, targetInfo) {\n    var _this = this;\n    if (!targetInfo) {\n      targetInfo = seriesModel.get('leafDepth', true) != null ? {\n        node: seriesModel.getViewRoot()\n      }\n      // FIXME\n      // better way?\n      // Find breadcrumb tail on center of containerGroup.\n      : this.findTarget(api.getWidth() / 2, api.getHeight() / 2);\n      if (!targetInfo) {\n        targetInfo = {\n          node: seriesModel.getData().tree.root\n        };\n      }\n    }\n    (this._breadcrumb || (this._breadcrumb = new Breadcrumb(this.group))).render(seriesModel, api, targetInfo.node, function (node) {\n      if (_this._state !== 'animating') {\n        helper.aboveViewRoot(seriesModel.getViewRoot(), node) ? _this._rootToNode({\n          node: node\n        }) : _this._zoomToNode({\n          node: node\n        });\n      }\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.remove = function () {\n    this._clearController();\n    this._containerGroup && this._containerGroup.removeAll();\n    this._storage = createStorage();\n    this._state = 'ready';\n    this._breadcrumb && this._breadcrumb.remove();\n  };\n  TreemapView.prototype.dispose = function () {\n    this._clearController();\n  };\n  TreemapView.prototype._zoomToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapZoomToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  TreemapView.prototype._rootToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapRootToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  /**\r\n   * @public\r\n   * @param {number} x Global coord x.\r\n   * @param {number} y Global coord y.\r\n   * @return {Object} info If not found, return undefined;\r\n   * @return {number} info.node Target node.\r\n   * @return {number} info.offsetX x refer to target node.\r\n   * @return {number} info.offsetY y refer to target node.\r\n   */\n  TreemapView.prototype.findTarget = function (x, y) {\n    var targetInfo;\n    var viewRoot = this.seriesModel.getViewRoot();\n    viewRoot.eachNode({\n      attr: 'viewChildren',\n      order: 'preorder'\n    }, function (node) {\n      var bgEl = this._storage.background[node.getRawIndex()];\n      // If invisible, there might be no element.\n      if (bgEl) {\n        var point = bgEl.transformCoordToLocal(x, y);\n        var shape = bgEl.shape;\n        // For performance consideration, don't use 'getBoundingRect'.\n        if (shape.x <= point[0] && point[0] <= shape.x + shape.width && shape.y <= point[1] && point[1] <= shape.y + shape.height) {\n          targetInfo = {\n            node: node,\n            offsetX: point[0],\n            offsetY: point[1]\n          };\n        } else {\n          return false; // Suppress visit subtree.\n        }\n      }\n    }, this);\n    return targetInfo;\n  };\n  TreemapView.type = 'treemap';\n  return TreemapView;\n}(ChartView);\n/**\r\n * @inner\r\n */\nfunction createStorage() {\n  return {\n    nodeGroup: [],\n    background: [],\n    content: []\n  };\n}\n/**\r\n * @inner\r\n * @return Return undefined means do not travel further.\r\n */\nfunction renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth) {\n  // Whether under viewRoot.\n  if (!thisNode) {\n    // Deleting nodes will be performed finally. This method just find\n    // element from old storage, or create new element, set them to new\n    // storage, and set styles.\n    return;\n  }\n  // -------------------------------------------------------------------\n  // Start of closure variables available in \"Procedures in renderNode\".\n  var thisLayout = thisNode.getLayout();\n  var data = seriesModel.getData();\n  var nodeModel = thisNode.getModel();\n  // Only for enabling highlight/downplay. Clear firstly.\n  // Because some node will not be rendered.\n  data.setItemGraphicEl(thisNode.dataIndex, null);\n  if (!thisLayout || !thisLayout.isInView) {\n    return;\n  }\n  var thisWidth = thisLayout.width;\n  var thisHeight = thisLayout.height;\n  var borderWidth = thisLayout.borderWidth;\n  var thisInvisible = thisLayout.invisible;\n  var thisRawIndex = thisNode.getRawIndex();\n  var oldRawIndex = oldNode && oldNode.getRawIndex();\n  var thisViewChildren = thisNode.viewChildren;\n  var upperHeight = thisLayout.upperHeight;\n  var isParent = thisViewChildren && thisViewChildren.length;\n  var itemStyleNormalModel = nodeModel.getModel('itemStyle');\n  var itemStyleEmphasisModel = nodeModel.getModel(['emphasis', 'itemStyle']);\n  var itemStyleBlurModel = nodeModel.getModel(['blur', 'itemStyle']);\n  var itemStyleSelectModel = nodeModel.getModel(['select', 'itemStyle']);\n  var borderRadius = itemStyleNormalModel.get('borderRadius') || 0;\n  // End of closure ariables available in \"Procedures in renderNode\".\n  // -----------------------------------------------------------------\n  // Node group\n  var group = giveGraphic('nodeGroup', Group);\n  if (!group) {\n    return;\n  }\n  parentGroup.add(group);\n  // x,y are not set when el is above view root.\n  group.x = thisLayout.x || 0;\n  group.y = thisLayout.y || 0;\n  group.markRedraw();\n  inner(group).nodeWidth = thisWidth;\n  inner(group).nodeHeight = thisHeight;\n  if (thisLayout.isAboveViewRoot) {\n    return group;\n  }\n  // Background\n  var bg = giveGraphic('background', Rect, depth, Z2_BG);\n  bg && renderBackground(group, bg, isParent && thisLayout.upperLabelHeight);\n  var emphasisModel = nodeModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var isDisabled = emphasisModel.get('disabled');\n  var focusOrIndices = focus === 'ancestor' ? thisNode.getAncestorsIndices() : focus === 'descendant' ? thisNode.getDescendantIndices() : focus;\n  // No children, render content.\n  if (isParent) {\n    // Because of the implementation about \"traverse\" in graphic hover style, we\n    // can not set hover listener on the \"group\" of non-leaf node. Otherwise the\n    // hover event from the descendents will be listenered.\n    if (isHighDownDispatcher(group)) {\n      setAsHighDownDispatcher(group, false);\n    }\n    if (bg) {\n      setAsHighDownDispatcher(bg, !isDisabled);\n      // Only for enabling highlight/downplay.\n      data.setItemGraphicEl(thisNode.dataIndex, bg);\n      enableHoverFocus(bg, focusOrIndices, blurScope);\n    }\n  } else {\n    var content = giveGraphic('content', Rect, depth, Z2_CONTENT);\n    content && renderContent(group, content);\n    bg.disableMorphing = true;\n    if (bg && isHighDownDispatcher(bg)) {\n      setAsHighDownDispatcher(bg, false);\n    }\n    setAsHighDownDispatcher(group, !isDisabled);\n    // Only for enabling highlight/downplay.\n    data.setItemGraphicEl(thisNode.dataIndex, group);\n    var cursorStyle = nodeModel.getShallow('cursor');\n    cursorStyle && content.attr('cursor', cursorStyle);\n    enableHoverFocus(group, focusOrIndices, blurScope);\n  }\n  return group;\n  // ----------------------------\n  // | Procedures in renderNode |\n  // ----------------------------\n  function renderBackground(group, bg, useUpperLabel) {\n    var ecData = getECData(bg);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    bg.setShape({\n      x: 0,\n      y: 0,\n      width: thisWidth,\n      height: thisHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(bg);\n    } else {\n      bg.invisible = false;\n      var style = thisNode.getVisual('style');\n      var visualBorderColor = style.stroke;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualBorderColor;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      emphasisStyle.fill = itemStyleEmphasisModel.get('borderColor');\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      blurStyle.fill = itemStyleBlurModel.get('borderColor');\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      selectStyle.fill = itemStyleSelectModel.get('borderColor');\n      if (useUpperLabel) {\n        var upperLabelWidth = thisWidth - 2 * borderWidth;\n        prepareText(\n        // PENDING: convert ZRColor to ColorString for text.\n        bg, visualBorderColor, style.opacity, {\n          x: borderWidth,\n          y: 0,\n          width: upperLabelWidth,\n          height: upperHeight\n        });\n      }\n      // For old bg.\n      else {\n        bg.removeTextContent();\n      }\n      bg.setStyle(normalStyle);\n      bg.ensureState('emphasis').style = emphasisStyle;\n      bg.ensureState('blur').style = blurStyle;\n      bg.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(bg);\n    }\n    group.add(bg);\n  }\n  function renderContent(group, content) {\n    var ecData = getECData(content);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    var contentWidth = Math.max(thisWidth - 2 * borderWidth, 0);\n    var contentHeight = Math.max(thisHeight - 2 * borderWidth, 0);\n    content.culling = true;\n    content.setShape({\n      x: borderWidth,\n      y: borderWidth,\n      width: contentWidth,\n      height: contentHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(content);\n    } else {\n      content.invisible = false;\n      var nodeStyle = thisNode.getVisual('style');\n      var visualColor = nodeStyle.fill;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualColor;\n      normalStyle.decal = nodeStyle.decal;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      // PENDING: convert ZRColor to ColorString for text.\n      prepareText(content, visualColor, nodeStyle.opacity, null);\n      content.setStyle(normalStyle);\n      content.ensureState('emphasis').style = emphasisStyle;\n      content.ensureState('blur').style = blurStyle;\n      content.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(content);\n    }\n    group.add(content);\n  }\n  function processInvisible(element) {\n    // Delay invisible setting utill animation finished,\n    // avoid element vanish suddenly before animation.\n    !element.invisible && willInvisibleEls.push(element);\n  }\n  function prepareText(rectEl, visualColor, visualOpacity,\n  // Can be null/undefined\n  upperLabelRect) {\n    var normalLabelModel = nodeModel.getModel(upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL);\n    var defaultText = convertOptionIdName(nodeModel.get('name'), null);\n    var isShow = normalLabelModel.getShallow('show');\n    setLabelStyle(rectEl, getLabelStatesModels(nodeModel, upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL), {\n      defaultText: isShow ? defaultText : null,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      labelFetcher: seriesModel,\n      labelDataIndex: thisNode.dataIndex\n    });\n    var textEl = rectEl.getTextContent();\n    if (!textEl) {\n      return;\n    }\n    var textStyle = textEl.style;\n    var textPadding = normalizeCssArray(textStyle.padding || 0);\n    if (upperLabelRect) {\n      rectEl.setTextConfig({\n        layoutRect: upperLabelRect\n      });\n      textEl.disableLabelLayout = true;\n    }\n    textEl.beforeUpdate = function () {\n      var width = Math.max((upperLabelRect ? upperLabelRect.width : rectEl.shape.width) - textPadding[1] - textPadding[3], 0);\n      var height = Math.max((upperLabelRect ? upperLabelRect.height : rectEl.shape.height) - textPadding[0] - textPadding[2], 0);\n      if (textStyle.width !== width || textStyle.height !== height) {\n        textEl.setStyle({\n          width: width,\n          height: height\n        });\n      }\n    };\n    textStyle.truncateMinChar = 2;\n    textStyle.lineOverflow = 'truncate';\n    addDrillDownIcon(textStyle, upperLabelRect, thisLayout);\n    var textEmphasisState = textEl.getState('emphasis');\n    addDrillDownIcon(textEmphasisState ? textEmphasisState.style : null, upperLabelRect, thisLayout);\n  }\n  function addDrillDownIcon(style, upperLabelRect, thisLayout) {\n    var text = style ? style.text : null;\n    if (!upperLabelRect && thisLayout.isLeafRoot && text != null) {\n      var iconChar = seriesModel.get('drillDownIcon', true);\n      style.text = iconChar ? iconChar + ' ' + text : text;\n    }\n  }\n  function giveGraphic(storageName, Ctor, depth, z) {\n    var element = oldRawIndex != null && oldStorage[storageName][oldRawIndex];\n    var lasts = lastsForAnimation[storageName];\n    if (element) {\n      // Remove from oldStorage\n      oldStorage[storageName][oldRawIndex] = null;\n      prepareAnimationWhenHasOld(lasts, element);\n    }\n    // If invisible and no old element, do not create new element (for optimizing).\n    else if (!thisInvisible) {\n      element = new Ctor();\n      if (element instanceof Displayable) {\n        element.z2 = calculateZ2(depth, z);\n      }\n      prepareAnimationWhenNoOld(lasts, element);\n    }\n    // Set to thisStorage\n    return thisStorage[storageName][thisRawIndex] = element;\n  }\n  function prepareAnimationWhenHasOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    if (element instanceof Group) {\n      lastCfg.oldX = element.x;\n      lastCfg.oldY = element.y;\n    } else {\n      lastCfg.oldShape = extend({}, element.shape);\n    }\n  }\n  // If a element is new, we need to find the animation start point carefully,\n  // otherwise it will looks strange when 'zoomToNode'.\n  function prepareAnimationWhenNoOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    var parentNode = thisNode.parentNode;\n    var isGroup = element instanceof graphic.Group;\n    if (parentNode && (!reRoot || reRoot.direction === 'drillDown')) {\n      var parentOldX = 0;\n      var parentOldY = 0;\n      // New nodes appear from right-bottom corner in 'zoomToNode' animation.\n      // For convenience, get old bounding rect from background.\n      var parentOldBg = lastsForAnimation.background[parentNode.getRawIndex()];\n      if (!reRoot && parentOldBg && parentOldBg.oldShape) {\n        parentOldX = parentOldBg.oldShape.width;\n        parentOldY = parentOldBg.oldShape.height;\n      }\n      // When no parent old shape found, its parent is new too,\n      // so we can just use {x:0, y:0}.\n      if (isGroup) {\n        lastCfg.oldX = 0;\n        lastCfg.oldY = parentOldY;\n      } else {\n        lastCfg.oldShape = {\n          x: parentOldX,\n          y: parentOldY,\n          width: 0,\n          height: 0\n        };\n      }\n    }\n    // Fade in, user can be aware that these nodes are new.\n    lastCfg.fadein = !isGroup;\n  }\n}\n// We cannot set all background with the same z, because the behaviour of\n// drill down and roll up differ background creation sequence from tree\n// hierarchy sequence, which cause lower background elements to overlap\n// upper ones. So we calculate z based on depth.\n// Moreover, we try to shrink down z interval to [0, 1] to avoid that\n// treemap with large z overlaps other components.\nfunction calculateZ2(depth, z2InLevel) {\n  return depth * Z2_BASE + z2InLevel;\n}\nexport default TreemapView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,0BAA0B;AAC5G,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC9I,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,MAAM,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,aAAa,MAAM,yBAAyB;AACxD,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,SAAS,EAAEC,mBAAmB,QAAQ,qBAAqB;AACpE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,IAAIC,KAAK,GAAGtB,OAAO,CAACsB,KAAK;AACzB,IAAIC,IAAI,GAAGvB,OAAO,CAACuB,IAAI;AACvB,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,iBAAiB,GAAG,OAAO;AAC/B,IAAIC,sBAAsB,GAAG,YAAY;AACzC;AACA,IAAIC,OAAO,GAAGrB,gBAAgB,GAAG,EAAE,CAAC,CAAC;AACrC,IAAIsB,KAAK,GAAGtB,gBAAgB,GAAG,CAAC;AAChC,IAAIuB,UAAU,GAAGvB,gBAAgB,GAAG,CAAC;AACrC,IAAIwB,iBAAiB,GAAGhB,eAAe,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;AAC1D;AACA;AACA,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,aAAa;AAC7H;AACA;AAAA,CACC,CAAC;AACF,IAAIiB,kBAAkB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACxC;EACA,IAAIC,SAAS,GAAGH,iBAAiB,CAACE,KAAK,CAAC;EACxC;EACAC,SAAS,CAACC,MAAM,GAAGD,SAAS,CAACE,IAAI,GAAGF,SAAS,CAACG,SAAS,GAAG,IAAI;EAC9D,OAAOH,SAAS;AAClB,CAAC;AACD,IAAII,KAAK,GAAGpB,SAAS,CAAC,CAAC;AACvB,IAAIqB,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/C/C,SAAS,CAAC8C,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7BH,KAAK,CAACI,MAAM,GAAG,OAAO;IACtBJ,KAAK,CAACK,QAAQ,GAAGC,aAAa,CAAC,CAAC;IAChC,OAAON,KAAK;EACd;EACA;AACF;AACA;EACEF,WAAW,CAACS,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC3E,IAAIC,MAAM,GAAGH,OAAO,CAACI,cAAc,CAAC;MAClCC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAEL;IACT,CAAC,CAAC;IACF,IAAIzD,OAAO,CAAC0D,MAAM,EAAEJ,WAAW,CAAC,GAAG,CAAC,EAAE;MACpC;IACF;IACA,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAIQ,KAAK,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IACtD,IAAIC,UAAU,GAAGnD,MAAM,CAACoD,kBAAkB,CAACR,OAAO,EAAEM,KAAK,EAAET,WAAW,CAAC;IACvE,IAAIY,WAAW,GAAGT,OAAO,IAAIA,OAAO,CAACT,IAAI;IACzC,IAAImB,UAAU,GAAGb,WAAW,CAACa,UAAU;IACvC,IAAIC,MAAM,GAAG,CAAC,IAAI,CAACC,QAAQ;IAC3B,IAAIC,WAAW,GAAG,IAAI,CAACpB,QAAQ;IAC/B;IACA,IAAIqB,MAAM,GAAGL,WAAW,KAAK,mBAAmB,IAAIF,UAAU,IAAIM,WAAW,GAAG;MAC9EE,aAAa,EAAEF,WAAW,CAACG,SAAS,CAACT,UAAU,CAACU,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;MACnEC,SAAS,EAAEnB,OAAO,CAACmB;IACrB,CAAC,GAAG,IAAI;IACR,IAAIC,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACX,UAAU,CAAC;IACzD,IAAIY,YAAY,GAAGzB,WAAW,CAAC0B,GAAG,CAAC,WAAW,CAAC;IAC/C,IAAIC,YAAY,GAAG,IAAI,CAACC,SAAS,CAACL,cAAc,EAAEvB,WAAW,EAAEiB,MAAM,CAAC;IACtEQ,YAAY,IAAI,CAACX,MAAM,KAAK,CAACF,WAAW,IAAIA,WAAW,KAAK,mBAAmB,IAAIA,WAAW,KAAK,mBAAmB,CAAC,GAAG,IAAI,CAACiB,YAAY,CAACN,cAAc,EAAEI,YAAY,EAAE3B,WAAW,EAAEiB,MAAM,CAAC,GAAGU,YAAY,CAACG,aAAa,CAAC,CAAC;IAC7N,IAAI,CAACC,gBAAgB,CAAC7B,GAAG,CAAC;IAC1B,IAAI,CAAC8B,iBAAiB,CAAChC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,CAAC;EACtD,CAAC;EACDrB,WAAW,CAACS,SAAS,CAAC0B,mBAAmB,GAAG,UAAUX,UAAU,EAAE;IAChE,IAAIU,cAAc,GAAG,IAAI,CAACU,eAAe;IACzC,IAAI,CAACV,cAAc,EAAE;MACnB;MACA;MACAA,cAAc,GAAG,IAAI,CAACU,eAAe,GAAG,IAAI5D,KAAK,CAAC,CAAC;MACnD,IAAI,CAAC6D,WAAW,CAACX,cAAc,CAAC;MAChC,IAAI,CAACY,KAAK,CAACC,GAAG,CAACb,cAAc,CAAC;IAChC;IACAA,cAAc,CAACc,CAAC,GAAGxB,UAAU,CAACwB,CAAC;IAC/Bd,cAAc,CAACe,CAAC,GAAGzB,UAAU,CAACyB,CAAC;IAC/B,OAAOf,cAAc;EACvB,CAAC;EACDlC,WAAW,CAACS,SAAS,CAAC8B,SAAS,GAAG,UAAUL,cAAc,EAAEvB,WAAW,EAAEiB,MAAM,EAAE;IAC/E,IAAIsB,QAAQ,GAAGvC,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI;IACzC,IAAIC,OAAO,GAAG,IAAI,CAAC3B,QAAQ;IAC3B;IACA,IAAI4B,iBAAiB,GAAG9C,aAAa,CAAC,CAAC;IACvC,IAAImB,WAAW,GAAGnB,aAAa,CAAC,CAAC;IACjC,IAAI+C,UAAU,GAAG,IAAI,CAAChD,QAAQ;IAC9B,IAAIiD,gBAAgB,GAAG,EAAE;IACzB,SAASC,YAAYA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;MAC3D,OAAOC,UAAU,CAACnD,WAAW,EAAEgB,WAAW,EAAE4B,UAAU,EAAE3B,MAAM,EAAE0B,iBAAiB,EAAEE,gBAAgB,EAAEE,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,CAAC;IAC7I;IACA;IACA;IACA;IACA;IACAE,UAAU,CAACb,QAAQ,CAACc,IAAI,GAAG,CAACd,QAAQ,CAACc,IAAI,CAAC,GAAG,EAAE,EAAEX,OAAO,IAAIA,OAAO,CAACW,IAAI,GAAG,CAACX,OAAO,CAACW,IAAI,CAAC,GAAG,EAAE,EAAE9B,cAAc,EAAEgB,QAAQ,KAAKG,OAAO,IAAI,CAACA,OAAO,EAAE,CAAC,CAAC;IACpJ;IACA,IAAIY,aAAa,GAAGC,YAAY,CAACX,UAAU,CAAC;IAC5C,IAAI,CAAC7B,QAAQ,GAAGwB,QAAQ;IACxB,IAAI,CAAC3C,QAAQ,GAAGoB,WAAW;IAC3B,IAAI,IAAI,CAACwC,eAAe,EAAE;MACxB,IAAIC,cAAc,GAAG,IAAI,CAACzD,WAAW,CAACa,UAAU;MAChD,IAAI6C,UAAU,GAAGnB,QAAQ,CAACc,IAAI,CAACM,SAAS,CAAC,CAAC;MAC1C,IAAID,UAAU,CAACE,KAAK,KAAKH,cAAc,CAACG,KAAK,IAAIF,UAAU,CAACG,MAAM,KAAKJ,cAAc,CAACI,MAAM,EAAE;QAC5F,IAAI,CAACL,eAAe,CAACM,IAAI,GAAG,CAAC;MAC/B;IACF;IACA,OAAO;MACLnB,iBAAiB,EAAEA,iBAAiB;MACpCW,aAAa,EAAEA,aAAa;MAC5BxB,aAAa,EAAEA;IACjB,CAAC;IACD,SAASsB,UAAUA,CAACW,gBAAgB,EAAEC,eAAe,EAAEf,WAAW,EAAEgB,QAAQ,EAAEf,KAAK,EAAE;MACnF;MACA;MACA;MACA,IAAIe,QAAQ,EAAE;QACZD,eAAe,GAAGD,gBAAgB;QAClCtH,IAAI,CAACsH,gBAAgB,EAAE,UAAUG,KAAK,EAAEC,KAAK,EAAE;UAC7C,CAACD,KAAK,CAACE,SAAS,CAAC,CAAC,IAAIC,WAAW,CAACF,KAAK,EAAEA,KAAK,CAAC;QACjD,CAAC,CAAC;MACJ;MACA;MACA;MAAA,KACK;QACH,IAAI7G,UAAU,CAAC0G,eAAe,EAAED,gBAAgB,EAAEO,MAAM,EAAEA,MAAM,CAAC,CAAClC,GAAG,CAACiC,WAAW,CAAC,CAACE,MAAM,CAACF,WAAW,CAAC,CAACG,MAAM,CAAC7H,KAAK,CAAC0H,WAAW,EAAE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;MACnJ;MACA,SAASH,MAAMA,CAAClD,IAAI,EAAE;QACpB;QACA,OAAOA,IAAI,CAACsD,KAAK,CAAC,CAAC;MACrB;MACA,SAASL,WAAWA,CAACM,QAAQ,EAAEC,QAAQ,EAAE;QACvC,IAAI7B,QAAQ,GAAG4B,QAAQ,IAAI,IAAI,GAAGZ,gBAAgB,CAACY,QAAQ,CAAC,GAAG,IAAI;QACnE,IAAI3B,OAAO,GAAG4B,QAAQ,IAAI,IAAI,GAAGZ,eAAe,CAACY,QAAQ,CAAC,GAAG,IAAI;QACjE,IAAIzC,KAAK,GAAGW,YAAY,CAACC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,CAAC;QAC/Df,KAAK,IAAIiB,UAAU,CAACL,QAAQ,IAAIA,QAAQ,CAAC8B,YAAY,IAAI,EAAE,EAAE7B,OAAO,IAAIA,OAAO,CAAC6B,YAAY,IAAI,EAAE,EAAE1C,KAAK,EAAE8B,QAAQ,EAAEf,KAAK,GAAG,CAAC,CAAC;MACjI;IACF;IACA,SAASK,YAAYA,CAACuB,OAAO,EAAE;MAC7B,IAAIxB,aAAa,GAAGzD,aAAa,CAAC,CAAC;MACnCiF,OAAO,IAAIrI,IAAI,CAACqI,OAAO,EAAE,UAAUC,KAAK,EAAEC,WAAW,EAAE;QACrD,IAAIC,MAAM,GAAG3B,aAAa,CAAC0B,WAAW,CAAC;QACvCvI,IAAI,CAACsI,KAAK,EAAE,UAAUG,EAAE,EAAE;UACxBA,EAAE,KAAKD,MAAM,CAACE,IAAI,CAACD,EAAE,CAAC,EAAE9F,KAAK,CAAC8F,EAAE,CAAC,CAACE,UAAU,GAAG,IAAI,CAAC;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAO9B,aAAa;IACtB;IACA,SAASxB,aAAaA,CAAA,EAAG;MACvBrF,IAAI,CAAC6G,aAAa,EAAE,UAAU+B,GAAG,EAAE;QACjC5I,IAAI,CAAC4I,GAAG,EAAE,UAAUH,EAAE,EAAE;UACtBA,EAAE,CAACI,MAAM,IAAIJ,EAAE,CAACI,MAAM,CAACd,MAAM,CAACU,EAAE,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;MACFzI,IAAI,CAACoG,gBAAgB,EAAE,UAAUqC,EAAE,EAAE;QACnCA,EAAE,CAACK,SAAS,GAAG,IAAI;QACnB;QACA;QACAL,EAAE,CAACM,KAAK,CAAC,CAAC;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EACDnG,WAAW,CAACS,SAAS,CAAC+B,YAAY,GAAG,UAAUN,cAAc,EAAEI,YAAY,EAAE3B,WAAW,EAAEiB,MAAM,EAAE;IAChG,IAAIwE,cAAc,GAAGzF,WAAW,CAAC0B,GAAG,CAAC,yBAAyB,CAAC;IAC/D,IAAIgE,YAAY,GAAG1F,WAAW,CAAC0B,GAAG,CAAC,iBAAiB,CAAC;IACrD;IACA,IAAIiE,QAAQ,GAAG,CAAC7I,UAAU,CAAC2I,cAAc,CAAC,GAAG,CAAC,GAAGA,cAAc,KAAK,CAAC;IACrE,IAAIG,MAAM,GAAG,CAAC9I,UAAU,CAAC4I,YAAY,CAAC,GAAG,IAAI,GAAGA,YAAY,KAAK,UAAU;IAC3E,IAAIG,aAAa,GAAGjI,aAAa,CAACkI,UAAU,CAAC,CAAC;IAC9C;IACArJ,IAAI,CAACkF,YAAY,CAAC2B,aAAa,EAAE,UAAUyB,KAAK,EAAEC,WAAW,EAAE;MAC7DvI,IAAI,CAACsI,KAAK,EAAE,UAAUG,EAAE,EAAEa,QAAQ,EAAE;QAClC,IAAIb,EAAE,CAACK,SAAS,EAAE;UAChB;QACF;QACA,IAAID,MAAM,GAAGJ,EAAE,CAACI,MAAM,CAAC,CAAC;QACxB,IAAIU,MAAM;QACV,IAAIC,UAAU,GAAG7G,KAAK,CAACkG,MAAM,CAAC;QAC9B,IAAIrE,MAAM,IAAIA,MAAM,CAACK,SAAS,KAAK,WAAW,EAAE;UAC9C0E,MAAM,GAAGV,MAAM,KAAKrE,MAAM,CAACC;UAC3B;UACA;UACA;UAAA,EACE;YACAgF,KAAK,EAAE;cACL7D,CAAC,EAAE,CAAC;cACJC,CAAC,EAAE,CAAC;cACJsB,KAAK,EAAEqC,UAAU,CAACE,SAAS;cAC3BtC,MAAM,EAAEoC,UAAU,CAACG;YACrB,CAAC;YACDC,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF;UACA;UAAA,EACE;YACAD,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC;QACH,CAAC,MAAM;UACL,IAAIC,OAAO,GAAG,CAAC;UACf,IAAIC,OAAO,GAAG,CAAC;UACf,IAAI,CAACP,UAAU,CAACb,UAAU,EAAE;YAC1B;YACA;YACA;YACAmB,OAAO,GAAGN,UAAU,CAACE,SAAS,GAAG,CAAC;YAClCK,OAAO,GAAGP,UAAU,CAACG,UAAU,GAAG,CAAC;UACrC;UACAJ,MAAM,GAAGhB,WAAW,KAAK,WAAW,GAAG;YACrC3C,CAAC,EAAEkE,OAAO;YACVjE,CAAC,EAAEkE,OAAO;YACVH,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC,GAAG;YACFJ,KAAK,EAAE;cACL7D,CAAC,EAAEkE,OAAO;cACVjE,CAAC,EAAEkE,OAAO;cACV5C,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE;YACV,CAAC;YACDwC,KAAK,EAAE;cACLC,OAAO,EAAE;YACX;UACF,CAAC;QACH;QACA;QACAN,MAAM,IAAIH,aAAa,CAACzD,GAAG,CAAC8C,EAAE,EAAEc,MAAM,EAAEL,QAAQ,EAAE,CAAC,EAAEC,MAAM,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACAnJ,IAAI,CAAC,IAAI,CAACmD,QAAQ,EAAE,UAAUmF,KAAK,EAAEC,WAAW,EAAE;MAChDvI,IAAI,CAACsI,KAAK,EAAE,UAAUG,EAAE,EAAEa,QAAQ,EAAE;QAClC,IAAIU,IAAI,GAAG9E,YAAY,CAACgB,iBAAiB,CAACqC,WAAW,CAAC,CAACe,QAAQ,CAAC;QAChE,IAAIC,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,CAACS,IAAI,EAAE;UACT;QACF;QACA,IAAIvB,EAAE,YAAYnI,OAAO,CAACsB,KAAK,EAAE;UAC/B,IAAIoI,IAAI,CAACC,IAAI,IAAI,IAAI,EAAE;YACrBV,MAAM,CAAC3D,CAAC,GAAG6C,EAAE,CAAC7C,CAAC;YACf2D,MAAM,CAAC1D,CAAC,GAAG4C,EAAE,CAAC5C,CAAC;YACf4C,EAAE,CAAC7C,CAAC,GAAGoE,IAAI,CAACC,IAAI;YAChBxB,EAAE,CAAC5C,CAAC,GAAGmE,IAAI,CAACE,IAAI;UAClB;QACF,CAAC,MAAM;UACL,IAAIF,IAAI,CAACG,QAAQ,EAAE;YACjBZ,MAAM,CAACE,KAAK,GAAGtJ,MAAM,CAAC,CAAC,CAAC,EAAEsI,EAAE,CAACgB,KAAK,CAAC;YACnChB,EAAE,CAAC2B,QAAQ,CAACJ,IAAI,CAACG,QAAQ,CAAC;UAC5B;UACA,IAAIH,IAAI,CAACK,MAAM,EAAE;YACf5B,EAAE,CAAC6B,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YACzBf,MAAM,CAACK,KAAK,GAAG;cACbC,OAAO,EAAE;YACX,CAAC;UACH;UACA;UACA;UAAA,KACK,IAAIpB,EAAE,CAACmB,KAAK,CAACC,OAAO,KAAK,CAAC,EAAE;YAC/BN,MAAM,CAACK,KAAK,GAAG;cACbC,OAAO,EAAE;YACX,CAAC;UACH;QACF;QACAT,aAAa,CAACzD,GAAG,CAAC8C,EAAE,EAAEc,MAAM,EAAEL,QAAQ,EAAE,CAAC,EAAEC,MAAM,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACjG,MAAM,GAAG,WAAW;IACzBkG,aAAa,CAACmB,QAAQ,CAACxK,IAAI,CAAC,YAAY;MACtC,IAAI,CAACmD,MAAM,GAAG,OAAO;MACrBgC,YAAY,CAACG,aAAa,CAAC,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAACmF,KAAK,CAAC,CAAC;EACnB,CAAC;EACD5H,WAAW,CAACS,SAAS,CAACiC,gBAAgB,GAAG,UAAU7B,GAAG,EAAE;IACtD,IAAIgH,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAIC,cAAc,GAAG,IAAI,CAAC5D,eAAe;IACzC,IAAI,CAAC4D,cAAc,EAAE;MACnB,IAAI,CAAC5D,eAAe,GAAG;QACrBwC,MAAM,EAAE,IAAI,CAAC7D;MACf,CAAC;MACDiF,cAAc,GAAG,IAAI,CAAC5D,eAAe;IACvC;IACA;IACA,IAAI,CAAC0D,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI1J,cAAc,CAACyC,GAAG,CAACmH,KAAK,CAAC,CAAC,CAAC;MAC/DH,UAAU,CAACI,MAAM,CAAC,IAAI,CAACtH,WAAW,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAAC;MAC/C0F,cAAc,CAACG,SAAS,GAAG,IAAI,CAACvH,WAAW,CAAC0B,GAAG,CAAC,YAAY,CAAC;MAC7D0F,cAAc,CAACtD,IAAI,GAAG,IAAI,CAAC9D,WAAW,CAAC0B,GAAG,CAAC,MAAM,CAAC;MAClDwF,UAAU,CAACM,EAAE,CAAC,KAAK,EAAEhL,IAAI,CAAC,IAAI,CAACiL,MAAM,EAAE,IAAI,CAAC,CAAC;MAC7CP,UAAU,CAACM,EAAE,CAAC,MAAM,EAAEhL,IAAI,CAAC,IAAI,CAACkL,OAAO,EAAE,IAAI,CAAC,CAAC;IACjD;IACA,IAAIC,IAAI,GAAG,IAAIjK,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEwC,GAAG,CAAC0H,QAAQ,CAAC,CAAC,EAAE1H,GAAG,CAAC2H,SAAS,CAAC,CAAC,CAAC;IAClEX,UAAU,CAACY,iBAAiB,CAAC,UAAUC,CAAC,EAAE1F,CAAC,EAAEC,CAAC,EAAE;MAC9C,OAAOqF,IAAI,CAACK,OAAO,CAAC3F,CAAC,EAAEC,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC;EACDjD,WAAW,CAACS,SAAS,CAACmI,gBAAgB,GAAG,YAAY;IACnD,IAAIf,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAI,CAAC3D,eAAe,GAAG,IAAI;IAC3B,IAAI0D,UAAU,EAAE;MACdA,UAAU,CAACgB,OAAO,CAAC,CAAC;MACpBhB,UAAU,GAAG,IAAI;IACnB;EACF,CAAC;EACD7H,WAAW,CAACS,SAAS,CAAC2H,MAAM,GAAG,UAAUM,CAAC,EAAE;IAC1C,IAAI,IAAI,CAACpI,MAAM,KAAK,WAAW,KAAKwI,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,EAAE,CAAC,GAAG9J,cAAc,IAAI4J,IAAI,CAACC,GAAG,CAACL,CAAC,CAACO,EAAE,CAAC,GAAG/J,cAAc,CAAC,EAAE;MACvG;MACA,IAAI8E,IAAI,GAAG,IAAI,CAACrD,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY,IAAI;MAC/C,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MACA,IAAIK,UAAU,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC;MACjC,IAAI,CAACD,UAAU,EAAE;QACf;MACF;MACA,IAAI,CAACxD,GAAG,CAACqI,cAAc,CAAC;QACtB7I,IAAI,EAAE,aAAa;QACnB8I,IAAI,EAAE,IAAI,CAACC,GAAG;QACdC,QAAQ,EAAE,IAAI,CAAC1I,WAAW,CAAC2I,EAAE;QAC7BC,QAAQ,EAAE;UACRvG,CAAC,EAAEqB,UAAU,CAACrB,CAAC,GAAG0F,CAAC,CAACM,EAAE;UACtB/F,CAAC,EAAEoB,UAAU,CAACpB,CAAC,GAAGyF,CAAC,CAACO,EAAE;UACtB1E,KAAK,EAAEF,UAAU,CAACE,KAAK;UACvBC,MAAM,EAAEH,UAAU,CAACG;QACrB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDxE,WAAW,CAACS,SAAS,CAAC4H,OAAO,GAAG,UAAUK,CAAC,EAAE;IAC3C,IAAIc,MAAM,GAAGd,CAAC,CAACe,OAAO;IACtB,IAAIC,MAAM,GAAGhB,CAAC,CAACiB,OAAO;IACtB,IAAIC,SAAS,GAAGlB,CAAC,CAACmB,KAAK;IACvB,IAAI,IAAI,CAACvJ,MAAM,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI0D,IAAI,GAAG,IAAI,CAACrD,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY,IAAI;MAC/C,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MACA,IAAIK,UAAU,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC;MACjC,IAAI,CAACD,UAAU,EAAE;QACf;MACF;MACA,IAAIiE,IAAI,GAAG,IAAIjK,YAAY,CAACgG,UAAU,CAACrB,CAAC,EAAEqB,UAAU,CAACpB,CAAC,EAAEoB,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACG,MAAM,CAAC;MAC5F;MACA,IAAI0D,SAAS,GAAG,IAAI;MACpB,IAAI/D,eAAe,GAAG,IAAI,CAACA,eAAe;MAC1C+D,SAAS,GAAG/D,eAAe,CAAC+D,SAAS;MACrC,IAAI4B,OAAO,GAAG3F,eAAe,CAACM,IAAI,GAAGN,eAAe,CAACM,IAAI,IAAI,CAAC;MAC9DqF,OAAO,IAAIF,SAAS;MACpB,IAAI1B,SAAS,EAAE;QACb,IAAI6B,OAAO,GAAG7B,SAAS,CAAC8B,GAAG,IAAI,CAAC;QAChC,IAAIC,OAAO,GAAG/B,SAAS,CAACgC,GAAG,IAAIC,QAAQ;QACvCL,OAAO,GAAGhB,IAAI,CAACoB,GAAG,CAACpB,IAAI,CAACkB,GAAG,CAACC,OAAO,EAAEH,OAAO,CAAC,EAAEC,OAAO,CAAC;MACzD;MACA,IAAIK,SAAS,GAAGN,OAAO,GAAG3F,eAAe,CAACM,IAAI;MAC9CN,eAAe,CAACM,IAAI,GAAGqF,OAAO;MAC9B,IAAItI,UAAU,GAAG,IAAI,CAACb,WAAW,CAACa,UAAU;MAC5C;MACAgI,MAAM,IAAIhI,UAAU,CAACwB,CAAC;MACtB0G,MAAM,IAAIlI,UAAU,CAACyB,CAAC;MACtB;MACA,IAAIoH,CAAC,GAAG/L,MAAM,CAACgM,MAAM,CAAC,CAAC;MACvBhM,MAAM,CAACiM,SAAS,CAACF,CAAC,EAAEA,CAAC,EAAE,CAAC,CAACb,MAAM,EAAE,CAACE,MAAM,CAAC,CAAC;MAC1CpL,MAAM,CAACuL,KAAK,CAACQ,CAAC,EAAEA,CAAC,EAAE,CAACD,SAAS,EAAEA,SAAS,CAAC,CAAC;MAC1C9L,MAAM,CAACiM,SAAS,CAACF,CAAC,EAAEA,CAAC,EAAE,CAACb,MAAM,EAAEE,MAAM,CAAC,CAAC;MACxCpB,IAAI,CAACkC,cAAc,CAACH,CAAC,CAAC;MACtB,IAAI,CAACxJ,GAAG,CAACqI,cAAc,CAAC;QACtB7I,IAAI,EAAE,eAAe;QACrB8I,IAAI,EAAE,IAAI,CAACC,GAAG;QACdC,QAAQ,EAAE,IAAI,CAAC1I,WAAW,CAAC2I,EAAE;QAC7BC,QAAQ,EAAE;UACRvG,CAAC,EAAEsF,IAAI,CAACtF,CAAC;UACTC,CAAC,EAAEqF,IAAI,CAACrF,CAAC;UACTsB,KAAK,EAAE+D,IAAI,CAAC/D,KAAK;UACjBC,MAAM,EAAE8D,IAAI,CAAC9D;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDxE,WAAW,CAACS,SAAS,CAACoC,WAAW,GAAG,UAAUX,cAAc,EAAE;IAC5D,IAAIhC,KAAK,GAAG,IAAI;IAChBgC,cAAc,CAACiG,EAAE,CAAC,OAAO,EAAE,UAAUO,CAAC,EAAE;MACtC,IAAIxI,KAAK,CAACI,MAAM,KAAK,OAAO,EAAE;QAC5B;MACF;MACA,IAAImK,SAAS,GAAGvK,KAAK,CAACS,WAAW,CAAC0B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;MACxD,IAAI,CAACoI,SAAS,EAAE;QACd;MACF;MACA,IAAIpJ,UAAU,GAAGnB,KAAK,CAACwK,UAAU,CAAChC,CAAC,CAACiC,OAAO,EAAEjC,CAAC,CAACkC,OAAO,CAAC;MACvD,IAAI,CAACvJ,UAAU,EAAE;QACf;MACF;MACA,IAAIU,IAAI,GAAGV,UAAU,CAACU,IAAI;MAC1B,IAAIA,IAAI,CAACuC,SAAS,CAAC,CAAC,CAACuG,UAAU,EAAE;QAC/B3K,KAAK,CAAC4K,WAAW,CAACzJ,UAAU,CAAC;MAC/B,CAAC,MAAM;QACL,IAAIoJ,SAAS,KAAK,YAAY,EAAE;UAC9BvK,KAAK,CAAC6K,WAAW,CAAC1J,UAAU,CAAC;QAC/B,CAAC,MAAM,IAAIoJ,SAAS,KAAK,MAAM,EAAE;UAC/B,IAAIO,SAAS,GAAGjJ,IAAI,CAACkJ,QAAQ,CAACC,IAAI,CAACC,YAAY,CAACpJ,IAAI,CAACqJ,SAAS,CAAC;UAC/D,IAAIC,IAAI,GAAGL,SAAS,CAAC3I,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;UACtC,IAAIiJ,UAAU,GAAGN,SAAS,CAAC3I,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,OAAO;UACzDgJ,IAAI,IAAIxM,UAAU,CAACwM,IAAI,EAAEC,UAAU,CAAC;QACtC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDtL,WAAW,CAACS,SAAS,CAACkC,iBAAiB,GAAG,UAAUhC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,EAAE;IAChF,IAAInB,KAAK,GAAG,IAAI;IAChB,IAAI,CAACmB,UAAU,EAAE;MACfA,UAAU,GAAGV,WAAW,CAAC0B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,IAAI,GAAG;QACxDN,IAAI,EAAEpB,WAAW,CAAC4K,WAAW,CAAC;MAChC;MACA;MACA;MACA;MAAA,EACE,IAAI,CAACb,UAAU,CAAC7J,GAAG,CAAC0H,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE1H,GAAG,CAAC2H,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1D,IAAI,CAACnH,UAAU,EAAE;QACfA,UAAU,GAAG;UACXU,IAAI,EAAEpB,WAAW,CAACwC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACY;QACnC,CAAC;MACH;IACF;IACA,CAAC,IAAI,CAACwH,WAAW,KAAK,IAAI,CAACA,WAAW,GAAG,IAAIrN,UAAU,CAAC,IAAI,CAAC2E,KAAK,CAAC,CAAC,EAAEpC,MAAM,CAACC,WAAW,EAAEE,GAAG,EAAEQ,UAAU,CAACU,IAAI,EAAE,UAAUA,IAAI,EAAE;MAC9H,IAAI7B,KAAK,CAACI,MAAM,KAAK,WAAW,EAAE;QAChCpC,MAAM,CAACuN,aAAa,CAAC9K,WAAW,CAAC4K,WAAW,CAAC,CAAC,EAAExJ,IAAI,CAAC,GAAG7B,KAAK,CAAC4K,WAAW,CAAC;UACxE/I,IAAI,EAAEA;QACR,CAAC,CAAC,GAAG7B,KAAK,CAAC6K,WAAW,CAAC;UACrBhJ,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACE/B,WAAW,CAACS,SAAS,CAAC0E,MAAM,GAAG,YAAY;IACzC,IAAI,CAACyD,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAChG,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC8I,SAAS,CAAC,CAAC;IACxD,IAAI,CAACnL,QAAQ,GAAGC,aAAa,CAAC,CAAC;IAC/B,IAAI,CAACF,MAAM,GAAG,OAAO;IACrB,IAAI,CAACkL,WAAW,IAAI,IAAI,CAACA,WAAW,CAACrG,MAAM,CAAC,CAAC;EAC/C,CAAC;EACDnF,WAAW,CAACS,SAAS,CAACoI,OAAO,GAAG,YAAY;IAC1C,IAAI,CAACD,gBAAgB,CAAC,CAAC;EACzB,CAAC;EACD5I,WAAW,CAACS,SAAS,CAACsK,WAAW,GAAG,UAAU1J,UAAU,EAAE;IACxD,IAAI,CAACR,GAAG,CAACqI,cAAc,CAAC;MACtB7I,IAAI,EAAE,mBAAmB;MACzB8I,IAAI,EAAE,IAAI,CAACC,GAAG;MACdC,QAAQ,EAAE,IAAI,CAAC1I,WAAW,CAAC2I,EAAE;MAC7BqC,UAAU,EAAEtK,UAAU,CAACU;IACzB,CAAC,CAAC;EACJ,CAAC;EACD/B,WAAW,CAACS,SAAS,CAACqK,WAAW,GAAG,UAAUzJ,UAAU,EAAE;IACxD,IAAI,CAACR,GAAG,CAACqI,cAAc,CAAC;MACtB7I,IAAI,EAAE,mBAAmB;MACzB8I,IAAI,EAAE,IAAI,CAACC,GAAG;MACdC,QAAQ,EAAE,IAAI,CAAC1I,WAAW,CAAC2I,EAAE;MAC7BqC,UAAU,EAAEtK,UAAU,CAACU;IACzB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,WAAW,CAACS,SAAS,CAACiK,UAAU,GAAG,UAAU1H,CAAC,EAAEC,CAAC,EAAE;IACjD,IAAI5B,UAAU;IACd,IAAIuK,QAAQ,GAAG,IAAI,CAACjL,WAAW,CAAC4K,WAAW,CAAC,CAAC;IAC7CK,QAAQ,CAACC,QAAQ,CAAC;MAChBC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;IACT,CAAC,EAAE,UAAUhK,IAAI,EAAE;MACjB,IAAIiK,IAAI,GAAG,IAAI,CAACzL,QAAQ,CAAC0L,UAAU,CAAClK,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;MACvD;MACA,IAAIgK,IAAI,EAAE;QACR,IAAIE,KAAK,GAAGF,IAAI,CAACG,qBAAqB,CAACnJ,CAAC,EAAEC,CAAC,CAAC;QAC5C,IAAI4D,KAAK,GAAGmF,IAAI,CAACnF,KAAK;QACtB;QACA,IAAIA,KAAK,CAAC7D,CAAC,IAAIkJ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIrF,KAAK,CAAC7D,CAAC,GAAG6D,KAAK,CAACtC,KAAK,IAAIsC,KAAK,CAAC5D,CAAC,IAAIiJ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIrF,KAAK,CAAC5D,CAAC,GAAG4D,KAAK,CAACrC,MAAM,EAAE;UACzHnD,UAAU,GAAG;YACXU,IAAI,EAAEA,IAAI;YACV4I,OAAO,EAAEuB,KAAK,CAAC,CAAC,CAAC;YACjBtB,OAAO,EAAEsB,KAAK,CAAC,CAAC;UAClB,CAAC;QACH,CAAC,MAAM;UACL,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAO7K,UAAU;EACnB,CAAC;EACDrB,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAACvB,SAAS,CAAC;AACZ;AACA;AACA;AACA,SAAS+B,aAAaA,CAAA,EAAG;EACvB,OAAO;IACLsB,SAAS,EAAE,EAAE;IACbmK,UAAU,EAAE,EAAE;IACdG,OAAO,EAAE;EACX,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,SAAStI,UAAUA,CAACnD,WAAW,EAAEgB,WAAW,EAAE4B,UAAU,EAAE3B,MAAM,EAAE0B,iBAAiB,EAAEE,gBAAgB,EAAEE,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAC5I;EACA,IAAI,CAACH,QAAQ,EAAE;IACb;IACA;IACA;IACA;EACF;EACA;EACA;EACA,IAAI2I,UAAU,GAAG3I,QAAQ,CAACY,SAAS,CAAC,CAAC;EACrC,IAAI4G,IAAI,GAAGvK,WAAW,CAACwC,OAAO,CAAC,CAAC;EAChC,IAAImJ,SAAS,GAAG5I,QAAQ,CAAC6I,QAAQ,CAAC,CAAC;EACnC;EACA;EACArB,IAAI,CAACsB,gBAAgB,CAAC9I,QAAQ,CAAC0H,SAAS,EAAE,IAAI,CAAC;EAC/C,IAAI,CAACiB,UAAU,IAAI,CAACA,UAAU,CAACI,QAAQ,EAAE;IACvC;EACF;EACA,IAAIC,SAAS,GAAGL,UAAU,CAAC9H,KAAK;EAChC,IAAIoI,UAAU,GAAGN,UAAU,CAAC7H,MAAM;EAClC,IAAIoI,WAAW,GAAGP,UAAU,CAACO,WAAW;EACxC,IAAIC,aAAa,GAAGR,UAAU,CAACnG,SAAS;EACxC,IAAI4G,YAAY,GAAGpJ,QAAQ,CAAC1B,WAAW,CAAC,CAAC;EACzC,IAAI+K,WAAW,GAAGpJ,OAAO,IAAIA,OAAO,CAAC3B,WAAW,CAAC,CAAC;EAClD,IAAI0C,gBAAgB,GAAGhB,QAAQ,CAAC8B,YAAY;EAC5C,IAAIwH,WAAW,GAAGX,UAAU,CAACW,WAAW;EACxC,IAAIC,QAAQ,GAAGvI,gBAAgB,IAAIA,gBAAgB,CAACwI,MAAM;EAC1D,IAAIC,oBAAoB,GAAGb,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC;EAC1D,IAAIa,sBAAsB,GAAGd,SAAS,CAACC,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;EAC1E,IAAIc,kBAAkB,GAAGf,SAAS,CAACC,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;EAClE,IAAIe,oBAAoB,GAAGhB,SAAS,CAACC,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;EACtE,IAAIgB,YAAY,GAAGJ,oBAAoB,CAAC9K,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC;EAChE;EACA;EACA;EACA,IAAIS,KAAK,GAAG0K,WAAW,CAAC,WAAW,EAAExO,KAAK,CAAC;EAC3C,IAAI,CAAC8D,KAAK,EAAE;IACV;EACF;EACAc,WAAW,CAACb,GAAG,CAACD,KAAK,CAAC;EACtB;EACAA,KAAK,CAACE,CAAC,GAAGqJ,UAAU,CAACrJ,CAAC,IAAI,CAAC;EAC3BF,KAAK,CAACG,CAAC,GAAGoJ,UAAU,CAACpJ,CAAC,IAAI,CAAC;EAC3BH,KAAK,CAAC2K,UAAU,CAAC,CAAC;EAClB1N,KAAK,CAAC+C,KAAK,CAAC,CAACgE,SAAS,GAAG4F,SAAS;EAClC3M,KAAK,CAAC+C,KAAK,CAAC,CAACiE,UAAU,GAAG4F,UAAU;EACpC,IAAIN,UAAU,CAACqB,eAAe,EAAE;IAC9B,OAAO5K,KAAK;EACd;EACA;EACA,IAAI6K,EAAE,GAAGH,WAAW,CAAC,YAAY,EAAEvO,IAAI,EAAE4E,KAAK,EAAEvE,KAAK,CAAC;EACtDqO,EAAE,IAAIC,gBAAgB,CAAC9K,KAAK,EAAE6K,EAAE,EAAEV,QAAQ,IAAIZ,UAAU,CAACwB,gBAAgB,CAAC;EAC1E,IAAIC,aAAa,GAAGxB,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;EAClD,IAAIwB,KAAK,GAAGD,aAAa,CAACzL,GAAG,CAAC,OAAO,CAAC;EACtC,IAAI2L,SAAS,GAAGF,aAAa,CAACzL,GAAG,CAAC,WAAW,CAAC;EAC9C,IAAI4L,UAAU,GAAGH,aAAa,CAACzL,GAAG,CAAC,UAAU,CAAC;EAC9C,IAAI6L,cAAc,GAAGH,KAAK,KAAK,UAAU,GAAGrK,QAAQ,CAACyK,mBAAmB,CAAC,CAAC,GAAGJ,KAAK,KAAK,YAAY,GAAGrK,QAAQ,CAAC0K,oBAAoB,CAAC,CAAC,GAAGL,KAAK;EAC7I;EACA,IAAId,QAAQ,EAAE;IACZ;IACA;IACA;IACA,IAAIrP,oBAAoB,CAACkF,KAAK,CAAC,EAAE;MAC/BjF,uBAAuB,CAACiF,KAAK,EAAE,KAAK,CAAC;IACvC;IACA,IAAI6K,EAAE,EAAE;MACN9P,uBAAuB,CAAC8P,EAAE,EAAE,CAACM,UAAU,CAAC;MACxC;MACA/C,IAAI,CAACsB,gBAAgB,CAAC9I,QAAQ,CAAC0H,SAAS,EAAEuC,EAAE,CAAC;MAC7C5P,gBAAgB,CAAC4P,EAAE,EAAEO,cAAc,EAAEF,SAAS,CAAC;IACjD;EACF,CAAC,MAAM;IACL,IAAI5B,OAAO,GAAGoB,WAAW,CAAC,SAAS,EAAEvO,IAAI,EAAE4E,KAAK,EAAEtE,UAAU,CAAC;IAC7D6M,OAAO,IAAIiC,aAAa,CAACvL,KAAK,EAAEsJ,OAAO,CAAC;IACxCuB,EAAE,CAACW,eAAe,GAAG,IAAI;IACzB,IAAIX,EAAE,IAAI/P,oBAAoB,CAAC+P,EAAE,CAAC,EAAE;MAClC9P,uBAAuB,CAAC8P,EAAE,EAAE,KAAK,CAAC;IACpC;IACA9P,uBAAuB,CAACiF,KAAK,EAAE,CAACmL,UAAU,CAAC;IAC3C;IACA/C,IAAI,CAACsB,gBAAgB,CAAC9I,QAAQ,CAAC0H,SAAS,EAAEtI,KAAK,CAAC;IAChD,IAAIyL,WAAW,GAAGjC,SAAS,CAACkC,UAAU,CAAC,QAAQ,CAAC;IAChDD,WAAW,IAAInC,OAAO,CAACN,IAAI,CAAC,QAAQ,EAAEyC,WAAW,CAAC;IAClDxQ,gBAAgB,CAAC+E,KAAK,EAAEoL,cAAc,EAAEF,SAAS,CAAC;EACpD;EACA,OAAOlL,KAAK;EACZ;EACA;EACA;EACA,SAAS8K,gBAAgBA,CAAC9K,KAAK,EAAE6K,EAAE,EAAEc,aAAa,EAAE;IAClD,IAAIC,MAAM,GAAG/Q,SAAS,CAACgQ,EAAE,CAAC;IAC1B;IACAe,MAAM,CAACtD,SAAS,GAAG1H,QAAQ,CAAC0H,SAAS;IACrCsD,MAAM,CAACC,WAAW,GAAGhO,WAAW,CAACgO,WAAW;IAC5ChB,EAAE,CAACnG,QAAQ,CAAC;MACVxE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJsB,KAAK,EAAEmI,SAAS;MAChBlI,MAAM,EAAEmI,UAAU;MAClBiC,CAAC,EAAErB;IACL,CAAC,CAAC;IACF,IAAIV,aAAa,EAAE;MACjB;MACA;MACA;MACAgC,gBAAgB,CAAClB,EAAE,CAAC;IACtB,CAAC,MAAM;MACLA,EAAE,CAACzH,SAAS,GAAG,KAAK;MACpB,IAAIc,KAAK,GAAGtD,QAAQ,CAACoL,SAAS,CAAC,OAAO,CAAC;MACvC,IAAIC,iBAAiB,GAAG/H,KAAK,CAACpH,MAAM;MACpC,IAAIoP,WAAW,GAAGvP,kBAAkB,CAAC0N,oBAAoB,CAAC;MAC1D6B,WAAW,CAACnP,IAAI,GAAGkP,iBAAiB;MACpC,IAAIE,aAAa,GAAGzP,iBAAiB,CAAC4N,sBAAsB,CAAC;MAC7D6B,aAAa,CAACpP,IAAI,GAAGuN,sBAAsB,CAAC/K,GAAG,CAAC,aAAa,CAAC;MAC9D,IAAI6M,SAAS,GAAG1P,iBAAiB,CAAC6N,kBAAkB,CAAC;MACrD6B,SAAS,CAACrP,IAAI,GAAGwN,kBAAkB,CAAChL,GAAG,CAAC,aAAa,CAAC;MACtD,IAAI8M,WAAW,GAAG3P,iBAAiB,CAAC8N,oBAAoB,CAAC;MACzD6B,WAAW,CAACtP,IAAI,GAAGyN,oBAAoB,CAACjL,GAAG,CAAC,aAAa,CAAC;MAC1D,IAAIoM,aAAa,EAAE;QACjB,IAAIW,eAAe,GAAG1C,SAAS,GAAG,CAAC,GAAGE,WAAW;QACjDyC,WAAW;QACX;QACA1B,EAAE,EAAEoB,iBAAiB,EAAE/H,KAAK,CAACC,OAAO,EAAE;UACpCjE,CAAC,EAAE4J,WAAW;UACd3J,CAAC,EAAE,CAAC;UACJsB,KAAK,EAAE6K,eAAe;UACtB5K,MAAM,EAAEwI;QACV,CAAC,CAAC;MACJ;MACA;MAAA,KACK;QACHW,EAAE,CAAC2B,iBAAiB,CAAC,CAAC;MACxB;MACA3B,EAAE,CAACjG,QAAQ,CAACsH,WAAW,CAAC;MACxBrB,EAAE,CAAC4B,WAAW,CAAC,UAAU,CAAC,CAACvI,KAAK,GAAGiI,aAAa;MAChDtB,EAAE,CAAC4B,WAAW,CAAC,MAAM,CAAC,CAACvI,KAAK,GAAGkI,SAAS;MACxCvB,EAAE,CAAC4B,WAAW,CAAC,QAAQ,CAAC,CAACvI,KAAK,GAAGmI,WAAW;MAC5CrR,oBAAoB,CAAC6P,EAAE,CAAC;IAC1B;IACA7K,KAAK,CAACC,GAAG,CAAC4K,EAAE,CAAC;EACf;EACA,SAASU,aAAaA,CAACvL,KAAK,EAAEsJ,OAAO,EAAE;IACrC,IAAIsC,MAAM,GAAG/Q,SAAS,CAACyO,OAAO,CAAC;IAC/B;IACAsC,MAAM,CAACtD,SAAS,GAAG1H,QAAQ,CAAC0H,SAAS;IACrCsD,MAAM,CAACC,WAAW,GAAGhO,WAAW,CAACgO,WAAW;IAC5C,IAAIa,YAAY,GAAG1G,IAAI,CAACoB,GAAG,CAACwC,SAAS,GAAG,CAAC,GAAGE,WAAW,EAAE,CAAC,CAAC;IAC3D,IAAI6C,aAAa,GAAG3G,IAAI,CAACoB,GAAG,CAACyC,UAAU,GAAG,CAAC,GAAGC,WAAW,EAAE,CAAC,CAAC;IAC7DR,OAAO,CAACsD,OAAO,GAAG,IAAI;IACtBtD,OAAO,CAAC5E,QAAQ,CAAC;MACfxE,CAAC,EAAE4J,WAAW;MACd3J,CAAC,EAAE2J,WAAW;MACdrI,KAAK,EAAEiL,YAAY;MACnBhL,MAAM,EAAEiL,aAAa;MACrBb,CAAC,EAAErB;IACL,CAAC,CAAC;IACF,IAAIV,aAAa,EAAE;MACjB;MACA;MACA;MACAgC,gBAAgB,CAACzC,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLA,OAAO,CAAClG,SAAS,GAAG,KAAK;MACzB,IAAIyJ,SAAS,GAAGjM,QAAQ,CAACoL,SAAS,CAAC,OAAO,CAAC;MAC3C,IAAIc,WAAW,GAAGD,SAAS,CAAC9P,IAAI;MAChC,IAAImP,WAAW,GAAGvP,kBAAkB,CAAC0N,oBAAoB,CAAC;MAC1D6B,WAAW,CAACnP,IAAI,GAAG+P,WAAW;MAC9BZ,WAAW,CAACa,KAAK,GAAGF,SAAS,CAACE,KAAK;MACnC,IAAIZ,aAAa,GAAGzP,iBAAiB,CAAC4N,sBAAsB,CAAC;MAC7D,IAAI8B,SAAS,GAAG1P,iBAAiB,CAAC6N,kBAAkB,CAAC;MACrD,IAAI8B,WAAW,GAAG3P,iBAAiB,CAAC8N,oBAAoB,CAAC;MACzD;MACA+B,WAAW,CAACjD,OAAO,EAAEwD,WAAW,EAAED,SAAS,CAAC1I,OAAO,EAAE,IAAI,CAAC;MAC1DmF,OAAO,CAAC1E,QAAQ,CAACsH,WAAW,CAAC;MAC7B5C,OAAO,CAACmD,WAAW,CAAC,UAAU,CAAC,CAACvI,KAAK,GAAGiI,aAAa;MACrD7C,OAAO,CAACmD,WAAW,CAAC,MAAM,CAAC,CAACvI,KAAK,GAAGkI,SAAS;MAC7C9C,OAAO,CAACmD,WAAW,CAAC,QAAQ,CAAC,CAACvI,KAAK,GAAGmI,WAAW;MACjDrR,oBAAoB,CAACsO,OAAO,CAAC;IAC/B;IACAtJ,KAAK,CAACC,GAAG,CAACqJ,OAAO,CAAC;EACpB;EACA,SAASyC,gBAAgBA,CAACiB,OAAO,EAAE;IACjC;IACA;IACA,CAACA,OAAO,CAAC5J,SAAS,IAAI1C,gBAAgB,CAACsC,IAAI,CAACgK,OAAO,CAAC;EACtD;EACA,SAAST,WAAWA,CAACU,MAAM,EAAEH,WAAW,EAAEI,aAAa;EACvD;EACAC,cAAc,EAAE;IACd,IAAIC,gBAAgB,GAAG5D,SAAS,CAACC,QAAQ,CAAC0D,cAAc,GAAG7Q,sBAAsB,GAAGD,iBAAiB,CAAC;IACtG,IAAIgR,WAAW,GAAGvR,mBAAmB,CAAC0N,SAAS,CAACjK,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;IAClE,IAAI+N,MAAM,GAAGF,gBAAgB,CAAC1B,UAAU,CAAC,MAAM,CAAC;IAChD1P,aAAa,CAACiR,MAAM,EAAEhR,oBAAoB,CAACuN,SAAS,EAAE2D,cAAc,GAAG7Q,sBAAsB,GAAGD,iBAAiB,CAAC,EAAE;MAClHgR,WAAW,EAAEC,MAAM,GAAGD,WAAW,GAAG,IAAI;MACxCE,YAAY,EAAET,WAAW;MACzBU,cAAc,EAAEN,aAAa;MAC7BO,YAAY,EAAE5P,WAAW;MACzB6P,cAAc,EAAE9M,QAAQ,CAAC0H;IAC3B,CAAC,CAAC;IACF,IAAIqF,MAAM,GAAGV,MAAM,CAACW,cAAc,CAAC,CAAC;IACpC,IAAI,CAACD,MAAM,EAAE;MACX;IACF;IACA,IAAIE,SAAS,GAAGF,MAAM,CAACzJ,KAAK;IAC5B,IAAI4J,WAAW,GAAGpT,iBAAiB,CAACmT,SAAS,CAACE,OAAO,IAAI,CAAC,CAAC;IAC3D,IAAIZ,cAAc,EAAE;MAClBF,MAAM,CAACe,aAAa,CAAC;QACnBC,UAAU,EAAEd;MACd,CAAC,CAAC;MACFQ,MAAM,CAACO,kBAAkB,GAAG,IAAI;IAClC;IACAP,MAAM,CAACQ,YAAY,GAAG,YAAY;MAChC,IAAI1M,KAAK,GAAGuE,IAAI,CAACoB,GAAG,CAAC,CAAC+F,cAAc,GAAGA,cAAc,CAAC1L,KAAK,GAAGwL,MAAM,CAAClJ,KAAK,CAACtC,KAAK,IAAIqM,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvH,IAAIpM,MAAM,GAAGsE,IAAI,CAACoB,GAAG,CAAC,CAAC+F,cAAc,GAAGA,cAAc,CAACzL,MAAM,GAAGuL,MAAM,CAAClJ,KAAK,CAACrC,MAAM,IAAIoM,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1H,IAAID,SAAS,CAACpM,KAAK,KAAKA,KAAK,IAAIoM,SAAS,CAACnM,MAAM,KAAKA,MAAM,EAAE;QAC5DiM,MAAM,CAAC/I,QAAQ,CAAC;UACdnD,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC,CAAC;MACJ;IACF,CAAC;IACDmM,SAAS,CAACO,eAAe,GAAG,CAAC;IAC7BP,SAAS,CAACQ,YAAY,GAAG,UAAU;IACnCC,gBAAgB,CAACT,SAAS,EAAEV,cAAc,EAAE5D,UAAU,CAAC;IACvD,IAAIgF,iBAAiB,GAAGZ,MAAM,CAACa,QAAQ,CAAC,UAAU,CAAC;IACnDF,gBAAgB,CAACC,iBAAiB,GAAGA,iBAAiB,CAACrK,KAAK,GAAG,IAAI,EAAEiJ,cAAc,EAAE5D,UAAU,CAAC;EAClG;EACA,SAAS+E,gBAAgBA,CAACpK,KAAK,EAAEiJ,cAAc,EAAE5D,UAAU,EAAE;IAC3D,IAAIkF,IAAI,GAAGvK,KAAK,GAAGA,KAAK,CAACuK,IAAI,GAAG,IAAI;IACpC,IAAI,CAACtB,cAAc,IAAI5D,UAAU,CAACxB,UAAU,IAAI0G,IAAI,IAAI,IAAI,EAAE;MAC5D,IAAIC,QAAQ,GAAG7Q,WAAW,CAAC0B,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrD2E,KAAK,CAACuK,IAAI,GAAGC,QAAQ,GAAGA,QAAQ,GAAG,GAAG,GAAGD,IAAI,GAAGA,IAAI;IACtD;EACF;EACA,SAAS/D,WAAWA,CAAC7H,WAAW,EAAE8L,IAAI,EAAE5N,KAAK,EAAE6N,CAAC,EAAE;IAChD,IAAI5B,OAAO,GAAG/C,WAAW,IAAI,IAAI,IAAIxJ,UAAU,CAACoC,WAAW,CAAC,CAACoH,WAAW,CAAC;IACzE,IAAI4E,KAAK,GAAGrO,iBAAiB,CAACqC,WAAW,CAAC;IAC1C,IAAImK,OAAO,EAAE;MACX;MACAvM,UAAU,CAACoC,WAAW,CAAC,CAACoH,WAAW,CAAC,GAAG,IAAI;MAC3C6E,0BAA0B,CAACD,KAAK,EAAE7B,OAAO,CAAC;IAC5C;IACA;IAAA,KACK,IAAI,CAACjD,aAAa,EAAE;MACvBiD,OAAO,GAAG,IAAI2B,IAAI,CAAC,CAAC;MACpB,IAAI3B,OAAO,YAAYpR,WAAW,EAAE;QAClCoR,OAAO,CAAC+B,EAAE,GAAGC,WAAW,CAACjO,KAAK,EAAE6N,CAAC,CAAC;MACpC;MACAK,yBAAyB,CAACJ,KAAK,EAAE7B,OAAO,CAAC;IAC3C;IACA;IACA,OAAOnO,WAAW,CAACgE,WAAW,CAAC,CAACmH,YAAY,CAAC,GAAGgD,OAAO;EACzD;EACA,SAAS8B,0BAA0BA,CAACD,KAAK,EAAE7B,OAAO,EAAE;IAClD,IAAIkC,OAAO,GAAGL,KAAK,CAAC7E,YAAY,CAAC,GAAG,CAAC,CAAC;IACtC,IAAIgD,OAAO,YAAY9Q,KAAK,EAAE;MAC5BgT,OAAO,CAAC3K,IAAI,GAAGyI,OAAO,CAAC9M,CAAC;MACxBgP,OAAO,CAAC1K,IAAI,GAAGwI,OAAO,CAAC7M,CAAC;IAC1B,CAAC,MAAM;MACL+O,OAAO,CAACzK,QAAQ,GAAGhK,MAAM,CAAC,CAAC,CAAC,EAAEuS,OAAO,CAACjJ,KAAK,CAAC;IAC9C;EACF;EACA;EACA;EACA,SAASkL,yBAAyBA,CAACJ,KAAK,EAAE7B,OAAO,EAAE;IACjD,IAAIkC,OAAO,GAAGL,KAAK,CAAC7E,YAAY,CAAC,GAAG,CAAC,CAAC;IACtC,IAAImF,UAAU,GAAGvO,QAAQ,CAACuO,UAAU;IACpC,IAAIC,OAAO,GAAGpC,OAAO,YAAYpS,OAAO,CAACsB,KAAK;IAC9C,IAAIiT,UAAU,KAAK,CAACrQ,MAAM,IAAIA,MAAM,CAACK,SAAS,KAAK,WAAW,CAAC,EAAE;MAC/D,IAAIkQ,UAAU,GAAG,CAAC;MAClB,IAAIC,UAAU,GAAG,CAAC;MAClB;MACA;MACA,IAAIC,WAAW,GAAG/O,iBAAiB,CAAC2I,UAAU,CAACgG,UAAU,CAACjQ,WAAW,CAAC,CAAC,CAAC;MACxE,IAAI,CAACJ,MAAM,IAAIyQ,WAAW,IAAIA,WAAW,CAAC9K,QAAQ,EAAE;QAClD4K,UAAU,GAAGE,WAAW,CAAC9K,QAAQ,CAAChD,KAAK;QACvC6N,UAAU,GAAGC,WAAW,CAAC9K,QAAQ,CAAC/C,MAAM;MAC1C;MACA;MACA;MACA,IAAI0N,OAAO,EAAE;QACXF,OAAO,CAAC3K,IAAI,GAAG,CAAC;QAChB2K,OAAO,CAAC1K,IAAI,GAAG8K,UAAU;MAC3B,CAAC,MAAM;QACLJ,OAAO,CAACzK,QAAQ,GAAG;UACjBvE,CAAC,EAAEmP,UAAU;UACblP,CAAC,EAAEmP,UAAU;UACb7N,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACV,CAAC;MACH;IACF;IACA;IACAwN,OAAO,CAACvK,MAAM,GAAG,CAACyK,OAAO;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,WAAWA,CAACjO,KAAK,EAAEyO,SAAS,EAAE;EACrC,OAAOzO,KAAK,GAAGxE,OAAO,GAAGiT,SAAS;AACpC;AACA,eAAetS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}