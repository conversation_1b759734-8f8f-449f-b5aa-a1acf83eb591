{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar opacityAccessPath = ['itemStyle', 'opacity'];\n/**\r\n * Piece of pie including Sector, Label, LabelLine\r\n */\nvar FunnelPiece = /** @class */function (_super) {\n  __extends(FunnelPiece, _super);\n  function FunnelPiece(data, idx) {\n    var _this = _super.call(this) || this;\n    var polygon = _this;\n    var labelLine = new graphic.Polyline();\n    var text = new graphic.Text();\n    polygon.setTextContent(text);\n    _this.setTextGuideLine(labelLine);\n    _this.updateData(data, idx, true);\n    return _this;\n  }\n  FunnelPiece.prototype.updateData = function (data, idx, firstCreate) {\n    var polygon = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var opacity = itemModel.get(opacityAccessPath);\n    opacity = opacity == null ? 1 : opacity;\n    if (!firstCreate) {\n      saveOldStyle(polygon);\n    }\n    // Update common style\n    polygon.useStyle(data.getItemVisual(idx, 'style'));\n    polygon.style.lineJoin = 'round';\n    if (firstCreate) {\n      polygon.setShape({\n        points: layout.points\n      });\n      polygon.style.opacity = 0;\n      graphic.initProps(polygon, {\n        style: {\n          opacity: opacity\n        }\n      }, seriesModel, idx);\n    } else {\n      graphic.updateProps(polygon, {\n        style: {\n          opacity: opacity\n        },\n        shape: {\n          points: layout.points\n        }\n      }, seriesModel, idx);\n    }\n    setStatesStylesFromModel(polygon, itemModel);\n    this._updateLabel(data, idx);\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  FunnelPiece.prototype._updateLabel = function (data, idx) {\n    var polygon = this;\n    var labelLine = this.getTextGuideLine();\n    var labelText = polygon.getTextContent();\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var labelLayout = layout.label;\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style.fill;\n    setLabelStyle(\n    // position will not be used in setLabelStyle\n    labelText, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      defaultOpacity: style.opacity,\n      defaultText: data.getName(idx)\n    }, {\n      normal: {\n        align: labelLayout.textAlign,\n        verticalAlign: labelLayout.verticalAlign\n      }\n    });\n    polygon.setTextConfig({\n      local: true,\n      inside: !!labelLayout.inside,\n      insideStroke: visualColor,\n      // insideFill: 'auto',\n      outsideFill: visualColor\n    });\n    var linePoints = labelLayout.linePoints;\n    labelLine.setShape({\n      points: linePoints\n    });\n    polygon.textGuideLineConfig = {\n      anchor: linePoints ? new graphic.Point(linePoints[0][0], linePoints[0][1]) : null\n    };\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    graphic.updateProps(labelText, {\n      style: {\n        x: labelLayout.x,\n        y: labelLayout.y\n      }\n    }, seriesModel, idx);\n    labelText.attr({\n      rotation: labelLayout.rotation,\n      originX: labelLayout.x,\n      originY: labelLayout.y,\n      z2: 10\n    });\n    setLabelLineStyle(polygon, getLabelLineStatesModels(itemModel), {\n      // Default use item visual color\n      stroke: visualColor\n    });\n  };\n  return FunnelPiece;\n}(graphic.Polygon);\nvar FunnelView = /** @class */function (_super) {\n  __extends(FunnelView, _super);\n  function FunnelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelView.type;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  FunnelView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    data.diff(oldData).add(function (idx) {\n      var funnelPiece = new FunnelPiece(data, idx);\n      data.setItemGraphicEl(idx, funnelPiece);\n      group.add(funnelPiece);\n    }).update(function (newIdx, oldIdx) {\n      var piece = oldData.getItemGraphicEl(oldIdx);\n      piece.updateData(data, newIdx);\n      group.add(piece);\n      data.setItemGraphicEl(newIdx, piece);\n    }).remove(function (idx) {\n      var piece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piece, seriesModel, idx);\n    }).execute();\n    this._data = data;\n  };\n  FunnelView.prototype.remove = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  FunnelView.prototype.dispose = function () {};\n  FunnelView.type = 'funnel';\n  return FunnelView;\n}(ChartView);\nexport default FunnelView;", "map": {"version": 3, "names": ["__extends", "graphic", "setStatesStylesFromModel", "toggleHoverEmphasis", "ChartView", "setLabelLineStyle", "getLabelLineStatesModels", "setLabelStyle", "getLabelStatesModels", "saveOldStyle", "opacityAccessPath", "FunnelPiece", "_super", "data", "idx", "_this", "call", "polygon", "labelLine", "Polyline", "text", "Text", "setTextContent", "setTextGuideLine", "updateData", "prototype", "firstCreate", "seriesModel", "hostModel", "itemModel", "getItemModel", "layout", "getItemLayout", "emphasisModel", "getModel", "opacity", "get", "useStyle", "getItemVisual", "style", "lineJoin", "setShape", "points", "initProps", "updateProps", "shape", "_updateLabel", "getTextGuideLine", "labelText", "getTextContent", "labelLayout", "label", "visualColor", "fill", "labelFetcher", "labelDataIndex", "defaultOpacity", "defaultText", "getName", "normal", "align", "textAlign", "verticalAlign", "setTextConfig", "local", "inside", "insideStroke", "outsideFill", "linePoints", "textGuideLineConfig", "anchor", "Point", "x", "y", "attr", "rotation", "originX", "originY", "z2", "stroke", "Polygon", "FunnelView", "apply", "arguments", "type", "ignoreLabelLineUpdate", "render", "ecModel", "api", "getData", "oldData", "_data", "group", "diff", "add", "funnelPiece", "setItemGraphicEl", "update", "newIdx", "oldIdx", "piece", "getItemGraphicEl", "remove", "removeElementWithFadeOut", "execute", "removeAll", "dispose"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/funnel/FunnelView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar opacityAccessPath = ['itemStyle', 'opacity'];\n/**\r\n * Piece of pie including Sector, Label, LabelLine\r\n */\nvar FunnelPiece = /** @class */function (_super) {\n  __extends(FunnelPiece, _super);\n  function FunnelPiece(data, idx) {\n    var _this = _super.call(this) || this;\n    var polygon = _this;\n    var labelLine = new graphic.Polyline();\n    var text = new graphic.Text();\n    polygon.setTextContent(text);\n    _this.setTextGuideLine(labelLine);\n    _this.updateData(data, idx, true);\n    return _this;\n  }\n  FunnelPiece.prototype.updateData = function (data, idx, firstCreate) {\n    var polygon = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var opacity = itemModel.get(opacityAccessPath);\n    opacity = opacity == null ? 1 : opacity;\n    if (!firstCreate) {\n      saveOldStyle(polygon);\n    }\n    // Update common style\n    polygon.useStyle(data.getItemVisual(idx, 'style'));\n    polygon.style.lineJoin = 'round';\n    if (firstCreate) {\n      polygon.setShape({\n        points: layout.points\n      });\n      polygon.style.opacity = 0;\n      graphic.initProps(polygon, {\n        style: {\n          opacity: opacity\n        }\n      }, seriesModel, idx);\n    } else {\n      graphic.updateProps(polygon, {\n        style: {\n          opacity: opacity\n        },\n        shape: {\n          points: layout.points\n        }\n      }, seriesModel, idx);\n    }\n    setStatesStylesFromModel(polygon, itemModel);\n    this._updateLabel(data, idx);\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  FunnelPiece.prototype._updateLabel = function (data, idx) {\n    var polygon = this;\n    var labelLine = this.getTextGuideLine();\n    var labelText = polygon.getTextContent();\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var labelLayout = layout.label;\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style.fill;\n    setLabelStyle(\n    // position will not be used in setLabelStyle\n    labelText, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      defaultOpacity: style.opacity,\n      defaultText: data.getName(idx)\n    }, {\n      normal: {\n        align: labelLayout.textAlign,\n        verticalAlign: labelLayout.verticalAlign\n      }\n    });\n    polygon.setTextConfig({\n      local: true,\n      inside: !!labelLayout.inside,\n      insideStroke: visualColor,\n      // insideFill: 'auto',\n      outsideFill: visualColor\n    });\n    var linePoints = labelLayout.linePoints;\n    labelLine.setShape({\n      points: linePoints\n    });\n    polygon.textGuideLineConfig = {\n      anchor: linePoints ? new graphic.Point(linePoints[0][0], linePoints[0][1]) : null\n    };\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    graphic.updateProps(labelText, {\n      style: {\n        x: labelLayout.x,\n        y: labelLayout.y\n      }\n    }, seriesModel, idx);\n    labelText.attr({\n      rotation: labelLayout.rotation,\n      originX: labelLayout.x,\n      originY: labelLayout.y,\n      z2: 10\n    });\n    setLabelLineStyle(polygon, getLabelLineStatesModels(itemModel), {\n      // Default use item visual color\n      stroke: visualColor\n    });\n  };\n  return FunnelPiece;\n}(graphic.Polygon);\nvar FunnelView = /** @class */function (_super) {\n  __extends(FunnelView, _super);\n  function FunnelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelView.type;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  FunnelView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    data.diff(oldData).add(function (idx) {\n      var funnelPiece = new FunnelPiece(data, idx);\n      data.setItemGraphicEl(idx, funnelPiece);\n      group.add(funnelPiece);\n    }).update(function (newIdx, oldIdx) {\n      var piece = oldData.getItemGraphicEl(oldIdx);\n      piece.updateData(data, newIdx);\n      group.add(piece);\n      data.setItemGraphicEl(newIdx, piece);\n    }).remove(function (idx) {\n      var piece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piece, seriesModel, idx);\n    }).execute();\n    this._data = data;\n  };\n  FunnelView.prototype.remove = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  FunnelView.prototype.dispose = function () {};\n  FunnelView.type = 'funnel';\n  return FunnelView;\n}(ChartView);\nexport default FunnelView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,iCAAiC;AAC7F,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,YAAY,QAAQ,oCAAoC;AACjE,IAAIC,iBAAiB,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC;AAChD;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CZ,SAAS,CAACW,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,IAAI,EAAEC,GAAG,EAAE;IAC9B,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC,IAAIC,OAAO,GAAGF,KAAK;IACnB,IAAIG,SAAS,GAAG,IAAIjB,OAAO,CAACkB,QAAQ,CAAC,CAAC;IACtC,IAAIC,IAAI,GAAG,IAAInB,OAAO,CAACoB,IAAI,CAAC,CAAC;IAC7BJ,OAAO,CAACK,cAAc,CAACF,IAAI,CAAC;IAC5BL,KAAK,CAACQ,gBAAgB,CAACL,SAAS,CAAC;IACjCH,KAAK,CAACS,UAAU,CAACX,IAAI,EAAEC,GAAG,EAAE,IAAI,CAAC;IACjC,OAAOC,KAAK;EACd;EACAJ,WAAW,CAACc,SAAS,CAACD,UAAU,GAAG,UAAUX,IAAI,EAAEC,GAAG,EAAEY,WAAW,EAAE;IACnE,IAAIT,OAAO,GAAG,IAAI;IAClB,IAAIU,WAAW,GAAGd,IAAI,CAACe,SAAS;IAChC,IAAIC,SAAS,GAAGhB,IAAI,CAACiB,YAAY,CAAChB,GAAG,CAAC;IACtC,IAAIiB,MAAM,GAAGlB,IAAI,CAACmB,aAAa,CAAClB,GAAG,CAAC;IACpC,IAAImB,aAAa,GAAGJ,SAAS,CAACK,QAAQ,CAAC,UAAU,CAAC;IAClD,IAAIC,OAAO,GAAGN,SAAS,CAACO,GAAG,CAAC1B,iBAAiB,CAAC;IAC9CyB,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO;IACvC,IAAI,CAACT,WAAW,EAAE;MAChBjB,YAAY,CAACQ,OAAO,CAAC;IACvB;IACA;IACAA,OAAO,CAACoB,QAAQ,CAACxB,IAAI,CAACyB,aAAa,CAACxB,GAAG,EAAE,OAAO,CAAC,CAAC;IAClDG,OAAO,CAACsB,KAAK,CAACC,QAAQ,GAAG,OAAO;IAChC,IAAId,WAAW,EAAE;MACfT,OAAO,CAACwB,QAAQ,CAAC;QACfC,MAAM,EAAEX,MAAM,CAACW;MACjB,CAAC,CAAC;MACFzB,OAAO,CAACsB,KAAK,CAACJ,OAAO,GAAG,CAAC;MACzBlC,OAAO,CAAC0C,SAAS,CAAC1B,OAAO,EAAE;QACzBsB,KAAK,EAAE;UACLJ,OAAO,EAAEA;QACX;MACF,CAAC,EAAER,WAAW,EAAEb,GAAG,CAAC;IACtB,CAAC,MAAM;MACLb,OAAO,CAAC2C,WAAW,CAAC3B,OAAO,EAAE;QAC3BsB,KAAK,EAAE;UACLJ,OAAO,EAAEA;QACX,CAAC;QACDU,KAAK,EAAE;UACLH,MAAM,EAAEX,MAAM,CAACW;QACjB;MACF,CAAC,EAAEf,WAAW,EAAEb,GAAG,CAAC;IACtB;IACAZ,wBAAwB,CAACe,OAAO,EAAEY,SAAS,CAAC;IAC5C,IAAI,CAACiB,YAAY,CAACjC,IAAI,EAAEC,GAAG,CAAC;IAC5BX,mBAAmB,CAAC,IAAI,EAAE8B,aAAa,CAACG,GAAG,CAAC,OAAO,CAAC,EAAEH,aAAa,CAACG,GAAG,CAAC,WAAW,CAAC,EAAEH,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC,CAAC;EACtH,CAAC;EACDzB,WAAW,CAACc,SAAS,CAACqB,YAAY,GAAG,UAAUjC,IAAI,EAAEC,GAAG,EAAE;IACxD,IAAIG,OAAO,GAAG,IAAI;IAClB,IAAIC,SAAS,GAAG,IAAI,CAAC6B,gBAAgB,CAAC,CAAC;IACvC,IAAIC,SAAS,GAAG/B,OAAO,CAACgC,cAAc,CAAC,CAAC;IACxC,IAAItB,WAAW,GAAGd,IAAI,CAACe,SAAS;IAChC,IAAIC,SAAS,GAAGhB,IAAI,CAACiB,YAAY,CAAChB,GAAG,CAAC;IACtC,IAAIiB,MAAM,GAAGlB,IAAI,CAACmB,aAAa,CAAClB,GAAG,CAAC;IACpC,IAAIoC,WAAW,GAAGnB,MAAM,CAACoB,KAAK;IAC9B,IAAIZ,KAAK,GAAG1B,IAAI,CAACyB,aAAa,CAACxB,GAAG,EAAE,OAAO,CAAC;IAC5C,IAAIsC,WAAW,GAAGb,KAAK,CAACc,IAAI;IAC5B9C,aAAa;IACb;IACAyC,SAAS,EAAExC,oBAAoB,CAACqB,SAAS,CAAC,EAAE;MAC1CyB,YAAY,EAAEzC,IAAI,CAACe,SAAS;MAC5B2B,cAAc,EAAEzC,GAAG;MACnB0C,cAAc,EAAEjB,KAAK,CAACJ,OAAO;MAC7BsB,WAAW,EAAE5C,IAAI,CAAC6C,OAAO,CAAC5C,GAAG;IAC/B,CAAC,EAAE;MACD6C,MAAM,EAAE;QACNC,KAAK,EAAEV,WAAW,CAACW,SAAS;QAC5BC,aAAa,EAAEZ,WAAW,CAACY;MAC7B;IACF,CAAC,CAAC;IACF7C,OAAO,CAAC8C,aAAa,CAAC;MACpBC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC,CAACf,WAAW,CAACe,MAAM;MAC5BC,YAAY,EAAEd,WAAW;MACzB;MACAe,WAAW,EAAEf;IACf,CAAC,CAAC;IACF,IAAIgB,UAAU,GAAGlB,WAAW,CAACkB,UAAU;IACvClD,SAAS,CAACuB,QAAQ,CAAC;MACjBC,MAAM,EAAE0B;IACV,CAAC,CAAC;IACFnD,OAAO,CAACoD,mBAAmB,GAAG;MAC5BC,MAAM,EAAEF,UAAU,GAAG,IAAInE,OAAO,CAACsE,KAAK,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IAC/E,CAAC;IACD;IACA;IACAnE,OAAO,CAAC2C,WAAW,CAACI,SAAS,EAAE;MAC7BT,KAAK,EAAE;QACLiC,CAAC,EAAEtB,WAAW,CAACsB,CAAC;QAChBC,CAAC,EAAEvB,WAAW,CAACuB;MACjB;IACF,CAAC,EAAE9C,WAAW,EAAEb,GAAG,CAAC;IACpBkC,SAAS,CAAC0B,IAAI,CAAC;MACbC,QAAQ,EAAEzB,WAAW,CAACyB,QAAQ;MAC9BC,OAAO,EAAE1B,WAAW,CAACsB,CAAC;MACtBK,OAAO,EAAE3B,WAAW,CAACuB,CAAC;MACtBK,EAAE,EAAE;IACN,CAAC,CAAC;IACFzE,iBAAiB,CAACY,OAAO,EAAEX,wBAAwB,CAACuB,SAAS,CAAC,EAAE;MAC9D;MACAkD,MAAM,EAAE3B;IACV,CAAC,CAAC;EACJ,CAAC;EACD,OAAOzC,WAAW;AACpB,CAAC,CAACV,OAAO,CAAC+E,OAAO,CAAC;AAClB,IAAIC,UAAU,GAAG,aAAa,UAAUrE,MAAM,EAAE;EAC9CZ,SAAS,CAACiF,UAAU,EAAErE,MAAM,CAAC;EAC7B,SAASqE,UAAUA,CAAA,EAAG;IACpB,IAAIlE,KAAK,GAAGH,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACsE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEpE,KAAK,CAACqE,IAAI,GAAGH,UAAU,CAACG,IAAI;IAC5BrE,KAAK,CAACsE,qBAAqB,GAAG,IAAI;IAClC,OAAOtE,KAAK;EACd;EACAkE,UAAU,CAACxD,SAAS,CAAC6D,MAAM,GAAG,UAAU3D,WAAW,EAAE4D,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAI3E,IAAI,GAAGc,WAAW,CAAC8D,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB/E,IAAI,CAACgF,IAAI,CAACH,OAAO,CAAC,CAACI,GAAG,CAAC,UAAUhF,GAAG,EAAE;MACpC,IAAIiF,WAAW,GAAG,IAAIpF,WAAW,CAACE,IAAI,EAAEC,GAAG,CAAC;MAC5CD,IAAI,CAACmF,gBAAgB,CAAClF,GAAG,EAAEiF,WAAW,CAAC;MACvCH,KAAK,CAACE,GAAG,CAACC,WAAW,CAAC;IACxB,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;MAClC,IAAIC,KAAK,GAAGV,OAAO,CAACW,gBAAgB,CAACF,MAAM,CAAC;MAC5CC,KAAK,CAAC5E,UAAU,CAACX,IAAI,EAAEqF,MAAM,CAAC;MAC9BN,KAAK,CAACE,GAAG,CAACM,KAAK,CAAC;MAChBvF,IAAI,CAACmF,gBAAgB,CAACE,MAAM,EAAEE,KAAK,CAAC;IACtC,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUxF,GAAG,EAAE;MACvB,IAAIsF,KAAK,GAAGV,OAAO,CAACW,gBAAgB,CAACvF,GAAG,CAAC;MACzCb,OAAO,CAACsG,wBAAwB,CAACH,KAAK,EAAEzE,WAAW,EAAEb,GAAG,CAAC;IAC3D,CAAC,CAAC,CAAC0F,OAAO,CAAC,CAAC;IACZ,IAAI,CAACb,KAAK,GAAG9E,IAAI;EACnB,CAAC;EACDoE,UAAU,CAACxD,SAAS,CAAC6E,MAAM,GAAG,YAAY;IACxC,IAAI,CAACV,KAAK,CAACa,SAAS,CAAC,CAAC;IACtB,IAAI,CAACd,KAAK,GAAG,IAAI;EACnB,CAAC;EACDV,UAAU,CAACxD,SAAS,CAACiF,OAAO,GAAG,YAAY,CAAC,CAAC;EAC7CzB,UAAU,CAACG,IAAI,GAAG,QAAQ;EAC1B,OAAOH,UAAU;AACnB,CAAC,CAAC7E,SAAS,CAAC;AACZ,eAAe6E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}