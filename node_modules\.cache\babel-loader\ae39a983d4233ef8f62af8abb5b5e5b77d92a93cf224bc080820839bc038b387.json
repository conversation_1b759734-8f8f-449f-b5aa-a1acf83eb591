{"ast": null, "code": "export function create() {\n  return [1, 0, 0, 1, 0, 0];\n}\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = 0;\n  out[5] = 0;\n  return out;\n}\nexport function copy(out, m) {\n  out[0] = m[0];\n  out[1] = m[1];\n  out[2] = m[2];\n  out[3] = m[3];\n  out[4] = m[4];\n  out[5] = m[5];\n  return out;\n}\nexport function mul(out, m1, m2) {\n  var out0 = m1[0] * m2[0] + m1[2] * m2[1];\n  var out1 = m1[1] * m2[0] + m1[3] * m2[1];\n  var out2 = m1[0] * m2[2] + m1[2] * m2[3];\n  var out3 = m1[1] * m2[2] + m1[3] * m2[3];\n  var out4 = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];\n  var out5 = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];\n  out[0] = out0;\n  out[1] = out1;\n  out[2] = out2;\n  out[3] = out3;\n  out[4] = out4;\n  out[5] = out5;\n  return out;\n}\nexport function translate(out, a, v) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4] + v[0];\n  out[5] = a[5] + v[1];\n  return out;\n}\nexport function rotate(out, a, rad, pivot) {\n  if (pivot === void 0) {\n    pivot = [0, 0];\n  }\n  var aa = a[0];\n  var ac = a[2];\n  var atx = a[4];\n  var ab = a[1];\n  var ad = a[3];\n  var aty = a[5];\n  var st = Math.sin(rad);\n  var ct = Math.cos(rad);\n  out[0] = aa * ct + ab * st;\n  out[1] = -aa * st + ab * ct;\n  out[2] = ac * ct + ad * st;\n  out[3] = -ac * st + ct * ad;\n  out[4] = ct * (atx - pivot[0]) + st * (aty - pivot[1]) + pivot[0];\n  out[5] = ct * (aty - pivot[1]) - st * (atx - pivot[0]) + pivot[1];\n  return out;\n}\nexport function scale(out, a, v) {\n  var vx = v[0];\n  var vy = v[1];\n  out[0] = a[0] * vx;\n  out[1] = a[1] * vy;\n  out[2] = a[2] * vx;\n  out[3] = a[3] * vy;\n  out[4] = a[4] * vx;\n  out[5] = a[5] * vy;\n  return out;\n}\nexport function invert(out, a) {\n  var aa = a[0];\n  var ac = a[2];\n  var atx = a[4];\n  var ab = a[1];\n  var ad = a[3];\n  var aty = a[5];\n  var det = aa * ad - ab * ac;\n  if (!det) {\n    return null;\n  }\n  det = 1.0 / det;\n  out[0] = ad * det;\n  out[1] = -ab * det;\n  out[2] = -ac * det;\n  out[3] = aa * det;\n  out[4] = (ac * aty - ad * atx) * det;\n  out[5] = (ab * atx - aa * aty) * det;\n  return out;\n}\nexport function clone(a) {\n  var b = create();\n  copy(b, a);\n  return b;\n}", "map": {"version": 3, "names": ["create", "identity", "out", "copy", "m", "mul", "m1", "m2", "out0", "out1", "out2", "out3", "out4", "out5", "translate", "a", "v", "rotate", "rad", "pivot", "aa", "ac", "atx", "ab", "ad", "aty", "st", "Math", "sin", "ct", "cos", "scale", "vx", "vy", "invert", "det", "clone", "b"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/core/matrix.js"], "sourcesContent": ["export function create() {\n    return [1, 0, 0, 1, 0, 0];\n}\nexport function identity(out) {\n    out[0] = 1;\n    out[1] = 0;\n    out[2] = 0;\n    out[3] = 1;\n    out[4] = 0;\n    out[5] = 0;\n    return out;\n}\nexport function copy(out, m) {\n    out[0] = m[0];\n    out[1] = m[1];\n    out[2] = m[2];\n    out[3] = m[3];\n    out[4] = m[4];\n    out[5] = m[5];\n    return out;\n}\nexport function mul(out, m1, m2) {\n    var out0 = m1[0] * m2[0] + m1[2] * m2[1];\n    var out1 = m1[1] * m2[0] + m1[3] * m2[1];\n    var out2 = m1[0] * m2[2] + m1[2] * m2[3];\n    var out3 = m1[1] * m2[2] + m1[3] * m2[3];\n    var out4 = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];\n    var out5 = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];\n    out[0] = out0;\n    out[1] = out1;\n    out[2] = out2;\n    out[3] = out3;\n    out[4] = out4;\n    out[5] = out5;\n    return out;\n}\nexport function translate(out, a, v) {\n    out[0] = a[0];\n    out[1] = a[1];\n    out[2] = a[2];\n    out[3] = a[3];\n    out[4] = a[4] + v[0];\n    out[5] = a[5] + v[1];\n    return out;\n}\nexport function rotate(out, a, rad, pivot) {\n    if (pivot === void 0) { pivot = [0, 0]; }\n    var aa = a[0];\n    var ac = a[2];\n    var atx = a[4];\n    var ab = a[1];\n    var ad = a[3];\n    var aty = a[5];\n    var st = Math.sin(rad);\n    var ct = Math.cos(rad);\n    out[0] = aa * ct + ab * st;\n    out[1] = -aa * st + ab * ct;\n    out[2] = ac * ct + ad * st;\n    out[3] = -ac * st + ct * ad;\n    out[4] = ct * (atx - pivot[0]) + st * (aty - pivot[1]) + pivot[0];\n    out[5] = ct * (aty - pivot[1]) - st * (atx - pivot[0]) + pivot[1];\n    return out;\n}\nexport function scale(out, a, v) {\n    var vx = v[0];\n    var vy = v[1];\n    out[0] = a[0] * vx;\n    out[1] = a[1] * vy;\n    out[2] = a[2] * vx;\n    out[3] = a[3] * vy;\n    out[4] = a[4] * vx;\n    out[5] = a[5] * vy;\n    return out;\n}\nexport function invert(out, a) {\n    var aa = a[0];\n    var ac = a[2];\n    var atx = a[4];\n    var ab = a[1];\n    var ad = a[3];\n    var aty = a[5];\n    var det = aa * ad - ab * ac;\n    if (!det) {\n        return null;\n    }\n    det = 1.0 / det;\n    out[0] = ad * det;\n    out[1] = -ab * det;\n    out[2] = -ac * det;\n    out[3] = aa * det;\n    out[4] = (ac * aty - ad * atx) * det;\n    out[5] = (ab * atx - aa * aty) * det;\n    return out;\n}\nexport function clone(a) {\n    var b = create();\n    copy(b, a);\n    return b;\n}\n"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAAA,EAAG;EACrB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC1BA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACV,OAAOA,GAAG;AACd;AACA,OAAO,SAASC,IAAIA,CAACD,GAAG,EAAEE,CAAC,EAAE;EACzBF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACbF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACbF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACbF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACbF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACbF,GAAG,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;EACb,OAAOF,GAAG;AACd;AACA,OAAO,SAASG,GAAGA,CAACH,GAAG,EAAEI,EAAE,EAAEC,EAAE,EAAE;EAC7B,IAAIC,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAIE,IAAI,GAAGH,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAIG,IAAI,GAAGJ,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAII,IAAI,GAAGL,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAIK,IAAI,GAAGN,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC;EAChD,IAAIO,IAAI,GAAGP,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC;EAChDJ,GAAG,CAAC,CAAC,CAAC,GAAGM,IAAI;EACbN,GAAG,CAAC,CAAC,CAAC,GAAGO,IAAI;EACbP,GAAG,CAAC,CAAC,CAAC,GAAGQ,IAAI;EACbR,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI;EACbT,GAAG,CAAC,CAAC,CAAC,GAAGU,IAAI;EACbV,GAAG,CAAC,CAAC,CAAC,GAAGW,IAAI;EACb,OAAOX,GAAG;AACd;AACA,OAAO,SAASY,SAASA,CAACZ,GAAG,EAAEa,CAAC,EAAEC,CAAC,EAAE;EACjCd,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC;EACbb,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC;EACbb,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC;EACbb,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC;EACbb,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACpBd,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOd,GAAG;AACd;AACA,OAAO,SAASe,MAAMA,CAACf,GAAG,EAAEa,CAAC,EAAEG,GAAG,EAAEC,KAAK,EAAE;EACvC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAAE;EACxC,IAAIC,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC;EACb,IAAIM,EAAE,GAAGN,CAAC,CAAC,CAAC,CAAC;EACb,IAAIO,GAAG,GAAGP,CAAC,CAAC,CAAC,CAAC;EACd,IAAIQ,EAAE,GAAGR,CAAC,CAAC,CAAC,CAAC;EACb,IAAIS,EAAE,GAAGT,CAAC,CAAC,CAAC,CAAC;EACb,IAAIU,GAAG,GAAGV,CAAC,CAAC,CAAC,CAAC;EACd,IAAIW,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACV,GAAG,CAAC;EACtB,IAAIW,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACZ,GAAG,CAAC;EACtBhB,GAAG,CAAC,CAAC,CAAC,GAAGkB,EAAE,GAAGS,EAAE,GAAGN,EAAE,GAAGG,EAAE;EAC1BxB,GAAG,CAAC,CAAC,CAAC,GAAG,CAACkB,EAAE,GAAGM,EAAE,GAAGH,EAAE,GAAGM,EAAE;EAC3B3B,GAAG,CAAC,CAAC,CAAC,GAAGmB,EAAE,GAAGQ,EAAE,GAAGL,EAAE,GAAGE,EAAE;EAC1BxB,GAAG,CAAC,CAAC,CAAC,GAAG,CAACmB,EAAE,GAAGK,EAAE,GAAGG,EAAE,GAAGL,EAAE;EAC3BtB,GAAG,CAAC,CAAC,CAAC,GAAG2B,EAAE,IAAIP,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGO,EAAE,IAAID,GAAG,GAAGN,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;EACjEjB,GAAG,CAAC,CAAC,CAAC,GAAG2B,EAAE,IAAIJ,GAAG,GAAGN,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGO,EAAE,IAAIJ,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;EACjE,OAAOjB,GAAG;AACd;AACA,OAAO,SAAS6B,KAAKA,CAAC7B,GAAG,EAAEa,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAIgB,EAAE,GAAGhB,CAAC,CAAC,CAAC,CAAC;EACb,IAAIiB,EAAE,GAAGjB,CAAC,CAAC,CAAC,CAAC;EACbd,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGiB,EAAE;EAClB9B,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGkB,EAAE;EAClB/B,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGiB,EAAE;EAClB9B,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGkB,EAAE;EAClB/B,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGiB,EAAE;EAClB9B,GAAG,CAAC,CAAC,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAGkB,EAAE;EAClB,OAAO/B,GAAG;AACd;AACA,OAAO,SAASgC,MAAMA,CAAChC,GAAG,EAAEa,CAAC,EAAE;EAC3B,IAAIK,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC;EACb,IAAIM,EAAE,GAAGN,CAAC,CAAC,CAAC,CAAC;EACb,IAAIO,GAAG,GAAGP,CAAC,CAAC,CAAC,CAAC;EACd,IAAIQ,EAAE,GAAGR,CAAC,CAAC,CAAC,CAAC;EACb,IAAIS,EAAE,GAAGT,CAAC,CAAC,CAAC,CAAC;EACb,IAAIU,GAAG,GAAGV,CAAC,CAAC,CAAC,CAAC;EACd,IAAIoB,GAAG,GAAGf,EAAE,GAAGI,EAAE,GAAGD,EAAE,GAAGF,EAAE;EAC3B,IAAI,CAACc,GAAG,EAAE;IACN,OAAO,IAAI;EACf;EACAA,GAAG,GAAG,GAAG,GAAGA,GAAG;EACfjC,GAAG,CAAC,CAAC,CAAC,GAAGsB,EAAE,GAAGW,GAAG;EACjBjC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACqB,EAAE,GAAGY,GAAG;EAClBjC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACmB,EAAE,GAAGc,GAAG;EAClBjC,GAAG,CAAC,CAAC,CAAC,GAAGkB,EAAE,GAAGe,GAAG;EACjBjC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACmB,EAAE,GAAGI,GAAG,GAAGD,EAAE,GAAGF,GAAG,IAAIa,GAAG;EACpCjC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACqB,EAAE,GAAGD,GAAG,GAAGF,EAAE,GAAGK,GAAG,IAAIU,GAAG;EACpC,OAAOjC,GAAG;AACd;AACA,OAAO,SAASkC,KAAKA,CAACrB,CAAC,EAAE;EACrB,IAAIsB,CAAC,GAAGrC,MAAM,CAAC,CAAC;EAChBG,IAAI,CAACkC,CAAC,EAAEtB,CAAC,CAAC;EACV,OAAOsB,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}