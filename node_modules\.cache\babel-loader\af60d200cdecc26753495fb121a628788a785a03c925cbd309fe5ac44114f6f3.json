{"ast": null, "code": "export function buildPath(ctx, shape) {\n  var x = shape.x;\n  var y = shape.y;\n  var width = shape.width;\n  var height = shape.height;\n  var r = shape.r;\n  var r1;\n  var r2;\n  var r3;\n  var r4;\n  if (width < 0) {\n    x = x + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y = y + height;\n    height = -height;\n  }\n  if (typeof r === 'number') {\n    r1 = r2 = r3 = r4 = r;\n  } else if (r instanceof Array) {\n    if (r.length === 1) {\n      r1 = r2 = r3 = r4 = r[0];\n    } else if (r.length === 2) {\n      r1 = r3 = r[0];\n      r2 = r4 = r[1];\n    } else if (r.length === 3) {\n      r1 = r[0];\n      r2 = r4 = r[1];\n      r3 = r[2];\n    } else {\n      r1 = r[0];\n      r2 = r[1];\n      r3 = r[2];\n      r4 = r[3];\n    }\n  } else {\n    r1 = r2 = r3 = r4 = 0;\n  }\n  var total;\n  if (r1 + r2 > width) {\n    total = r1 + r2;\n    r1 *= width / total;\n    r2 *= width / total;\n  }\n  if (r3 + r4 > width) {\n    total = r3 + r4;\n    r3 *= width / total;\n    r4 *= width / total;\n  }\n  if (r2 + r3 > height) {\n    total = r2 + r3;\n    r2 *= height / total;\n    r3 *= height / total;\n  }\n  if (r1 + r4 > height) {\n    total = r1 + r4;\n    r1 *= height / total;\n    r4 *= height / total;\n  }\n  ctx.moveTo(x + r1, y);\n  ctx.lineTo(x + width - r2, y);\n  r2 !== 0 && ctx.arc(x + width - r2, y + r2, r2, -Math.PI / 2, 0);\n  ctx.lineTo(x + width, y + height - r3);\n  r3 !== 0 && ctx.arc(x + width - r3, y + height - r3, r3, 0, Math.PI / 2);\n  ctx.lineTo(x + r4, y + height);\n  r4 !== 0 && ctx.arc(x + r4, y + height - r4, r4, Math.PI / 2, Math.PI);\n  ctx.lineTo(x, y + r1);\n  r1 !== 0 && ctx.arc(x + r1, y + r1, r1, Math.PI, Math.PI * 1.5);\n}", "map": {"version": 3, "names": ["buildPath", "ctx", "shape", "x", "y", "width", "height", "r", "r1", "r2", "r3", "r4", "Array", "length", "total", "moveTo", "lineTo", "arc", "Math", "PI"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/graphic/helper/roundRect.js"], "sourcesContent": ["export function buildPath(ctx, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var width = shape.width;\n    var height = shape.height;\n    var r = shape.r;\n    var r1;\n    var r2;\n    var r3;\n    var r4;\n    if (width < 0) {\n        x = x + width;\n        width = -width;\n    }\n    if (height < 0) {\n        y = y + height;\n        height = -height;\n    }\n    if (typeof r === 'number') {\n        r1 = r2 = r3 = r4 = r;\n    }\n    else if (r instanceof Array) {\n        if (r.length === 1) {\n            r1 = r2 = r3 = r4 = r[0];\n        }\n        else if (r.length === 2) {\n            r1 = r3 = r[0];\n            r2 = r4 = r[1];\n        }\n        else if (r.length === 3) {\n            r1 = r[0];\n            r2 = r4 = r[1];\n            r3 = r[2];\n        }\n        else {\n            r1 = r[0];\n            r2 = r[1];\n            r3 = r[2];\n            r4 = r[3];\n        }\n    }\n    else {\n        r1 = r2 = r3 = r4 = 0;\n    }\n    var total;\n    if (r1 + r2 > width) {\n        total = r1 + r2;\n        r1 *= width / total;\n        r2 *= width / total;\n    }\n    if (r3 + r4 > width) {\n        total = r3 + r4;\n        r3 *= width / total;\n        r4 *= width / total;\n    }\n    if (r2 + r3 > height) {\n        total = r2 + r3;\n        r2 *= height / total;\n        r3 *= height / total;\n    }\n    if (r1 + r4 > height) {\n        total = r1 + r4;\n        r1 *= height / total;\n        r4 *= height / total;\n    }\n    ctx.moveTo(x + r1, y);\n    ctx.lineTo(x + width - r2, y);\n    r2 !== 0 && ctx.arc(x + width - r2, y + r2, r2, -Math.PI / 2, 0);\n    ctx.lineTo(x + width, y + height - r3);\n    r3 !== 0 && ctx.arc(x + width - r3, y + height - r3, r3, 0, Math.PI / 2);\n    ctx.lineTo(x + r4, y + height);\n    r4 !== 0 && ctx.arc(x + r4, y + height - r4, r4, Math.PI / 2, Math.PI);\n    ctx.lineTo(x, y + r1);\n    r1 !== 0 && ctx.arc(x + r1, y + r1, r1, Math.PI, Math.PI * 1.5);\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAClC,IAAIC,CAAC,GAAGD,KAAK,CAACC,CAAC;EACf,IAAIC,CAAC,GAAGF,KAAK,CAACE,CAAC;EACf,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;EACvB,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EACzB,IAAIC,CAAC,GAAGL,KAAK,CAACK,CAAC;EACf,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIN,KAAK,GAAG,CAAC,EAAE;IACXF,CAAC,GAAGA,CAAC,GAAGE,KAAK;IACbA,KAAK,GAAG,CAACA,KAAK;EAClB;EACA,IAAIC,MAAM,GAAG,CAAC,EAAE;IACZF,CAAC,GAAGA,CAAC,GAAGE,MAAM;IACdA,MAAM,GAAG,CAACA,MAAM;EACpB;EACA,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IACvBC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGJ,CAAC;EACzB,CAAC,MACI,IAAIA,CAAC,YAAYK,KAAK,EAAE;IACzB,IAAIL,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MAChBL,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,MACI,IAAIA,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MACrBL,EAAE,GAAGE,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;MACdE,EAAE,GAAGE,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,MACI,IAAIA,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MACrBL,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;MACTE,EAAE,GAAGE,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;MACdG,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,MACI;MACDC,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;MACTE,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;MACTG,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;MACTI,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IACb;EACJ,CAAC,MACI;IACDC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAAC;EACzB;EACA,IAAIG,KAAK;EACT,IAAIN,EAAE,GAAGC,EAAE,GAAGJ,KAAK,EAAE;IACjBS,KAAK,GAAGN,EAAE,GAAGC,EAAE;IACfD,EAAE,IAAIH,KAAK,GAAGS,KAAK;IACnBL,EAAE,IAAIJ,KAAK,GAAGS,KAAK;EACvB;EACA,IAAIJ,EAAE,GAAGC,EAAE,GAAGN,KAAK,EAAE;IACjBS,KAAK,GAAGJ,EAAE,GAAGC,EAAE;IACfD,EAAE,IAAIL,KAAK,GAAGS,KAAK;IACnBH,EAAE,IAAIN,KAAK,GAAGS,KAAK;EACvB;EACA,IAAIL,EAAE,GAAGC,EAAE,GAAGJ,MAAM,EAAE;IAClBQ,KAAK,GAAGL,EAAE,GAAGC,EAAE;IACfD,EAAE,IAAIH,MAAM,GAAGQ,KAAK;IACpBJ,EAAE,IAAIJ,MAAM,GAAGQ,KAAK;EACxB;EACA,IAAIN,EAAE,GAAGG,EAAE,GAAGL,MAAM,EAAE;IAClBQ,KAAK,GAAGN,EAAE,GAAGG,EAAE;IACfH,EAAE,IAAIF,MAAM,GAAGQ,KAAK;IACpBH,EAAE,IAAIL,MAAM,GAAGQ,KAAK;EACxB;EACAb,GAAG,CAACc,MAAM,CAACZ,CAAC,GAAGK,EAAE,EAAEJ,CAAC,CAAC;EACrBH,GAAG,CAACe,MAAM,CAACb,CAAC,GAAGE,KAAK,GAAGI,EAAE,EAAEL,CAAC,CAAC;EAC7BK,EAAE,KAAK,CAAC,IAAIR,GAAG,CAACgB,GAAG,CAACd,CAAC,GAAGE,KAAK,GAAGI,EAAE,EAAEL,CAAC,GAAGK,EAAE,EAAEA,EAAE,EAAE,CAACS,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;EAChElB,GAAG,CAACe,MAAM,CAACb,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,GAAGI,EAAE,CAAC;EACtCA,EAAE,KAAK,CAAC,IAAIT,GAAG,CAACgB,GAAG,CAACd,CAAC,GAAGE,KAAK,GAAGK,EAAE,EAAEN,CAAC,GAAGE,MAAM,GAAGI,EAAE,EAAEA,EAAE,EAAE,CAAC,EAAEQ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;EACxElB,GAAG,CAACe,MAAM,CAACb,CAAC,GAAGQ,EAAE,EAAEP,CAAC,GAAGE,MAAM,CAAC;EAC9BK,EAAE,KAAK,CAAC,IAAIV,GAAG,CAACgB,GAAG,CAACd,CAAC,GAAGQ,EAAE,EAAEP,CAAC,GAAGE,MAAM,GAAGK,EAAE,EAAEA,EAAE,EAAEO,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,IAAI,CAACC,EAAE,CAAC;EACtElB,GAAG,CAACe,MAAM,CAACb,CAAC,EAAEC,CAAC,GAAGI,EAAE,CAAC;EACrBA,EAAE,KAAK,CAAC,IAAIP,GAAG,CAACgB,GAAG,CAACd,CAAC,GAAGK,EAAE,EAAEJ,CAAC,GAAGI,EAAE,EAAEA,EAAE,EAAEU,IAAI,CAACC,EAAE,EAAED,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}