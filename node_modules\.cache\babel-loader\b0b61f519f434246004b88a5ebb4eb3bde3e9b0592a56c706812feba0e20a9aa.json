{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"page-con\"\n};\nconst _hoisted_2 = {\n  class: \"main-con\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_PyramidChart = _resolveComponent(\"PyramidChart\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_PyramidChart)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_Pyramid<PERSON>hart"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\App.vue"], "sourcesContent": ["<template>\n  <div class=\"page-con\">\n    <div class=\"main-con\">\n      <PyramidChart />\n    </div>\n  </div>\n</template>\n\n<script>\nimport PyramidChart from './components/PyramidChart.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    PyramidChart\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  background: #0a1a2e;\n}\n\n#app {\n  height: 100vh;\n  width: 100vw;\n}\n\n.page-con {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #0a1a2e 0%, #16213e 30%, #1e3a5f 60%, #2a4a7a 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.page-con::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),\n    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);\n  pointer-events: none;\n}\n\n.main-con {\n  width: 90%;\n  height: 80%;\n  max-width: 1000px;\n  max-height: 700px;\n  position: relative;\n  z-index: 1;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;;uBADvBC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAgBC,uBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}