{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>',\n  data() {\n    return {\n      myChart: null\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\"个人\", \"单位\"];\n      var colors = [{\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#1e4a72\"\n        }, {\n          offset: 0.5,\n          color: \"#2d6ba3\"\n        }, {\n          offset: 1,\n          color: \"#5fb3f0\"\n        }]\n      }, {\n        type: \"linear\",\n        x: 0,\n        y: 0,\n        x2: 0,\n        y2: 1,\n        global: false,\n        colorStops: [{\n          offset: 0,\n          color: \"#0f3a5f\"\n        }, {\n          offset: 0.5,\n          color: \"#1e5a8a\"\n        }, {\n          offset: 1,\n          color: \"#4da6d9\"\n        }]\n      }];\n      var renderItem = function (params, api) {\n        var yValue = api.value(1);\n        var start = api.coord([api.value(0), 0]);\n        var end = api.coord([api.value(0), yValue]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseWidth = height * 0.8; // 底面宽度\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的多个面\n        var topPoint = [x, y]; // 顶点\n        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点\n        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点\n        var bottomBack = [x, start[1] + baseWidth * 0.3]; // 底面后点（营造3D效果）\n\n        var group = {\n          type: \"group\",\n          children: [\n          // 主面（正面）\n          {\n            type: \"polygon\",\n            z2: 3,\n            shape: {\n              points: [topPoint, bottomLeft, bottomRight]\n            },\n            style: {\n              fill: style.fill,\n              stroke: 'rgba(255,255,255,0.1)',\n              lineWidth: 1\n            }\n          },\n          // 左侧面（营造3D效果）\n          {\n            type: \"polygon\",\n            z2: 2,\n            shape: {\n              points: [topPoint, bottomLeft, bottomBack]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: style.fill.colorStops[0].color\n                }, {\n                  offset: 1,\n                  color: style.fill.colorStops[2].color\n                }]\n              },\n              opacity: 0.7,\n              stroke: 'rgba(255,255,255,0.05)',\n              lineWidth: 1\n            }\n          },\n          // 右侧面（营造3D效果）\n          {\n            type: \"polygon\",\n            z2: 1,\n            shape: {\n              points: [topPoint, bottomRight, bottomBack]\n            },\n            style: {\n              fill: {\n                type: \"linear\",\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: style.fill.colorStops[0].color\n                }, {\n                  offset: 1,\n                  color: style.fill.colorStops[2].color\n                }]\n              },\n              opacity: 0.5,\n              stroke: 'rgba(255,255,255,0.05)',\n              lineWidth: 1\n            }\n          }]\n        };\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4)\n          },\n          formatter: function (params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false\n        },\n        xAxis: {\n          data: xData,\n          show: false // 隐藏X轴\n        },\n        yAxis: {\n          show: false,\n          // 隐藏Y轴\n          max: 100\n        },\n        series: [{\n          type: \"custom\",\n          itemStyle: {\n            color: function (params) {\n              return colors[params.dataIndex];\n            }\n          },\n          label: {\n            show: true,\n            position: [0, -30],\n            color: \"#ffffff\",\n            fontSize: this.fontSize(0.8),\n            fontWeight: 'bold',\n            formatter: function (params) {\n              return params.data + '%';\n            },\n            align: 'center'\n          },\n          data: data,\n          renderItem: renderItem\n        },\n        // 添加底部标签\n        {\n          type: \"custom\",\n          renderItem: function (params, api) {\n            var x = api.coord([params.dataIndex, 0])[0];\n            var y = api.coord([params.dataIndex, 0])[1] + 40;\n            return {\n              type: 'group',\n              children: [\n              // 白色虚线\n              {\n                type: 'line',\n                shape: {\n                  x1: x,\n                  y1: y - 30,\n                  x2: x,\n                  y2: y - 10\n                },\n                style: {\n                  stroke: '#ffffff',\n                  lineWidth: 2,\n                  lineDash: [5, 5],\n                  opacity: 0.8\n                }\n              },\n              // 标签文字\n              {\n                type: 'text',\n                style: {\n                  text: xData[params.dataIndex],\n                  x: x,\n                  y: y,\n                  textAlign: 'center',\n                  textVerticalAlign: 'top',\n                  fill: '#ffffff',\n                  fontSize: 16,\n                  fontWeight: 'normal'\n                }\n              }]\n            };\n          },\n          data: data\n        }]\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "myChart", "mounted", "$refs", "volumn", "reload<PERSON><PERSON>", "window", "addEventListener", "beforeUnmount", "dispose<PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON>", "$echarts", "init", "xData", "colors", "type", "x", "y", "x2", "y2", "global", "colorStops", "offset", "color", "renderItem", "params", "api", "yValue", "value", "start", "coord", "end", "style", "height", "baseWidth", "topPoint", "bottomLeft", "bottomRight", "bottomBack", "group", "children", "z2", "shape", "points", "fill", "stroke", "lineWidth", "opacity", "option", "title", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "formatter", "grid", "bottom", "right", "containLabel", "xAxis", "show", "yAxis", "max", "series", "itemStyle", "dataIndex", "label", "position", "align", "x1", "y1", "lineDash", "textAlign", "textVerticalAlign", "setOption", "res", "clientWidth", "innerWidth", "document", "documentElement", "body", "dispose"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\n        \"个人\",\n        \"单位\",\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#1e4a72\",\n            },\n            {\n              offset: 0.5,\n              color: \"#2d6ba3\",\n            },\n            {\n              offset: 1,\n              color: \"#5fb3f0\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#0f3a5f\",\n            },\n            {\n              offset: 0.5,\n              color: \"#1e5a8a\",\n            },\n            {\n              offset: 1,\n              color: \"#4da6d9\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var yValue = api.value(1);\n        var start = api.coord([api.value(0), 0]);\n        var end = api.coord([api.value(0), yValue]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseWidth = height * 0.8; // 底面宽度\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的多个面\n        var topPoint = [x, y]; // 顶点\n        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点\n        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点\n        var bottomBack = [x, start[1] + baseWidth * 0.3]; // 底面后点（营造3D效果）\n\n        var group = {\n          type: \"group\",\n          children: [\n            // 主面（正面）\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomLeft, bottomRight],\n              },\n              style: {\n                fill: style.fill,\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 1,\n              },\n            },\n            // 左侧面（营造3D效果）\n            {\n              type: \"polygon\",\n              z2: 2,\n              shape: {\n                points: [topPoint, bottomLeft, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.7,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 1,\n              },\n            },\n            // 右侧面（营造3D效果）\n            {\n              type: \"polygon\",\n              z2: 1,\n              shape: {\n                points: [topPoint, bottomRight, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.5,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 1,\n              },\n            },\n          ],\n        };\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n          },\n          formatter: function(params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false,\n        },\n        xAxis: {\n          data: xData,\n          show: false, // 隐藏X轴\n        },\n        yAxis: {\n          show: false, // 隐藏Y轴\n          max: 100,\n        },\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n            label: {\n              show: true,\n              position: [0, -30],\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.8),\n              fontWeight: 'bold',\n              formatter: function(params) {\n                return params.data + '%';\n              },\n              align: 'center'\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n          // 添加底部标签\n          {\n            type: \"custom\",\n            renderItem: function(params, api) {\n              var x = api.coord([params.dataIndex, 0])[0];\n              var y = api.coord([params.dataIndex, 0])[1] + 40;\n\n              return {\n                type: 'group',\n                children: [\n                  // 白色虚线\n                  {\n                    type: 'line',\n                    shape: {\n                      x1: x,\n                      y1: y - 30,\n                      x2: x,\n                      y2: y - 10\n                    },\n                    style: {\n                      stroke: '#ffffff',\n                      lineWidth: 2,\n                      lineDash: [5, 5],\n                      opacity: 0.8\n                    }\n                  },\n                  // 标签文字\n                  {\n                    type: 'text',\n                    style: {\n                      text: xData[params.dataIndex],\n                      x: x,\n                      y: y,\n                      textAlign: 'center',\n                      textVerticalAlign: 'top',\n                      fill: '#ffffff',\n                      fontSize: 16,\n                      fontWeight: 'normal'\n                    }\n                  }\n                ]\n              };\n            },\n            data: data,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,EAAE;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;MAClB;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC,IAAI,CAACF,WAAW,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACAG,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,OAAO,EAAE;IACPC,SAASA,CAAA,EAAG;MACV,IAAI,CAACV,OAAM,GAAI,IAAI,CAACW,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACV,KAAK,CAACC,MAAM,CAAC;MACpD,IAAIJ,IAAG,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC;MACnB,IAAIc,KAAI,GAAI,CACV,IAAI,EACJ,IAAI,CACL;MACD,IAAIC,MAAK,GAAI,CACX;QACEC,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,EACD;QACER,IAAI,EAAE,QAAQ;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,CACV;UACEC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE;QACT,CAAC,EACD;UACED,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,CACF;MAED,IAAIC,UAAS,GAAI,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;QACrC,IAAIC,MAAK,GAAID,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;QACzB,IAAIC,KAAI,GAAIH,GAAG,CAACI,KAAK,CAAC,CAACJ,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,IAAIG,GAAE,GAAIL,GAAG,CAACI,KAAK,CAAC,CAACJ,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC;QAC3C,IAAIK,KAAI,GAAIN,GAAG,CAACM,KAAK,CAAC,CAAC;;QAEvB;QACA,IAAIC,MAAK,GAAIJ,KAAK,CAAC,CAAC,IAAIE,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAIG,SAAQ,GAAID,MAAK,GAAI,GAAG,EAAE;QAC9B,IAAIjB,CAAA,GAAIa,KAAK,CAAC,CAAC,CAAC;QAChB,IAAIZ,CAAA,GAAIc,GAAG,CAAC,CAAC,CAAC;;QAEd;QACA,IAAII,QAAO,GAAI,CAACnB,CAAC,EAAEC,CAAC,CAAC,EAAE;QACvB,IAAImB,UAAS,GAAI,CAACpB,CAAA,GAAIkB,SAAQ,GAAI,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAChD,IAAIQ,WAAU,GAAI,CAACrB,CAAA,GAAIkB,SAAQ,GAAI,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACjD,IAAIS,UAAS,GAAI,CAACtB,CAAC,EAAEa,KAAK,CAAC,CAAC,IAAIK,SAAQ,GAAI,GAAG,CAAC,EAAE;;QAElD,IAAIK,KAAI,GAAI;UACVxB,IAAI,EAAE,OAAO;UACbyB,QAAQ,EAAE;UACR;UACA;YACEzB,IAAI,EAAE,SAAS;YACf0B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACR,QAAQ,EAAEC,UAAU,EAAEC,WAAW;YAC5C,CAAC;YACDL,KAAK,EAAE;cACLY,IAAI,EAAEZ,KAAK,CAACY,IAAI;cAChBC,MAAM,EAAE,uBAAuB;cAC/BC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACE/B,IAAI,EAAE,SAAS;YACf0B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACR,QAAQ,EAAEC,UAAU,EAAEE,UAAU;YAC3C,CAAC;YACDN,KAAK,EAAE;cACLY,IAAI,EAAE;gBACJ7B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE,CACV;kBAAEC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAES,KAAK,CAACY,IAAI,CAACvB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACpD;kBAAED,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAES,KAAK,CAACY,IAAI,CAACvB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM;cAEvD,CAAC;cACDwB,OAAO,EAAE,GAAG;cACZF,MAAM,EAAE,wBAAwB;cAChCC,SAAS,EAAE;YACb;UACF,CAAC;UACD;UACA;YACE/B,IAAI,EAAE,SAAS;YACf0B,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE;cACLC,MAAM,EAAE,CAACR,QAAQ,EAAEE,WAAW,EAAEC,UAAU;YAC5C,CAAC;YACDN,KAAK,EAAE;cACLY,IAAI,EAAE;gBACJ7B,IAAI,EAAE,QAAQ;gBACdC,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE,CACV;kBAAEC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAES,KAAK,CAACY,IAAI,CAACvB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM,CAAC,EACpD;kBAAED,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAES,KAAK,CAACY,IAAI,CAACvB,UAAU,CAAC,CAAC,CAAC,CAACE;gBAAM;cAEvD,CAAC;cACDwB,OAAO,EAAE,GAAG;cACZF,MAAM,EAAE,wBAAwB;cAChCC,SAAS,EAAE;YACb;UACF,CAAC;QAEL,CAAC;QAED,OAAOP,KAAK;MACd,CAAC;;MAED;MACA,IAAIS,MAAK,GAAI;QACXC,KAAK,EAAE;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,IAAI;UACTC,SAAS,EAAE;YACT9B,KAAK,EAAE,SAAS;YAChB+B,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,mBAAmB;UACpCC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfR,SAAS,EAAE;YACT9B,KAAK,EAAE,SAAS;YAChB+B,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG;UAC7B,CAAC;UACDQ,SAAS,EAAE,SAAAA,CAASrC,MAAM,EAAE;YAC1B,OAAOA,MAAM,CAAC3B,IAAG,GAAI,IAAG,GAAI2B,MAAM,CAACG,KAAI,GAAI,GAAG;UAChD;QACF,CAAC;QACDmC,IAAI,EAAE;UACJX,GAAG,EAAE,KAAK;UACVD,IAAI,EAAE,KAAK;UACXa,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLpE,IAAI,EAAEc,KAAK;UACXuD,IAAI,EAAE,KAAK,CAAE;QACf,CAAC;QACDC,KAAK,EAAE;UACLD,IAAI,EAAE,KAAK;UAAE;UACbE,GAAG,EAAE;QACP,CAAC;QAEDC,MAAM,EAAE,CACN;UACExD,IAAI,EAAE,QAAQ;UACdyD,SAAS,EAAE;YACTjD,KAAK,EAAE,SAAAA,CAASE,MAAM,EAAE;cACtB,OAAOX,MAAM,CAACW,MAAM,CAACgD,SAAS,CAAC;YACjC;UACF,CAAC;UACDC,KAAK,EAAE;YACLN,IAAI,EAAE,IAAI;YACVO,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAClBpD,KAAK,EAAE,SAAS;YAChB+B,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,GAAG,CAAC;YAC5BC,UAAU,EAAE,MAAM;YAClBO,SAAS,EAAE,SAAAA,CAASrC,MAAM,EAAE;cAC1B,OAAOA,MAAM,CAAC1B,IAAG,GAAI,GAAG;YAC1B,CAAC;YACD6E,KAAK,EAAE;UACT,CAAC;UACD7E,IAAI,EAAEA,IAAI;UACVyB,UAAU,EAAEA;QACd,CAAC;QACD;QACA;UACET,IAAI,EAAE,QAAQ;UACdS,UAAU,EAAE,SAAAA,CAASC,MAAM,EAAEC,GAAG,EAAE;YAChC,IAAIV,CAAA,GAAIU,GAAG,CAACI,KAAK,CAAC,CAACL,MAAM,CAACgD,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAIxD,CAAA,GAAIS,GAAG,CAACI,KAAK,CAAC,CAACL,MAAM,CAACgD,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YAEhD,OAAO;cACL1D,IAAI,EAAE,OAAO;cACbyB,QAAQ,EAAE;cACR;cACA;gBACEzB,IAAI,EAAE,MAAM;gBACZ2B,KAAK,EAAE;kBACLmC,EAAE,EAAE7D,CAAC;kBACL8D,EAAE,EAAE7D,CAAA,GAAI,EAAE;kBACVC,EAAE,EAAEF,CAAC;kBACLG,EAAE,EAAEF,CAAA,GAAI;gBACV,CAAC;gBACDe,KAAK,EAAE;kBACLa,MAAM,EAAE,SAAS;kBACjBC,SAAS,EAAE,CAAC;kBACZiC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBAChBhC,OAAO,EAAE;gBACX;cACF,CAAC;cACD;cACA;gBACEhC,IAAI,EAAE,MAAM;gBACZiB,KAAK,EAAE;kBACLkB,IAAI,EAAErC,KAAK,CAACY,MAAM,CAACgD,SAAS,CAAC;kBAC7BzD,CAAC,EAAEA,CAAC;kBACJC,CAAC,EAAEA,CAAC;kBACJ+D,SAAS,EAAE,QAAQ;kBACnBC,iBAAiB,EAAE,KAAK;kBACxBrC,IAAI,EAAE,SAAS;kBACfU,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd;cACF;YAEJ,CAAC;UACH,CAAC;UACDxD,IAAI,EAAEA;QACR,CAAC;MAEL,CAAC;MACD,IAAI,CAACC,OAAO,CAACkF,SAAS,CAAClC,MAAM,CAAC;IAChC,CAAC;IACD;IACAM,QAAQA,CAAC6B,GAAG,EAAE;MACZ,MAAMC,WAAU,GACd/E,MAAM,CAACgF,UAAS,IAChBC,QAAQ,CAACC,eAAe,CAACH,WAAU,IACnCE,QAAQ,CAACE,IAAI,CAACJ,WAAW;MAC3B,IAAI,CAACA,WAAW,EAAE;MAClB,MAAM9B,QAAO,GAAI,EAAC,IAAK8B,WAAU,GAAI,IAAI,CAAC;MAC1C,OAAOD,GAAE,GAAI7B,QAAQ;IACvB,CAAC;IACD;IACA9C,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACR,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACyF,OAAO,CAAC,CAAC;MACxB;IACF,CAAC;IACD;IACArF,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACI,YAAY,CAAC,CAAC;MACnB,IAAI,CAACE,SAAS,CAAC,CAAC;IAClB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}