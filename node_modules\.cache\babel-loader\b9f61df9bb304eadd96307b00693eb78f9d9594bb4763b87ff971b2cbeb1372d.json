{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SunburstView from './SunburstView.js';\nimport SunburstSeriesModel from './SunburstSeries.js';\nimport sunburstLayout from './sunburstLayout.js';\nimport sunburstVisual from './sunburstVisual.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport { installSunburstAction } from './sunburstAction.js';\nexport function install(registers) {\n  registers.registerChartView(SunburstView);\n  registers.registerSeriesModel(SunburstSeriesModel);\n  registers.registerLayout(curry(sunburstLayout, 'sunburst'));\n  registers.registerProcessor(curry(dataFilter, 'sunburst'));\n  registers.registerVisual(sunburstVisual);\n  installSunburstAction(registers);\n}", "map": {"version": 3, "names": ["SunburstView", "SunburstSeriesModel", "sunburstLayout", "sunburstVisual", "dataFilter", "curry", "installSunburstAction", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerProcessor", "registerVisual"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/sunburst/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SunburstView from './SunburstView.js';\nimport SunburstSeriesModel from './SunburstSeries.js';\nimport sunburstLayout from './sunburstLayout.js';\nimport sunburstVisual from './sunburstVisual.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport { installSunburstAction } from './sunburstAction.js';\nexport function install(registers) {\n  registers.registerChartView(SunburstView);\n  registers.registerSeriesModel(SunburstSeriesModel);\n  registers.registerLayout(curry(sunburstLayout, 'sunburst'));\n  registers.registerProcessor(curry(dataFilter, 'sunburst'));\n  registers.registerVisual(sunburstVisual);\n  installSunburstAction(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,mBAAmB,MAAM,qBAAqB;AACrD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACT,YAAY,CAAC;EACzCQ,SAAS,CAACE,mBAAmB,CAACT,mBAAmB,CAAC;EAClDO,SAAS,CAACG,cAAc,CAACN,KAAK,CAACH,cAAc,EAAE,UAAU,CAAC,CAAC;EAC3DM,SAAS,CAACI,iBAAiB,CAACP,KAAK,CAACD,UAAU,EAAE,UAAU,CAAC,CAAC;EAC1DI,SAAS,CAACK,cAAc,CAACV,cAAc,CAAC;EACxCG,qBAAqB,CAACE,SAAS,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}