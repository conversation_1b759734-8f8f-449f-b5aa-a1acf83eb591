{"ast": null, "code": "import { liftColor } from '../tool/color.js';\nimport { getClassId } from './cssClassId.js';\nexport function createCSSEmphasis(el, attrs, scope) {\n  if (!el.ignore) {\n    if (el.isSilent()) {\n      var style = {\n        'pointer-events': 'none'\n      };\n      setClassAttribute(style, attrs, scope, true);\n    } else {\n      var emphasisStyle = el.states.emphasis && el.states.emphasis.style ? el.states.emphasis.style : {};\n      var fill = emphasisStyle.fill;\n      if (!fill) {\n        var normalFill = el.style && el.style.fill;\n        var selectFill = el.states.select && el.states.select.style && el.states.select.style.fill;\n        var fromFill = el.currentStates.indexOf('select') >= 0 ? selectFill || normalFill : normalFill;\n        if (fromFill) {\n          fill = liftColor(fromFill);\n        }\n      }\n      var lineWidth = emphasisStyle.lineWidth;\n      if (lineWidth) {\n        var scaleX = !emphasisStyle.strokeNoScale && el.transform ? el.transform[0] : 1;\n        lineWidth = lineWidth / scaleX;\n      }\n      var style = {\n        cursor: 'pointer'\n      };\n      if (fill) {\n        style.fill = fill;\n      }\n      if (emphasisStyle.stroke) {\n        style.stroke = emphasisStyle.stroke;\n      }\n      if (lineWidth) {\n        style['stroke-width'] = lineWidth;\n      }\n      setClassAttribute(style, attrs, scope, true);\n    }\n  }\n}\nfunction setClassAttribute(style, attrs, scope, withHover) {\n  var styleKey = JSON.stringify(style);\n  var className = scope.cssStyleCache[styleKey];\n  if (!className) {\n    className = scope.zrId + '-cls-' + getClassId();\n    scope.cssStyleCache[styleKey] = className;\n    scope.cssNodes['.' + className + (withHover ? ':hover' : '')] = style;\n  }\n  attrs[\"class\"] = attrs[\"class\"] ? attrs[\"class\"] + ' ' + className : className;\n}", "map": {"version": 3, "names": ["liftColor", "getClassId", "createCSSEmphasis", "el", "attrs", "scope", "ignore", "isSilent", "style", "setClassAttribute", "emphasisStyle", "states", "emphasis", "fill", "normalFill", "selectFill", "select", "fromFill", "currentStates", "indexOf", "lineWidth", "scaleX", "strokeNoScale", "transform", "cursor", "stroke", "withHover", "styleKey", "JSON", "stringify", "className", "cssStyleCache", "zrId", "cssNodes"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/svg/cssEmphasis.js"], "sourcesContent": ["import { liftColor } from '../tool/color.js';\nimport { getClassId } from './cssClassId.js';\nexport function createCSSEmphasis(el, attrs, scope) {\n    if (!el.ignore) {\n        if (el.isSilent()) {\n            var style = {\n                'pointer-events': 'none'\n            };\n            setClassAttribute(style, attrs, scope, true);\n        }\n        else {\n            var emphasisStyle = el.states.emphasis && el.states.emphasis.style\n                ? el.states.emphasis.style\n                : {};\n            var fill = emphasisStyle.fill;\n            if (!fill) {\n                var normalFill = el.style && el.style.fill;\n                var selectFill = el.states.select\n                    && el.states.select.style\n                    && el.states.select.style.fill;\n                var fromFill = el.currentStates.indexOf('select') >= 0\n                    ? (selectFill || normalFill)\n                    : normalFill;\n                if (fromFill) {\n                    fill = liftColor(fromFill);\n                }\n            }\n            var lineWidth = emphasisStyle.lineWidth;\n            if (lineWidth) {\n                var scaleX = (!emphasisStyle.strokeNoScale && el.transform)\n                    ? el.transform[0]\n                    : 1;\n                lineWidth = lineWidth / scaleX;\n            }\n            var style = {\n                cursor: 'pointer'\n            };\n            if (fill) {\n                style.fill = fill;\n            }\n            if (emphasisStyle.stroke) {\n                style.stroke = emphasisStyle.stroke;\n            }\n            if (lineWidth) {\n                style['stroke-width'] = lineWidth;\n            }\n            setClassAttribute(style, attrs, scope, true);\n        }\n    }\n}\nfunction setClassAttribute(style, attrs, scope, withHover) {\n    var styleKey = JSON.stringify(style);\n    var className = scope.cssStyleCache[styleKey];\n    if (!className) {\n        className = scope.zrId + '-cls-' + getClassId();\n        scope.cssStyleCache[styleKey] = className;\n        scope.cssNodes['.' + className + (withHover ? ':hover' : '')] = style;\n    }\n    attrs[\"class\"] = attrs[\"class\"] ? (attrs[\"class\"] + ' ' + className) : className;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAChD,IAAI,CAACF,EAAE,CAACG,MAAM,EAAE;IACZ,IAAIH,EAAE,CAACI,QAAQ,CAAC,CAAC,EAAE;MACf,IAAIC,KAAK,GAAG;QACR,gBAAgB,EAAE;MACtB,CAAC;MACDC,iBAAiB,CAACD,KAAK,EAAEJ,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC;IAChD,CAAC,MACI;MACD,IAAIK,aAAa,GAAGP,EAAE,CAACQ,MAAM,CAACC,QAAQ,IAAIT,EAAE,CAACQ,MAAM,CAACC,QAAQ,CAACJ,KAAK,GAC5DL,EAAE,CAACQ,MAAM,CAACC,QAAQ,CAACJ,KAAK,GACxB,CAAC,CAAC;MACR,IAAIK,IAAI,GAAGH,aAAa,CAACG,IAAI;MAC7B,IAAI,CAACA,IAAI,EAAE;QACP,IAAIC,UAAU,GAAGX,EAAE,CAACK,KAAK,IAAIL,EAAE,CAACK,KAAK,CAACK,IAAI;QAC1C,IAAIE,UAAU,GAAGZ,EAAE,CAACQ,MAAM,CAACK,MAAM,IAC1Bb,EAAE,CAACQ,MAAM,CAACK,MAAM,CAACR,KAAK,IACtBL,EAAE,CAACQ,MAAM,CAACK,MAAM,CAACR,KAAK,CAACK,IAAI;QAClC,IAAII,QAAQ,GAAGd,EAAE,CAACe,aAAa,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAC/CJ,UAAU,IAAID,UAAU,GACzBA,UAAU;QAChB,IAAIG,QAAQ,EAAE;UACVJ,IAAI,GAAGb,SAAS,CAACiB,QAAQ,CAAC;QAC9B;MACJ;MACA,IAAIG,SAAS,GAAGV,aAAa,CAACU,SAAS;MACvC,IAAIA,SAAS,EAAE;QACX,IAAIC,MAAM,GAAI,CAACX,aAAa,CAACY,aAAa,IAAInB,EAAE,CAACoB,SAAS,GACpDpB,EAAE,CAACoB,SAAS,CAAC,CAAC,CAAC,GACf,CAAC;QACPH,SAAS,GAAGA,SAAS,GAAGC,MAAM;MAClC;MACA,IAAIb,KAAK,GAAG;QACRgB,MAAM,EAAE;MACZ,CAAC;MACD,IAAIX,IAAI,EAAE;QACNL,KAAK,CAACK,IAAI,GAAGA,IAAI;MACrB;MACA,IAAIH,aAAa,CAACe,MAAM,EAAE;QACtBjB,KAAK,CAACiB,MAAM,GAAGf,aAAa,CAACe,MAAM;MACvC;MACA,IAAIL,SAAS,EAAE;QACXZ,KAAK,CAAC,cAAc,CAAC,GAAGY,SAAS;MACrC;MACAX,iBAAiB,CAACD,KAAK,EAAEJ,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC;IAChD;EACJ;AACJ;AACA,SAASI,iBAAiBA,CAACD,KAAK,EAAEJ,KAAK,EAAEC,KAAK,EAAEqB,SAAS,EAAE;EACvD,IAAIC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC;EACpC,IAAIsB,SAAS,GAAGzB,KAAK,CAAC0B,aAAa,CAACJ,QAAQ,CAAC;EAC7C,IAAI,CAACG,SAAS,EAAE;IACZA,SAAS,GAAGzB,KAAK,CAAC2B,IAAI,GAAG,OAAO,GAAG/B,UAAU,CAAC,CAAC;IAC/CI,KAAK,CAAC0B,aAAa,CAACJ,QAAQ,CAAC,GAAGG,SAAS;IACzCzB,KAAK,CAAC4B,QAAQ,CAAC,GAAG,GAAGH,SAAS,IAAIJ,SAAS,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAGlB,KAAK;EACzE;EACAJ,KAAK,CAAC,OAAO,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAIA,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG0B,SAAS,GAAIA,SAAS;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}