{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Text } from '../util/graphic.js';\nexport function getTextRect(text, font, align, verticalAlign, padding, rich, truncate, lineHeight) {\n  var textEl = new Text({\n    style: {\n      text: text,\n      font: font,\n      align: align,\n      verticalAlign: verticalAlign,\n      padding: padding,\n      rich: rich,\n      overflow: truncate ? 'truncate' : null,\n      lineHeight: lineHeight\n    }\n  });\n  return textEl.getBoundingRect();\n}", "map": {"version": 3, "names": ["Text", "getTextRect", "text", "font", "align", "verticalAlign", "padding", "rich", "truncate", "lineHeight", "textEl", "style", "overflow", "getBoundingRect"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/legacy/getTextRect.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Text } from '../util/graphic.js';\nexport function getTextRect(text, font, align, verticalAlign, padding, rich, truncate, lineHeight) {\n  var textEl = new Text({\n    style: {\n      text: text,\n      font: font,\n      align: align,\n      verticalAlign: verticalAlign,\n      padding: padding,\n      rich: rich,\n      overflow: truncate ? 'truncate' : null,\n      lineHeight: lineHeight\n    }\n  });\n  return textEl.getBoundingRect();\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EACjG,IAAIC,MAAM,GAAG,IAAIV,IAAI,CAAC;IACpBW,KAAK,EAAE;MACLT,IAAI,EAAEA,IAAI;MACVC,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAEA,KAAK;MACZC,aAAa,EAAEA,aAAa;MAC5BC,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAEA,IAAI;MACVK,QAAQ,EAAEJ,QAAQ,GAAG,UAAU,GAAG,IAAI;MACtCC,UAAU,EAAEA;IACd;EACF,CAAC,CAAC;EACF,OAAOC,MAAM,CAACG,eAAe,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}