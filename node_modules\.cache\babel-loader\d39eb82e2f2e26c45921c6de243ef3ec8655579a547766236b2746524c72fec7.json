{"ast": null, "code": "import \"core-js/modules/es.array-buffer.detached.js\";\nimport \"core-js/modules/es.array-buffer.transfer.js\";\nimport \"core-js/modules/es.array-buffer.transfer-to-fixed-length.js\";\nimport \"core-js/modules/es.typed-array.with.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\n/* global Float32Array */\nvar supportFloat32Array = typeof Float32Array !== 'undefined';\nvar Float32ArrayCtor = !supportFloat32Array ? Array : Float32Array;\nexport function createFloat32Array(arg) {\n  if (isArray(arg)) {\n    // Return self directly if don't support TypedArray.\n    return supportFloat32Array ? new Float32Array(arg) : arg;\n  }\n  // Else is number\n  return new Float32ArrayCtor(arg);\n}", "map": {"version": 3, "names": ["isArray", "supportFloat32Array", "Float32Array", "Float32ArrayCtor", "Array", "createFloat32Array", "arg"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/util/vendor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\n/* global Float32Array */\nvar supportFloat32Array = typeof Float32Array !== 'undefined';\nvar Float32ArrayCtor = !supportFloat32Array ? Array : Float32Array;\nexport function createFloat32Array(arg) {\n  if (isArray(arg)) {\n    // Return self directly if don't support TypedArray.\n    return supportFloat32Array ? new Float32Array(arg) : arg;\n  }\n  // Else is number\n  return new Float32ArrayCtor(arg);\n}"], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,0BAA0B;AAClD;AACA,IAAIC,mBAAmB,GAAG,OAAOC,YAAY,KAAK,WAAW;AAC7D,IAAIC,gBAAgB,GAAG,CAACF,mBAAmB,GAAGG,KAAK,GAAGF,YAAY;AAClE,OAAO,SAASG,kBAAkBA,CAACC,GAAG,EAAE;EACtC,IAAIN,OAAO,CAACM,GAAG,CAAC,EAAE;IAChB;IACA,OAAOL,mBAAmB,GAAG,IAAIC,YAAY,CAACI,GAAG,CAAC,GAAGA,GAAG;EAC1D;EACA;EACA,OAAO,IAAIH,gBAAgB,CAACG,GAAG,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}