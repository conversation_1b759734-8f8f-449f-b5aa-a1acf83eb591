{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar PolarModel = /** @class */function (_super) {\n  __extends(PolarModel, _super);\n  function PolarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PolarModel.type;\n    return _this;\n  }\n  PolarModel.prototype.findAxisModel = function (axisType) {\n    var foundAxisModel;\n    var ecModel = this.ecModel;\n    ecModel.eachComponent(axisType, function (axisModel) {\n      if (axisModel.getCoordSysModel() === this) {\n        foundAxisModel = axisModel;\n      }\n    }, this);\n    return foundAxisModel;\n  };\n  PolarModel.type = 'polar';\n  PolarModel.dependencies = ['radiusAxis', 'angleAxis'];\n  PolarModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    center: ['50%', '50%'],\n    radius: '80%'\n  };\n  return PolarModel;\n}(ComponentModel);\nexport default PolarModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "PolarModel", "_super", "_this", "apply", "arguments", "type", "prototype", "findAxisModel", "axisType", "foundAxisModel", "ecModel", "eachComponent", "axisModel", "getCoordSysModel", "dependencies", "defaultOption", "z", "center", "radius"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/coord/polar/PolarModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar PolarModel = /** @class */function (_super) {\n  __extends(PolarModel, _super);\n  function PolarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PolarModel.type;\n    return _this;\n  }\n  PolarModel.prototype.findAxisModel = function (axisType) {\n    var foundAxisModel;\n    var ecModel = this.ecModel;\n    ecModel.eachComponent(axisType, function (axisModel) {\n      if (axisModel.getCoordSysModel() === this) {\n        foundAxisModel = axisModel;\n      }\n    }, this);\n    return foundAxisModel;\n  };\n  PolarModel.type = 'polar';\n  PolarModel.dependencies = ['radiusAxis', 'angleAxis'];\n  PolarModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    center: ['50%', '50%'],\n    radius: '80%'\n  };\n  return PolarModel;\n}(ComponentModel);\nexport default PolarModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CH,SAAS,CAACE,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5B,OAAOH,KAAK;EACd;EACAF,UAAU,CAACM,SAAS,CAACC,aAAa,GAAG,UAAUC,QAAQ,EAAE;IACvD,IAAIC,cAAc;IAClB,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1BA,OAAO,CAACC,aAAa,CAACH,QAAQ,EAAE,UAAUI,SAAS,EAAE;MACnD,IAAIA,SAAS,CAACC,gBAAgB,CAAC,CAAC,KAAK,IAAI,EAAE;QACzCJ,cAAc,GAAGG,SAAS;MAC5B;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAOH,cAAc;EACvB,CAAC;EACDT,UAAU,CAACK,IAAI,GAAG,OAAO;EACzBL,UAAU,CAACc,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC;EACrDd,UAAU,CAACe,aAAa,GAAG;IACzB;IACAC,CAAC,EAAE,CAAC;IACJC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACtBC,MAAM,EAAE;EACV,CAAC;EACD,OAAOlB,UAAU;AACnB,CAAC,CAACD,cAAc,CAAC;AACjB,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}