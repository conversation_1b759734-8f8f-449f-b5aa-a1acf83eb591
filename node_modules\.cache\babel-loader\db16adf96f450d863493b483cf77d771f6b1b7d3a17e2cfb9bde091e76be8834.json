{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport LargeSymbolDraw from '../helper/LargeSymbolDraw.js';\nimport pointsLayout from '../../layout/points.js';\nimport ChartView from '../../view/Chart.js';\nvar ScatterView = /** @class */function (_super) {\n  __extends(ScatterView, _super);\n  function ScatterView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScatterView.type;\n    return _this;\n  }\n  ScatterView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var symbolDraw = this._updateSymbolDraw(data, seriesModel);\n    symbolDraw.updateData(data, {\n      // TODO\n      // If this parameter should be a shape or a bounding volume\n      // shape will be more general.\n      // But bounding volume like bounding rect will be much faster in the contain calculation\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this._finished = true;\n  };\n  ScatterView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var symbolDraw = this._updateSymbolDraw(data, seriesModel);\n    symbolDraw.incrementalPrepareUpdate(data);\n    this._finished = false;\n  };\n  ScatterView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    this._symbolDraw.incrementalUpdate(taskParams, seriesModel.getData(), {\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this._finished = taskParams.end === seriesModel.getData().count();\n  };\n  ScatterView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    // Must mark group dirty and make sure the incremental layer will be cleared\n    // PENDING\n    this.group.dirty();\n    if (!this._finished || data.count() > 1e4) {\n      return {\n        update: true\n      };\n    } else {\n      var res = pointsLayout('').reset(seriesModel, ecModel, api);\n      if (res.progress) {\n        res.progress({\n          start: 0,\n          end: data.count(),\n          count: data.count()\n        }, data);\n      }\n      this._symbolDraw.updateLayout(data);\n    }\n  };\n  ScatterView.prototype.eachRendered = function (cb) {\n    this._symbolDraw && this._symbolDraw.eachRendered(cb);\n  };\n  ScatterView.prototype._getClipShape = function (seriesModel) {\n    if (!seriesModel.get('clip', true)) {\n      return;\n    }\n    var coordSys = seriesModel.coordinateSystem;\n    // PENDING make `0.1` configurable, for example, `clipTolerance`?\n    return coordSys && coordSys.getArea && coordSys.getArea(.1);\n  };\n  ScatterView.prototype._updateSymbolDraw = function (data, seriesModel) {\n    var symbolDraw = this._symbolDraw;\n    var pipelineContext = seriesModel.pipelineContext;\n    var isLargeDraw = pipelineContext.large;\n    if (!symbolDraw || isLargeDraw !== this._isLargeDraw) {\n      symbolDraw && symbolDraw.remove();\n      symbolDraw = this._symbolDraw = isLargeDraw ? new LargeSymbolDraw() : new SymbolDraw();\n      this._isLargeDraw = isLargeDraw;\n      this.group.removeAll();\n    }\n    this.group.add(symbolDraw.group);\n    return symbolDraw;\n  };\n  ScatterView.prototype.remove = function (ecModel, api) {\n    this._symbolDraw && this._symbolDraw.remove(true);\n    this._symbolDraw = null;\n  };\n  ScatterView.prototype.dispose = function () {};\n  ScatterView.type = 'scatter';\n  return ScatterView;\n}(ChartView);\nexport default ScatterView;", "map": {"version": 3, "names": ["__extends", "SymbolDraw", "LargeSymbolDraw", "pointsLayout", "ChartView", "ScatterView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "data", "getData", "symbolDraw", "_updateSymbolDraw", "updateData", "clipShape", "_getClipShape", "_finished", "incrementalPrepareRender", "incrementalPrepareUpdate", "incrementalRender", "taskParams", "_symbolDraw", "incrementalUpdate", "end", "count", "updateTransform", "group", "dirty", "update", "res", "reset", "progress", "start", "updateLayout", "eachRendered", "cb", "get", "coordSys", "coordinateSystem", "getArea", "pipelineContext", "isLargeDraw", "large", "_isLargeDraw", "remove", "removeAll", "add", "dispose"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/scatter/ScatterView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport LargeSymbolDraw from '../helper/LargeSymbolDraw.js';\nimport pointsLayout from '../../layout/points.js';\nimport ChartView from '../../view/Chart.js';\nvar ScatterView = /** @class */function (_super) {\n  __extends(ScatterView, _super);\n  function ScatterView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScatterView.type;\n    return _this;\n  }\n  ScatterView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var symbolDraw = this._updateSymbolDraw(data, seriesModel);\n    symbolDraw.updateData(data, {\n      // TODO\n      // If this parameter should be a shape or a bounding volume\n      // shape will be more general.\n      // But bounding volume like bounding rect will be much faster in the contain calculation\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this._finished = true;\n  };\n  ScatterView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var symbolDraw = this._updateSymbolDraw(data, seriesModel);\n    symbolDraw.incrementalPrepareUpdate(data);\n    this._finished = false;\n  };\n  ScatterView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    this._symbolDraw.incrementalUpdate(taskParams, seriesModel.getData(), {\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this._finished = taskParams.end === seriesModel.getData().count();\n  };\n  ScatterView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    // Must mark group dirty and make sure the incremental layer will be cleared\n    // PENDING\n    this.group.dirty();\n    if (!this._finished || data.count() > 1e4) {\n      return {\n        update: true\n      };\n    } else {\n      var res = pointsLayout('').reset(seriesModel, ecModel, api);\n      if (res.progress) {\n        res.progress({\n          start: 0,\n          end: data.count(),\n          count: data.count()\n        }, data);\n      }\n      this._symbolDraw.updateLayout(data);\n    }\n  };\n  ScatterView.prototype.eachRendered = function (cb) {\n    this._symbolDraw && this._symbolDraw.eachRendered(cb);\n  };\n  ScatterView.prototype._getClipShape = function (seriesModel) {\n    if (!seriesModel.get('clip', true)) {\n      return;\n    }\n    var coordSys = seriesModel.coordinateSystem;\n    // PENDING make `0.1` configurable, for example, `clipTolerance`?\n    return coordSys && coordSys.getArea && coordSys.getArea(.1);\n  };\n  ScatterView.prototype._updateSymbolDraw = function (data, seriesModel) {\n    var symbolDraw = this._symbolDraw;\n    var pipelineContext = seriesModel.pipelineContext;\n    var isLargeDraw = pipelineContext.large;\n    if (!symbolDraw || isLargeDraw !== this._isLargeDraw) {\n      symbolDraw && symbolDraw.remove();\n      symbolDraw = this._symbolDraw = isLargeDraw ? new LargeSymbolDraw() : new SymbolDraw();\n      this._isLargeDraw = isLargeDraw;\n      this.group.removeAll();\n    }\n    this.group.add(symbolDraw.group);\n    return symbolDraw;\n  };\n  ScatterView.prototype.remove = function (ecModel, api) {\n    this._symbolDraw && this._symbolDraw.remove(true);\n    this._symbolDraw = null;\n  };\n  ScatterView.prototype.dispose = function () {};\n  ScatterView.type = 'scatter';\n  return ScatterView;\n}(ChartView);\nexport default ScatterView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CN,SAAS,CAACK,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7B,OAAOH,KAAK;EACd;EACAF,WAAW,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAClE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAACH,IAAI,EAAEH,WAAW,CAAC;IAC1DK,UAAU,CAACE,UAAU,CAACJ,IAAI,EAAE;MAC1B;MACA;MACA;MACA;MACAK,SAAS,EAAE,IAAI,CAACC,aAAa,CAACT,WAAW;IAC3C,CAAC,CAAC;IACF,IAAI,CAACU,SAAS,GAAG,IAAI;EACvB,CAAC;EACDlB,WAAW,CAACM,SAAS,CAACa,wBAAwB,GAAG,UAAUX,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACpF,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAACH,IAAI,EAAEH,WAAW,CAAC;IAC1DK,UAAU,CAACO,wBAAwB,CAACT,IAAI,CAAC;IACzC,IAAI,CAACO,SAAS,GAAG,KAAK;EACxB,CAAC;EACDlB,WAAW,CAACM,SAAS,CAACe,iBAAiB,GAAG,UAAUC,UAAU,EAAEd,WAAW,EAAEC,OAAO,EAAE;IACpF,IAAI,CAACc,WAAW,CAACC,iBAAiB,CAACF,UAAU,EAAEd,WAAW,CAACI,OAAO,CAAC,CAAC,EAAE;MACpEI,SAAS,EAAE,IAAI,CAACC,aAAa,CAACT,WAAW;IAC3C,CAAC,CAAC;IACF,IAAI,CAACU,SAAS,GAAGI,UAAU,CAACG,GAAG,KAAKjB,WAAW,CAACI,OAAO,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC;EACnE,CAAC;EACD1B,WAAW,CAACM,SAAS,CAACqB,eAAe,GAAG,UAAUnB,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC3E,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAACgB,KAAK,CAACC,KAAK,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACX,SAAS,IAAIP,IAAI,CAACe,KAAK,CAAC,CAAC,GAAG,GAAG,EAAE;MACzC,OAAO;QACLI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,MAAM;MACL,IAAIC,GAAG,GAAGjC,YAAY,CAAC,EAAE,CAAC,CAACkC,KAAK,CAACxB,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;MAC3D,IAAIqB,GAAG,CAACE,QAAQ,EAAE;QAChBF,GAAG,CAACE,QAAQ,CAAC;UACXC,KAAK,EAAE,CAAC;UACRT,GAAG,EAAEd,IAAI,CAACe,KAAK,CAAC,CAAC;UACjBA,KAAK,EAAEf,IAAI,CAACe,KAAK,CAAC;QACpB,CAAC,EAAEf,IAAI,CAAC;MACV;MACA,IAAI,CAACY,WAAW,CAACY,YAAY,CAACxB,IAAI,CAAC;IACrC;EACF,CAAC;EACDX,WAAW,CAACM,SAAS,CAAC8B,YAAY,GAAG,UAAUC,EAAE,EAAE;IACjD,IAAI,CAACd,WAAW,IAAI,IAAI,CAACA,WAAW,CAACa,YAAY,CAACC,EAAE,CAAC;EACvD,CAAC;EACDrC,WAAW,CAACM,SAAS,CAACW,aAAa,GAAG,UAAUT,WAAW,EAAE;IAC3D,IAAI,CAACA,WAAW,CAAC8B,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC;IACF;IACA,IAAIC,QAAQ,GAAG/B,WAAW,CAACgC,gBAAgB;IAC3C;IACA,OAAOD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAAC,EAAE,CAAC;EAC7D,CAAC;EACDzC,WAAW,CAACM,SAAS,CAACQ,iBAAiB,GAAG,UAAUH,IAAI,EAAEH,WAAW,EAAE;IACrE,IAAIK,UAAU,GAAG,IAAI,CAACU,WAAW;IACjC,IAAImB,eAAe,GAAGlC,WAAW,CAACkC,eAAe;IACjD,IAAIC,WAAW,GAAGD,eAAe,CAACE,KAAK;IACvC,IAAI,CAAC/B,UAAU,IAAI8B,WAAW,KAAK,IAAI,CAACE,YAAY,EAAE;MACpDhC,UAAU,IAAIA,UAAU,CAACiC,MAAM,CAAC,CAAC;MACjCjC,UAAU,GAAG,IAAI,CAACU,WAAW,GAAGoB,WAAW,GAAG,IAAI9C,eAAe,CAAC,CAAC,GAAG,IAAID,UAAU,CAAC,CAAC;MACtF,IAAI,CAACiD,YAAY,GAAGF,WAAW;MAC/B,IAAI,CAACf,KAAK,CAACmB,SAAS,CAAC,CAAC;IACxB;IACA,IAAI,CAACnB,KAAK,CAACoB,GAAG,CAACnC,UAAU,CAACe,KAAK,CAAC;IAChC,OAAOf,UAAU;EACnB,CAAC;EACDb,WAAW,CAACM,SAAS,CAACwC,MAAM,GAAG,UAAUrC,OAAO,EAAEC,GAAG,EAAE;IACrD,IAAI,CAACa,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,MAAM,CAAC,IAAI,CAAC;IACjD,IAAI,CAACvB,WAAW,GAAG,IAAI;EACzB,CAAC;EACDvB,WAAW,CAACM,SAAS,CAAC2C,OAAO,GAAG,YAAY,CAAC,CAAC;EAC9CjD,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAACD,SAAS,CAAC;AACZ,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}