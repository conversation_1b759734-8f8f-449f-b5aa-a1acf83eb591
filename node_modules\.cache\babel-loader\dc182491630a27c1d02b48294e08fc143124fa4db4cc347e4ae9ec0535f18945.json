{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport ParallelView from './ParallelView.js';\nimport ParallelSeriesModel from './ParallelSeries.js';\nimport parallelVisual from './parallelVisual.js';\nimport { install as installParallelComponent } from '../../component/parallel/install.js';\nexport function install(registers) {\n  use(installParallelComponent);\n  registers.registerChartView(ParallelView);\n  registers.registerSeriesModel(ParallelSeriesModel);\n  registers.registerVisual(registers.PRIORITY.VISUAL.BRUSH, parallelVisual);\n}", "map": {"version": 3, "names": ["use", "<PERSON><PERSON><PERSON><PERSON>iew", "ParallelSeriesModel", "parallelVisual", "install", "installParallelComponent", "registers", "registerChartView", "registerSeriesModel", "registerVisual", "PRIORITY", "VISUAL", "BRUSH"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/parallel/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport ParallelView from './ParallelView.js';\nimport ParallelSeriesModel from './ParallelSeries.js';\nimport parallelVisual from './parallelVisual.js';\nimport { install as installParallelComponent } from '../../component/parallel/install.js';\nexport function install(registers) {\n  use(installParallelComponent);\n  registers.registerChartView(ParallelView);\n  registers.registerSeriesModel(ParallelSeriesModel);\n  registers.registerVisual(registers.PRIORITY.VISUAL.BRUSH, parallelVisual);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,mBAAmB,MAAM,qBAAqB;AACrD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,OAAO,IAAIC,wBAAwB,QAAQ,qCAAqC;AACzF,OAAO,SAASD,OAAOA,CAACE,SAAS,EAAE;EACjCN,GAAG,CAACK,wBAAwB,CAAC;EAC7BC,SAAS,CAACC,iBAAiB,CAACN,YAAY,CAAC;EACzCK,SAAS,CAACE,mBAAmB,CAACN,mBAAmB,CAAC;EAClDI,SAAS,CAACG,cAAc,CAACH,SAAS,CAACI,QAAQ,CAACC,MAAM,CAACC,KAAK,EAAET,cAAc,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}