{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { numericToNumber } from '../../util/number.js';\nimport { eqNaN } from 'zrender/lib/core/util.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar DEFAULT_SMOOTH = 0.3;\nvar ParallelView = /** @class */function (_super) {\n  __extends(ParallelView, _super);\n  function ParallelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelView.type;\n    _this._dataGroup = new graphic.Group();\n    _this._initialized = false;\n    return _this;\n  }\n  ParallelView.prototype.init = function () {\n    this.group.add(this._dataGroup);\n  };\n  /**\r\n   * @override\r\n   */\n  ParallelView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var dataGroup = this._dataGroup;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    data.diff(oldData).add(add).update(update).remove(remove).execute();\n    function add(newDataIndex) {\n      var line = addEl(data, dataGroup, newDataIndex, dimensions, coordSys);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function update(newDataIndex, oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      var points = createLinePoints(data, newDataIndex, dimensions, coordSys);\n      data.setItemGraphicEl(newDataIndex, line);\n      graphic.updateProps(line, {\n        shape: {\n          points: points\n        }\n      }, seriesModel, newDataIndex);\n      saveOldStyle(line);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function remove(oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      dataGroup.remove(line);\n    }\n    // First create\n    if (!this._initialized) {\n      this._initialized = true;\n      var clipPath = createGridClipShape(coordSys, seriesModel, function () {\n        // Callback will be invoked immediately if there is no animation\n        setTimeout(function () {\n          dataGroup.removeClipPath();\n        });\n      });\n      dataGroup.setClipPath(clipPath);\n    }\n    this._data = data;\n  };\n  ParallelView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this._initialized = true;\n    this._data = null;\n    this._dataGroup.removeAll();\n  };\n  ParallelView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    var progressiveEls = this._progressiveEls = [];\n    for (var dataIndex = taskParams.start; dataIndex < taskParams.end; dataIndex++) {\n      var line = addEl(data, this._dataGroup, dataIndex, dimensions, coordSys);\n      line.incremental = true;\n      updateElCommon(line, data, dataIndex, seriesScope);\n      progressiveEls.push(line);\n    }\n  };\n  ParallelView.prototype.remove = function () {\n    this._dataGroup && this._dataGroup.removeAll();\n    this._data = null;\n  };\n  ParallelView.type = 'parallel';\n  return ParallelView;\n}(ChartView);\nfunction createGridClipShape(coordSys, seriesModel, cb) {\n  var parallelModel = coordSys.model;\n  var rect = coordSys.getRect();\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    }\n  });\n  var dim = parallelModel.get('layout') === 'horizontal' ? 'width' : 'height';\n  rectEl.setShape(dim, 0);\n  graphic.initProps(rectEl, {\n    shape: {\n      width: rect.width,\n      height: rect.height\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nfunction createLinePoints(data, dataIndex, dimensions, coordSys) {\n  var points = [];\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimName = dimensions[i];\n    var value = data.get(data.mapDimension(dimName), dataIndex);\n    if (!isEmptyValue(value, coordSys.getAxis(dimName).type)) {\n      points.push(coordSys.dataToPoint(value, dimName));\n    }\n  }\n  return points;\n}\nfunction addEl(data, dataGroup, dataIndex, dimensions, coordSys) {\n  var points = createLinePoints(data, dataIndex, dimensions, coordSys);\n  var line = new graphic.Polyline({\n    shape: {\n      points: points\n    },\n    // silent: true,\n    z2: 10\n  });\n  dataGroup.add(line);\n  data.setItemGraphicEl(dataIndex, line);\n  return line;\n}\nfunction makeSeriesScope(seriesModel) {\n  var smooth = seriesModel.get('smooth', true);\n  smooth === true && (smooth = DEFAULT_SMOOTH);\n  smooth = numericToNumber(smooth);\n  eqNaN(smooth) && (smooth = 0);\n  return {\n    smooth: smooth\n  };\n}\nfunction updateElCommon(el, data, dataIndex, seriesScope) {\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.fill = null;\n  el.setShape('smooth', seriesScope.smooth);\n  var itemModel = data.getItemModel(dataIndex);\n  var emphasisModel = itemModel.getModel('emphasis');\n  setStatesStylesFromModel(el, itemModel, 'lineStyle');\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\n// function simpleDiff(oldData, newData, dimensions) {\n//     let oldLen;\n//     if (!oldData\n//         || !oldData.__plProgressive\n//         || (oldLen = oldData.count()) !== newData.count()\n//     ) {\n//         return true;\n//     }\n//     let dimLen = dimensions.length;\n//     for (let i = 0; i < oldLen; i++) {\n//         for (let j = 0; j < dimLen; j++) {\n//             if (oldData.get(dimensions[j], i) !== newData.get(dimensions[j], i)) {\n//                 return true;\n//             }\n//         }\n//     }\n//     return false;\n// }\n// FIXME put in common util?\nfunction isEmptyValue(val, axisType) {\n  return axisType === 'category' ? val == null : val == null || isNaN(val); // axisType === 'value'\n}\nexport default ParallelView;", "map": {"version": 3, "names": ["__extends", "graphic", "setStatesStylesFromModel", "toggleHoverEmphasis", "ChartView", "numericToNumber", "eqNaN", "saveOldStyle", "DEFAULT_SMOOTH", "<PERSON><PERSON><PERSON><PERSON>iew", "_super", "_this", "apply", "arguments", "type", "_dataGroup", "Group", "_initialized", "prototype", "init", "group", "add", "render", "seriesModel", "ecModel", "api", "payload", "_progressiveEls", "dataGroup", "data", "getData", "oldData", "_data", "coordSys", "coordinateSystem", "dimensions", "seriesScope", "makeSeriesScope", "diff", "update", "remove", "execute", "newDataIndex", "line", "addEl", "updateElCommon", "oldDataIndex", "getItemGraphicEl", "points", "createLinePoints", "setItemGraphicEl", "updateProps", "shape", "clipPath", "createGridClipShape", "setTimeout", "removeClip<PERSON>ath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "incrementalPrepareRender", "removeAll", "incrementalRender", "taskParams", "progressiveEls", "dataIndex", "start", "end", "incremental", "push", "cb", "parallelModel", "model", "rect", "getRect", "rectEl", "Rect", "x", "y", "width", "height", "dim", "get", "setShape", "initProps", "i", "length", "dimName", "value", "mapDimension", "isEmptyValue", "getAxis", "dataToPoint", "Polyline", "z2", "smooth", "el", "useStyle", "getItemVisual", "style", "fill", "itemModel", "getItemModel", "emphasisModel", "getModel", "val", "axisType", "isNaN"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/chart/parallel/ParallelView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { numericToNumber } from '../../util/number.js';\nimport { eqNaN } from 'zrender/lib/core/util.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar DEFAULT_SMOOTH = 0.3;\nvar ParallelView = /** @class */function (_super) {\n  __extends(ParallelView, _super);\n  function ParallelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelView.type;\n    _this._dataGroup = new graphic.Group();\n    _this._initialized = false;\n    return _this;\n  }\n  ParallelView.prototype.init = function () {\n    this.group.add(this._dataGroup);\n  };\n  /**\r\n   * @override\r\n   */\n  ParallelView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var dataGroup = this._dataGroup;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    data.diff(oldData).add(add).update(update).remove(remove).execute();\n    function add(newDataIndex) {\n      var line = addEl(data, dataGroup, newDataIndex, dimensions, coordSys);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function update(newDataIndex, oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      var points = createLinePoints(data, newDataIndex, dimensions, coordSys);\n      data.setItemGraphicEl(newDataIndex, line);\n      graphic.updateProps(line, {\n        shape: {\n          points: points\n        }\n      }, seriesModel, newDataIndex);\n      saveOldStyle(line);\n      updateElCommon(line, data, newDataIndex, seriesScope);\n    }\n    function remove(oldDataIndex) {\n      var line = oldData.getItemGraphicEl(oldDataIndex);\n      dataGroup.remove(line);\n    }\n    // First create\n    if (!this._initialized) {\n      this._initialized = true;\n      var clipPath = createGridClipShape(coordSys, seriesModel, function () {\n        // Callback will be invoked immediately if there is no animation\n        setTimeout(function () {\n          dataGroup.removeClipPath();\n        });\n      });\n      dataGroup.setClipPath(clipPath);\n    }\n    this._data = data;\n  };\n  ParallelView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this._initialized = true;\n    this._data = null;\n    this._dataGroup.removeAll();\n  };\n  ParallelView.prototype.incrementalRender = function (taskParams, seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var coordSys = seriesModel.coordinateSystem;\n    var dimensions = coordSys.dimensions;\n    var seriesScope = makeSeriesScope(seriesModel);\n    var progressiveEls = this._progressiveEls = [];\n    for (var dataIndex = taskParams.start; dataIndex < taskParams.end; dataIndex++) {\n      var line = addEl(data, this._dataGroup, dataIndex, dimensions, coordSys);\n      line.incremental = true;\n      updateElCommon(line, data, dataIndex, seriesScope);\n      progressiveEls.push(line);\n    }\n  };\n  ParallelView.prototype.remove = function () {\n    this._dataGroup && this._dataGroup.removeAll();\n    this._data = null;\n  };\n  ParallelView.type = 'parallel';\n  return ParallelView;\n}(ChartView);\nfunction createGridClipShape(coordSys, seriesModel, cb) {\n  var parallelModel = coordSys.model;\n  var rect = coordSys.getRect();\n  var rectEl = new graphic.Rect({\n    shape: {\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    }\n  });\n  var dim = parallelModel.get('layout') === 'horizontal' ? 'width' : 'height';\n  rectEl.setShape(dim, 0);\n  graphic.initProps(rectEl, {\n    shape: {\n      width: rect.width,\n      height: rect.height\n    }\n  }, seriesModel, cb);\n  return rectEl;\n}\nfunction createLinePoints(data, dataIndex, dimensions, coordSys) {\n  var points = [];\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimName = dimensions[i];\n    var value = data.get(data.mapDimension(dimName), dataIndex);\n    if (!isEmptyValue(value, coordSys.getAxis(dimName).type)) {\n      points.push(coordSys.dataToPoint(value, dimName));\n    }\n  }\n  return points;\n}\nfunction addEl(data, dataGroup, dataIndex, dimensions, coordSys) {\n  var points = createLinePoints(data, dataIndex, dimensions, coordSys);\n  var line = new graphic.Polyline({\n    shape: {\n      points: points\n    },\n    // silent: true,\n    z2: 10\n  });\n  dataGroup.add(line);\n  data.setItemGraphicEl(dataIndex, line);\n  return line;\n}\nfunction makeSeriesScope(seriesModel) {\n  var smooth = seriesModel.get('smooth', true);\n  smooth === true && (smooth = DEFAULT_SMOOTH);\n  smooth = numericToNumber(smooth);\n  eqNaN(smooth) && (smooth = 0);\n  return {\n    smooth: smooth\n  };\n}\nfunction updateElCommon(el, data, dataIndex, seriesScope) {\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.fill = null;\n  el.setShape('smooth', seriesScope.smooth);\n  var itemModel = data.getItemModel(dataIndex);\n  var emphasisModel = itemModel.getModel('emphasis');\n  setStatesStylesFromModel(el, itemModel, 'lineStyle');\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\n// function simpleDiff(oldData, newData, dimensions) {\n//     let oldLen;\n//     if (!oldData\n//         || !oldData.__plProgressive\n//         || (oldLen = oldData.count()) !== newData.count()\n//     ) {\n//         return true;\n//     }\n//     let dimLen = dimensions.length;\n//     for (let i = 0; i < oldLen; i++) {\n//         for (let j = 0; j < dimLen; j++) {\n//             if (oldData.get(dimensions[j], i) !== newData.get(dimensions[j], i)) {\n//                 return true;\n//             }\n//         }\n//     }\n//     return false;\n// }\n// FIXME put in common util?\nfunction isEmptyValue(val, axisType) {\n  return axisType === 'category' ? val == null : val == null || isNaN(val); // axisType === 'value'\n}\nexport default ParallelView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,YAAY,QAAQ,oCAAoC;AACjE,IAAIC,cAAc,GAAG,GAAG;AACxB,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDV,SAAS,CAACS,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9BH,KAAK,CAACI,UAAU,GAAG,IAAId,OAAO,CAACe,KAAK,CAAC,CAAC;IACtCL,KAAK,CAACM,YAAY,GAAG,KAAK;IAC1B,OAAON,KAAK;EACd;EACAF,YAAY,CAACS,SAAS,CAACC,IAAI,GAAG,YAAY;IACxC,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,IAAI,CAACN,UAAU,CAAC;EACjC,CAAC;EACD;AACF;AACA;EACEN,YAAY,CAACS,SAAS,CAACI,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC5E;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAIC,SAAS,GAAG,IAAI,CAACb,UAAU;IAC/B,IAAIc,IAAI,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIC,QAAQ,GAAGV,WAAW,CAACW,gBAAgB;IAC3C,IAAIC,UAAU,GAAGF,QAAQ,CAACE,UAAU;IACpC,IAAIC,WAAW,GAAGC,eAAe,CAACd,WAAW,CAAC;IAC9CM,IAAI,CAACS,IAAI,CAACP,OAAO,CAAC,CAACV,GAAG,CAACA,GAAG,CAAC,CAACkB,MAAM,CAACA,MAAM,CAAC,CAACC,MAAM,CAACA,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;IACnE,SAASpB,GAAGA,CAACqB,YAAY,EAAE;MACzB,IAAIC,IAAI,GAAGC,KAAK,CAACf,IAAI,EAAED,SAAS,EAAEc,YAAY,EAAEP,UAAU,EAAEF,QAAQ,CAAC;MACrEY,cAAc,CAACF,IAAI,EAAEd,IAAI,EAAEa,YAAY,EAAEN,WAAW,CAAC;IACvD;IACA,SAASG,MAAMA,CAACG,YAAY,EAAEI,YAAY,EAAE;MAC1C,IAAIH,IAAI,GAAGZ,OAAO,CAACgB,gBAAgB,CAACD,YAAY,CAAC;MACjD,IAAIE,MAAM,GAAGC,gBAAgB,CAACpB,IAAI,EAAEa,YAAY,EAAEP,UAAU,EAAEF,QAAQ,CAAC;MACvEJ,IAAI,CAACqB,gBAAgB,CAACR,YAAY,EAAEC,IAAI,CAAC;MACzC1C,OAAO,CAACkD,WAAW,CAACR,IAAI,EAAE;QACxBS,KAAK,EAAE;UACLJ,MAAM,EAAEA;QACV;MACF,CAAC,EAAEzB,WAAW,EAAEmB,YAAY,CAAC;MAC7BnC,YAAY,CAACoC,IAAI,CAAC;MAClBE,cAAc,CAACF,IAAI,EAAEd,IAAI,EAAEa,YAAY,EAAEN,WAAW,CAAC;IACvD;IACA,SAASI,MAAMA,CAACM,YAAY,EAAE;MAC5B,IAAIH,IAAI,GAAGZ,OAAO,CAACgB,gBAAgB,CAACD,YAAY,CAAC;MACjDlB,SAAS,CAACY,MAAM,CAACG,IAAI,CAAC;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAAC1B,YAAY,EAAE;MACtB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAIoC,QAAQ,GAAGC,mBAAmB,CAACrB,QAAQ,EAAEV,WAAW,EAAE,YAAY;QACpE;QACAgC,UAAU,CAAC,YAAY;UACrB3B,SAAS,CAAC4B,cAAc,CAAC,CAAC;QAC5B,CAAC,CAAC;MACJ,CAAC,CAAC;MACF5B,SAAS,CAAC6B,WAAW,CAACJ,QAAQ,CAAC;IACjC;IACA,IAAI,CAACrB,KAAK,GAAGH,IAAI;EACnB,CAAC;EACDpB,YAAY,CAACS,SAAS,CAACwC,wBAAwB,GAAG,UAAUnC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrF,IAAI,CAACR,YAAY,GAAG,IAAI;IACxB,IAAI,CAACe,KAAK,GAAG,IAAI;IACjB,IAAI,CAACjB,UAAU,CAAC4C,SAAS,CAAC,CAAC;EAC7B,CAAC;EACDlD,YAAY,CAACS,SAAS,CAAC0C,iBAAiB,GAAG,UAAUC,UAAU,EAAEtC,WAAW,EAAEC,OAAO,EAAE;IACrF,IAAIK,IAAI,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC;IAChC,IAAIG,QAAQ,GAAGV,WAAW,CAACW,gBAAgB;IAC3C,IAAIC,UAAU,GAAGF,QAAQ,CAACE,UAAU;IACpC,IAAIC,WAAW,GAAGC,eAAe,CAACd,WAAW,CAAC;IAC9C,IAAIuC,cAAc,GAAG,IAAI,CAACnC,eAAe,GAAG,EAAE;IAC9C,KAAK,IAAIoC,SAAS,GAAGF,UAAU,CAACG,KAAK,EAAED,SAAS,GAAGF,UAAU,CAACI,GAAG,EAAEF,SAAS,EAAE,EAAE;MAC9E,IAAIpB,IAAI,GAAGC,KAAK,CAACf,IAAI,EAAE,IAAI,CAACd,UAAU,EAAEgD,SAAS,EAAE5B,UAAU,EAAEF,QAAQ,CAAC;MACxEU,IAAI,CAACuB,WAAW,GAAG,IAAI;MACvBrB,cAAc,CAACF,IAAI,EAAEd,IAAI,EAAEkC,SAAS,EAAE3B,WAAW,CAAC;MAClD0B,cAAc,CAACK,IAAI,CAACxB,IAAI,CAAC;IAC3B;EACF,CAAC;EACDlC,YAAY,CAACS,SAAS,CAACsB,MAAM,GAAG,YAAY;IAC1C,IAAI,CAACzB,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4C,SAAS,CAAC,CAAC;IAC9C,IAAI,CAAC3B,KAAK,GAAG,IAAI;EACnB,CAAC;EACDvB,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACL,SAAS,CAAC;AACZ,SAASkD,mBAAmBA,CAACrB,QAAQ,EAAEV,WAAW,EAAE6C,EAAE,EAAE;EACtD,IAAIC,aAAa,GAAGpC,QAAQ,CAACqC,KAAK;EAClC,IAAIC,IAAI,GAAGtC,QAAQ,CAACuC,OAAO,CAAC,CAAC;EAC7B,IAAIC,MAAM,GAAG,IAAIxE,OAAO,CAACyE,IAAI,CAAC;IAC5BtB,KAAK,EAAE;MACLuB,CAAC,EAAEJ,IAAI,CAACI,CAAC;MACTC,CAAC,EAAEL,IAAI,CAACK,CAAC;MACTC,KAAK,EAAEN,IAAI,CAACM,KAAK;MACjBC,MAAM,EAAEP,IAAI,CAACO;IACf;EACF,CAAC,CAAC;EACF,IAAIC,GAAG,GAAGV,aAAa,CAACW,GAAG,CAAC,QAAQ,CAAC,KAAK,YAAY,GAAG,OAAO,GAAG,QAAQ;EAC3EP,MAAM,CAACQ,QAAQ,CAACF,GAAG,EAAE,CAAC,CAAC;EACvB9E,OAAO,CAACiF,SAAS,CAACT,MAAM,EAAE;IACxBrB,KAAK,EAAE;MACLyB,KAAK,EAAEN,IAAI,CAACM,KAAK;MACjBC,MAAM,EAAEP,IAAI,CAACO;IACf;EACF,CAAC,EAAEvD,WAAW,EAAE6C,EAAE,CAAC;EACnB,OAAOK,MAAM;AACf;AACA,SAASxB,gBAAgBA,CAACpB,IAAI,EAAEkC,SAAS,EAAE5B,UAAU,EAAEF,QAAQ,EAAE;EAC/D,IAAIe,MAAM,GAAG,EAAE;EACf,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,UAAU,CAACiD,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIE,OAAO,GAAGlD,UAAU,CAACgD,CAAC,CAAC;IAC3B,IAAIG,KAAK,GAAGzD,IAAI,CAACmD,GAAG,CAACnD,IAAI,CAAC0D,YAAY,CAACF,OAAO,CAAC,EAAEtB,SAAS,CAAC;IAC3D,IAAI,CAACyB,YAAY,CAACF,KAAK,EAAErD,QAAQ,CAACwD,OAAO,CAACJ,OAAO,CAAC,CAACvE,IAAI,CAAC,EAAE;MACxDkC,MAAM,CAACmB,IAAI,CAAClC,QAAQ,CAACyD,WAAW,CAACJ,KAAK,EAAED,OAAO,CAAC,CAAC;IACnD;EACF;EACA,OAAOrC,MAAM;AACf;AACA,SAASJ,KAAKA,CAACf,IAAI,EAAED,SAAS,EAAEmC,SAAS,EAAE5B,UAAU,EAAEF,QAAQ,EAAE;EAC/D,IAAIe,MAAM,GAAGC,gBAAgB,CAACpB,IAAI,EAAEkC,SAAS,EAAE5B,UAAU,EAAEF,QAAQ,CAAC;EACpE,IAAIU,IAAI,GAAG,IAAI1C,OAAO,CAAC0F,QAAQ,CAAC;IAC9BvC,KAAK,EAAE;MACLJ,MAAM,EAAEA;IACV,CAAC;IACD;IACA4C,EAAE,EAAE;EACN,CAAC,CAAC;EACFhE,SAAS,CAACP,GAAG,CAACsB,IAAI,CAAC;EACnBd,IAAI,CAACqB,gBAAgB,CAACa,SAAS,EAAEpB,IAAI,CAAC;EACtC,OAAOA,IAAI;AACb;AACA,SAASN,eAAeA,CAACd,WAAW,EAAE;EACpC,IAAIsE,MAAM,GAAGtE,WAAW,CAACyD,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;EAC5Ca,MAAM,KAAK,IAAI,KAAKA,MAAM,GAAGrF,cAAc,CAAC;EAC5CqF,MAAM,GAAGxF,eAAe,CAACwF,MAAM,CAAC;EAChCvF,KAAK,CAACuF,MAAM,CAAC,KAAKA,MAAM,GAAG,CAAC,CAAC;EAC7B,OAAO;IACLA,MAAM,EAAEA;EACV,CAAC;AACH;AACA,SAAShD,cAAcA,CAACiD,EAAE,EAAEjE,IAAI,EAAEkC,SAAS,EAAE3B,WAAW,EAAE;EACxD0D,EAAE,CAACC,QAAQ,CAAClE,IAAI,CAACmE,aAAa,CAACjC,SAAS,EAAE,OAAO,CAAC,CAAC;EACnD+B,EAAE,CAACG,KAAK,CAACC,IAAI,GAAG,IAAI;EACpBJ,EAAE,CAACb,QAAQ,CAAC,QAAQ,EAAE7C,WAAW,CAACyD,MAAM,CAAC;EACzC,IAAIM,SAAS,GAAGtE,IAAI,CAACuE,YAAY,CAACrC,SAAS,CAAC;EAC5C,IAAIsC,aAAa,GAAGF,SAAS,CAACG,QAAQ,CAAC,UAAU,CAAC;EAClDpG,wBAAwB,CAAC4F,EAAE,EAAEK,SAAS,EAAE,WAAW,CAAC;EACpDhG,mBAAmB,CAAC2F,EAAE,EAAEO,aAAa,CAACrB,GAAG,CAAC,OAAO,CAAC,EAAEqB,aAAa,CAACrB,GAAG,CAAC,WAAW,CAAC,EAAEqB,aAAa,CAACrB,GAAG,CAAC,UAAU,CAAC,CAAC;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAACe,GAAG,EAAEC,QAAQ,EAAE;EACnC,OAAOA,QAAQ,KAAK,UAAU,GAAGD,GAAG,IAAI,IAAI,GAAGA,GAAG,IAAI,IAAI,IAAIE,KAAK,CAACF,GAAG,CAAC,CAAC,CAAC;AAC5E;AACA,eAAe9F,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}