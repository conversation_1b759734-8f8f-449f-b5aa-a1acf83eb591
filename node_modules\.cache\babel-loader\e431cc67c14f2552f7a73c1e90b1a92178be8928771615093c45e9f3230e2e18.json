{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Gradient from './Gradient.js';\nvar LinearGradient = function (_super) {\n  __extends(LinearGradient, _super);\n  function LinearGradient(x, y, x2, y2, colorStops, globalCoord) {\n    var _this = _super.call(this, colorStops) || this;\n    _this.x = x == null ? 0 : x;\n    _this.y = y == null ? 0 : y;\n    _this.x2 = x2 == null ? 1 : x2;\n    _this.y2 = y2 == null ? 0 : y2;\n    _this.type = 'linear';\n    _this.global = globalCoord || false;\n    return _this;\n  }\n  return LinearGradient;\n}(Gradient);\nexport default LinearGradient;\n;", "map": {"version": 3, "names": ["__extends", "Gradient", "LinearGradient", "_super", "x", "y", "x2", "y2", "colorStops", "globalCoord", "_this", "call", "type", "global"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/graphic/LinearGradient.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Gradient from './Gradient.js';\nvar LinearGradient = (function (_super) {\n    __extends(LinearGradient, _super);\n    function LinearGradient(x, y, x2, y2, colorStops, globalCoord) {\n        var _this = _super.call(this, colorStops) || this;\n        _this.x = x == null ? 0 : x;\n        _this.y = y == null ? 0 : y;\n        _this.x2 = x2 == null ? 1 : x2;\n        _this.y2 = y2 == null ? 0 : y2;\n        _this.type = 'linear';\n        _this.global = globalCoord || false;\n        return _this;\n    }\n    return LinearGradient;\n}(Gradient));\nexport default LinearGradient;\n;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAIC,cAAc,GAAI,UAAUC,MAAM,EAAE;EACpCH,SAAS,CAACE,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAC3D,IAAIC,KAAK,GAAGP,MAAM,CAACQ,IAAI,CAAC,IAAI,EAAEH,UAAU,CAAC,IAAI,IAAI;IACjDE,KAAK,CAACN,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,CAAC,GAAGA,CAAC;IAC3BM,KAAK,CAACL,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,CAAC,GAAGA,CAAC;IAC3BK,KAAK,CAACJ,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,CAAC,GAAGA,EAAE;IAC9BI,KAAK,CAACH,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,CAAC,GAAGA,EAAE;IAC9BG,KAAK,CAACE,IAAI,GAAG,QAAQ;IACrBF,KAAK,CAACG,MAAM,GAAGJ,WAAW,IAAI,KAAK;IACnC,OAAOC,KAAK;EAChB;EACA,OAAOR,cAAc;AACzB,CAAC,CAACD,QAAQ,CAAE;AACZ,eAAeC,cAAc;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}