{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport GeoModel from '../../coord/geo/GeoModel.js';\nimport geoCreator from '../../coord/geo/geoCreator.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nimport GeoView from './GeoView.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nfunction registerMap(mapName, geoJson, specialAreas) {\n  geoSourceManager.registerMap(mapName, geoJson, specialAreas);\n}\nexport function install(registers) {\n  registers.registerCoordinateSystem('geo', geoCreator);\n  registers.registerComponentModel(GeoModel);\n  registers.registerComponentView(GeoView);\n  registers.registerImpl('registerMap', registerMap);\n  registers.registerImpl('getMap', function (mapName) {\n    return geoSourceManager.getMapForUser(mapName);\n  });\n  function makeAction(method, actionInfo) {\n    actionInfo.update = 'geo:updateSelectStatus';\n    registers.registerAction(actionInfo, function (payload, ecModel) {\n      var selected = {};\n      var allSelected = [];\n      ecModel.eachComponent({\n        mainType: 'geo',\n        query: payload\n      }, function (geoModel) {\n        geoModel[method](payload.name);\n        var geo = geoModel.coordinateSystem;\n        each(geo.regions, function (region) {\n          selected[region.name] = geoModel.isSelected(region.name) || false;\n        });\n        // Notice: there might be duplicated name in different regions.\n        var names = [];\n        each(selected, function (v, name) {\n          selected[name] && names.push(name);\n        });\n        allSelected.push({\n          geoIndex: geoModel.componentIndex,\n          // Use singular, the same naming convention as the event `selectchanged`.\n          name: names\n        });\n      });\n      return {\n        selected: selected,\n        allSelected: allSelected,\n        name: payload.name\n      };\n    });\n  }\n  makeAction('toggleSelected', {\n    type: 'geoToggleSelect',\n    event: 'geoselectchanged'\n  });\n  makeAction('select', {\n    type: 'geoSelect',\n    event: 'geoselected'\n  });\n  makeAction('unSelect', {\n    type: 'geoUnSelect',\n    event: 'geounselected'\n  });\n  /**\r\n   * @payload\r\n   * @property {string} [componentType=series]\r\n   * @property {number} [dx]\r\n   * @property {number} [dy]\r\n   * @property {number} [zoom]\r\n   * @property {number} [originX]\r\n   * @property {number} [originY]\r\n   */\n  registers.registerAction({\n    type: 'geoRoam',\n    event: 'geoRoam',\n    update: 'updateTransform'\n  }, function (payload, ecModel, api) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      var geo = componentModel.coordinateSystem;\n      if (geo.type !== 'geo') {\n        return;\n      }\n      var res = updateCenterAndZoom(geo, payload, componentModel.get('scaleLimit'), api);\n      componentModel.setCenter && componentModel.setCenter(res.center);\n      componentModel.setZoom && componentModel.setZoom(res.zoom);\n      // All map series with same `map` use the same geo coordinate system\n      // So the center and zoom must be in sync. Include the series not selected by legend\n      if (componentType === 'series') {\n        each(componentModel.seriesGroup, function (seriesModel) {\n          seriesModel.setCenter(res.center);\n          seriesModel.setZoom(res.zoom);\n        });\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["GeoModel", "geoCreator", "each", "updateCenterAndZoom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geoSourceManager", "registerMap", "mapName", "geoJson", "<PERSON><PERSON><PERSON><PERSON>", "install", "registers", "registerCoordinateSystem", "registerComponentModel", "registerComponentView", "registerImpl", "getMapForUser", "makeAction", "method", "actionInfo", "update", "registerAction", "payload", "ecModel", "selected", "allSelected", "eachComponent", "mainType", "query", "geoModel", "name", "geo", "coordinateSystem", "regions", "region", "isSelected", "names", "v", "push", "geoIndex", "componentIndex", "type", "event", "api", "componentType", "componentModel", "res", "get", "setCenter", "center", "setZoom", "zoom", "seriesGroup", "seriesModel"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/component/geo/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport GeoModel from '../../coord/geo/GeoModel.js';\nimport geoCreator from '../../coord/geo/geoCreator.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nimport GeoView from './GeoView.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nfunction registerMap(mapName, geoJson, specialAreas) {\n  geoSourceManager.registerMap(mapName, geoJson, specialAreas);\n}\nexport function install(registers) {\n  registers.registerCoordinateSystem('geo', geoCreator);\n  registers.registerComponentModel(GeoModel);\n  registers.registerComponentView(GeoView);\n  registers.registerImpl('registerMap', registerMap);\n  registers.registerImpl('getMap', function (mapName) {\n    return geoSourceManager.getMapForUser(mapName);\n  });\n  function makeAction(method, actionInfo) {\n    actionInfo.update = 'geo:updateSelectStatus';\n    registers.registerAction(actionInfo, function (payload, ecModel) {\n      var selected = {};\n      var allSelected = [];\n      ecModel.eachComponent({\n        mainType: 'geo',\n        query: payload\n      }, function (geoModel) {\n        geoModel[method](payload.name);\n        var geo = geoModel.coordinateSystem;\n        each(geo.regions, function (region) {\n          selected[region.name] = geoModel.isSelected(region.name) || false;\n        });\n        // Notice: there might be duplicated name in different regions.\n        var names = [];\n        each(selected, function (v, name) {\n          selected[name] && names.push(name);\n        });\n        allSelected.push({\n          geoIndex: geoModel.componentIndex,\n          // Use singular, the same naming convention as the event `selectchanged`.\n          name: names\n        });\n      });\n      return {\n        selected: selected,\n        allSelected: allSelected,\n        name: payload.name\n      };\n    });\n  }\n  makeAction('toggleSelected', {\n    type: 'geoToggleSelect',\n    event: 'geoselectchanged'\n  });\n  makeAction('select', {\n    type: 'geoSelect',\n    event: 'geoselected'\n  });\n  makeAction('unSelect', {\n    type: 'geoUnSelect',\n    event: 'geounselected'\n  });\n  /**\r\n   * @payload\r\n   * @property {string} [componentType=series]\r\n   * @property {number} [dx]\r\n   * @property {number} [dy]\r\n   * @property {number} [zoom]\r\n   * @property {number} [originX]\r\n   * @property {number} [originY]\r\n   */\n  registers.registerAction({\n    type: 'geoRoam',\n    event: 'geoRoam',\n    update: 'updateTransform'\n  }, function (payload, ecModel, api) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      var geo = componentModel.coordinateSystem;\n      if (geo.type !== 'geo') {\n        return;\n      }\n      var res = updateCenterAndZoom(geo, payload, componentModel.get('scaleLimit'), api);\n      componentModel.setCenter && componentModel.setCenter(res.center);\n      componentModel.setZoom && componentModel.setZoom(res.zoom);\n      // All map series with same `map` use the same geo coordinate system\n      // So the center and zoom must be in sync. Include the series not selected by legend\n      if (componentType === 'series') {\n        each(componentModel.seriesGroup, function (seriesModel) {\n          seriesModel.setCenter(res.center);\n          seriesModel.setZoom(res.zoom);\n        });\n      }\n    });\n  });\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAE;EACnDJ,gBAAgB,CAACC,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAEC,YAAY,CAAC;AAC9D;AACA,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,wBAAwB,CAAC,KAAK,EAAEX,UAAU,CAAC;EACrDU,SAAS,CAACE,sBAAsB,CAACb,QAAQ,CAAC;EAC1CW,SAAS,CAACG,qBAAqB,CAACV,OAAO,CAAC;EACxCO,SAAS,CAACI,YAAY,CAAC,aAAa,EAAET,WAAW,CAAC;EAClDK,SAAS,CAACI,YAAY,CAAC,QAAQ,EAAE,UAAUR,OAAO,EAAE;IAClD,OAAOF,gBAAgB,CAACW,aAAa,CAACT,OAAO,CAAC;EAChD,CAAC,CAAC;EACF,SAASU,UAAUA,CAACC,MAAM,EAAEC,UAAU,EAAE;IACtCA,UAAU,CAACC,MAAM,GAAG,wBAAwB;IAC5CT,SAAS,CAACU,cAAc,CAACF,UAAU,EAAE,UAAUG,OAAO,EAAEC,OAAO,EAAE;MAC/D,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjB,IAAIC,WAAW,GAAG,EAAE;MACpBF,OAAO,CAACG,aAAa,CAAC;QACpBC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAEN;MACT,CAAC,EAAE,UAAUO,QAAQ,EAAE;QACrBA,QAAQ,CAACX,MAAM,CAAC,CAACI,OAAO,CAACQ,IAAI,CAAC;QAC9B,IAAIC,GAAG,GAAGF,QAAQ,CAACG,gBAAgB;QACnC9B,IAAI,CAAC6B,GAAG,CAACE,OAAO,EAAE,UAAUC,MAAM,EAAE;UAClCV,QAAQ,CAACU,MAAM,CAACJ,IAAI,CAAC,GAAGD,QAAQ,CAACM,UAAU,CAACD,MAAM,CAACJ,IAAI,CAAC,IAAI,KAAK;QACnE,CAAC,CAAC;QACF;QACA,IAAIM,KAAK,GAAG,EAAE;QACdlC,IAAI,CAACsB,QAAQ,EAAE,UAAUa,CAAC,EAAEP,IAAI,EAAE;UAChCN,QAAQ,CAACM,IAAI,CAAC,IAAIM,KAAK,CAACE,IAAI,CAACR,IAAI,CAAC;QACpC,CAAC,CAAC;QACFL,WAAW,CAACa,IAAI,CAAC;UACfC,QAAQ,EAAEV,QAAQ,CAACW,cAAc;UACjC;UACAV,IAAI,EAAEM;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAO;QACLZ,QAAQ,EAAEA,QAAQ;QAClBC,WAAW,EAAEA,WAAW;QACxBK,IAAI,EAAER,OAAO,CAACQ;MAChB,CAAC;IACH,CAAC,CAAC;EACJ;EACAb,UAAU,CAAC,gBAAgB,EAAE;IAC3BwB,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,CAAC;EACFzB,UAAU,CAAC,QAAQ,EAAE;IACnBwB,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EACFzB,UAAU,CAAC,UAAU,EAAE;IACrBwB,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,SAAS,CAACU,cAAc,CAAC;IACvBoB,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBtB,MAAM,EAAE;EACV,CAAC,EAAE,UAAUE,OAAO,EAAEC,OAAO,EAAEoB,GAAG,EAAE;IAClC,IAAIC,aAAa,GAAGtB,OAAO,CAACsB,aAAa,IAAI,QAAQ;IACrDrB,OAAO,CAACG,aAAa,CAAC;MACpBC,QAAQ,EAAEiB,aAAa;MACvBhB,KAAK,EAAEN;IACT,CAAC,EAAE,UAAUuB,cAAc,EAAE;MAC3B,IAAId,GAAG,GAAGc,cAAc,CAACb,gBAAgB;MACzC,IAAID,GAAG,CAACU,IAAI,KAAK,KAAK,EAAE;QACtB;MACF;MACA,IAAIK,GAAG,GAAG3C,mBAAmB,CAAC4B,GAAG,EAAET,OAAO,EAAEuB,cAAc,CAACE,GAAG,CAAC,YAAY,CAAC,EAAEJ,GAAG,CAAC;MAClFE,cAAc,CAACG,SAAS,IAAIH,cAAc,CAACG,SAAS,CAACF,GAAG,CAACG,MAAM,CAAC;MAChEJ,cAAc,CAACK,OAAO,IAAIL,cAAc,CAACK,OAAO,CAACJ,GAAG,CAACK,IAAI,CAAC;MAC1D;MACA;MACA,IAAIP,aAAa,KAAK,QAAQ,EAAE;QAC9B1C,IAAI,CAAC2C,cAAc,CAACO,WAAW,EAAE,UAAUC,WAAW,EAAE;UACtDA,WAAW,CAACL,SAAS,CAACF,GAAG,CAACG,MAAM,CAAC;UACjCI,WAAW,CAACH,OAAO,CAACJ,GAAG,CAACK,IAAI,CAAC;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}