{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// In somehow. If we export like\n// export * as LineChart './chart/line/install'\n// The exported code will be transformed to\n// import * as LineChart_1 './chart/line/install'; export {LineChart_1 as LineChart};\n// Treeshaking in webpack will not work even if we configured sideEffects to false in package.json\nexport { install as LineChart } from '../chart/line/install.js';\nexport { install as BarChart } from '../chart/bar/install.js';\nexport { install as PieChart } from '../chart/pie/install.js';\nexport { install as ScatterChart } from '../chart/scatter/install.js';\nexport { install as RadarChart } from '../chart/radar/install.js';\nexport { install as MapChart } from '../chart/map/install.js';\nexport { install as TreeChart } from '../chart/tree/install.js';\nexport { install as TreemapChart } from '../chart/treemap/install.js';\nexport { install as GraphChart } from '../chart/graph/install.js';\nexport { install as GaugeChart } from '../chart/gauge/install.js';\nexport { install as FunnelChart } from '../chart/funnel/install.js';\nexport { install as ParallelChart } from '../chart/parallel/install.js';\nexport { install as SankeyChart } from '../chart/sankey/install.js';\nexport { install as BoxplotChart } from '../chart/boxplot/install.js';\nexport { install as CandlestickChart } from '../chart/candlestick/install.js';\nexport { install as EffectScatterChart } from '../chart/effectScatter/install.js';\nexport { install as LinesChart } from '../chart/lines/install.js';\nexport { install as HeatmapChart } from '../chart/heatmap/install.js';\nexport { install as PictorialBarChart } from '../chart/bar/installPictorialBar.js';\nexport { install as ThemeRiverChart } from '../chart/themeRiver/install.js';\nexport { install as SunburstChart } from '../chart/sunburst/install.js';\nexport { install as CustomChart } from '../chart/custom/install.js';", "map": {"version": 3, "names": ["install", "Line<PERSON>hart", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RadarChart", "MapChart", "<PERSON><PERSON><PERSON>", "Treemap<PERSON>hart", "GraphChart", "Gauge<PERSON>hart", "FunnelChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boxplot<PERSON>hart", "CandlestickChart", "EffectScatterChart", "LinesChart", "HeatmapChart", "PictorialBarChart", "ThemeRiverChart", "SunburstChart", "CustomChart"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/export/charts.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// In somehow. If we export like\n// export * as LineChart './chart/line/install'\n// The exported code will be transformed to\n// import * as LineChart_1 './chart/line/install'; export {LineChart_1 as LineChart};\n// Treeshaking in webpack will not work even if we configured sideEffects to false in package.json\nexport { install as LineChart } from '../chart/line/install.js';\nexport { install as BarChart } from '../chart/bar/install.js';\nexport { install as PieChart } from '../chart/pie/install.js';\nexport { install as ScatterChart } from '../chart/scatter/install.js';\nexport { install as RadarChart } from '../chart/radar/install.js';\nexport { install as MapChart } from '../chart/map/install.js';\nexport { install as TreeChart } from '../chart/tree/install.js';\nexport { install as TreemapChart } from '../chart/treemap/install.js';\nexport { install as GraphChart } from '../chart/graph/install.js';\nexport { install as GaugeChart } from '../chart/gauge/install.js';\nexport { install as FunnelChart } from '../chart/funnel/install.js';\nexport { install as ParallelChart } from '../chart/parallel/install.js';\nexport { install as SankeyChart } from '../chart/sankey/install.js';\nexport { install as BoxplotChart } from '../chart/boxplot/install.js';\nexport { install as CandlestickChart } from '../chart/candlestick/install.js';\nexport { install as EffectScatterChart } from '../chart/effectScatter/install.js';\nexport { install as LinesChart } from '../chart/lines/install.js';\nexport { install as HeatmapChart } from '../chart/heatmap/install.js';\nexport { install as PictorialBarChart } from '../chart/bar/installPictorialBar.js';\nexport { install as ThemeRiverChart } from '../chart/themeRiver/install.js';\nexport { install as SunburstChart } from '../chart/sunburst/install.js';\nexport { install as CustomChart } from '../chart/custom/install.js';"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,SAAS,QAAQ,0BAA0B;AAC/D,SAASD,OAAO,IAAIE,QAAQ,QAAQ,yBAAyB;AAC7D,SAASF,OAAO,IAAIG,QAAQ,QAAQ,yBAAyB;AAC7D,SAASH,OAAO,IAAII,YAAY,QAAQ,6BAA6B;AACrE,SAASJ,OAAO,IAAIK,UAAU,QAAQ,2BAA2B;AACjE,SAASL,OAAO,IAAIM,QAAQ,QAAQ,yBAAyB;AAC7D,SAASN,OAAO,IAAIO,SAAS,QAAQ,0BAA0B;AAC/D,SAASP,OAAO,IAAIQ,YAAY,QAAQ,6BAA6B;AACrE,SAASR,OAAO,IAAIS,UAAU,QAAQ,2BAA2B;AACjE,SAAST,OAAO,IAAIU,UAAU,QAAQ,2BAA2B;AACjE,SAASV,OAAO,IAAIW,WAAW,QAAQ,4BAA4B;AACnE,SAASX,OAAO,IAAIY,aAAa,QAAQ,8BAA8B;AACvE,SAASZ,OAAO,IAAIa,WAAW,QAAQ,4BAA4B;AACnE,SAASb,OAAO,IAAIc,YAAY,QAAQ,6BAA6B;AACrE,SAASd,OAAO,IAAIe,gBAAgB,QAAQ,iCAAiC;AAC7E,SAASf,OAAO,IAAIgB,kBAAkB,QAAQ,mCAAmC;AACjF,SAAShB,OAAO,IAAIiB,UAAU,QAAQ,2BAA2B;AACjE,SAASjB,OAAO,IAAIkB,YAAY,QAAQ,6BAA6B;AACrE,SAASlB,OAAO,IAAImB,iBAAiB,QAAQ,qCAAqC;AAClF,SAASnB,OAAO,IAAIoB,eAAe,QAAQ,gCAAgC;AAC3E,SAASpB,OAAO,IAAIqB,aAAa,QAAQ,8BAA8B;AACvE,SAASrB,OAAO,IAAIsB,WAAW,QAAQ,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}