{"ast": null, "code": "import { isString } from '../core/util.js';\nexport function parseXML(svg) {\n  if (isString(svg)) {\n    var parser = new DOMParser();\n    svg = parser.parseFromString(svg, 'text/xml');\n  }\n  var svgNode = svg;\n  if (svgNode.nodeType === 9) {\n    svgNode = svgNode.firstChild;\n  }\n  while (svgNode.nodeName.toLowerCase() !== 'svg' || svgNode.nodeType !== 1) {\n    svgNode = svgNode.nextSibling;\n  }\n  return svgNode;\n}", "map": {"version": 3, "names": ["isString", "parseXML", "svg", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "svgNode", "nodeType", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "toLowerCase", "nextS<PERSON>ling"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/zrender/lib/tool/parseXML.js"], "sourcesContent": ["import { isString } from '../core/util.js';\nexport function parseXML(svg) {\n    if (isString(svg)) {\n        var parser = new DOMParser();\n        svg = parser.parseFromString(svg, 'text/xml');\n    }\n    var svgNode = svg;\n    if (svgNode.nodeType === 9) {\n        svgNode = svgNode.firstChild;\n    }\n    while (svgNode.nodeName.toLowerCase() !== 'svg' || svgNode.nodeType !== 1) {\n        svgNode = svgNode.nextSibling;\n    }\n    return svgNode;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC1B,IAAIF,QAAQ,CAACE,GAAG,CAAC,EAAE;IACf,IAAIC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC5BF,GAAG,GAAGC,MAAM,CAACE,eAAe,CAACH,GAAG,EAAE,UAAU,CAAC;EACjD;EACA,IAAII,OAAO,GAAGJ,GAAG;EACjB,IAAII,OAAO,CAACC,QAAQ,KAAK,CAAC,EAAE;IACxBD,OAAO,GAAGA,OAAO,CAACE,UAAU;EAChC;EACA,OAAOF,OAAO,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIJ,OAAO,CAACC,QAAQ,KAAK,CAAC,EAAE;IACvED,OAAO,GAAGA,OAAO,CAACK,WAAW;EACjC;EACA,OAAOL,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}