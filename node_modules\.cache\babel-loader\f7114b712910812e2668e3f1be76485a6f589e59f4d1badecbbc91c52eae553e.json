{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bar-card\"\n};\nconst _hoisted_2 = {\n  ref: \"volumn\",\n  class: \"volume\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, null, 512 /* NEED_PATCH */)]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2"], "sources": ["E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue"], "sourcesContent": ["<template>\n  <div class=\"bar-card\">\n    <div ref=\"volumn\" class=\"volume\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Pyramid<PERSON>hart',\n  data() {\n    return {\n      myChart: null,\n    };\n  },\n  mounted() {\n    // 获取数据。\n    if (this.$refs.volumn) {\n      this.reloadChart();\n      // 自适应浏览器。\n      window.addEventListener(\"resize\", () => {\n        this.reloadChart();\n      });\n    }\n  },\n  // 组件销毁。\n  beforeUnmount() {\n    this.disposeChart();\n  },\n  methods: {\n    drawChart() {\n      this.myChart = this.$echarts.init(this.$refs.volumn);\n      var data = [85, 15];\n      var xData = [\n        \"个人\",\n        \"单位\",\n      ];\n      var colors = [\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#1e4a72\",\n            },\n            {\n              offset: 0.5,\n              color: \"#2d6ba3\",\n            },\n            {\n              offset: 1,\n              color: \"#5fb3f0\",\n            },\n          ],\n        },\n        {\n          type: \"linear\",\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          global: false,\n          colorStops: [\n            {\n              offset: 0,\n              color: \"#0f3a5f\",\n            },\n            {\n              offset: 0.5,\n              color: \"#1e5a8a\",\n            },\n            {\n              offset: 1,\n              color: \"#4da6d9\",\n            },\n          ],\n        },\n      ];\n      \n      var renderItem = function(params, api) {\n        var dataIndex = params.dataIndex;\n        var value = api.value(1);\n\n        // 调整高度比例，避免差距过大\n        var normalizedHeight;\n        if (value >= 50) {\n          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例\n        } else {\n          normalizedHeight = 30 + value * 0.6; // 小值适当放大\n        }\n\n        var start = api.coord([dataIndex, 0]);\n        var end = api.coord([dataIndex, normalizedHeight]);\n        var style = api.style();\n\n        // 计算锥形的尺寸\n        var height = start[1] - end[1];\n        var baseRadius = height * 0.4; // 底面半径\n        var x = start[0];\n        var y = end[1];\n\n        // 创建3D锥形的关键点\n        var topPoint = [x, y]; // 顶点\n\n        // 底面的四个关键点（形成正方形底面）\n        var bottomFront = [x, start[1]]; // 前面中心\n        var bottomBack = [x, start[1] + baseRadius * 0.6]; // 后面中心（营造3D深度）\n        var bottomLeft = [x - baseRadius, start[1] + baseRadius * 0.3]; // 左侧\n        var bottomRight = [x + baseRadius, start[1] + baseRadius * 0.3]; // 右侧\n\n        var group = {\n          type: \"group\",\n          children: [\n            // 前面（主面）\n            {\n              type: \"polygon\",\n              z2: 4,\n              shape: {\n                points: [topPoint, bottomLeft, bottomFront, bottomRight],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: style.fill.colorStops\n                },\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 0.5,\n              },\n            },\n            // 左侧面\n            {\n              type: \"polygon\",\n              z2: 3,\n              shape: {\n                points: [topPoint, bottomBack, bottomLeft],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 0.5, color: style.fill.colorStops[1].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.7,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 0.5,\n              },\n            },\n            // 右侧面\n            {\n              type: \"polygon\",\n              z2: 2,\n              shape: {\n                points: [topPoint, bottomRight, bottomBack],\n              },\n              style: {\n                fill: {\n                  type: \"linear\",\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: style.fill.colorStops[0].color },\n                    { offset: 0.5, color: style.fill.colorStops[1].color },\n                    { offset: 1, color: style.fill.colorStops[2].color }\n                  ]\n                },\n                opacity: 0.5,\n                stroke: 'rgba(255,255,255,0.05)',\n                lineWidth: 0.5,\n              },\n            },\n            // 底面（可选，增强立体感）\n            {\n              type: \"polygon\",\n              z2: 1,\n              shape: {\n                points: [bottomLeft, bottomFront, bottomRight, bottomBack],\n              },\n              style: {\n                fill: style.fill.colorStops[2].color,\n                opacity: 0.3,\n                stroke: 'rgba(255,255,255,0.1)',\n                lineWidth: 0.5,\n              },\n            },\n          ],\n        };\n\n        return group;\n      };\n\n      // 绘制图表\n      var option = {\n        title: {\n          text: '警情主体类型分布',\n          left: 'center',\n          top: '5%',\n          textStyle: {\n            color: '#ffffff',\n            fontSize: this.fontSize(0.6),\n            fontWeight: 'normal'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          backgroundColor: \"rgba(9,35,75,0.8)\",\n          borderColor: \"#2187F9\",\n          borderWidth: 1,\n          borderRadius: 8,\n          textStyle: {\n            color: \"#A7EFFF\",\n            fontSize: this.fontSize(0.4),\n          },\n          formatter: function(params) {\n            return params.name + ': ' + params.value + '%';\n          }\n        },\n        grid: {\n          top: \"25%\",\n          left: \"10%\",\n          bottom: \"25%\",\n          right: \"10%\",\n          containLabel: false,\n        },\n        xAxis: {\n          data: xData,\n          show: false, // 隐藏X轴\n        },\n        yAxis: {\n          show: false, // 隐藏Y轴\n          max: 120, // 调整最大值以适应新的高度计算\n        },\n\n        series: [\n          {\n            type: \"custom\",\n            itemStyle: {\n              color: function(params) {\n                return colors[params.dataIndex];\n              },\n            },\n            label: {\n              show: true,\n              position: [0, -40],\n              color: \"#ffffff\",\n              fontSize: this.fontSize(0.8),\n              fontWeight: 'bold',\n              formatter: function(params) {\n                return params.data + '%';\n              },\n              align: 'center'\n            },\n            data: data,\n            renderItem: renderItem,\n          },\n          // 添加底部标签\n          {\n            type: \"custom\",\n            renderItem: function(params, api) {\n              var x = api.coord([params.dataIndex, 0])[0];\n              var y = api.coord([params.dataIndex, 0])[1] + 60;\n\n              return {\n                type: 'group',\n                children: [\n                  // 白色虚线\n                  {\n                    type: 'line',\n                    shape: {\n                      x1: x,\n                      y1: y - 50,\n                      x2: x,\n                      y2: y - 20\n                    },\n                    style: {\n                      stroke: '#ffffff',\n                      lineWidth: 2,\n                      lineDash: [5, 5],\n                      opacity: 0.8\n                    }\n                  },\n                  // 标签文字\n                  {\n                    type: 'text',\n                    style: {\n                      text: xData[params.dataIndex],\n                      x: x,\n                      y: y,\n                      textAlign: 'center',\n                      textVerticalAlign: 'top',\n                      fill: '#ffffff',\n                      fontSize: 18,\n                      fontWeight: 'normal'\n                    }\n                  }\n                ]\n              };\n            },\n            data: data,\n          },\n        ],\n      };\n      this.myChart.setOption(option);\n    },\n    // 字体自适应。\n    fontSize(res) {\n      const clientWidth =\n        window.innerWidth ||\n        document.documentElement.clientWidth ||\n        document.body.clientWidth;\n      if (!clientWidth) return;\n      const fontSize = 40 * (clientWidth / 1920);\n      return res * fontSize;\n    },\n    // 销毁图表。\n    disposeChart() {\n      if (this.myChart) {\n        this.myChart.dispose();\n      }\n    },\n    // 重新加载图表。\n    reloadChart() {\n      this.disposeChart();\n      this.drawChart();\n    },\n  },\n};\n</script>\n\n<style scoped>\n.bar-card {\n  width: 100%;\n  height: 100%;\n  .volume {\n    width: 100%;\n    height: 100%;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EACdC,GAAG,EAAC,QAAQ;EAACD,KAAK,EAAC;;;uBAD1BE,mBAAA,CAEM,OAFNC,UAEM,GADJC,mBAAA,CAAmC,OAAnCC,UAAmC,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}