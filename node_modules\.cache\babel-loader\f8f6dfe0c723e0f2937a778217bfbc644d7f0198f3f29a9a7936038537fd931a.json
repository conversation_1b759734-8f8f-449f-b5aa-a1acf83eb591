{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\r\n * @param extent Both extent[0] and extent[1] should be valid number.\r\n *               Should be extent[0] < extent[1].\r\n * @param splitNumber splitNumber should be >= 1.\r\n */\nexport function intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var span = extent[1] - extent[0];\n  var interval = result.interval = nice(span / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\r\n * @return interval precision\r\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nexport function normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nexport function scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}", "map": {"version": 3, "names": ["getPrecision", "round", "nice", "quantityExponent", "isValueNice", "val", "exp10", "Math", "pow", "abs", "f", "isIntervalOrLogScale", "scale", "type", "intervalScaleNiceTicks", "extent", "splitNumber", "minInterval", "maxInterval", "result", "span", "interval", "precision", "intervalPrecision", "getIntervalPrecision", "niceTickExtent", "ceil", "floor", "fixExtent", "increaseInterval", "clamp", "idx", "max", "min", "isFinite", "contain", "normalize"], "sources": ["E:/1-test/test-echarts-bar3d/node_modules/echarts/lib/scale/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\r\n * @param extent Both extent[0] and extent[1] should be valid number.\r\n *               Should be extent[0] < extent[1].\r\n * @param splitNumber splitNumber should be >= 1.\r\n */\nexport function intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var span = extent[1] - extent[0];\n  var interval = result.interval = nice(span / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\r\n * @return interval precision\r\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nexport function normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nexport function scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC/E,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAE;EAC/B,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,gBAAgB,CAACI,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC;EACzD,IAAIK,CAAC,GAAGH,IAAI,CAACE,GAAG,CAACJ,GAAG,GAAGC,KAAK,CAAC;EAC7B,OAAOI,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AAC5D;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU,IAAID,KAAK,CAACC,IAAI,KAAK,KAAK;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACpF,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,IAAI,GAAGL,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EAChC,IAAIM,QAAQ,GAAGF,MAAM,CAACE,QAAQ,GAAGnB,IAAI,CAACkB,IAAI,GAAGJ,WAAW,EAAE,IAAI,CAAC;EAC/D,IAAIC,WAAW,IAAI,IAAI,IAAII,QAAQ,GAAGJ,WAAW,EAAE;IACjDI,QAAQ,GAAGF,MAAM,CAACE,QAAQ,GAAGJ,WAAW;EAC1C;EACA,IAAIC,WAAW,IAAI,IAAI,IAAIG,QAAQ,GAAGH,WAAW,EAAE;IACjDG,QAAQ,GAAGF,MAAM,CAACE,QAAQ,GAAGH,WAAW;EAC1C;EACA;EACA,IAAII,SAAS,GAAGH,MAAM,CAACI,iBAAiB,GAAGC,oBAAoB,CAACH,QAAQ,CAAC;EACzE;EACA,IAAII,cAAc,GAAGN,MAAM,CAACM,cAAc,GAAG,CAACxB,KAAK,CAACM,IAAI,CAACmB,IAAI,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAAC,GAAGA,QAAQ,EAAEC,SAAS,CAAC,EAAErB,KAAK,CAACM,IAAI,CAACoB,KAAK,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAAC,GAAGA,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAC1KM,SAAS,CAACH,cAAc,EAAEV,MAAM,CAAC;EACjC,OAAOI,MAAM;AACf;AACA,OAAO,SAASU,gBAAgBA,CAACR,QAAQ,EAAE;EACzC,IAAIf,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,gBAAgB,CAACkB,QAAQ,CAAC,CAAC;EACpD;EACA,IAAIX,CAAC,GAAGW,QAAQ,GAAGf,KAAK;EACxB,IAAI,CAACI,CAAC,EAAE;IACNA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;IAClBA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;IAClBA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM;IACL;IACAA,CAAC,IAAI,CAAC;EACR;EACA,OAAOT,KAAK,CAACS,CAAC,GAAGJ,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA,OAAO,SAASkB,oBAAoBA,CAACH,QAAQ,EAAE;EAC7C;EACA,OAAOrB,YAAY,CAACqB,QAAQ,CAAC,GAAG,CAAC;AACnC;AACA,SAASS,KAAKA,CAACL,cAAc,EAAEM,GAAG,EAAEhB,MAAM,EAAE;EAC1CU,cAAc,CAACM,GAAG,CAAC,GAAGxB,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAAC0B,GAAG,CAACR,cAAc,CAACM,GAAG,CAAC,EAAEhB,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF;AACA;AACA,OAAO,SAASa,SAASA,CAACH,cAAc,EAAEV,MAAM,EAAE;EAChD,CAACmB,QAAQ,CAACT,cAAc,CAAC,CAAC,CAAC,CAAC,KAAKA,cAAc,CAAC,CAAC,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/D,CAACmB,QAAQ,CAACT,cAAc,CAAC,CAAC,CAAC,CAAC,KAAKA,cAAc,CAAC,CAAC,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/De,KAAK,CAACL,cAAc,EAAE,CAAC,EAAEV,MAAM,CAAC;EAChCe,KAAK,CAACL,cAAc,EAAE,CAAC,EAAEV,MAAM,CAAC;EAChC,IAAIU,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,EAAE;IACzCA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC;EACvC;AACF;AACA,OAAO,SAASU,OAAOA,CAAC9B,GAAG,EAAEU,MAAM,EAAE;EACnC,OAAOV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC,IAAIV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA,OAAO,SAASqB,SAASA,CAAC/B,GAAG,EAAEU,MAAM,EAAE;EACrC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3B,OAAO,GAAG;EACZ;EACA,OAAO,CAACV,GAAG,GAAGU,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC;AACpD;AACA,OAAO,SAASH,KAAKA,CAACP,GAAG,EAAEU,MAAM,EAAE;EACjC,OAAOV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}